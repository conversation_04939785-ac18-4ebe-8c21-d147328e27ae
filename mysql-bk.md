# Local Setup

- Set database to collaction of "utf8mb4_unicode_ci" and charset of "utf8mb4"

## Backup Db server

- IP: *************

## For uploading backup

\\*************\BackupDatabase
backupuser
Nova*1681826969*

- connect to new mysql session `mysql -u root`
- run the command `warnings` to see warnings also instead of just amount
- run the command `nowarnings` to turn them back off

- run the next commands so the backup doesnt hit an issue
- `SET FOREIGN_KEY_CHECKS=0;` to ignore
- `set global net_buffer_length=1000000;`
- `set global max_allowed_packet=1000000000;`
- `drop database dbname`
- `create database newdbname`
- `use newdbname`
- `source D:/path/to/file.sql`
- `SET FOREIGN_KEY_CHECKS=1;` to set checks back on
