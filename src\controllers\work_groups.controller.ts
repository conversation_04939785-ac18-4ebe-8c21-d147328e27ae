import type { Request, Response } from 'express';

import { WorkAreaGroups } from '@app/models/tickets.schema';

export async function getAreaGroups(req: Request, res: Response) {
  try {
    const { workAreaId } = req.body;

    if (!workAreaId) {
      return res.status(400).json({ ok: false });
    }

    if (typeof workAreaId !== 'number') {
      return res.status(400).json({ ok: false });
    }

    const getAllGroupArea = await WorkAreaGroups.query()
      .where('work_area_groups.work_area_id', workAreaId)
      .where('work_area_groups.work_status_id', 50)
      .orderBy('work_area_groups.name');

    if (getAllGroupArea.length > 0) {
      return res.status(200).json({ ok: true, data: getAllGroupArea });
    }
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}
