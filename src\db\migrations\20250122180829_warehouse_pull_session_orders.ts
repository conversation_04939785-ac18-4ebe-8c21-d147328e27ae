import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(
    'warehouse_pull_session_orders',
    (table: Knex.TableBuilder): void => {
      table.increments('id').unsigned().primary();
      table.integer('warehouse_pull_session_id', 10).notNullable().unsigned();
      table
        .foreign('warehouse_pull_session_id', 'wpsi_warehouse_pull_sessions_fk')
        .references('id')
        .inTable('warehouse_pull_sessions');
      table.integer('mo_id').notNullable();
      table
        .foreign('mo_id', 'mi_mo_numbers_fk')
        .references('mo_id')
        .inTable('mo_numbers');
      table.integer('work_area_voucher_id').nullable().unsigned();
      table.string('default_reason').nullable();
      table.string('default_comment').nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table
        .timestamp('updated_at')
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('warehouse_pull_session_orders');
}
