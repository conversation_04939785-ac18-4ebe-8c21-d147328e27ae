import { Router } from 'express';

import {
  getBraidInventoryItem,
  getBraidJobInfo,
  getBraidOrder,
  postBraidInfoByNumber,
  postBraidOrderJobProductionUsingInventory,
  postCreateBraidInventoryItem,
  postCreateBraidOrderJob,
  postCreateBraidOrderJobProduction,
  postCreateBraidOrderJobProductionWithInventory,
  postCreateFinishBraidOrderJobProduction,
  postFindBraidInventoryItemsBySku,
  postFinishBraidOrderJobProduction,
  postRemoveBraidOrderJobProduction,
  putUpdateBraidInventoryItem,
} from '@app/controllers/braid.controller';

const braidRouter = Router();

braidRouter.route('/order/bynumber').post(postBraidInfoByNumber);
braidRouter.route('/order/:id').get(getBraidOrder);

braidRouter.route('/job/create').post(postCreateBraidOrderJob);
braidRouter.route('/job/:id').get(getBraidJobInfo);
braidRouter
  .route('/job/:id/useinventoryitems')
  .post(postCreateBraidOrderJobProductionWithInventory);
braidRouter
  .route('/job/:id/useinventory')
  .post(postBraidOrderJobProductionUsingInventory);

braidRouter.route('/production/create').post(postCreateBraidOrderJobProduction);
braidRouter.route('/production/finish').post(postFinishBraidOrderJobProduction);
braidRouter
  .route('/production/createfinish')
  .post(postCreateFinishBraidOrderJobProduction);
braidRouter
  .route('/production/:id/remove')
  .delete(postRemoveBraidOrderJobProduction);

braidRouter.route('/inventory/create').post(postCreateBraidInventoryItem);
braidRouter
  .route('/inventory/findbysku')
  .post(postFindBraidInventoryItemsBySku);
braidRouter.route('/inventory/:id').get(getBraidInventoryItem);
braidRouter.route('/inventory/:id/edit').put(putUpdateBraidInventoryItem);

export { braidRouter };
