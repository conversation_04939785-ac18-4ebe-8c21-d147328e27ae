import type { Request, Response } from 'express';

const {
  WorkAreaTicketStatuses,
  WorkStatuses,
} = require('../models/tickets.schema');

export async function getGlobalStatus(req: Request, res: Response) {
  try {
    const getWorkStatusesName = await WorkStatuses.query();

    return res.status(200).json({
      ok: true,
      data: getWorkStatusesName,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function addStatusArea(req: Request, res: Response) {
  try {
    const { statusName, workAreaId, globalStatus, colorStatus } = req.body;

    // NOTE: la logica para el campo de secuencia está pendiente
    const getSequenceHightHigh = await WorkAreaTicketStatuses.query()
      .where('work_area_ticket_statuses.work_area_id', workAreaId)
      .where('work_area_ticket_statuses.work_status_id', globalStatus)
      .max({ highResult: 'work_area_ticket_statuses.sequence' });

    const addNewStatusArea = await WorkAreaTicketStatuses.query().insert({
      work_area_id: workAreaId,
      name: statusName,
      color_hex: colorStatus.slice(1),
      work_status_id: globalStatus,
      sequence: getSequenceHightHigh[0].highResult + 1000,
    });

    return res.status(200).json({
      ok: true,
      data: addNewStatusArea,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function updateStatusArea(req: Request, res: Response) {
  try {
    const { dataInputs, statusSelected } = req.body;

    const { nameStatusArea, generalStatus, colorStatusArea } = dataInputs;

    const updateStatus = await WorkAreaTicketStatuses.query()
      .update({
        name: nameStatusArea ? nameStatusArea : statusSelected[0].name,
        color_hex: colorStatusArea
          ? colorStatusArea.slice(1)
          : statusSelected[0].colorHex,
        work_status_id: generalStatus
          ? generalStatus
          : statusSelected[0].generalStatus,
      })
      .where('work_area_ticket_statuses.id', statusSelected[0].id);

    return res.status(200).json({
      ok: true,
      data: updateStatus,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getStatusArea(req: Request, res: Response) {
  try {
    const { workAreaId } = req.query;

    const getStatusArea = await WorkAreaTicketStatuses.query()
      .join(
        'work_statuses',
        'work_area_ticket_statuses.work_status_id',
        '=',
        'work_statuses.id'
      )
      .where('work_area_ticket_statuses.work_area_id', workAreaId)
      .select(
        { generalStatus: 'work_statuses.id' },
        { workStatusName: 'work_statuses.name' },
        'work_area_ticket_statuses.name',
        { colorHex: 'work_area_ticket_statuses.color_hex' },
        'work_area_ticket_statuses.id'
      )
      .orderBy('work_area_ticket_statuses.sequence', 'asc');

    return res.status(200).json({
      ok: true,
      data: getStatusArea,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}
