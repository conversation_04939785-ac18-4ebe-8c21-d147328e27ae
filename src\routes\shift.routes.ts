import { Router } from 'express';

import {
  addEmployeeInGroup,
  addEmployeeShiftLeave,
  createMultiplesShift,
  createShift,
  createShiftFromShift,
  deleteEmployeeInGroup,
  deleteEmployeeShiftLeave,
  deleteMultiplesShift,
  getAllLeaveReasons,
  getAllRolesByWorkType,
  getAllVoidStatuses,
  getEmployeeByGroup,
  getEmployeesInShift,
  getShiftInfo,
  getShiftInfoByArea,
  getShiftInfoByGroupWeek,
  insertEmployeeInShift,
  restoreEmployeeInGroup,
  updateEmployeeLateShiftMinutes,
  updateEmployeeRole,
  updateEmployeeShiftLeave,
  updateEmployeeShiftTime,
  updateEmployeeToVoidShiftStatus,
  updateEmployeesGroupRoles,
  updateShiftInformation,
} from '@app/controllers/shift.controller';

const shiftsRouter = Router();

shiftsRouter.route('/employeebygroup').post(getEmployeeByGroup);
shiftsRouter.route('/insertEmployeeInShift').post(insertEmployeeInShift);
shiftsRouter.route('/getShiftInformation').post(getShiftInfo);
shiftsRouter.route('/createShift').post(createShift);
shiftsRouter.route('/getShiftByArea').post(getShiftInfoByArea);
shiftsRouter.route('/updateShiftInformation').post(updateShiftInformation);
shiftsRouter.route('/getShiftByAreaWeek').post(getShiftInfoByGroupWeek);
shiftsRouter.route('/createShiftFromPrevShift').post(createShiftFromShift);
shiftsRouter.route('/createMultiplesShift').post(createMultiplesShift);
shiftsRouter.route('/deleteEmployeeInGroup').post(deleteEmployeeInGroup);
shiftsRouter.route('/restoreEmployeeInGroup').post(restoreEmployeeInGroup);
shiftsRouter.route('/addEmployeeInGroup').post(addEmployeeInGroup);
shiftsRouter.route('/getEmployeesInShift').post(getEmployeesInShift);
shiftsRouter.route('/deleteMultiplesShift').post(deleteMultiplesShift);
shiftsRouter.route('/getAllVoidStatuses').post(getAllVoidStatuses);
shiftsRouter.route('/getAllLeaveReasons').post(getAllLeaveReasons);
shiftsRouter
  .route('/updateEmployeeToVoidShiftStatus')
  .post(updateEmployeeToVoidShiftStatus);
shiftsRouter
  .route('/updateEmployeeLateShiftMinutes')
  .post(updateEmployeeLateShiftMinutes);
shiftsRouter.route('/updateEmployeeShiftTime').post(updateEmployeeShiftTime);
shiftsRouter.route('/addEmployeeShiftLeave').post(addEmployeeShiftLeave);
shiftsRouter.route('/updateEmployeeShiftLeave').post(updateEmployeeShiftLeave);
shiftsRouter.route('/getAllRolesByWorkType').post(getAllRolesByWorkType);
shiftsRouter.route('/updateEmployeeRole').post(updateEmployeeRole);
shiftsRouter
  .route('/updateEmployeesGroupRoles')
  .post(updateEmployeesGroupRoles);
shiftsRouter.route('/deleteEmployeeShiftLeave').post(deleteEmployeeShiftLeave);

export { shiftsRouter };
