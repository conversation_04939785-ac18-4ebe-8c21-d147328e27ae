import type { ModelObject } from 'objection';

import { Model } from '@app/db';

export class WorkFragments extends Model {
  static get tableName(): string {
    return 'work_fragments';
  }

  id!: number;
  mo_id!: number;
  fragment_type_id!: number;
  work_fragment_group_id!: number;
  name!: string;
  created_at: string;
  updated_at: string;
  custom_integer_1: number;
  custom_decimal_1: number;
  custom_decimal_2: number;
  custom_string_1: string;
  barcode: string;
  custom_string_2: string;
  custom_string_3: string;

  static totalFragments({ where }): Promise<number> {
    return this.query().where(where).resultSize();
  }
}
export type WorkFragmentShape = ModelObject<WorkFragments>;

export class WorkFragmentCustomFields extends Model {
  static get tableName(): string {
    return 'work_fragment_custom_fields';
  }

  id!: number;
  name!: string;
  work_fragment_type_id!: number;
  column_name!: string;
  type!: string;
  list_values!: string | null;
}

export class WorkFragmentLog extends Model {
  static get tableName(): string {
    return 'work_fragment_log';
  }
}
export class WorkFragmentTypes extends Model {
  static get tableName(): string {
    return 'work_fragment_types';
  }
}

export class WorkFragmentGroupRolls extends Model {
  static get tableName(): string {
    return 'work_fragment_group_rolls';
  }
}
