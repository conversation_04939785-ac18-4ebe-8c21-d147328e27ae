import { GoodAllocations } from '@app/models/barcode_stickers.schema';
import { buildLogger } from '@app/settings';

const logger = buildLogger('service:barcode_stickers');

interface IStickerInfo {
  option: number;
  value: string;
}

export const getStickerInfo = async ({
  option,
  value,
}: IStickerInfo): Promise<GoodAllocations[]> => {
  try {
    if (!option || !value) {
      throw new Error('No option or value found');
    }

    const table = {
      1: 'ManufactureNumber',
      2: 'PONumber_',
      3: 'OrderNumber',
      4: 'ManufactureBarcode_',
    };

    const column: string | undefined = table[option];

    if (!column) {
      throw new Error('Invalid option');
    }

    const data = await GoodAllocations.query().where(column, value);

    if (data.length === 0) {
      throw new Error(`${column} not found`);
    }

    return data;
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      throw Error('Error getting sticker info');
    }
  }
};
