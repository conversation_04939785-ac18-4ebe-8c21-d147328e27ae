import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(
    'test_knex_migration',
    (t: Knex.TableBuilder) => {
      t.increments('id').unsigned().primary();
      t.dateTime('createdAt');
      t.dateTime('updatedAt');
      t.dateTime('deletedAt').notNullable();
      t.date('dateonlyfield');
      t.string('name').notNullable();
      t.text('decription').nullable();
      t.decimal('price', 6, 2).notNullable();
      t.enum('category', ['apparel', 'electronics', 'furniture']).notNullable();
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('test_knex_migration');
}
