import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(
    'mo_twill_laser_job_types',
    (table: Knex.TableBuilder): void => {
      table.increments('id').unsigned().primary();
      table.string('name').notNullable();
      table.string('description').notNullable();
      table.boolean('is_active').notNullable().defaultTo(true);
      table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
      table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
    }
  );

  await knex.schema.createTable(
    'mo_twill_laser_jobs',
    (table: Knex.TableBuilder): void => {
      table.increments('id').unsigned().primary();
      table.integer('mo_id').notNullable();
      table.integer('mo_twill_laser_job_type_id', 10).notNullable().unsigned();
      table.integer('colors_normal').notNullable().unsigned();
      table.integer('colors_special').notNullable().unsigned();
      table.boolean('is_active').notNullable().defaultTo(true);
      table.timestamp('finished_at').nullable();
      table.string('machine_id').nullable();
      table.integer('employee_id').nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
      table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
      table.foreign('mo_id').references('mo_id').inTable('mo_numbers');
      table
        .foreign('mo_twill_laser_job_type_id')
        .references('id')
        .inTable('mo_twill_laser_job_types');
    }
  );

  await knex.schema.createTable(
    'mo_twill_laser_job_consumptions',
    (table: Knex.TableBuilder): void => {
      table.increments('id').unsigned().primary();
      table.integer('mo_twill_laser_job_id', 10).notNullable().unsigned();
      table.integer('height').notNullable().unsigned();
      table.integer('width').notNullable().unsigned();
      table.integer('quantity').notNullable().unsigned();
      table.boolean('is_active').notNullable().defaultTo(true);
      table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
      table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
      table
        .foreign('mo_twill_laser_job_id')
        .references('id')
        .inTable('mo_twill_laser_jobs');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('mo_twill_laser_job_consumptions');
  await knex.schema.dropTable('mo_twill_laser_jobs');
  await knex.schema.dropTable('mo_twill_laser_job_types');
}
