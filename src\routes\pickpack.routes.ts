import { Router } from 'express';

import {
  createMoContainerReq,
  getStation,
  getStations,
  listOrders,
  nextStationContainerToSplit,
} from '@app/controllers/pickpack.controller';

const pickpackRouter = Router();

pickpackRouter.route('/listOrders').get(listOrders);
pickpackRouter.route('/stations').get(getStations);
pickpackRouter.route('/stations/:station_id').get(getStation);
pickpackRouter
  .route('/stations/:station_id/nextBin')
  .get(nextStationContainerToSplit);

pickpackRouter.route('/container/createfrommo').post(createMoContainerReq);

export { pickpackRouter };
