import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(
    'plotter_print_groups',
    (table: Knex.TableBuilder) => {
      table.increments('id').unsigned().primary();
      table.integer('created_by_employee_id').unsigned().notNullable();
      table
        .timestamp('created_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      table
        .timestamp('updated_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('plotter_print_groups');
}
