import type { Request, Response } from 'express';

import {
  braidInventoryItemById,
  braidOrderById,
  braidOrderJobById,
  briadOrderByNumber,
  createAndFinishBraidOrderJobProduction,
  createBraidInventoryItemFromScratch,
  createBraidOrderJob,
  createBraidOrderJobProduction,
  createBraidOrderJobProductionUsingInventory,
  createBraidOrderJobProductionWithInventoryItems,
  findBraidInventoryItemsBySku,
  finishBraidOrderJobProduction,
  removeBraidOrderJob,
  removeBraidOrderJobProduction,
  updateBraidInventoryItem,
} from '@app/services/braid';

export async function getBraidOrder(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const braidOrderId = Number(req.params.id);

  try {
    const orderInfo = await braidOrderById(braidOrderId);

    return res.status(200).json({
      ok: true,
      data: orderInfo,
    });
  } catch (error) {
    return res.status(500).json({ ok: false, error: error.message });
  }
}

interface BraidInfoByNumberBody {
  braid_num: string;
}

export async function postBraidInfoByNumber(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { braid_num: braidOrderNumber }: BraidInfoByNumberBody = req.body;

  try {
    const orderInfo = await briadOrderByNumber(braidOrderNumber);

    return res.status(200).json({
      ok: true,
      data: orderInfo,
    });
  } catch (error) {
    return res.status(500).json({ ok: false, error: error.message });
  }
}

export async function getBraidJobInfo(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const braidOrderJobId = Number(req.params.id);

  try {
    const orderInfo = await braidOrderJobById(braidOrderJobId);

    return res.status(200).json({
      ok: true,
      data: orderInfo,
    });
  } catch (error) {
    return res.status(500).json({ ok: false, error: error.message });
  }
}

interface CreateBraidOrderJobBody {
  braid_order_id: number;
  is_repo: boolean;
  required_yards?: number | null;
}

export const postCreateBraidOrderJob = async (
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> => {
  try {
    const { braid_order_id, is_repo, required_yards }: CreateBraidOrderJobBody =
      req.body;

    console.log('creating braid order body', req.body);

    const newBraidOrderJob = await createBraidOrderJob(
      braid_order_id,
      is_repo,
      required_yards
    );

    return res.status(200).json({
      ok: true,
      data: newBraidOrderJob,
    });
  } catch (error) {
    return res.status(500).json({ ok: false, error: error.message });
  }
};

interface CreateBraidOrderJobProductionBody {
  braid_order_job_id: number;
  employee_id: number;
  machine_id: number;
  yards: number;
  finish: boolean;
}

export const postCreateBraidOrderJobProduction = async (
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> => {
  try {
    const {
      braid_order_job_id,
      employee_id,
      machine_id,
      yards,
      finish,
    }: CreateBraidOrderJobProductionBody = req.body;

    const newBraidOrderJobProduction = await createBraidOrderJobProduction(
      braid_order_job_id,
      employee_id,
      machine_id,
      {
        yards,
        finish,
      }
    );

    return res.status(200).json({
      ok: true,
      data: newBraidOrderJobProduction,
    });
  } catch (error) {
    return res.status(500).json({ ok: false, error: error.message });
  }
};

interface finishBraidOrderJobProductionBody {
  braid_order_job_production_id: number;
  yards: number;
  reason_for_extra?: string | null;
  machine_id?: number | null;
  employee_id?: number | null;
}

export const postFinishBraidOrderJobProduction = async (
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> => {
  try {
    const {
      braid_order_job_production_id,
      yards,
      machine_id,
      employee_id,
    }: finishBraidOrderJobProductionBody = req.body;

    if (!braid_order_job_production_id) {
      throw new Error('braid_order_job_production_id is required');
    }

    const finishedBraidOrderJobProduction = await finishBraidOrderJobProduction(
      braid_order_job_production_id,
      {
        yards,
        employee_id,
        machine_id,
      }
    );

    return res.status(200).json({
      ok: true,
      data: finishedBraidOrderJobProduction,
    });
  } catch (error) {
    console.log('error', error);
    return res.status(500).json({ ok: false, error: error.message });
  }
};

interface createBraidInventoryItemBody {
  pattern: string;
  colors: string;
  size: number;
  fabric: string;
  yards: number;
  location: string;
  notes: string;
}

export const postCreateBraidInventoryItem = async (
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> => {
  try {
    const {
      pattern,
      colors,
      size,
      fabric,
      yards,
    }: createBraidInventoryItemBody = req.body;

    const newBraidInventoryItem = await createBraidInventoryItemFromScratch(
      pattern,
      colors,
      size,
      fabric,
      yards,
      {
        location: req.body.location,
        notes: req.body.notes,
      }
    );

    return res.status(200).json({
      ok: true,
      data: newBraidInventoryItem,
    });
  } catch (error) {
    return res.status(500).json({ ok: false, error: error.message });
  }
};

export const getBraidInventoryItem = async (
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> => {
  const { id } = req.params;

  try {
    const braidInventoryItem = await braidInventoryItemById(Number(id));

    return res.status(200).json({
      ok: true,
      data: braidInventoryItem,
    });
  } catch (error) {
    return res.status(500).json({ ok: false, error: error.message });
  }
};

interface findBraidInventoryItemsBySkuBody {
  fabric: string;
  pattern: string;
  colors: string;
  size: number;
  page: number;
  limit: number;
}

export const postFindBraidInventoryItemsBySku = async (
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> => {
  const { fabric, pattern, colors, size, page, limit } =
    req.body as findBraidInventoryItemsBySkuBody;

  try {
    const braidInventoryItems = await findBraidInventoryItemsBySku(
      {
        fabric,
        pattern,
        colors,
        size,
      },
      {
        page,
        limit,
      }
    );

    return res.status(200).json({
      ok: true,
      ...braidInventoryItems,
    });
  } catch (error) {
    return res.status(500).json({ ok: false, error: error.message });
  }
};

interface updateBraidInventoryItemBody {
  location: string;
  notes: string;
}

export const putUpdateBraidInventoryItem = async (
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> => {
  const { id } = req.params;

  try {
    const updatedBraidInventoryItem = await updateBraidInventoryItem(
      Number(id),
      req.body as updateBraidInventoryItemBody
    );

    return res.status(200).json({
      ok: true,
      data: updatedBraidInventoryItem,
    });
  } catch (error) {
    return res.status(500).json({ ok: false, error: error.message });
  }
};

interface createFinishBraidOrderJobProductionBody {
  braid_order_job_id: number;
  yards: number;
  machine_id: number;
  employee_id: number;
}

export const postCreateFinishBraidOrderJobProduction = async (
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> => {
  try {
    const {
      braid_order_job_id,
      yards,
      machine_id,
      employee_id,
    }: createFinishBraidOrderJobProductionBody = req.body;

    if (!braid_order_job_id) {
      throw new Error('braid_order_job_id is required');
    }

    const finishedBraidOrderJobProduction =
      await createAndFinishBraidOrderJobProduction(
        braid_order_job_id,
        yards,
        employee_id,
        machine_id
      );

    return res.status(200).json({
      ok: true,
      data: finishedBraidOrderJobProduction,
    });
  } catch (error) {
    console.log('error', error);
    return res.status(500).json({ ok: false, error: error.message });
  }
};

interface createBraidOrderJobProductionWithInventoryBody {
  allocations: {
    yards: number;
    braid_inventory_item_id: number;
  }[];
}

export const postCreateBraidOrderJobProductionWithInventory = async (
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> => {
  try {
    const { id } = req.params;
    const { allocations }: createBraidOrderJobProductionWithInventoryBody =
      req.body;

    const newBraidOrderJobProduction =
      await createBraidOrderJobProductionWithInventoryItems(
        Number(id),
        allocations
      );

    return res.status(200).json({
      ok: true,
      data: newBraidOrderJobProduction,
    });
  } catch (error) {
    return res.status(500).json({ ok: false, error: error.message });
  }
};

interface BraidOrderJobProductionUsingInventoryBody {
  yards: number;
}

export const postBraidOrderJobProductionUsingInventory = async (
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> => {
  try {
    const { id } = req.params;
    const { yards }: BraidOrderJobProductionUsingInventoryBody = req.body;

    const braidOrderJobInfo = await createBraidOrderJobProductionUsingInventory(
      Number(id),
      yards
    );

    return res.status(200).json({
      ok: true,
      data: braidOrderJobInfo,
    });
  } catch (error) {
    return res.status(500).json({ ok: false, error: error.message });
  }
};

export const postRemoveBraidOrderJobProduction = async (
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> => {
  const { id } = req.params;
  const { remove_finished, ignore_inventory } = req.body as {
    remove_finished: boolean;
    ignore_inventory: boolean;
  };

  try {
    const braidJobInfo = await removeBraidOrderJobProduction(Number(id), {
      remove_finished,
      ignore_inventory,
    });

    return res.status(200).json({ ok: true, data: braidJobInfo });
  } catch (error) {
    return res.status(500).json({ ok: false, error: error.message });
  }
};

export const postRemoveBraidOrderJob = async (
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> => {
  const { braid_order_job_id } = req.params;

  try {
    const braidJobInfo = await removeBraidOrderJob(Number(braid_order_job_id));

    return res.status(200).json({ ok: true, data: braidJobInfo });
  } catch (error) {
    return res.status(500).json({ ok: false, error: error.message });
  }
};
