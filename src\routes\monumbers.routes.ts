import { Router } from 'express';

import {
  GetOrdersActiveByMo,
  getActiveVoucherList,
  getInfoMoActive,
  getMainAndSecondaryVouchersFromMo,
  getMosActive,
  getSizesByMo,
  getVoucherBarcode,
  getVoucherMo,
  getVoucherMoByMoid,
  searchMoByNum,
  searchMoByNumPlotterView,
  searchOrderByMoOrVoucher,
} from '@app/controllers/monumbers.controller';
import { SearchOrderByMoOrVoucherSchema } from '@app/interface/zod_schemas';
import { validateData } from '@app/middlewares/validateData.middleware';

const vouchersRouter: Router = Router();

vouchersRouter.route('/barcode/:postBarcode').get(getVoucherBarcode);

vouchersRouter.route('/mo/:postMo').get(getVoucherMo);

vouchersRouter.route('/mid/:postMo').get(getVoucherMoByMoid);

vouchersRouter.route('/voucherlist').post(getActiveVoucherList);

vouchersRouter.route('/byMo/:mo/:client').get(searchMoByNum);

vouchersRouter.route('/byPlotterMO').post(searchMoByNumPlotterView);

vouchersRouter.route('/orders-active').get(GetOrdersActiveByMo);

vouchersRouter.route('/mos/mo').get(getMosActive);
vouchersRouter.route('/mos/mo').post(getInfoMoActive);
vouchersRouter
  .route('/mos/main/secondary/vouchers/:moID')
  .get(getMainAndSecondaryVouchersFromMo);
vouchersRouter
  .route('/search/order')
  .post(validateData(SearchOrderByMoOrVoucherSchema), searchOrderByMoOrVoucher);
vouchersRouter.route('/sizes/:moID').get(getSizesByMo);

export { vouchersRouter };
