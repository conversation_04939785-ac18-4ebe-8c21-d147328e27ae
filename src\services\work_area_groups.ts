import { WorkAreas } from '@app/models/tickets.schema';

export interface WorkAreaGroupDetails {
  groupname: string;
  name: string;
  id: number;
  first_name: string;
  last_name: string;
  new_version: boolean;
  update_customer: number;
  type: number;
  area: number;
  supervisor: number;
  manager: number;
  ticket_view: Date;
}

export const getGroupDetailsByBarcode = async (barcode: string) => {
  // buscar en work area groups por medio de barcode si existe buscar ese id en work area para obtener area name y customer code 0. Sino existe retornar Falso
  const getWorkArea = await WorkAreas.query()
    .join(
      'work_area_groups',
      'work_areas.work_area_id',
      '=',
      'work_area_groups.work_area_id'
    )
    .leftJoin(
      'employees',
      'work_area_groups.supervisor_employee_id ',
      '=',
      'employees.employee_id '
    )
    .join('work_types', 'work_areas.work_type_id', '=', 'work_types.id')
    .where('work_area_groups.barcode', barcode)
    .where('work_areas.work_status_id', 50)
    .select(
      'work_area_groups.name as groupname',
      'work_types.name',
      'work_area_groups.id',
      'employees.first_name',
      'employees.last_name',
      'work_area_groups.new_version',
      'work_area_groups.update_customer',
      { type: 'work_areas.work_type_id' },
      { area: 'work_areas.work_area_id' },
      { supervisor: 'work_area_groups.supervisor_employee_id' },
      { manager: 'work_area_groups.manager_employee_id' },
      { ticket_view: 'work_areas.disabled_date' }
    )
    .castTo<WorkAreaGroupDetails[]>();

  return getWorkArea;
};
