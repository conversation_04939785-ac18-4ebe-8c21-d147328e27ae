import { cprlettering } from './cprletteringConfig';
import { cprweekly } from './cprweeklyConfig';

export type ReportType = 'cprweekly' | 'cprlettering';

export type PageType = 'accepted' | 'rejected' | 'summary'; // rejected and summary pages are skipped

export type ReportFormatType =
  | 'fat'
  | 'normal'
  | 'thin'
  | 'border'
  | 'fatborder'
  | 'suyapa'
  | 'suyapaNew'
  | 'suyapaNewFat';

export type LineItemTypes =
  | 'item'
  | 'subitem'
  | 'classHeader'
  | 'classTotal'
  | 'header'
  | 'finalTotal';

export interface FilePart {
  x: number;
  y: number;
  w: number;
  sw: number;
  A: string;
  // R: { T: string; S: number; TS: any[] };
  // oc: any; // undefined
  text: string;
  page: number;
}

export interface MadeItem {
  page: number; // 1;
  row: number; // 13.68;
  class_code: string; // 'JVT';
  class_name: string; // 'JV LEAGUE TWILL';
  style: string; // 'JVTGT2';
  order: number; // 14765358;
  vch: number; // 2;
  ltr?: number | null; // 8;
  size?: number | null; // 3;
  CP?: string | null; // 'T';
  qty: number; // 12;
  unit: number; // 0;
  cut: number; // 0;
  sew: number; // 48;
  total: number; // 48;
  stars?: boolean;
  style_text_overlap_order?: boolean;
  sub_code?: string;
  sub_labor?: number;
  prt?: number;
  prs?: number;
  art?: number;
  diff: number;
  factory?: string;
}

export interface ReportData {
  report_type: ReportType | null;
  run_date: string | null;
  run_time: string | null;
  invoice_number: string | null;
  report_start: string | null;
  report_end: string | null;
  pay_date: string | null;
  accepted_format_type: string | null;
  accepted_pages: number[];
  rejected_pages: number[];
  summary_pages: number[];
}

export interface SummaryData {
  page: number; // 318,
  row: number; // 12.593,
  class_code: string; // '551',
  class_name: string; // 'MOTIONFLEX TOPS MEN',
  sum_item_count: number; // 2,
  sum_unit_count: number; // 2,
  sum_labor: number; // 0,
  sum_cut: number; // 3.08,
  sum_sew: number; // 0,
  sum_total: number; // used in lettering
  summed_items_total?: number;
}

export interface ClassHeader {
  class_code: string | null;
  class_name: string | null;
}

export type LineItemExpected = {
  key: string | null;
  x: number;
  buffer?: number;
  // replaces x & buffer, not implemented yet
  min?: number;
  max?: number;
  combineTillNext?: boolean;
  ignoreRepeat?: boolean;
  trim?: boolean;
  // means should be combined to previous expected and not run as main item
  merged?: boolean;
};

export type LineItemInfo = {
  type: LineItemTypes;
  expected: LineItemExpected[];
  rowTypeCondition: (
    parts: FilePart[],
    lastRowType: string,
    format: ReportFormatType | null
  ) => boolean;
  ignoreRowData?: boolean;
  reportType?: ReportType;
  pageType?: PageType; // rejected and summary pages are skipped
  reportFormat?: ReportFormatType;
  ignorePartFunc?: (part: FilePart) => boolean;
  allowWithoutReportType?: boolean;
  matchPartIndexToExpected?: boolean;
  infoCheckFunc?: (
    parts: FilePart[],
    curItem: Partial<MadeItem>,
    curHeader: Partial<ClassHeader>,
    curReport: ReportData
  ) => void;
  specialPartProcess?: (
    parts: FilePart[]
  ) => [
    FilePart[],
    { [K in keyof Partial<MadeItem>]: MadeItem[K] } | null | undefined,
    { [P in keyof Partial<ReportData>]: ReportData[P] } | null | undefined
  ];
};

export const testFunction = () => {
  console.log('test function');
};

export const lineItemTypes: {
  [key in ReportType]: {
    [lineName: string]: LineItemInfo;
  };
} = {
  cprweekly: cprweekly,
  cprlettering: cprlettering,
};

export const getRowType = (
  fileType: ReportType | null,
  parts: FilePart[],
  lastRowType: string,
  format: ReportFormatType | null,
  page: number,
  row: number
): [
  rowTypeName: string,
  fileType?: ReportType,
  checkedReportType?: ReportType
] => {
  if (!parts || !parts.length) {
    console.log('no parts', page, row);
    throw new Error('no parts');
  }

  if (
    parts.length === 1 &&
    parts[0].text.includes(
      '---------------------------------------------------------------'
    )
  ) {
    return ['skiprow'];
  }

  if (
    // lastRowType === 'topColumnHeadersFat' &&
    parts.length > 10 &&
    parts[6].text === '-' &&
    parts[7].text === '-' &&
    parts[8].text === '-' &&
    parts[9].text === '-' &&
    parts[10].text === '-'
  ) {
    return ['skiprow'];
  }

  // loop lineItemTypes and see if any match using rowTypeCondition function
  let foundLineTypeName: string | null = null;
  let foundLineReportType: ReportType | null = null;
  let foundCheckedReportType: ReportType | null = null;
  for (const reportType of Object.keys(lineItemTypes)) {
    for (const lineType of Object.keys(
      lineItemTypes[reportType as ReportType]
    )) {
      const lineTypeInfo = lineItemTypes[reportType as ReportType][lineType];
      if (!fileType && !lineTypeInfo.allowWithoutReportType) {
        continue;
      }
      if (
        fileType &&
        !lineTypeInfo.allowWithoutReportType &&
        reportType !== fileType
      ) {
        continue;
      }
      // console.log('testing', lineType, 'for', reportType);
      if (
        lineTypeInfo.rowTypeCondition &&
        lineTypeInfo.rowTypeCondition(parts, lastRowType, format)
      ) {
        foundLineTypeName = lineType;
        foundLineReportType = lineTypeInfo.reportType;
        foundCheckedReportType = reportType as ReportType;
        break;
      }
    }
  }

  if (foundLineTypeName) {
    return [foundLineTypeName, foundLineReportType, foundCheckedReportType];
  }

  return null;
};
