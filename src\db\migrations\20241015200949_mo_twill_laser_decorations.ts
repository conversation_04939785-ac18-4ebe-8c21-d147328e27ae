import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(
    'mo_twill_laser_decorations',
    (table: Knex.TableBuilder): void => {
      table.increments('id').unsigned().primary();
      table.integer('mo_id').notNullable();
      table.integer('child_mo_id').nullable().unique();
      table.integer('mo_twill_laser_job_type_id', 10).notNullable().unsigned();
      table.text('comment').nullable();
      table.boolean('is_active').notNullable().defaultTo(true);
      table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
      table
        .timestamp('updated_at')
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
      table
        .foreign('mo_id', 'mi_mo_numbers_foreign')
        .references('mo_id')
        .inTable('mo_numbers');
      table
        .foreign('child_mo_id', 'cmi_mo_numbers_foreign')
        .references('mo_id')
        .inTable('mo_numbers');
      table
        .foreign(
          'mo_twill_laser_job_type_id',
          'mtljti_mo_twill_laser_job_types_foreign'
        )
        .references('id')
        .inTable('mo_twill_laser_job_types');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('mo_twill_laser_decorations');
}
