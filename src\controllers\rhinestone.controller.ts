import dayjs from 'dayjs';
import type { Request, Response } from 'express';
import { raw, ref, transaction } from 'objection';

import { roundToTheNextMultiple } from '@app/helpers/rounding';
import { Machine } from '@app/models/machines.schema';
import { MoNumber } from '@app/models/pedreria.schema';
import {
  RhinestoneColor,
  RhinestoneColorProvider,
  RhinestoneInventory,
  RhinestoneLog,
  RhinestoneMachineInventory,
  RhinestoneOrder,
  RhinestoneOrderItem,
  RhinestoneOrderRange,
  RhinestoneOrderRangeFile,
  RhinestoneProvider,
  RhinestoneRange,
  RhinestoneSize,
  RhinestoneSizeProvider,
} from '@app/models/rhinestone.schema';
import { createOrder } from '@app/services/rhinestone.services';
import { buildLogger } from '@app/settings';

const Excel = require('excel4node');

const logger = buildLogger('rhinestone.controller.ts');

export interface IOrder {
  moID: number;
  isRepo: boolean;
}

export interface IToken {
  area_id: number;
  employee_id: number;
}

export async function CreateOrder(req: Request, res: Response) {
  try {
    const { mo } = req.body as unknown as { mo: IOrder };
    const { area_id, employee_id } = req.body.tokenInfo as unknown as IToken;

    if (mo) throw new Error('No se puede crear una orden sin MO');

    const ordersId: number = await createOrder({
      mo,
      token: { area_id, employee_id },
    });

    if (ordersId) throw new Error('No se puede crear una orden sin MO');

    return res.status(201).json({
      ok: true,
      message: 'Orden creada',
      data: ordersId,
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function AddStonesToFile(req: Request, res: Response) {
  try {
    const { filePerRangeID, orderID } = req.query as unknown as {
      filePerRangeID: number;
      orderID: number;
    };

    const { stone } = req.body as unknown as {
      stone: {
        // TODO: Is this really a part number?
        partNumber: string | number;
        quantity: number;
        colorID: string | number;
        sizeID: string | number;
      };
    };

    if (!filePerRangeID || !orderID)
      throw new Error('No se puede agregar piedras a una orden sin ID');

    if (!stone)
      throw new Error(
        'No se puede agregar una piedra a la orden sin una piedra'
      );

    if (!stone.partNumber || isNaN(Number(stone.partNumber))) {
      throw new Error('No se puede agregar una piedra sin un part number');
    }
    if (isNaN(Number(stone.quantity))) {
      throw new Error('No se puede agregar una piedra sin cantidad');
    }
    if (isNaN(Number(stone.colorID))) {
      throw new Error('No se puede agregar una piedra sin color');
    }

    const order = await RhinestoneOrder.query().findById(+orderID);

    if (!order) throw new Error('No existe la orden');

    const stoneExist = await RhinestoneOrderItem.query()
      .where({ rhinestone_inventory_id: Number(stone.partNumber) })
      .where({ order_id: orderID })
      .where({ is_active: 1 });

    if (stoneExist.length > 0)
      throw new Error('La piedra ya se encuentra en la orden');

    await RhinestoneOrderItem.query().insert({
      rhinestone_inventory_id: Number(stone.partNumber),
      quantity: stone.quantity,
      color_id: Number(stone.colorID),
      size_id: Number(stone.sizeID),
      order_id: orderID,
      file_per_range_id: filePerRangeID,
    });

    return res.status(201).json({
      ok: true,
      message: 'Piedras agregadas',
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

interface CustomWhereOrder {
  isRepo?: boolean | null;
  artDate?: string | null;
  moOrder?: string | null;
}

export async function GetAllOrders(req: Request, res: Response) {
  try {
    const { isRepo, artDate, moOrder } =
      req.body as unknown as CustomWhereOrder;

    let query = RhinestoneOrder.query()
      .select([
        'rhinestones_orders.id',
        'rhinestones_orders.mo_id',
        raw(
          'CASE WHEN rhinestones_orders.is_repo = 1 THEN "YES" ELSE "NO" END'
        ).as('is_repo'),
        'rhinestones_orders.art_date',
        'mo_numbers.style',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        { num: 'mo_numbers.mo_order_item_num' },
        'mo_numbers.quantity',
      ])
      .innerJoin('mo_numbers', 'mo_numbers.mo_id', 'rhinestones_orders.mo_id')
      .where('rhinestones_orders.is_active', 1);

    if (isRepo) query = query.where({ is_repo: isRepo ? 1 : 0 });

    if (moOrder) query = query.where('mo_order', 'like', `%${moOrder}%`);

    if (artDate)
      query = query.where('art_date', 'between', [
        artDate,
        artDate.split(' ')[0] + ' 23:59:59',
      ]);

    const orders = await query.castTo<
      {
        id: number;
        mo_id: number;
        is_repo: string;
        art_date: string;
        style: string;
        mo_order: string;
        num: string;
        quantity: number;
      }[]
    >();

    if (orders.length === 0) throw new Error('No hay ordenes');

    return res.status(200).json({
      ok: true,
      message: 'Se obtuvieron las ordenes correctamente',
      data: orders,
      total: orders.length,
    });
  } catch (error) {
    console.log(error);

    if (error instanceof Error) {
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function GetOrdersByMoOrder(req: Request, res: Response) {
  try {
    const { moOrder } = req.query as unknown as { moOrder: number };

    if (!moOrder) throw new Error('No se puede buscar una orden sin ID');

    const orderInfo = await RhinestoneOrder.query()
      .select([
        'rhinestones_orders.id',
        'rhinestones_orders.mo_id',
        raw(
          'CASE WHEN rhinestones_orders.is_repo = 1 THEN "REPO" ELSE "PROD" END'
        ).as('is_repo'),
        'rhinestones_orders.art_date',
        'rhinestones_orders.is_active',
        'mo_numbers.style',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        { num: 'mo_numbers.mo_order_item_num' },
        'mo_numbers.quantity',
      ])
      .innerJoin('mo_numbers', 'mo_numbers.mo_id', 'rhinestones_orders.mo_id')
      .where('rhinestones_orders.is_active', 1)
      .where('mo_numbers.num', 'like', `%${moOrder}%`)
      .orderBy('rhinestones_orders.art_date', 'desc')
      .castTo<
        {
          id: number;
          mo_id: number;
          is_repo: string;
          art_date: string;
          style: string;
          mo_order: string;
          num: string;
          quantity: number;
        }[]
      >();

    if (!orderInfo) throw new Error('No existen ordenes para ese MO Order');

    return res
      .status(200)
      .json({ ok: true, data: orderInfo, total: orderInfo.length });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function GetRhinestoneOrderItems(req: Request, res: Response) {
  try {
    const { order_id } = req.params as unknown as { order_id: number };

    if (!order_id) throw new Error('No se puede buscar una orden sin ID');

    const orderItems = await RhinestoneOrderItem.query()
      .innerJoin(
        'rhinestones_orders',
        'rhinestones_orders.id',
        'rhinestones_order_items.order_id'
      )
      .innerJoin(
        'rhinestones_sizes',
        'rhinestones_sizes.id',
        'rhinestones_order_items.size_id'
      )
      .innerJoin(
        'rhinestones_colors',
        'rhinestones_colors.id',
        'rhinestones_order_items.color_id'
      )
      .where('rhinestones_order_items.order_id', order_id)
      .where('rhinestones_order_items.is_active', 1)
      .select([
        { id: 'rhinestones_order_items.id' },
        'rhinestones_order_items.order_id',
        'rhinestones_order_items.size_id',
        'rhinestones_order_items.color_id',
        'rhinestones_order_items.quantity',
        'rhinestones_sizes.size_name',
        'rhinestones_colors.color_name',
      ])
      .castTo<
        {
          id: number;
          order_id: number;
          size_id: number;
          color_id: number;
          quantity: number;
          size_name: string;
          color_name: string;
        }[]
      >();

    if (!orderItems) throw new Error('No existen items para esa orden');

    return res
      .status(200)
      .json({ ok: true, orderItems, total: orderItems.length });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

interface IRhinestoneColor {
  colorName: string;
  descriptionColor: string;
  statusColor?: string | number;
  addColorToProvider?: boolean;
}

export async function AddRhinestoneColor(req: Request, res: Response) {
  try {
    const {
      colorName,
      descriptionColor,
      statusColor,
      addColorToProvider = false,
    } = req.body as unknown as IRhinestoneColor;

    const useStatusColor =
      !statusColor ||
      statusColor === '1' ||
      statusColor === 1 ||
      statusColor === 'true' ||
      statusColor
        ? true
        : false;

    if (!colorName)
      throw new Error('No se puede agregar un color sin nombre de color');

    const color: RhinestoneColor = await RhinestoneColor.query().findOne({
      color_name: colorName,
    });

    if (color) throw new Error('Ya existe ese color');

    const newColor = await RhinestoneColor.query().insert({
      color_name: colorName,
      description_color: descriptionColor ? descriptionColor : colorName,
      is_active: useStatusColor,
    });

    if (!newColor) throw new Error('No se puede agregar el color');

    if (addColorToProvider) {
      const providers = await RhinestoneProvider.query().where({
        is_active: 1,
      });

      if (providers.length === 0) throw new Error('No hay proveedores');

      const colorProviders = await Promise.all(
        providers.map(async (provider) => {
          const colorProvider = await RhinestoneColorProvider.query().insert({
            rhinestones_color_id: newColor.id,
            rhinestones_provider_id: provider.id,
          });

          if (!colorProvider)
            throw new Error('No se puede agregar el color a los proveedor');

          return colorProvider;
        })
      );

      if (colorProviders.length === 0)
        throw new Error('No se puede agregar el color a los proveedores');
    }

    return res
      .status(201)
      .json({ ok: true, message: 'Color agregado', data: newColor });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function UpdateRhinestoneColor(req: Request, res: Response) {
  try {
    const { colorID } = req.query as unknown as { colorID: number };
    const { colorName, descriptionColor, statusColor } =
      req.body as unknown as IRhinestoneColor;

    const useStatusColor =
      statusColor === undefined || statusColor === null
        ? undefined
        : statusColor === '1' ||
          statusColor === 1 ||
          // just extra check, probably unnessesary
          statusColor === 'true' ||
          statusColor
        ? true
        : false;

    if (!colorID) throw new Error('No se puede actualizar un color sin ID');

    const color = await RhinestoneColor.query()
      .findById(+colorID)
      .select([
        'rhinestones_colors.id',
        { color_name: 'rhinestones_colors.color_name' },
        'rhinestones_colors.description_color',
        'rhinestones_colors.is_active',
      ])
      .castTo<{
        id: number;
        color_name: string;
        description_color: string;
        is_active: boolean;
      }>();

    if (!color) throw new Error('No existe ese color');

    const updateColor = await RhinestoneColor.query().patchAndFetchById(
      +colorID,
      {
        color_name: colorName ? colorName : color.color_name,
        description_color: descriptionColor
          ? descriptionColor
          : color.description_color,
        is_active: useStatusColor ? useStatusColor : color.is_active,
      }
    );

    if (!updateColor) throw new Error('No se puede actualizar el color');

    return res
      .status(200)
      .json({ ok: true, message: 'Color actualizado', data: updateColor });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function GetRhinestoneColors(_: Request, res: Response) {
  try {
    const colors: RhinestoneColor[] = await RhinestoneColor.query().where({
      is_active: 1,
    });

    if (colors.length === 0) throw new Error('No hay colores');

    return res.status(200).json({
      ok: true,
      message: 'Colores obtenidos',
      data: colors,
      total: colors.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

interface IRhinestoneSize {
  sizeName: string;
  sizeDescription: string;
  statusSize?: string | number;
}

export async function AddRhinestoneSize(req: Request, res: Response) {
  try {
    const {
      sizeName,
      sizeDescription,
      statusSize = 1,
    } = req.body as unknown as IRhinestoneSize;

    const useStatusSize =
      statusSize === undefined ||
      statusSize === null ||
      statusSize === 1 ||
      statusSize === '1'
        ? true
        : false;

    if (!sizeName)
      throw new Error('No se puede agregar un tamaño sin nombre de tamaño');

    const size: RhinestoneSize = await RhinestoneSize.query().findOne({
      size_name: sizeName,
    });

    if (size) throw new Error('Ya existe ese tamaño');

    const newSize: RhinestoneSize = await RhinestoneSize.query().insert({
      size_name: sizeName,
      size_description: sizeDescription ? sizeDescription : sizeName,
      is_active: useStatusSize,
    });

    if (!newSize) throw new Error('No se puede agregar el tamaño');

    return res
      .status(201)
      .json({ ok: true, message: 'Tamaño agregado', data: newSize });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function UpdateRhinestoneSize(req: Request, res: Response) {
  try {
    const { sizeID } = req.query as unknown as { sizeID: number };
    const { sizeName, sizeDescription, statusSize } =
      req.body as unknown as IRhinestoneSize;

    const useStatusSize =
      statusSize === undefined || statusSize === null
        ? undefined
        : statusSize === 1 || statusSize === '1'
        ? true
        : false;

    if (!sizeID) throw new Error('No se puede actualizar un tamaño sin ID');

    const size = await RhinestoneSize.query()
      .findById(+sizeID)
      .select([
        'rhinestones_sizes.id',
        { size_name: 'rhinestones_sizes.size_name' },
        'rhinestones_sizes.size_description',
        'rhinestones_sizes.is_active',
      ])
      .castTo<{
        id: number;
        size_name: string;
        size_description: string;
        is_active: boolean;
      }>();

    if (!size) throw new Error('No existe ese tamaño');

    const updateSize: RhinestoneSize =
      await RhinestoneSize.query().patchAndFetchById(+sizeID, {
        size_name: sizeName ? sizeName : size.size_name,
        size_description: sizeDescription
          ? sizeDescription
          : size.size_description,
        is_active: useStatusSize ? useStatusSize : size.is_active,
      });

    if (!updateSize) throw new Error('No se puede actualizar el tamaño');

    return res
      .status(200)
      .json({ ok: true, data: updateSize, message: 'Tamaño actualizado' });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function GetRhinestoneSizes(_: Request, res: Response) {
  try {
    const sizes: RhinestoneSize[] = await RhinestoneSize.query().where({
      is_active: 1,
    });

    if (sizes.length === 0) throw new Error('No hay tamaños');

    return res.status(200).json({
      ok: true,
      message: 'Tamaños obtenidos',
      data: sizes,
      total: sizes.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

interface IRhinestoneItem {
  sizeID: number;
  colorID: number;
  quantity: number;
  partNumber?: string;
  isActive?: number;
}

export async function UpdateRhinestoneItem(req: Request, res: Response) {
  try {
    const { itemID } = req.params as unknown as { itemID: number };
    const {
      colorID,
      sizeID,
      quantity,
      isActive = 1,
      partNumber,
    } = req.body as unknown as IRhinestoneItem;

    if (partNumber && isNaN(Number(partNumber))) {
      throw new Error('No se puede actualizar un item sin part number');
    }

    const useIsActive = isActive === undefined ? true : isActive ? true : false;

    if (!itemID) throw new Error('No se puede actualizar un item sin ID');

    const item = await RhinestoneOrderItem.query()
      .findById(+itemID)
      .select([
        'rhinestones_order_items.id',
        'rhinestones_order_items.size_id',
        'rhinestones_order_items.color_id',
        'rhinestones_order_items.quantity',
        'rhinestones_order_items.is_active',
        'rhinestones_order_items.rhinestone_inventory_id',
      ])
      .castTo<{
        id: number;
        size_id: number;
        color_id: number;
        quantity: number;
        is_active: boolean;
        rhinestone_inventory_id: number;
      }>();

    if (!item) throw new Error('No existe ese item');

    const updateItem = await RhinestoneOrderItem.query().patchAndFetchById(
      +itemID,
      {
        color_id: colorID ? colorID : item.color_id,
        size_id: sizeID ? sizeID : item.size_id,
        quantity: quantity ? quantity : item.quantity,
        is_active: useIsActive,
        // TODO: Is partNumber a string or id???
        rhinestone_inventory_id: partNumber
          ? Number(partNumber)
          : item.rhinestone_inventory_id,
      }
    );

    if (!updateItem)
      throw new Error('No se puede actualizar el rhinestones item');

    return res.status(200).json({ ok: true, message: 'Item actualizado' });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

interface IRhinestoneMachine {
  machine_id: number;
  machine: string;
}

interface IRhinestoneMachineWithOrders extends IRhinestoneMachine {
  orders: number | null;
}

export async function GetRhinestoneMachine(_req: Request, res: Response) {
  try {
    const machines = await Machine.query()
      .where({
        machine_type_id: 16,
      })
      .where('status', 1);

    if (machines.length === 0) throw new Error('No hay maquinas');

    const machinesWithOrders: IRhinestoneMachineWithOrders[] =
      await Promise.all(
        machines.map(async (machine: IRhinestoneMachine) => {
          const orders = await RhinestoneOrder.query()
            .where({ machine_id: machine.machine_id })
            .where('is_active', 1)
            .andWhere('film_finish_date', null);

          return {
            ...machine,
            orders: orders.length,
          };
        })
      );

    return res.status(200).json({
      ok: true,
      message: 'Maquinas obtenidas',
      data: machinesWithOrders,
      total: machines.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function AddOrdersToMachine(req: Request, res: Response) {
  try {
    const { machineID } = req.query as unknown as { machineID: number };
    const { orders } = req.body as unknown as { orders: number[] };

    if (!machineID) throw new Error('No se puede agregar una maquina sin ID');

    if (orders.length === 0)
      throw new Error('No se puede agregar una maquina sin ordenes');

    const machine = await Machine.query()
      .where('machine_id', machineID)
      .where('machine_type_id', 16);

    if (!machine)
      throw new Error('No existe esa maquina o no es una maquina de pedreria');

    const ordersTransaction = await transaction(
      RhinestoneOrder,
      async (RhinestoneOrders) => {
        const ordersUpdated = await Promise.all(
          orders.map(async (order: number) => {
            const orderUpdated =
              await RhinestoneOrders.query().patchAndFetchById(order, {
                machine_id: machineID,
                film_machine_assigned_date: new Date(),
              });

            if (!orderUpdated)
              throw new Error('No se puede actualizar la orden');

            return {
              order_id: orderUpdated.id,
              machine_id: orderUpdated.machine_id,
            };
          })
        );

        return ordersUpdated;
      }
    );

    return res.status(200).json({
      ok: true,
      message: 'Ordenes actualizadas',
      data: ordersTransaction,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function GetRhinestonePartNumbersByName(
  req: Request,
  res: Response
) {
  try {
    const { partNumber } = req.query as unknown as { partNumber: string };

    if (!partNumber)
      throw new Error('No se puede buscar un part_number sin nombre');

    const partNumbers = await RhinestoneInventory.query()
      .where('name', 'like', `%${partNumber}%`)
      .select([
        'rhinestone_inventory.id',
        'rhinestone_inventory.name',
        'rhinestone_inventory.quantity',
      ])
      .castTo<{ id: number; name: string; quantity: number }[]>();

    if (partNumbers.length === 0) throw new Error('No hay part_numbers');

    return res.status(200).json({
      ok: true,
      message: 'Part_numbers obtenidos',
      data: partNumbers,
      total: partNumbers.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function GetRhinestoneInventoryByMachine(
  req: Request,
  res: Response
) {
  try {
    const { machineID } = req.query as unknown as { machineID: number };

    if (!machineID)
      throw new Error('No se puede buscar el inventario de una maquina sin ID');

    const inventory = await RhinestoneMachineInventory.query()
      .leftJoin(
        'rhinestone_inventory',
        'rhinestone_inventory.id',
        'rhinestone_machine_inventory.rhinestone_inventory_id'
      )
      .where({ machine_id: machineID })
      .select([
        'rhinestone_inventory.id',
        'rhinestone_machine_inventory.onhand_quantity',
        'rhinestone_machine_inventory.reserved',
        { part_number: 'rhinestone_inventory.name' },
      ])
      .castTo<
        {
          id: number;
          onhand_quantity: number;
          reserved: number;
          partNumber: string;
        }[]
      >();

    if (inventory.length === 0) throw new Error('No hay inventario');

    return res.status(200).json({
      ok: true,
      message: 'Inventario obtenido',
      data: inventory,
      total: inventory.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function AddRhinestoneInventoryToMachine(
  req: Request,
  res: Response
) {
  try {
    const { machineID } = req.query as unknown as { machineID: number };
    const { partNumberID, quantity } = req.body as unknown as {
      partNumberID: number;
      quantity: number;
    };

    if (!machineID)
      throw new Error('No se puede agregar un inventario sin ID de maquina');

    if (!partNumberID)
      throw new Error(
        'No se puede agregar un inventario sin ID de part_number'
      );

    if (!quantity)
      throw new Error('No se puede agregar un inventario sin cantidad');

    const machine = await Machine.query()
      .where('machine_id', machineID)
      .where('machine_type_id', 16)
      .first();

    if (!machine) throw new Error('No existe esa maquina');

    const partNumber = await RhinestoneInventory.query().findById(
      +partNumberID
    );

    if (!partNumber) throw new Error('No existe ese part_number');

    const inventoryFound = await RhinestoneMachineInventory.query()
      .where('machine_id', machineID)
      .where('rhinestone_inventory_id', partNumberID)
      .first();

    if (inventoryFound) {
      const inventoryUpdated =
        await RhinestoneMachineInventory.query().patchAndFetchById(
          +inventoryFound.id,
          {
            onhand_quantity: quantity,
          }
        );

      if (!inventoryUpdated)
        throw new Error('No se puede actualizar el inventario');

      return res.status(201).json({
        ok: true,
        message: 'Inventario actualizado',
        data: inventoryUpdated,
      });
    }

    const inventory = await RhinestoneMachineInventory.query().insert({
      machine_id: machineID,
      rhinestone_inventory_id: partNumberID,
      onhand_quantity: quantity,
    });

    if (!inventory) throw new Error('No se puede agregar el inventario');

    return res
      .status(201)
      .json({ ok: true, message: 'Inventario agregado', data: inventory });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function GetRhinestoneOrdersByMachine(
  req: Request,
  res: Response
) {
  try {
    const { machineID } = req.query as unknown as { machineID: number };

    if (!machineID)
      throw new Error('No se puede buscar las ordenes de una maquina sin ID');

    const orders = await RhinestoneOrder.query()
      .where('machine_id', machineID)
      .where('is_active', 1)
      .andWhere('film_finish_date', null)
      .select('id')
      .castTo<{ id: number }[]>();

    if (orders.length === 0) throw new Error('No hay ordenes');

    const items = await RhinestoneOrderItem.query()
      .join(
        'rhinestones_orders',
        'rhinestones_orders.id',
        'rhinestones_order_items.order_id'
      )
      .join(
        'rhinestones_sizes',
        'rhinestones_sizes.id',
        'rhinestones_order_items.size_id'
      )
      .join(
        'rhinestones_colors',
        'rhinestones_colors.id',
        'rhinestones_order_items.color_id'
      )
      .join(
        'rhinestone_inventory',
        'rhinestone_inventory.id',
        'rhinestones_order_items.rhinestone_inventory_id'
      )
      .whereIn(
        'order_id',
        orders.map((order) => order.id)
      )
      .where('rhinestones_order_items.is_active', 1)
      .select([
        'rhinestones_order_items.id',
        'rhinestones_order_items.order_id',
        'rhinestones_order_items.quantity',
        'rhinestones_order_items.size_id',
        'rhinestones_sizes.size_name',
        'rhinestones_order_items.color_id',
        'rhinestones_colors.color_name',
        'rhinestones_order_items.rhinestone_inventory_id',
        { part_number: 'rhinestone_inventory.name' },
      ])
      .castTo<
        {
          id: number;
          order_id: number;
          quantity: number;
          size_id: number;
          size_name: string;
          color_id: number;
          color_name: string;
          rhinestone_inventory_id: number;
          part_number: string;
        }[]
      >();

    if (items.length === 0) throw new Error('No hay items');

    const itemsGrouped = items.reduce(
      (acc, item) => {
        const itemFound = acc.find(
          (i) => i.rhinestone_inventory_id === item.rhinestone_inventory_id
        );

        if (itemFound) {
          itemFound.quantity += item.quantity;
        } else {
          acc.push({
            rhinestone_inventory_id: item.rhinestone_inventory_id,
            part_number: item.part_number,
            color_name: item.color_name,
            size_name: item.size_name,
            quantity: item.quantity,
          });
        }

        return acc;
      },
      [] as {
        rhinestone_inventory_id: number;
        part_number: string;
        color_name: string;
        size_name: string;
        quantity: number;
      }[]
    );

    const inventory = await RhinestoneMachineInventory.query()
      .where({ machine_id: machineID })
      .select([
        'rhinestone_machine_inventory.id',
        'rhinestone_machine_inventory.onhand_quantity',
        'rhinestone_machine_inventory.reserved',
        'rhinestone_machine_inventory.rhinestone_inventory_id',
      ])
      .castTo<
        {
          id: number;
          onhand_quantity: number;
          reserved: number;
          rhinestone_inventory_id: number;
        }[]
      >();

    if (inventory.length === 0) throw new Error('No hay inventario');

    const itemsWithInventory = itemsGrouped.map((item) => {
      const inventoryFound = inventory.find(
        (i): boolean =>
          i.rhinestone_inventory_id === item.rhinestone_inventory_id
      );

      if (!inventoryFound) {
        return {
          ...item,
          quantity_required: roundToTheNextMultiple(+item.quantity, 100),
          on_hand: 0,
          reserved: 0,
        };
      }

      return {
        ...item,
        quantity_required: roundToTheNextMultiple(+item.quantity, 100),
        on_hand: inventoryFound.onhand_quantity,
        reserved: inventoryFound.reserved,
      };
    });

    return res.status(200).json({
      ok: true,
      message: 'Ordenes obtenidas',
      data: itemsWithInventory,
      total: itemsWithInventory.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

interface IPartNumbers {
  partNumberID: number;
  toReserve: number;
}

export async function UpdatePartNumberToInventoryOfMachine(
  req: Request,
  res: Response
) {
  try {
    const { machineID } = req.query as unknown as { machineID: number };
    const { partNumbers } = req.body as unknown as {
      partNumbers: IPartNumbers[];
    };

    if (!machineID)
      throw new Error('No se puede agregar un inventario sin ID de maquina');

    if (partNumbers.length === 0)
      throw new Error(
        'No se puede agregar un inventario sin ID de part_numbers'
      );

    const machine = await Machine.query()
      .where('machine_id', machineID)
      .where('machine_type_id', 16)
      .first();

    if (!machine)
      throw new Error('No existe esa maquina o no es una maquina de pedreria');

    const partNumbersTransaction = await transaction(
      RhinestoneInventory,
      RhinestoneMachineInventory,
      async (RhinestoneInventory, RhinestoneMachineInventory) => {
        const partNumberUpdated = [];

        for await (const partNumber of partNumbers) {
          const partNumberFound = await RhinestoneInventory.query().findById(
            +partNumber.partNumberID
          );

          if (!partNumberFound) throw new Error('No existe ese part_number');

          const rhinestoneMachineInventory =
            await RhinestoneMachineInventory.query()
              .where('rhinestone_inventory_id', partNumber.partNumberID)
              .where('machine_id', machineID)
              .first();

          if (!rhinestoneMachineInventory)
            throw new Error('No existe ese inventario');

          // TODO: Check for reserved field
          const checkInventory = rhinestoneMachineInventory.onhand_quantity - 0; // rhinestoneMachineInventory.reserved doesnt exist in the database

          if (checkInventory < partNumber.toReserve)
            throw new Error(
              'No se puede reservar mas de lo que hay en inventario'
            );

          const inventory =
            await RhinestoneMachineInventory.query().updateAndFetchById(
              +rhinestoneMachineInventory.id,
              {
                onhand_quantity:
                  rhinestoneMachineInventory.onhand_quantity -
                  partNumber.toReserve,
                // TODO: add reserved
                // reserved: partNumber.toReserve + 0 // rhinestoneMachineInventory.reserved
              }
            );

          if (!inventory) throw new Error('No se puede agregar el inventario');

          partNumberUpdated.push(inventory);
        }

        return partNumberUpdated;
      }
    );

    return res.status(201).json({
      ok: true,
      message: 'Inventario agregado',
      data: partNumbersTransaction,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function GetMoNumbersByMoOrder(req: Request, res: Response) {
  try {
    const { order } = req.query as unknown as { order: string };

    if (!order) throw new Error('No se puede buscar un MO sin una orden');

    const moNumbers = await MoNumber.query()
      .where('mo_numbers.num', 'like', `${order}%`)
      .where('company_code', 3)
      .whereNotIn('mo_numbers.mo_status', [
        'Void',
        'Cancelled',
        'Materials',
        'Complete',
      ])
      .select([
        'mo_numbers.mo_id',
        'mo_numbers.mo_order',
        'mo_numbers.mo_order_item_num',
        'mo_numbers.quantity',
        'mo_numbers.style',
        'mo_numbers.required_date',
      ])
      .castTo<
        {
          mo_id: number;
          mo_order: string;
          mo_order_item_num: string;
          quantity: number;
          style: string;
          required_date: Date;
        }[]
      >();

    if (moNumbers.length === 0) throw new Error('No hay MOs');

    return res.status(200).json({
      ok: true,
      message: 'Mos obtenidos',
      data: moNumbers,
      total: moNumbers.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

interface IProvider {
  name: string;
  shortName: string;
  materialCode: string;
  isActive?: boolean;
}

export async function CreateProvider(req: Request, res: Response) {
  try {
    const {
      name,
      shortName,
      materialCode,
      isActive = true,
    } = req.body as unknown as IProvider;

    if (!name) throw new Error('No se puede agregar un proveedor sin nombre');
    if (!shortName)
      throw new Error('No se puede agregar un proveedor sin nombre corto');
    if (!materialCode)
      throw new Error(
        'No se puede agregar un proveedor sin codigo de material'
      );

    const provider: RhinestoneProvider =
      await RhinestoneProvider.query().findOne({
        name,
        short_name: shortName,
      });

    if (provider) throw new Error('Ya existe ese proveedor');

    const newProvider: RhinestoneProvider =
      await RhinestoneProvider.query().insert({
        name,
        short_name: shortName,
        material_code: materialCode,
        is_active: isActive,
      });

    if (!newProvider) throw new Error('No se puede agregar el proveedor');

    return res
      .status(201)
      .json({ ok: true, message: 'Proveedor agregado', data: newProvider });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function GetRhinestoneProviders(_: Request, res: Response) {
  try {
    const providers: RhinestoneProvider[] =
      await RhinestoneProvider.query().where({ is_active: 1 });

    if (providers.length === 0) throw new Error('No hay proveedores');

    return res.status(200).json({
      ok: true,
      message: 'Proveedores obtenidos',
      data: providers,
      total: providers.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function GetRhinestonesProviderByID(req: Request, res: Response) {
  try {
    const { providerID } = req.params as unknown as { providerID: number };

    if (!providerID) throw new Error('No se puede buscar un proveedor sin ID');

    const provider: RhinestoneProvider = await RhinestoneProvider.query()
      .findById(providerID)
      .select(
        'rhinestones_providers.id',
        'rhinestones_providers.name',
        'rhinestones_providers.short_name',
        'rhinestones_providers.material_code',
        'rhinestones_providers.is_active'
      );

    if (!provider) throw new Error('No existe ese proveedor');

    return res.status(200).json({
      ok: true,
      message: 'Proveedor obtenido',
      data: provider,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function UpdateRhinestonesProvider(req: Request, res: Response) {
  try {
    const { providerID } = req.query as unknown as { providerID: number };
    const { name, shortName, materialCode, isActive } =
      req.body as unknown as IProvider;

    if (!providerID)
      throw new Error('No se puede actualizar un proveedor sin ID');

    const provider = await RhinestoneProvider.query().findById(+providerID);

    if (!provider) throw new Error('No existe ese proveedor');

    const updateProvider: RhinestoneProvider =
      await RhinestoneProvider.query().patchAndFetchById(+providerID, {
        name: name ? name : provider.name,
        short_name: shortName ? shortName : provider.short_name,
        material_code: materialCode ? materialCode : provider.material_code,
        is_active: isActive ? isActive : provider.is_active,
      });

    if (!updateProvider) throw new Error('No se puede actualizar el proveedor');

    return res.status(200).json({
      ok: true,
      message: 'Proveedor actualizado',
      data: updateProvider,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

interface IAddRhinestonesColorProvider {
  providerID: number;
  colors: number[];
}

export async function AddRhinestoneColorsToProvider(
  req: Request,
  res: Response
) {
  try {
    const { providerID, colors } =
      req.body as unknown as IAddRhinestonesColorProvider;

    if (!providerID)
      throw new Error('No se puede agregar un proveedor sin ID de proveedor');

    if (colors.length === 0)
      throw new Error('No se puede agregar un proveedor sin colores');

    const provider: RhinestoneProvider = await RhinestoneProvider.query()
      .findById(providerID)
      .where({ is_active: 1 });

    if (!provider) throw new Error('No existe ese proveedor');

    const colorProviderTransaction = await transaction(
      RhinestoneColorProvider,
      RhinestoneColor,
      async (RhinestonesColorsProviders, RhinestonesColor) => {
        const colorProviders = await Promise.all(
          colors.map(async (color: number) => {
            const colorFound = await RhinestonesColor.query()
              .findById(color)
              .where({ is_active: 1 });

            if (!colorFound) throw new Error(`No existe ese color ${color}`);

            const colorProviderFound = await RhinestonesColorsProviders.query()
              .findOne({
                rhinestones_color_id: color,
                rhinestones_provider_id: providerID,
                is_active: 1,
              })
              .select(
                'rhinestones_colors_providers.id',
                'rhinestones_colors_providers.rhinestones_color_id',
                'rhinestones_colors_providers.rhinestones_provider_id'
              )
              .castTo<{
                id: number;
                rhinestones_color_id: number;
                rhinestones_provider_id: number;
              }>();

            if (colorProviderFound) return colorProviderFound;

            const colorProvider: RhinestoneColorProvider =
              await RhinestonesColorsProviders.query().insert({
                rhinestones_color_id: color,
                rhinestones_provider_id: providerID,
                is_active: true,
              });

            if (!colorProvider)
              throw new Error('No se puede agregar el color proveedor');

            return colorProvider;
          })
        );

        return colorProviders;
      }
    );

    return res.status(201).json({
      ok: true,
      message: 'Colores agregados al proveedor',
      data: colorProviderTransaction,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function DeleteRhinestonesColorProvider(
  req: Request,
  res: Response
) {
  try {
    const { colorProviderID } = req.query as unknown as {
      colorProviderID: number;
    };

    if (!colorProviderID)
      throw new Error('No se puede eliminar un color de proveedor sin ID');

    const colorProvider: RhinestoneColorProvider =
      await RhinestoneColorProvider.query()
        .findById(colorProviderID)
        .where({ is_active: 1 });

    if (!colorProvider) throw new Error('No existe ese color proveedor');

    const deleteColorProvider =
      await RhinestoneColorProvider.query().patchAndFetchById(colorProviderID, {
        is_active: false,
      });

    if (!deleteColorProvider)
      throw new Error('No se puede eliminar el color proveedor');

    console.log(deleteColorProvider);

    return res.status(200).json({
      ok: true,
      message: 'Color proveedor eliminado',
      data: [],
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

interface IAddRhinestonesSizeProvider {
  providerID: number;
  sizes: number[];
}

export async function AddRhinestonesSizesToProvider(
  req: Request,
  res: Response
) {
  try {
    const { providerID, sizes } =
      req.body as unknown as IAddRhinestonesSizeProvider;

    if (!providerID)
      throw new Error('No se puede agregar un proveedor sin ID de proveedor');

    if (sizes.length === 0)
      throw new Error('No se puede agregar un proveedor sin tamaños');

    const provider: RhinestoneProvider = await RhinestoneProvider.query()
      .findById(providerID)
      .where({ is_active: 1 });

    if (!provider) throw new Error('No existe ese proveedor');

    const sizeProviderTransaction: RhinestoneSizeProvider[] = await transaction(
      RhinestoneSizeProvider,
      RhinestoneSize,
      async (RhinestonesSizesProviders, RhinestonesSizes) => {
        const sizeProviders: RhinestoneSizeProvider[] = await Promise.all(
          sizes.map(async (size: number) => {
            const sizeFound = await RhinestonesSizes.query()
              .findById(size)
              .where({ is_active: 1 });

            if (!sizeFound) throw new Error(`No existe ese tamaño ${size}`);

            const sizeProviderFound = await RhinestonesSizesProviders.query()
              .findOne({
                rhinestones_size_id: size,
                rhinestones_provider_id: providerID,
                is_active: 1,
              })
              .select(
                'rhinestones_sizes_providers.id',
                'rhinestones_sizes_providers.rhinestones_size_id',
                'rhinestones_sizes_providers.rhinestones_provider_id'
              );

            if (sizeProviderFound) return sizeProviderFound;

            const sizeProvider: RhinestoneSizeProvider =
              await RhinestonesSizesProviders.query().insert({
                rhinestones_size_id: size,
                rhinestones_provider_id: providerID,
                is_active: true,
              });

            if (!sizeProvider)
              throw new Error('No se puede agregar el tamaño proveedor');

            return sizeProvider;
          })
        );

        return sizeProviders;
      }
    );

    return res.status(201).json({
      ok: true,
      message: 'Tamaños agregados al proveedor',
      data: sizeProviderTransaction,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }
    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function DeleteRhinestonesSizeProvider(
  req: Request,
  res: Response
) {
  try {
    const { sizeProviderID } = req.query as unknown as {
      sizeProviderID: number;
    };

    if (!sizeProviderID)
      throw new Error('No se puede eliminar un tamaño de proveedor sin ID');

    const sizeProvider: RhinestoneSizeProvider =
      await RhinestoneSizeProvider.query()
        .findById(sizeProviderID)
        .where({ is_active: 1 });

    if (!sizeProvider) throw new Error('No existe ese tamaño proveedor');

    const deleteSizeProvider: RhinestoneSizeProvider =
      await RhinestoneSizeProvider.query().patchAndFetchById(sizeProviderID, {
        is_active: false,
      });

    if (!deleteSizeProvider)
      throw new Error('No se puede eliminar el tamaño proveedor');

    return res.status(200).json({
      ok: true,
      message: 'Tamaño proveedor eliminado',
      data: [],
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }
    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function GetMoRhinestonesInfo(req: Request, res: Response) {
  try {
    const { moID } = req.query as unknown as { moID: number };

    if (!moID) throw new Error('No se puede buscar una MO sin ID');

    const history = [];

    const mo = await MoNumber.query()
      .where('mo_id', moID)
      .where('company_code', 3)
      .whereNotIn('mo_status', ['Void', 'Cancelled', 'Materials', 'Complete'])
      .select([
        'mo_id',
        'quantity',
        'style',
        'required_date',
        'mo_order',
        'mo_order_item_num',
      ])
      .first()
      .castTo<{
        mo_id: number;
        mo_order: string;
        mo_order_item_num: string;
        quantity: number;
        style: string;
        required_date: string;
      }>();

    if (!mo) throw new Error('No existe esa MO');
    const styleIsYouth = mo.style.substring(0, 1) === 'Y';

    const style: string = styleIsYouth
      ? mo.style.substring(1, 3)
      : mo.style.substring(0, 2);

    const provider = (await RhinestoneProvider.query()
      .where('short_name', 'like', `%${style}%`)
      .where('is_active', 1)
      .select(['id', 'name', 'short_name', 'material_code'])
      .first()) as unknown as {
      id: number;
      name: string;
      short_name: string;
      material_code: string;
    };

    if (!provider) throw new Error(`No hay proveedor para el estilo ${style}`);

    const reposition: boolean = (await RhinestoneOrder.query()
      .where('mo_id', moID)
      .where('is_active', 1)
      .first())
      ? true
      : false;

    if (reposition) {
      const orders = await RhinestoneOrder.query()
        .where('mo_id', moID)
        .where('is_active', 1)
        .select(['id', 'art_date', 'is_repo']);

      history.push(...orders);
    }

    const sizes: RhinestoneSize[] = await RhinestoneSize.query()
      .join(
        'rhinestones_sizes_providers',
        'rhinestones_sizes_providers.rhinestones_size_id',
        'rhinestones_sizes.id'
      )
      .join(
        'rhinestones_providers',
        'rhinestones_providers.id',
        'rhinestones_sizes_providers.rhinestones_provider_id'
      )
      .where('rhinestones_sizes_providers.is_active', 1)
      .where('rhinestones_providers.is_active', 1)
      .where('rhinestones_providers.id', provider.id)
      .select(['rhinestones_sizes.id', 'rhinestones_sizes.size_name']);

    if (sizes.length === 0)
      throw new Error(`No hay tamaños asignados al proveedor ${provider.name}`);

    const colors: RhinestoneColor[] = await RhinestoneColor.query()
      .join(
        'rhinestones_colors_providers',
        'rhinestones_colors_providers.rhinestones_color_id',
        'rhinestones_colors.id'
      )
      .join(
        'rhinestones_providers',
        'rhinestones_providers.id',
        'rhinestones_colors_providers.rhinestones_provider_id'
      )
      .where('rhinestones_colors_providers.is_active', 1)
      .where('rhinestones_providers.is_active', 1)
      .where('rhinestones_providers.id', provider.id)
      .select(['rhinestones_colors.id', 'rhinestones_colors.color_name']);

    if (colors.length === 0)
      throw new Error(`No hay colores asignados al proveedor ${provider.name}`);

    return res.status(200).json({
      ok: true,
      message: 'Informacion de la MO obtenida',
      data: {
        mo,
        sizes,
        colors,
        reposition,
        history,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function GetPartNumberBySizeAndColor(req: Request, res: Response) {
  try {
    const { sizeID, colorID } = req.query as unknown as {
      sizeID: number;
      colorID: number;
    };

    if (!sizeID)
      throw new Error('No se puede buscar un part_number sin ID de tamaño');

    if (!colorID)
      throw new Error('No se puede buscar un part_number sin ID de color');

    const partNumber: RhinestoneInventory = await RhinestoneInventory.query()
      .join(
        'rhinestones_sizes',
        'rhinestones_sizes.id',
        'rhinestone_inventory.size_id'
      )
      .join(
        'rhinestones_colors',
        'rhinestones_colors.id',
        'rhinestone_inventory.color_id'
      )
      .where({ size_id: sizeID, color_id: colorID })
      .select([
        'rhinestone_inventory.id',
        'rhinestone_inventory.name',
        { size_name: 'rhinestones_sizes.size_name' },
        { size_rate: 'rhinestones_sizes.varsity_rate' },
        'rhinestones_colors.color_name',
      ])
      .first();

    if (!partNumber) throw new Error('No existe ese part number');

    return res.status(200).json({
      ok: true,
      message: 'Part number obtenido',
      data: partNumber,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }
    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function AddRhinestonesRange(req: Request, res: Response) {
  try {
    const { name } = req.body as unknown as { name: string };

    if (!name) throw new Error('No se puede agregar un rango sin nombre');

    const range: RhinestoneRange = await RhinestoneRange.query().findOne({
      name,
    });

    if (range) throw new Error('Ya existe ese rango');

    const newRange = await RhinestoneRange.query().insert({
      name: name.toUpperCase(),
      is_active: true,
    });

    if (!newRange) throw new Error('No se puede agregar el rango');

    return res.status(201).json({
      ok: true,
      message: 'Rango agregado',
      data: newRange,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }
    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function UpdateRhinestonesRange(req: Request, res: Response) {
  try {
    const { rangeID } = req.query as unknown as { rangeID: number };
    const { name, isActive } = req.body as unknown as {
      name: string;
      isActive: boolean;
    };

    if (!rangeID) throw new Error('No se puede actualizar un rango sin ID');

    const range = await RhinestoneRange.query().findById(+rangeID);

    if (!range) throw new Error('No existe ese rango');

    const updateRange: RhinestoneRange =
      await RhinestoneRange.query().patchAndFetchById(+rangeID, {
        name: name ? name.toUpperCase() : range.name,
        is_active: isActive ? isActive : range.is_active,
      });

    if (!updateRange) throw new Error('No se puede actualizar el rango');

    return res.status(200).json({
      ok: true,
      message: 'Rango actualizado',
      data: updateRange,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }
    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function GetRhinestonesRanges(_: Request, res: Response) {
  try {
    const ranges: RhinestoneRange[] = await RhinestoneRange.query().where({
      is_active: true,
    });

    if (ranges.length === 0) throw new Error('No hay rangos');

    return res.status(200).json({
      ok: true,
      message: 'Rangos obtenidos',
      data: ranges,
      total: ranges.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }
    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function GetRhinestonesRangeByName(req: Request, res: Response) {
  try {
    const { rangeName } = req.params as unknown as { rangeName: string };

    if (!rangeName) throw new Error('No se puede buscar un rango sin nombre');

    const range: RhinestoneRange[] = await RhinestoneRange.query()
      .where('name', 'like', `${rangeName}%`)
      .where('is_active', true)
      .select(['id', 'name', 'is_active']);

    if (!range) throw new Error('No existe ese rango');

    return res.status(200).json({
      ok: true,
      message: 'Rango obtenido',
      data: range,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }
    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export interface BaseItem {
  id: number;
  name: string;
}

type Color = BaseItem;
type Size = BaseItem;

interface IStone {
  partNumberID: number;
  quantity: number;
  color: Color;
  size: Size;
}

interface IRangeFile {
  cute: string | number;
  description: string;
  stones: IStone[];
}
interface IRange {
  quantityPerRange: number;
  rangeID: number;
  files: IRangeFile[];
}
interface ICreateRhinestoneOrder {
  mo: IOrder;
  ranges: IRange[];
}

export async function CreateRhinestoneOrder(req: Request, res: Response) {
  try {
    const { mo, ranges } = req.body as unknown as ICreateRhinestoneOrder;
    const { area_id, employee_id } = req.body.tokenInfo as unknown as IToken;

    if (!mo.moID) throw new Error('No se puede agregar una orden sin una MO');

    if (ranges.length === 0)
      throw new Error(
        'No se puede agregar una orden sin al menos un rango de talla'
      );

    const orderId: number = await createOrder({
      mo: mo,
      token: { area_id, employee_id },
    });

    if (!orderId) {
      logger.error(`Error al crear la orden: ${orderId}`);
      throw new Error(`${orderId}`);
    }

    const addOrderTransaction = await transaction(
      RhinestoneOrderRangeFile,
      RhinestoneOrderRange,
      RhinestoneOrderItem,
      RhinestoneColor,
      RhinestoneSize,
      async (
        RhinestonesFilesRange,
        RhinestonesRangesPerOrder,
        RhinestoneOrderItems,
        RhinestonesColors,
        RhinestonesSizes
      ) => {
        for (const range of ranges) {
          const rangePerOrder = await RhinestonesRangesPerOrder.query().insert({
            range_id: range.rangeID,
            order_id: orderId,
            quantity: range.quantityPerRange,
            is_active: true,
          });

          if (!rangePerOrder)
            throw new Error('No se puede agregar el rango a la orden');

          for (const file of range.files) {
            const filePerRange = await RhinestonesFilesRange.query().insert({
              rhinestones_range_per_order_id: rangePerOrder.id,
              cute: Number(file.cute),
              description: file.description,
              is_active: true,
            });

            if (!filePerRange)
              throw new Error('No se puede agregar el archivo al rango');

            for (const stone of file.stones) {
              const size = (await RhinestonesSizes.query().findOne({
                id: stone.size.id,
              })) as unknown as
                | {
                    id: number;
                  }
                | undefined;

              if (!size) throw new Error('No existe ese tamaño');

              const color = (await RhinestonesColors.query().findOne({
                id: stone.color.id,
              })) as unknown as
                | {
                    id: number;
                  }
                | undefined;

              if (!color) throw new Error('No existe ese color');

              if (stone.quantity > 0) {
                const stonePerFile = await RhinestoneOrderItems.query().insert({
                  order_id: orderId,
                  rhinestone_inventory_id: stone.partNumberID,
                  file_per_range_id: filePerRange.id,
                  quantity: stone.quantity,
                  is_active: true,
                  size_id: size.id,
                  color_id: color.id,
                });

                if (!stonePerFile)
                  throw new Error('No se puede agregar la piedra a la orden');
              }
            }
          }
        }

        return true;
      }
    );

    if (!addOrderTransaction)
      throw new Error('No se puede agregar los rangos a la orden');

    await RhinestoneOrder.query().findById(orderId).patch({
      art_date: new Date(),
    });

    return res.status(201).json({
      ok: true,
      message: 'Orden agregada',
      data: [],
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

interface ITotalPiecesByDay {
  total: number;
  employee_name: string;
}

export async function GetTotalPiecesByDay(_req: Request, res: Response) {
  try {
    const today: string = dayjs().format('YYYY-MM-DD');

    const logs = await RhinestoneLog.query()
      .innerJoin(
        'employees',
        'employees.employee_id',
        'rhinestones_logs.employee_id'
      )
      .where('rhinestones_logs.action', 'create')
      .where('rhinestones_logs.module_name', 'order')
      .whereBetween('rhinestones_logs.created_at', [
        `${today} 00:00:00`,
        `${today} 23:59:59`,
      ])
      .select([
        'rhinestones_logs.employee_id',
        'rhinestones_logs.module_id',
        'employees.first_name',
        'employees.last_name',
      ])
      .castTo<
        {
          employee_id: number;
          module_id: number;
          first_name: string;
          last_name: string;
        }[]
      >();

    if (logs.length === 0)
      throw new Error('No hay ordenes creadas el dia de hoy');

    const totalPieces: ITotalPiecesByDay[] = [];

    for (const log of logs) {
      const total = await RhinestoneOrder.query()
        .innerJoin('mo_numbers', 'mo_numbers.mo_id', 'rhinestones_orders.mo_id')
        .where('rhinestones_orders.is_active', 1)
        .where('rhinestones_orders.id', +log.module_id)
        .select(['mo_numbers.quantity'])
        .castTo<{ quantity: number }[]>();

      console.log('t', total);

      totalPieces.push({
        total: total[0].quantity,
        employee_name: `${log.first_name} ${log.last_name}`.toLowerCase(),
      });
    }

    const totalPiecesByDay: ITotalPiecesByDay[] = totalPieces.reduce(
      (acc: ITotalPiecesByDay[], curr: ITotalPiecesByDay) => {
        const found = acc.find(
          (item: ITotalPiecesByDay) => item.employee_name === curr.employee_name
        );

        if (!found) {
          return [
            ...acc,
            {
              total: curr.total,
              employee_name: curr.employee_name,
            },
          ];
        }

        found.total += curr.total;

        return acc;
      },
      []
    );

    return res.status(200).json({
      ok: true,
      message: 'Piezas obtenidas',
      data: totalPiecesByDay,
      total: totalPiecesByDay.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }
    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

interface ITotalOrdersByDay {
  total: number;
  employee_name: string;
}

export async function GetTotalOrdersByDay(_req: Request, res: Response) {
  try {
    const today: string = dayjs().format('YYYY-MM-DD');

    const logs = await RhinestoneLog.query()
      .innerJoin(
        'employees',
        'employees.employee_id',
        'rhinestones_logs.employee_id'
      )
      .where('rhinestones_logs.action', 'create')
      .where('rhinestones_logs.module_name', 'order')
      .whereBetween('rhinestones_logs.created_at', [
        `${today} 00:00:00`,
        `${today} 23:59:59`,
      ])
      .select([
        'rhinestones_logs.employee_id',
        'rhinestones_logs.module_id',
        'employees.first_name',
        'employees.last_name',
      ])
      .castTo<
        {
          employee_id: number;
          module_id: number;
          first_name: string;
          last_name: string;
        }[]
      >();

    if (logs.length === 0)
      throw new Error('No hay ordenes creadas el dia de hoy');

    const totalOrders: ITotalOrdersByDay[] = [];

    for (const log of logs) {
      totalOrders.push({
        total: 1,
        employee_name: `${log.first_name} ${log.last_name}`.toLowerCase(),
      });
    }

    const totalOrdersByDay: ITotalOrdersByDay[] = totalOrders.reduce(
      (acc: ITotalOrdersByDay[], curr: ITotalOrdersByDay) => {
        const found = acc.find(
          (item: ITotalOrdersByDay) => item.employee_name === curr.employee_name
        );

        if (!found) {
          return [
            ...acc,
            {
              total: curr.total,
              employee_name: curr.employee_name,
            },
          ];
        }

        found.total += curr.total;

        return acc;
      },
      []
    );

    return res.status(200).json({
      ok: true,
      message: 'Ordenes obtenidas',
      data: totalOrdersByDay,
      total: totalOrdersByDay.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }
    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function GetRhinestoneOrderByOrderNumber(
  req: Request,
  res: Response
) {
  try {
    const { orderNumber } = req.params as unknown as { orderNumber: string };

    if (!orderNumber)
      throw new Error('No se puede buscar una orden sin numero de orden');

    const orders = await RhinestoneOrder.query()
      .join('mo_numbers', 'mo_numbers.mo_id', 'rhinestones_orders.mo_id')
      .where('mo_numbers.mo_order', 'like', `${orderNumber}%`)
      .where('rhinestones_orders.is_active', 1)
      .select([
        'rhinestones_orders.id',
        'mo_numbers.mo_order',
        'mo_numbers.mo_order_item_num',
        'mo_numbers.quantity',
        'mo_numbers.style',
        'mo_numbers.required_date',
      ])
      .castTo<
        {
          id: number;
          mo_order: string;
          mo_order_item_num: string;
          quantity: number;
          style: string;
          required_date: string;
        }[]
      >();

    if (orders.length === 0) throw new Error('No hay ordenes');

    return res.status(200).json({
      ok: true,
      message: 'Ordenes obtenidas',
      data: orders,
      total: orders.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }
    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function DeleteRhinestoneOrder(req: Request, res: Response) {
  try {
    const { orderID } = req.query as unknown as { orderID: number };

    if (!orderID) throw new Error('No se puede eliminar una orden sin ID');

    const order = await RhinestoneOrder.query()
      .findById(+orderID)
      .where({ is_active: 1 });

    if (!order) throw new Error(`No existe la orden con ID ${orderID}`);

    const deleteOrder = await RhinestoneOrder.query()
      .update({ is_active: false })
      .where({ id: +orderID });

    if (!deleteOrder) throw new Error('No se puede eliminar la orden');

    return res.status(200).json({
      ok: true,
      message: 'Orden eliminada',
      data: [],
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }
    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function GetOrderInfo(req: Request, res: Response) {
  try {
    const { orderID } = req.query as unknown as { orderID: number };

    // search MO information
    const mo = await MoNumber.query()
      .leftJoin(
        'rhinestones_orders',
        'mo_numbers.mo_id',
        'rhinestones_orders.mo_id'
      )
      .where({ 'rhinestones_orders.id': orderID })
      .where('rhinestones_orders.is_active', 1)
      .select([
        'mo_numbers.mo_id',
        'mo_numbers.mo_order',
        { num: 'mo_numbers.mo_order_item_num' },
        'mo_numbers.quantity',
        'mo_numbers.style',
        'mo_numbers.required_date',
        'mo_numbers.mo_status',
        'rhinestones_orders.art_date',
        'rhinestones_orders.is_repo',
      ])
      .first()
      .castTo<{
        mo_id: number;
        mo_order: string;
        num: number;
        quantity: number;
        style: string;
        required_date: string;
        mo_status: string;
        art_date: string;
        is_repo: number;
      }>();

    if (!mo) throw new Error('No hay informacion de la orden');

    // obtener los rangos asociados a la orden
    const ranges = await RhinestoneOrderRange.query()
      .innerJoin(
        'rhinestones_ranges',
        'rhinestones_ranges.id',
        'rhinestones_ranges_per_order.range_id'
      )
      .where('rhinestones_ranges_per_order.order_id', orderID)
      .where('rhinestones_ranges_per_order.is_active', 1)
      .select([
        'rhinestones_ranges_per_order.id',
        'rhinestones_ranges_per_order.range_id',
        'rhinestones_ranges_per_order.quantity',
        'rhinestones_ranges.name',
      ])
      .castTo<
        {
          id: number;
          range_id: number;
          quantity: number;
          name: string;
        }[]
      >();

    if (ranges.length === 0)
      return res.status(200).json({
        ok: true,
        message: 'Información de la orden',
        data: {
          mo,
          ranges: [],
          sizes: [],
          colors: [],
        },
      });

    // obtener los colores y tallas de piedras
    const style: string = mo.style.substring(0, 2);
    const provider = await RhinestoneProvider.query()
      .where('short_name', 'like', `%${style}%`)
      .where('is_active', 1)
      .select(['id', 'name', 'short_name', 'material_code'])
      .first()
      .castTo<{
        id: number;
        name: string;
        short_name: string;
        material_code: string;
      }>();

    if (!provider) throw new Error(`No hay proveedor para el estilo ${style}`);

    const sizes = await RhinestoneSize.query()
      .join(
        'rhinestones_sizes_providers',
        'rhinestones_sizes_providers.rhinestones_size_id',
        'rhinestones_sizes.id'
      )
      .join(
        'rhinestones_providers',
        'rhinestones_providers.id',
        'rhinestones_sizes_providers.rhinestones_provider_id'
      )
      .where('rhinestones_sizes_providers.is_active', 1)
      .where('rhinestones_providers.is_active', 1)
      .where('rhinestones_providers.id', provider.id)
      .select(['rhinestones_sizes.id', 'rhinestones_sizes.size_name'])
      .castTo<
        {
          id: number;
          size_name: string;
        }[]
      >();

    if (sizes.length === 0)
      throw new Error(`No hay tamaños asignados al proveedor ${provider.name}`);

    const colors = await RhinestoneColor.query()
      .join(
        'rhinestones_colors_providers',
        'rhinestones_colors_providers.rhinestones_color_id',
        'rhinestones_colors.id'
      )
      .join(
        'rhinestones_providers',
        'rhinestones_providers.id',
        'rhinestones_colors_providers.rhinestones_provider_id'
      )
      .where('rhinestones_colors_providers.is_active', 1)
      .where('rhinestones_providers.is_active', 1)
      .where('rhinestones_providers.id', provider.id)
      .select(['rhinestones_colors.id', 'rhinestones_colors.color_name'])
      .castTo<
        {
          id: number;
          color_name: string;
        }[]
      >();

    if (colors.length === 0)
      throw new Error(`No hay colores asignados al proveedor ${provider.name}`);

    return res.status(200).json({
      ok: true,
      message: 'Informacion de la orden',
      data: {
        mo,
        ranges,
        sizes,
        colors,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function GetFilesByOrderRange(req: Request, res: Response) {
  try {
    const { rangePerOrderID } = req.query as unknown as {
      rangePerOrderID: number;
    };

    const filesInfo = [];

    if (!rangePerOrderID)
      throw new Error('No se puede obtener archivos sin ID');

    // obtener los archivos asociados al rango
    const files = await RhinestoneOrderRangeFile.query()
      .where({
        rhinestones_range_per_order_id: rangePerOrderID,
      })
      .where('is_active', 1)
      .select(['id', 'description', 'cute', 'file_url'])
      .castTo<
        {
          id: number;
          description: string;
          cute: string;
          file_url: string;
        }[]
      >();

    if (!files) throw new Error('No se encontraron archivos');

    // obtener las piedras asociadas a los archivos
    for (const file of files) {
      const stones = await RhinestoneOrderItem.query()
        .innerJoin(
          'rhinestones_colors',
          'rhinestones_colors.id',
          'rhinestones_order_items.color_id'
        )
        .innerJoin(
          'rhinestones_sizes',
          'rhinestones_sizes.id',
          'rhinestones_order_items.size_id'
        )
        .where('rhinestones_order_items.file_per_range_id', +file.id)
        .where('rhinestones_order_items.is_active', 1)
        .select([
          'rhinestones_order_items.id',
          'rhinestones_order_items.color_id',
          'rhinestones_order_items.size_id',
          'rhinestones_order_items.quantity',
          'rhinestones_colors.color_name',
          'rhinestones_sizes.size_name',
        ]);

      if (!stones) throw new Error('No se encontraron piedras');

      filesInfo.push({
        ...file,
        stones,
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Archivos encontrados',
      data: filesInfo,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

// delete file from range
export async function DeleteFileFromARange(req: Request, res: Response) {
  try {
    const { fileID } = req.query as unknown as { fileID: number };

    if (!fileID) throw new Error('No se puede eliminar el archivo sin ID');

    // validar que el archivo exista
    const file = await RhinestoneOrderRangeFile.query()
      .where('id', fileID)
      .first();

    if (!file) throw new Error('No se encontro el archivo');

    // eliminar todas las piedras del archivo cambiando su estado is_active a 0
    const deleteFileWithStones = await transaction(
      RhinestoneOrderItem,
      RhinestoneOrderRangeFile,
      async (RhinestoneOrderItems, RhinestoneFileRange) => {
        await RhinestoneOrderItems.query()
          .where('file_per_range_id', fileID)
          .patch({ is_active: false });

        await RhinestoneFileRange.query()
          .where('id', fileID)
          .patch({ is_active: false });

        return true;
      }
    );

    if (!deleteFileWithStones)
      throw new Error('No se pudo eliminar el archivo');

    return res.status(200).json({
      ok: true,
      message: 'Archivo eliminado',
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

// delete range from order
export async function DeleteRangeFromAOrder(req: Request, res: Response) {
  try {
    const { rangeID } = req.query as unknown as { rangeID: number };

    if (!rangeID) throw new Error('No se puede eliminar el rango sin ID');

    // validar que el rango exista
    const range = await RhinestoneOrderRange.query()
      .where('id', rangeID)
      .where('is_active', 1)
      .first();

    if (!range) throw new Error('No se encontro el rango');

    // obtener todos los archivos del rango
    const files = await RhinestoneOrderRangeFile.query()
      .where('rhinestones_range_per_order_id', rangeID)
      .where('is_active', 1)
      .select('id')
      .castTo<{ id: number }[]>();

    const deleteRangeWithFiles = await transaction(
      RhinestoneOrderItem,
      RhinestoneOrderRangeFile,
      RhinestoneOrderRange,
      async (
        RhinestoneOrderItems,
        RhinestoneFilesRange,
        RhinestoneRangesPerOrder
      ) => {
        await RhinestoneOrderItems.query()
          .whereIn(
            'file_per_range_id',
            files.map((file): number => +file.id)
          )
          .patch({ is_active: false });

        await RhinestoneFilesRange.query()
          .where('rhinestones_range_per_order_id', rangeID)
          .patch({ is_active: false });

        await RhinestoneRangesPerOrder.query()
          .where('id', rangeID)
          .patch({ is_active: false });

        return true;
      }
    );

    if (!deleteRangeWithFiles) throw new Error('No se pudo eliminar el rango');

    return res.status(200).json({
      ok: true,
      message: 'Rango eliminado',
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

// update file
export async function UpdateFile(req: Request, res: Response) {
  try {
    const { fileID } = req.query as unknown as { fileID: number };
    const { description, cute, fileURL, isActive } = req.body as unknown as {
      description: string;
      cute: string | number;
      fileURL: string;
      isActive: boolean;
    };

    if (!fileID) throw new Error('No se puede actualizar el archivo sin ID');

    // validar que el archivo exista
    const file = await RhinestoneOrderRangeFile.query()
      .where('id', fileID)
      .where('is_active', 1)
      .first()
      .select(['id', 'description', 'cute', 'file_url'])
      .castTo<{
        id: number;
        description: string;
        cute: number;
        file_url: string;
      }>();

    if (!file) throw new Error('No se encontro el archivo');

    console.log({
      isActive,
    });

    const updateFile = await RhinestoneOrderRangeFile.query()
      .where('id', fileID)
      .patch({
        description: description || file.description,
        cute: Number(cute) || file.cute,
        file_url: fileURL || file.file_url, //TODO: falta hacer el proceso de subida de archivos
        is_active: isActive,
      });

    if (!updateFile) throw new Error('No se pudo actualizar el archivo');

    return res.status(200).json({
      ok: true,
      message: 'Archivo actualizado',
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

// update range
export async function UpdateRange(req: Request, res: Response) {
  try {
    const { rangeID } = req.query as unknown as { rangeID: number };
    const { quantity, isActive } = req.body as unknown as {
      quantity: number;
      isActive: boolean;
    };

    if (!rangeID) throw new Error('No se puede actualizar el rango sin ID');

    // validar que el rango exista
    const range = await RhinestoneOrderRange.query()
      .where('id', rangeID)
      .where('is_active', 1)
      .first()
      .select(['id', 'quantity', 'is_active'])
      .castTo<{
        id: number;
        quantity: number;
        is_active: number;
      }>();

    if (!range) throw new Error('No se encontro el rango');

    const updateRange = await RhinestoneOrderRange.query()
      .where('id', rangeID)
      .patch({
        quantity: quantity || range.quantity,
        is_active: isActive,
      });

    if (!updateRange) throw new Error('No se pudo actualizar el rango');

    return res.status(200).json({
      ok: true,
      message: 'Rango actualizado',
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(400).json({ ok: false, message: error.message });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

interface IRockDetails {
  id: number;
  partNumberID: number;
  color_name: string;
  size_name: string;
  quantity: number;
  quantity_original: number;
}

interface IReport {
  id: number;
  is_repo: string;
  quantity: number;
  art_date: string;
  pieces_of_order: number;
  export_date: string;
  category: string;
  style: string;
  num: string;
  status: string;
  rock_details: IRockDetails[];
}
export async function RhinestoneOrdersReport(req: Request, res: Response) {
  try {
    const { startDate, endDate } = req.body as unknown as {
      startDate: string;
      endDate?: string;
    };

    if (!startDate)
      throw new Error('No se puede generar el reporte sin fecha de inicio');

    if (endDate && endDate < startDate)
      throw new Error(
        'La fecha de fin no puede ser menor a la fecha de inicio'
      );

    const start = startDate;
    const end = endDate ? endDate : startDate.split(' ')[0] + ' 23:59:59';
    const report: IReport[] = [];

    const orders = await RhinestoneOrder.query()
      .leftJoin('mo_numbers', 'mo_numbers.mo_id', 'rhinestones_orders.mo_id')
      .where('rhinestones_orders.is_active', 1)
      .whereBetween('rhinestones_orders.art_date', [start, end])
      .select([
        { moID: 'mo_numbers.mo_id' },
        { orderID: 'rhinestones_orders.id' },
        { artDate: 'rhinestones_orders.art_date' },
        raw(
          'CASE WHEN rhinestones_orders.is_repo = 1 THEN "REPO" ELSE "PROD" END'
        ).as('isRepo'),
        'mo_numbers.quantity',
        { exportDate: 'mo_numbers.required_date' },
        'mo_numbers.style',
        { order: 'mo_numbers.num' },
        { status: 'mo_numbers.mo_status' },
        MoNumber.query()
          .select('style_category')
          .where(
            'mo_id',
            MoNumber.query()
              .select('parent_mo_id')
              .where('mo_id', ref('rhinestones_orders.mo_id'))
          )
          .as('category'),
      ])
      .castTo<
        {
          moID: number;
          orderID: number;
          artDate: string;
          isRepo: string;
          quantity: number;
          exportDate: string;
          style: string;
          order: string;
          status: string;
          category: string;
        }[]
      >();

    for (const order of orders) {
      const orderWithRanges = await RhinestoneOrderRange.query().where(
        'order_id',
        +order.orderID
      );

      if (orderWithRanges.length === 0) {
        const orderItems = (await RhinestoneOrderItem.query()
          .join(
            'rhinestones_sizes',
            'rhinestones_sizes.id',
            'rhinestones_order_items.size_id'
          )
          .join(
            'rhinestones_colors',
            'rhinestones_colors.id',
            'rhinestones_order_items.color_id'
          )
          .where('rhinestones_order_items.order_id', +order.orderID)
          .where('rhinestones_order_items.is_active', 1)
          .select([
            { id: 'rhinestones_order_items.id' },
            { partNumberID: 'rhinestones_order_items.rhinestone_inventory_id' },
            'rhinestones_order_items.quantity',
            'rhinestones_sizes.size_name',
            'rhinestones_colors.color_name',
          ])) as unknown as {
          id: number;
          partNumberID: number;
          quantity: number;
          color_name: string;
          size_name: string;
        }[];

        report.push({
          id: order.orderID,
          is_repo: order.isRepo,
          quantity: order.quantity,
          art_date: order.artDate,
          pieces_of_order: order.quantity,
          export_date: order.exportDate,
          category: order.category,
          style: order.style,
          num: order.order,
          status: order.status,
          rock_details: orderItems.map((orderItem) => ({
            id: orderItem.id,
            partNumberID: orderItem.partNumberID,
            color_name: orderItem.color_name,
            size_name: orderItem.size_name,
            quantity: orderItem.quantity * order.quantity,
            quantity_original: orderItem.quantity,
          })),
        });
      } else {
        const rangesInOrder = (await RhinestoneOrderRange.query()
          .where('order_id', order.orderID)
          .where('is_active', 1)
          .select(['id', 'quantity'])) as unknown as {
          id: number;
          quantity: number;
        }[];

        const filesInRanges = (await RhinestoneOrderRangeFile.query()
          .whereIn(
            'rhinestones_range_per_order_id',
            rangesInOrder.map((range) => range.id)
          )
          .where('is_active', 1)
          .select([
            'id',
            'rhinestones_range_per_order_id',
            'cute',
          ])) as unknown as {
          id: number;
          rhinestones_range_per_order_id: number;
          cute: number;
        }[];

        const orderItems = (await RhinestoneOrderItem.query()
          .join(
            'rhinestones_sizes',
            'rhinestones_sizes.id',
            'rhinestones_order_items.size_id'
          )
          .join(
            'rhinestones_colors',
            'rhinestones_colors.id',
            'rhinestones_order_items.color_id'
          )
          .whereIn(
            'rhinestones_order_items.file_per_range_id',
            filesInRanges.map((file) => file.id)
          )
          .where('rhinestones_order_items.is_active', 1)
          .select([
            { id: 'rhinestones_order_items.id' },
            {
              fileID: 'rhinestones_order_items.file_per_range_id',
            },
            {
              partNumberID: 'rhinestones_order_items.rhinestone_inventory_id',
            },
            'rhinestones_order_items.quantity',
            'rhinestones_sizes.size_name',
            'rhinestones_colors.color_name',
          ])) as unknown as {
          id: number;
          fileID: number;
          partNumberID: number;
          quantity: number;
          color_name: string;
          size_name: string;
        }[];

        for (const item of orderItems) {
          const file = filesInRanges.find(
            (file): boolean => file.id === item.fileID
          ) as unknown as {
            cute: number;
            rhinestones_range_per_order_id: number;
          };

          const range = rangesInOrder.find(
            (range): boolean => range.id === file.rhinestones_range_per_order_id
          ) as unknown as { quantity: number };

          const existingOrder = report.find(
            (orderReport): boolean => orderReport.id === order.orderID
          );

          if (existingOrder) {
            const existingPartNumber = existingOrder.rock_details.find(
              (part): boolean => part.partNumberID === item.partNumberID
            );

            if (existingPartNumber) {
              existingPartNumber.quantity +=
                item.quantity * range.quantity * file.cute;
              existingPartNumber.quantity_original += item.quantity;
            } else {
              existingOrder.rock_details.push({
                id: item.id,
                partNumberID: item.partNumberID,
                color_name: item.color_name,
                size_name: item.size_name,
                quantity: item.quantity * range.quantity * file.cute,
                quantity_original: item.quantity,
              });
            }
          } else {
            report.push({
              id: order.orderID,
              is_repo: order.isRepo,
              quantity: order.quantity,
              art_date: order.artDate,
              pieces_of_order: order.quantity,
              export_date: order.exportDate,
              category: order.category,
              style: order.style,
              num: order.order,
              status: order.status,
              rock_details: [
                {
                  id: item.id,
                  partNumberID: item.partNumberID,
                  color_name: item.color_name,
                  size_name: item.size_name,
                  quantity: item.quantity * range.quantity * file.cute,
                  quantity_original: item.quantity,
                },
              ],
            });
          }
        }
      }
    }

    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet('Rock Details');

    const styleHeader = workbook.createStyle({
      alignment: {
        horizontal: 'center',
      },
      font: {
        bold: true,
      },
      border: {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      },
      fill: {
        type: 'pattern',
        patternType: 'solid',
        bgColor: '#DBFFDB',
        fgColor: '#DBFFDB',
      },
    });

    const styleStones = workbook.createStyle({
      border: {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      },
      fill: {
        type: 'pattern',
        patternType: 'solid',
        bgColor: '#C0A6C0',
        fgColor: '#C0A6C0',
      },
    });

    worksheet.cell(1, 1).string('SCHEDULE').style(styleHeader);
    worksheet.cell(1, 2).string('ORDEN').style(styleHeader);
    worksheet.cell(1, 3).string('VCH').style(styleHeader);
    worksheet.cell(1, 4).string('DUPLICADO').style(styleHeader);
    worksheet.cell(1, 5).string('ESTILO').style(styleHeader);
    worksheet.cell(1, 6).string('CAT').style(styleHeader);
    worksheet.cell(1, 7).string('QTY').style(styleHeader);
    worksheet.cell(1, 8).string('ENTREGADO').style(styleHeader);
    worksheet.cell(1, 9).string('PIEDRAS POR ORDEN').style(styleHeader);
    worksheet.cell(1, 10).string('TIPO').style(styleHeader);

    worksheet.column(1).setWidth(15);
    worksheet.column(2).setWidth(10);
    worksheet.column(3).setWidth(5);
    worksheet.column(4).setWidth(15);
    worksheet.column(5).setWidth(25);
    worksheet.column(6).setWidth(25);
    worksheet.column(7).setWidth(6);
    worksheet.column(8).setWidth(15);
    worksheet.column(9).setWidth(15);
    worksheet.column(10).setWidth(15);

    let row = 2;

    for (const order of report) {
      worksheet
        .cell(row, 1)
        .string(dayjs(order.export_date).format('DD/MM/YYYY'));
      worksheet.cell(row, 2).string(order.num.split('/')[0]);
      worksheet.cell(row, 3).string(order.num.split('/')[1]);
      worksheet.cell(row, 4).string(order.num);
      worksheet.cell(row, 5).string(order.style);
      worksheet
        .cell(row, 6)
        .string(order.category ? order.category : 'SIN CAT');
      worksheet.cell(row, 7).number(order.pieces_of_order);
      worksheet.cell(row, 8).string(dayjs(order.art_date).format('DD/MM/YYYY'));
      worksheet
        .cell(row, 9)
        .number(order.rock_details.reduce((a, b) => a + b.quantity, 0));
      worksheet.cell(row, 10).string(order.is_repo);

      let column = 11;
      for (let i = 0; i < order.rock_details.length; i++) {
        worksheet
          .cell(1, column)
          .string(`Piedra ${i + 1}`)
          .style(styleHeader);
        worksheet
          .cell(1, column + 1)
          .string('Cantidad')
          .style(styleHeader);

        worksheet
          .cell(row, column)
          .string(
            `${order.rock_details[i].size_name}/${order.rock_details[i].color_name}`
          )
          .style(styleStones);
        worksheet
          .cell(row, column + 1)
          .number(order.rock_details[i].quantity)
          .style(styleStones);

        column += 2;
      }

      row++;
    }

    workbook.write('Excel.xlsx', res);

    return res.status(201);
  } catch (error) {
    console.log('error', error);

    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({ ok: false, message: error.message });
    }

    logger.error('Error interno del servidor');

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function ConvertOrders(req: Request, res: Response) {
  try {
    const { orderID } = req.query as unknown as { orderID: number };

    if (!orderID) throw new Error('No se puede convertir una orden sin ID');

    const orderWithRange = await RhinestoneOrder.query()
      .join(
        'rhinestones_ranges_per_order',
        'rhinestones_orders.id',
        'rhinestones_ranges_per_order.order_id'
      )
      .where('rhinestones_orders.id', orderID)
      .where('rhinestones_orders.is_active', 1)
      .select('rhinestones_orders.id')
      .first()
      .castTo<{ id: number }>();

    if (orderWithRange) throw new Error('La orden ya cuenta con rangos');

    // search range default
    const range = await RhinestoneRange.query()
      .where('name', 'DEFAULT')
      .select('id')
      .first()
      .castTo<{ id: number }>();

    if (!range) throw new Error('No existe el rango default');

    // search order to convert
    const order = await RhinestoneOrder.query()
      .join('mo_numbers', 'mo_numbers.mo_id', 'rhinestones_orders.mo_id')
      .where({ id: orderID })
      .where({ is_active: 1 })
      .select([
        'rhinestones_orders.id',
        'rhinestones_orders.mo_id',
        'rhinestones_orders.sheets_per_art',
        'mo_numbers.quantity',
      ])
      .first()
      .castTo<{
        id: number;
        mo_id: number;
        sheets_per_art: number;
        quantity: number;
      }>();

    if (!order) throw new Error('No existe esa orden');

    const updateOrder: boolean = await transaction(
      RhinestoneOrderRange,
      RhinestoneOrderRangeFile,
      RhinestoneOrderItem,
      async (
        RhinestonesRangesPerOrder,
        RhinestonesFilesRange,
        RhinestoneOrderItems
      ) => {
        // create new range per order
        const newRangePerOrder =
          (await RhinestonesRangesPerOrder.query().insert({
            order_id: order.id,
            range_id: range.id,
            quantity: order.quantity,
          })) as unknown as { id: number };

        if (!newRangePerOrder) throw new Error('No se pudo convertir la orden');

        // add file to new range per order
        const file = (await RhinestonesFilesRange.query().insert({
          rhinestones_range_per_order_id: newRangePerOrder.id,
          description: 'DEFAULT',
          cute: order.sheets_per_art ? order.sheets_per_art : 1,
        })) as unknown as { id: number };

        if (!file) throw new Error('No se pudo convertir la orden');

        // add file to items
        const items = await RhinestoneOrderItems.query()
          .update({
            file_per_range_id: file.id,
          })
          .whereIn('order_id', [order.id])
          .where({ is_active: 1 });

        if (!items) throw new Error('No se pudo convertir la orden');

        return true;
      }
    );

    if (!updateOrder) throw new Error('No se pudo convertir la orden');

    return res.status(200).json({ ok: true, message: 'ok' });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({ ok: false, message: error.message });
    }

    logger.error('Error interno del servidor');

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function getRhinestoneOrderDetails(req: Request, res: Response) {
  try {
    const { barcode } = req.body as unknown as { barcode: string };

    const mainOrder = await MoNumber.query()
      .where('num', barcode)
      .select('mo_id')
      .first()
      .castTo<{ mo_id: number }>();

    if (!mainOrder.mo_id)
      return res
        .status(400)
        .json({ ok: false, message: 'No se encontro la orden principal' });

    const details = await RhinestoneOrderItem.query()
      .innerJoin(
        'rhinestones_files_range',
        'rhinestones_files_range.id',
        'rhinestones_order_items.file_per_range_id'
      )
      .innerJoin(
        'rhinestones_ranges_per_order',
        'rhinestones_ranges_per_order.id',
        'rhinestones_files_range.rhinestones_range_per_order_id'
      )
      .innerJoin(
        'rhinestones_orders',
        'rhinestones_orders.id',
        'rhinestones_order_items.order_id'
      )
      .innerJoin('mo_numbers', 'mo_numbers.mo_id', 'rhinestones_orders.mo_id')
      .innerJoin(
        'rhinestones_colors',
        'rhinestones_colors.id',
        'rhinestones_order_items.color_id'
      )
      .innerJoin(
        'rhinestones_sizes',
        'rhinestones_sizes.id',
        'rhinestones_order_items.size_id'
      )
      .innerJoin(
        'rhinestones_ranges',
        'rhinestones_ranges.id',
        'rhinestones_ranges_per_order.range_id'
      )
      .where('rhinestones_order_items.is_active', 1)
      .where('rhinestones_files_range.is_active', 1)
      .where('rhinestones_ranges_per_order.is_active', 1)
      .where('rhinestones_orders.is_active', 1)
      .where('rhinestones_colors.is_active', 1)
      .where('rhinestones_sizes.is_active', 1)
      .where('rhinestones_ranges.is_active', 1)
      .where('mo_numbers.parent_mo_id', mainOrder.mo_id)
      .select([
        { id: 'rhinestones_orders.id' },
        { mo: 'mo_numbers.num' },
        { style: 'mo_numbers.style' },
        { quantity: 'mo_numbers.quantity' },
        { range_name: 'rhinestones_ranges.name' },
        {
          is_repo: 'rhinestones_orders.is_repo',
        },
        {
          range_quantity: 'rhinestones_ranges_per_order.quantity',
        },
        { cut: 'rhinestones_files_range.cute' },
        { color: 'rhinestones_colors.color_name' },
        { size: 'rhinestones_sizes.size_name' },
        { items_quantity: 'rhinestones_order_items.quantity' },
        raw(
          'rhinestones_order_items.quantity * rhinestones_files_range.cute * rhinestones_ranges_per_order.quantity'
        ).as('total_stones'),
      ])
      .castTo<
        {
          id: string;
          mo: string;
          style: string;
          quantity: number;
          range_name: string;
          is_repo: number;
          range_quantity: number;
          cut: string;
          color: string;
          size: string;
          items_quantity: number;
          total_stones: number;
        }[]
      >();

    const groupedData = details.reduce((acc, item) => {
      const key = `${item.size}-${item.color}`;

      if (!acc[key]) {
        acc[key] = {
          size: item.size,
          color: item.color,
          total_stones: 0,
        };
      }

      acc[key].total_stones += item.total_stones;

      return acc;
    }, {});

    const groupedArray = Object.values(groupedData);

    const uniqueRanges = new Set(details.map((item) => item.range_name)).size;
    const uniqueMOList = Array.from(new Set(details.map((item) => item.mo)));
    const totalCuts = details.reduce(
      (sum, item) => Number(sum) + Number(item.cut),
      0
    );

    return res.status(200).json({
      ok: true,
      data: {
        totalCuts,
        totalRanges: uniqueRanges,
        orders: uniqueMOList,
        stones: groupedArray,
        details,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({ ok: false, message: error.message });
    }

    logger.error('Error interno del servidor');

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}
