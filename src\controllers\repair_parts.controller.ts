import dayjs from 'dayjs';
import type { Request, Response } from 'express';
import { raw, transaction } from 'objection';

import { Employee } from '@app/models/employee.schema';
import {
  RepairPartCategories,
  RepairPartLocations,
  RepairPartPurchase,
  RepairPartPurchaseLog,
  RepairPartRequestItems,
  RepairPartRequests,
  RepairPartSuppliers,
  RepairParts,
  RepairPartsMachine,
} from '@app/models/repair_parts.schema';
import { buildLogger } from '@app/settings';

const logger = buildLogger('repair_parts.controller.ts');

export async function repairParts(req: Request, res: Response) {
  try {
    const { part_name, part_code, barcode, category, building } =
      req.query as unknown as {
        part_name?: string;
        part_code?: string;
        barcode?: string;
        category?: string;
        building?: string | string[];
      };
    const page = Number(req.query.page ?? 1);
    const limit = Number(req.query.limit ?? 10);

    const buildings = Array.isArray(building)
      ? building.map((b: string): number => Number(b))
      : [Number(building)];

    const results = (await RepairParts.query()
      .innerJoin(
        'repair_part_categories',
        'repair_parts.category_id',
        'repair_part_categories.category_id'
      )
      .innerJoin(
        'buildings',
        'buildings.building_id',
        'repair_parts.building_id'
      )
      .innerJoin(
        'repair_part_locations',
        'repair_part_locations.locations_id',
        'repair_parts.location_id'
      )
      .select([
        { part_id: 'repair_parts.part_id' },
        { part_name: 'repair_parts.part_name' },
        { quantity: 'repair_parts.quantity' },
        { description: 'repair_parts.description' },
        { brand: 'repair_parts.brand' },
        { type_of_currency: 'repair_parts.type_of_currency' },
        { price: 'repair_parts.price' },
        { part_code: 'repair_parts.part_code' },
        { barcode: 'repair_parts.barcode' },
        { min_stock: 'repair_parts.min_stock' },
        { created_at: 'repair_parts.created_at' },
        { measurement: 'repair_parts.measurement' },
        { category: 'repair_part_categories.category' },
        { building: 'buildings.building' },
        { location: 'repair_part_locations.location' },
      ])
      .modify((qb) => {
        if (part_name) {
          qb.where('repair_parts.part_name', 'like', `%${part_name}%`);
        }
        if (part_code) {
          qb.where('repair_parts.part_code', 'like', `%${part_code}%`);
        }
        if (barcode) {
          qb.where('repair_parts.barcode', 'like', `%${barcode}%`);
        }
        if (category) {
          qb.where('repair_part_categories.category', 'like', `%${category}%`);
        }
        if (building) {
          qb.whereIn('repair_parts.building_id', buildings);
        }
      })
      .where('repair_parts.is_active', 1)
      .where('repair_part_categories.is_active', 1)
      .orderBy('repair_parts.created_at', 'desc')
      .page(page - 1, limit)) as unknown as {
      total: number;
      results: {
        part_id: number;
        part_name: string;
        quantity: number;
        description: string;
        brand: string;
        type_of_currency: string;
        price: number;
        part_code: string;
        barcode: string;
        min_stock: number;
        created_at: string;
        measurement: string;
        category: string;
        building: string;
        location: string;
      }[];
    };

    return res.status(200).json({
      ok: true,
      message: `Partes de reparación encontradas ${results.results.length}`,
      data: results.results,
      pagination: {
        total: results.total,
        page,
        limit,
        total_pages: Math.ceil(results.total / limit),
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al obtener partes de reparación',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function newRepairPart(req: Request, res: Response) {
  try {
    const {
      tokenInfo,
      code,
      barcode,
      description,
      quantity,
      brand,
      price,
      min_stock,
      location,
      category,
    } = req.body as unknown as {
      tokenInfo: {
        area_id: number;
      };
      code: string;
      barcode: string;
      description: string;
      quantity: number;
      brand: string;
      price: number;
      min_stock: number;
      location: number;
      category: number;
    };
    const { area_id } = tokenInfo;

    const part = await RepairParts.query()
      .modify((qb) => {
        if (barcode) {
          qb.where('repair_parts.barcode', 'like', `%${barcode}%`);
        }
      })
      .where('repair_parts.part_code', code)
      .where('repair_parts.building_id', area_id)
      .where('repair_parts.is_active', true)
      .select('part_id')
      .first()
      .castTo<{ part_id: number }>();

    if (part) {
      return res.status(400).json({
        ok: false,
        message: 'El repuesto ya existe',
      });
    }

    if (!barcode) {
      const newPart = (await RepairParts.query().insert({
        category_id: category,
        part_name: code,
        quantity,
        description,
        part_status: 'active',
        brand,
        price,
        part_code: code,
        building_id: area_id,
        min_stock,
        location_id: location,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      })) as unknown as { id: number };

      await RepairParts.query()
        .update({
          barcode: `VPRP${newPart.id}`,
        })
        .where('part_id', newPart.id);

      return res.status(200).json({
        ok: true,
        message: 'Parte de reparación creada',
      });
    }

    await RepairParts.query().insert({
      barcode,
      category_id: category,
      part_name: code,
      quantity,
      description,
      part_status: 'active',
      brand,
      price,
      part_code: code,
      building_id: area_id,
      min_stock,
      location_id: location,
      created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function deleteRepairPart(req: Request, res: Response) {
  try {
    const { tokenInfo } = req.body as unknown as {
      tokenInfo: {
        area_id: number;
      };
    };
    const { part_id } = req.query as unknown as {
      part_id: number;
    };

    const part = await RepairParts.query()
      .where('repair_parts.part_id', part_id)
      .where('repair_parts.building_id', tokenInfo.area_id)
      .where('repair_parts.is_active', true)
      .select('part_id')
      .first()
      .castTo<{ part_id: number }>();

    if (!part) {
      return res.status(400).json({
        ok: false,
        message: 'El repuesto no existe',
      });
    }

    await RepairParts.query()
      .update({
        part_status: 'inactive',
        is_active: false,
      })
      .where('part_id', part_id);

    return res.status(200).json({
      ok: true,
      message: 'Parte de reparación eliminada',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function updateRepairPart(req: Request, res: Response) {
  try {
    const { tokenInfo, part } = req.body as unknown as {
      tokenInfo: {
        area_id: number;
      };
      part: {
        part_id: number;
        barcode: string;
        brand: string;
        category_id: number;
        part_name: string;
        description: string;
        location_id: number;
        measurement: string;
        min_stock: number;
        part_code: string;
        price: number;
        type_of_currency: string;
      };
    };

    const partExists = await RepairParts.query()
      .where('repair_parts.part_id', part.part_id)
      .where('repair_parts.building_id', tokenInfo.area_id)
      .where('repair_parts.is_active', true)
      .select([
        { part_id: 'part_id' },
        { category_id: 'category_id' },
        { location_id: 'location_id' },
      ])
      .first()
      .castTo<{ part_id: number; category_id: number; location_id: number }>();

    if (!partExists) {
      return res.status(400).json({
        ok: false,
        message: 'El repuesto no existe',
      });
    }

    await RepairParts.query()
      .update({
        barcode: part.barcode,
        brand: part.brand,
        category_id: part.category_id
          ? part.category_id
          : partExists.category_id,
        part_name: part.part_name,
        description: part.description,
        location_id: part.location_id
          ? part.location_id
          : partExists.location_id,
        measurement: part.measurement,
        min_stock: part.min_stock,
        part_code: part.part_code,
        price: part.price,
        type_of_currency: part.type_of_currency,
      })
      .where('part_id', part.part_id)
      .where('building_id', tokenInfo.area_id);

    return res.status(200).json({
      ok: true,
      message: 'Parte de reparación actualizada',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function repairPartsWithLowStock(req: Request, res: Response) {
  try {
    const { tokenInfo } = req.body as unknown as {
      tokenInfo: {
        area_id: number;
        employee_id: number;
      };
    };

    const { part_name, part_code, barcode, category, building } =
      req.query as unknown as {
        part_name?: string;
        part_code?: string;
        barcode?: string;
        category?: string;
        building?: string | string[];
      };

    const page = Number(req.query.page ?? 1);
    const limit = Number(req.query.limit ?? 10);
    const area_id = tokenInfo.area_id;

    const buildings = Array.isArray(building)
      ? building.map((b: string): number => Number(b))
      : [Number(building)];

    const results = (await RepairParts.query()
      .innerJoin(
        'repair_part_categories',
        'repair_parts.category_id',
        'repair_part_categories.category_id'
      )
      .innerJoin(
        'buildings',
        'buildings.building_id',
        'repair_parts.building_id'
      )
      .innerJoin(
        'repair_part_locations',
        'repair_part_locations.locations_id',
        'repair_parts.location_id'
      )
      .where('repair_parts.is_active', 1)
      .where('repair_part_categories.is_active', 1)
      .where('repair_parts.quantity', '<=', 'repair_parts.min_stock')
      .where('repair_parts.building_id', area_id)
      .select([
        { part_id: 'repair_parts.part_id' },
        { part_name: 'repair_parts.part_name' },
        { quantity: 'repair_parts.quantity' },
        { description: 'repair_parts.description' },
        { brand: 'repair_parts.brand' },
        { type_of_currency: 'repair_parts.type_of_currency' },
        { price: 'repair_parts.price' },
        { part_code: 'repair_parts.part_code' },
        { barcode: 'repair_parts.barcode' },
        { min_stock: 'repair_parts.min_stock' },
        { created_at: 'repair_parts.created_at' },
        { measurement: 'repair_parts.measurement' },
        { category: 'repair_part_categories.category' },
        { building: 'buildings.building' },
        { location: 'repair_part_locations.location' },
      ])
      .orderBy('repair_parts.created_at', 'desc')
      .modify((qb) => {
        if (part_name) {
          qb.where('repair_parts.part_name', 'like', `%${part_name}%`);
        }
        if (part_code) {
          qb.where('repair_parts.part_code', 'like', `%${part_code}%`);
        }
        if (barcode) {
          qb.where('repair_parts.barcode', 'like', `%${barcode}%`);
        }
        if (category) {
          qb.where('repair_part_categories.category', 'like', `%${category}%`);
        }
        if (building) {
          qb.whereIn('repair_parts.building_id', buildings);
        }
      })
      .page(page - 1, limit)) as unknown as {
      total: number;
      results: {
        part_id: number;
        part_name: string;
        quantity: number;
        description: string;
        brand: string;
        type_of_currency: string;
        price: number;
        part_code: string;
        barcode: string;
        min_stock: number;
        created_at: string;
        measurement: string;
        category: string;
        building: string;
        location: string;
      }[];
    };

    return res.status(200).json({
      ok: true,
      message: `Partes de reparación encontradas ${results.results.length}`,
      data: results.results,
      pagination: {
        total: results.total,
        page,
        limit,
        total_pages: Math.ceil(results.total / limit),
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al obtener partes de reparación',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function suppliers(_req: Request, res: Response) {
  try {
    const suppliers = await RepairPartSuppliers.query()
      .where('repair_part_suppliers.status', 'active')
      .select(
        'supplier_id',
        'supplier',
        'company',
        'telephone',
        'email',
        'address'
      )
      .orderBy('company', 'asc')
      .castTo<
        {
          supplier_id: number;
          supplier: string;
          company: string;
          telephone: string;
          email: string;
          address: string;
        }[]
      >();

    if (suppliers.length === 0) {
      return res.status(404).json({
        ok: false,
        message: 'No se encontraron proveedores activos',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Proveedores obtenidos',
      data: suppliers,
      total: suppliers.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al obtener los proveedores',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function deleteSupplier(req: Request, res: Response) {
  try {
    const { supplier_id } = req.query as unknown as {
      supplier_id: number;
    };

    const supplier = await RepairPartSuppliers.query()
      .where('supplier_id', supplier_id)
      .where('status', 'active')
      .select('supplier_id')
      .first()
      .castTo<{ supplier_id: number }>();

    if (!supplier) {
      return res.status(404).json({
        ok: false,
        message: 'Proveedor no encontrado',
      });
    }

    await RepairPartSuppliers.query()
      .update({
        status: 'deleted',
      })
      .where('supplier_id', supplier_id);

    return res.status(200).json({
      ok: true,
      message: 'Proveedor eliminado',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al eliminar el proveedor',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function createSupplier(req: Request, res: Response) {
  try {
    const { supplier, company, telephone, email, address } =
      req.body as unknown as {
        supplier: string;
        company: string;
        telephone: string;
        email: string;
        address: string;
      };

    const supplierExists = await RepairPartSuppliers.query()
      .where('supplier', supplier)
      .where('status', 'active')
      .select('supplier')
      .first()
      .castTo<{ supplier: string }>();

    if (supplierExists) {
      return res.status(400).json({
        ok: false,
        message: 'El proveedor ya existe',
      });
    }

    await RepairPartSuppliers.query().insert({
      supplier,
      company,
      telephone,
      email,
      address,
      status: 'active',
    });

    return res.status(200).json({
      ok: true,
      message: 'Proveedor creado',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al crear el proveedor',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function updateSupplier(req: Request, res: Response) {
  try {
    const { supplier } = req.body as unknown as {
      supplier: {
        supplier_id: number;
        supplier: string;
        company: string;
        telephone: string;
        email: string;
        address: string;
      };
    };

    const supplierExists = await RepairPartSuppliers.query()
      .where('supplier_id', supplier.supplier_id)
      .where('status', 'active')
      .select('supplier')
      .first()
      .castTo<{ supplier: string }>();

    if (!supplierExists) {
      return res.status(404).json({
        ok: false,
        message: 'Proveedor no encontrado',
      });
    }

    await RepairPartSuppliers.query()
      .update({
        supplier: supplier.supplier,
        company: supplier.company,
        telephone: supplier.telephone,
        email: supplier.email,
        address: supplier.address,
      })
      .where('supplier_id', supplier.supplier_id);

    return res.status(200).json({
      ok: true,
      message: 'Proveedor actualizado',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al actualizar el proveedor',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function categories(req: Request, res: Response) {
  try {
    const { category } = req.query as unknown as { category: string };

    const categories = await RepairPartCategories.query()
      .where('repair_part_categories.is_active', 1)
      .modify((qb) => {
        if (category) {
          qb.where('category', 'like', `%${category}%`);
        }
      })
      .select('category_id', 'category')
      .orderBy('category', 'asc')
      .castTo<
        {
          category_id: number;
          category: string;
        }[]
      >();

    if (categories.length === 0) {
      return res.status(404).json({
        ok: false,
        message: 'No se encontraron categorias activos',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Categorias obtenidas',
      data: categories,
      total: categories.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al obtener las categorias',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function createCategory(req: Request, res: Response) {
  try {
    const { category } = req.body as unknown as {
      category: string;
    };

    const categoryExists = await RepairPartCategories.query()
      .where('category', category)
      .where('is_active', 1)
      .select('category')
      .first()
      .castTo<{ category: string }>();

    if (categoryExists) {
      return res.status(400).json({
        ok: false,
        message: 'La categoria ya existe',
      });
    }

    await RepairPartCategories.query().insert({
      category,
      is_active: true,
      category_status: 'active',
    });

    return res.status(200).json({
      ok: true,
      message: 'Categoria creada',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al crear la categoria',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function updateCategory(req: Request, res: Response) {
  try {
    const { category_id, category } = req.body as unknown as {
      category_id: number;
      category: string;
    };

    const categoryExists = await RepairPartCategories.query()
      .where('category_id', category_id)
      .where('is_active', 1)
      .select('category_id')
      .first()
      .castTo<{ category_id: number }>();

    if (!categoryExists) {
      return res.status(404).json({
        ok: false,
        message: 'Categoria no encontrada',
      });
    }

    await RepairPartCategories.query()
      .update({
        category,
      })
      .where('category_id', category_id);

    return res.status(200).json({
      ok: true,
      message: 'Categoria actualizada',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al actualizar la categoria',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function deleteCategory(req: Request, res: Response) {
  try {
    const { category_id } = req.query as unknown as {
      category_id: number;
    };

    const category = await RepairPartCategories.query()
      .where('category_id', category_id)
      .where('is_active', 1)
      .select('category_id')
      .first()
      .castTo<{ category_id: number }>();

    if (!category) {
      return res.status(404).json({
        ok: false,
        message: 'Categoria no encontrada',
      });
    }

    await RepairPartCategories.query()
      .update({
        is_active: false,
        category_status: 'deleted',
      })
      .where('category_id', category_id);

    return res.status(200).json({
      ok: true,
      message: 'Categoria eliminada',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al eliminar la categoria',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function locations(req: Request, res: Response) {
  try {
    const { tokenInfo } = req.body as unknown as {
      tokenInfo: {
        area_id: number;
      };
    };

    const location_name = req.query.location_name as string;
    const page = Number(req.query.page ?? 1);
    const limit = Number(req.query.limit ?? 10);
    const area_id = tokenInfo.area_id;

    const locations = (await RepairPartLocations.query()
      .where('repair_part_locations.location_status', 'active')
      .where('repair_part_locations.building_id', area_id)
      .select([
        {
          location_id: 'locations_id',
        },
        'location',
      ])
      .orderBy('location', 'asc')
      .modify((qb) => {
        if (location_name) {
          qb.where('location', 'like', `%${location_name}%`);
        }
      })
      .page(page - 1, limit)) as unknown as {
      total: number;
      results: {
        location: string;
        location_id: number;
      }[];
    };

    return res.status(200).json({
      ok: true,
      message: `Ubicaciones obtenidas ${locations.results.length}`,
      data: locations.results,
      pagination: {
        total: locations.total,
        page,
        limit,
        total_pages: Math.ceil(locations.total / limit),
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al obtener las ubicaciones',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function createLocation(req: Request, res: Response) {
  try {
    const { tokenInfo, location } = req.body as unknown as {
      tokenInfo: {
        area_id: number;
      };
      location: string;
    };

    const { area_id } = tokenInfo;

    const locationExists = await RepairPartLocations.query()
      .where('location', location)
      .where('building_id', area_id)
      .select('location')
      .first()
      .castTo<{ location: string }>();

    if (locationExists) {
      return res.status(400).json({
        ok: false,
        message: 'La ubicacion ya existe',
      });
    }

    await RepairPartLocations.query().insert({
      location,
      building_id: area_id,
      location_status: 'active',
    });

    return res.status(200).json({
      ok: true,
      message: 'Ubicacion creada',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al crear la ubicacion',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function updateLocation(req: Request, res: Response) {
  try {
    const { tokenInfo, location } = req.body as unknown as {
      tokenInfo: {
        area_id: number;
      };
      location: {
        location_id: number;
        location: string;
      };
    };

    const { area_id } = tokenInfo;

    const locationExists = await RepairPartLocations.query()
      .where('locations_id', location.location_id)
      .where('building_id', area_id)
      .select('location')
      .first()
      .castTo<{ location: string }>();

    if (!locationExists) {
      return res.status(400).json({
        ok: false,
        message: 'La ubicacion no existe',
      });
    }

    await RepairPartLocations.query()
      .update({
        location: location.location,
      })
      .where('locations_id', location.location_id)
      .where('building_id', area_id);

    return res.status(200).json({
      ok: true,
      message: 'Ubicacion actualizada',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al actualizar la ubicacion',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function deleteLocation(req: Request, res: Response) {
  try {
    const { tokenInfo } = req.body as unknown as {
      tokenInfo: { area_id: number };
    };

    const { area_id } = tokenInfo;
    const { location_id } = req.query as unknown as { location_id: number };

    const location = await RepairPartLocations.query()
      .where('locations_id', location_id)
      .where('building_id', area_id)
      .select('location')
      .first()
      .castTo<{ location: string }>();

    if (!location) {
      return res.status(400).json({
        ok: false,
        message: 'La ubicacion no existe',
      });
    }

    await RepairPartLocations.query()
      .where('locations_id', location_id)
      .where('building_id', area_id)
      .update({
        location_status: 'deleted',
      });

    return res.status(200).json({
      ok: true,
      message: 'Ubicacion eliminada',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al eliminar la ubicacion',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function createOrder(req: Request, res: Response) {
  try {
    const { tokenInfo, barcode, quantity, supplierID } =
      req.body as unknown as {
        tokenInfo: {
          area_id: number;
        };
        barcode: string;
        quantity: number;
        supplierID: number;
      };

    const { area_id } = tokenInfo;

    const part = await RepairParts.query()
      .where('repair_parts.barcode', barcode)
      .orWhere('repair_parts.part_code', barcode)
      .where('repair_parts.building_id', area_id)
      .select('repair_parts.part_id')
      .first()
      .castTo<{ part_id: number }>();

    if (!part) {
      return res.status(404).json({
        ok: false,
        message: 'No se encontraron partes de reparación',
      });
    }

    const supplier = await RepairPartSuppliers.query()
      .where('supplier_id', supplierID)
      .select('supplier_id')
      .first()
      .castTo<{ supplier_id: number }>();

    if (!supplier) {
      return res.status(404).json({
        ok: false,
        message: 'No se encontraron proveedores',
      });
    }

    const order = await RepairPartPurchase.query().insert({
      supplier_id: supplier.supplier_id,
      part_id: part.part_id,
      quantity: quantity,
      purchase_status: 'pendiente',
    });

    if (!order) {
      return res.status(404).json({
        ok: false,
        message: 'No se pudo crear la orden de compra',
      });
    }

    return res.status(201).json({
      ok: true,
      message: 'Orden de compra creada',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error en crear la orden de compra',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function getPendingOrders(req: Request, res: Response) {
  try {
    const { tokenInfo } = req.body as unknown as {
      tokenInfo: {
        area_id: number;
      };
    };

    const { area_id } = tokenInfo;

    const company = req.query.company as string;
    const part_code = req.query.part_code as string;
    const part_name = req.query.part_name as string;
    const page = Number(req.query.page ?? 1);
    const limit = Number(req.query.limit ?? 20);

    const results = (await RepairPartPurchase.query()
      .innerJoin(
        'repair_part_suppliers',
        'repair_part_suppliers.supplier_id',
        'repair_part_purchase.supplier_id'
      )
      .innerJoin(
        'repair_parts',
        'repair_parts.part_id',
        'repair_part_purchase.part_id'
      )
      .where('repair_part_purchase.purchase_status', 'pendiente')
      .where('repair_parts.building_id', area_id)
      .select([
        'repair_part_purchase.purchase_id',
        'repair_part_purchase.quantity',
        'repair_part_suppliers.company',
        'repair_part_suppliers.supplier_id',
        'repair_parts.description',
        'repair_parts.part_code',
        'repair_parts.part_id',
        'repair_parts.part_name',
        'repair_parts.price',
      ])
      .orderBy('repair_part_purchase.created_at', 'asc')
      .modify((qb) => {
        if (company) {
          qb.where('repair_part_suppliers.company', 'like', `%${company}%`);
        }

        if (part_code) {
          qb.where('repair_parts.part_code', 'like', `%${part_code}%`);
        }

        if (part_name) {
          qb.where('repair_parts.part_name', 'like', `%${part_name}%`);
        }
      })
      .page(page - 1, limit)) as unknown as {
      total: number;
      results: {
        company: string;
        description: string;
        part_code: string;
        part_id: number;
        part_name: string;
        price: number;
        purchase_id: number;
        quantity: number;
        supplier_id: number;
      }[];
    };

    return res.status(200).json({
      ok: true,
      message: 'Ordenes de compra obtenidas',
      data: results.results,
      pagination: {
        total: results.total,
        page,
        limit,
        total_pages: Math.ceil(results.total / limit),
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: 'Error al obtener las ordenes de compra',
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function receiveDeliveryNote(req: Request, res: Response) {
  try {
    const { parts, delivery_note } = req.body as unknown as {
      parts: {
        purchase_id: number;
        supplier_id: number;
        part_id: number;
        quantity: number;
        price: number;
      }[];
      delivery_note: string;
    };

    const uniqueSuppliers = new Set(parts.map((p) => p.supplier_id));

    if (uniqueSuppliers.size > 1)
      throw new Error(
        'Todas las repuestos deben pertenecer al mismo proveedor.'
      );

    const receiveDeliveryNoteTransaction = await transaction(
      RepairParts,
      RepairPartPurchase,
      RepairPartPurchaseLog,
      async (RepairParts, RepairPartPurchase, RepairPartPurchaseLog) => {
        for (const part of parts) {
          const { purchase_id, supplier_id, part_id, quantity, price } = part;

          const purchase = await RepairPartPurchase.query()
            .where('purchase_id', purchase_id)
            .where('supplier_id', supplier_id)
            .where('part_id', part_id)
            .where('purchase_status', 'pendiente')
            .first()
            .select('quantity')
            .castTo<{ quantity: number }>();

          if (!purchase) {
            logger.error(`No se encontro la orden de compra: ${purchase_id}`);

            throw new Error(
              `No se encontro la orden de compra: ${purchase_id}`
            );
          }

          if (quantity > purchase.quantity) {
            logger.error(
              `La cantidad recibida es mayor a la cantidad de la orden de compra: ${purchase_id}`
            );

            throw new Error(
              `La cantidad recibida es mayor a la cantidad de la orden de compra: ${purchase_id}`
            );
          }

          const repairPart = await RepairParts.query()
            .where('part_id', part_id)
            .select('part_id', 'quantity', 'price')
            .first()
            .castTo<{ part_id: number; quantity: number; price: number }>();

          if (!repairPart) {
            logger.error(`No se encontro el repuesto: ${part_id}`);

            throw new Error(`No se encontro el repuesto: ${part_id}`);
          }

          const oldQuantity = Number(repairPart.quantity);
          const oldPrice = Number(repairPart.price);

          const newQuantity = oldQuantity + Number(quantity);
          const newPrice =
            parseFloat(price.toFixed(2)) > oldPrice
              ? parseFloat(price.toFixed(2))
              : Number(repairPart.price.toFixed(2));

          await RepairParts.query().where('part_id', part_id).update({
            quantity: newQuantity,
            price: newPrice,
          });

          if (newPrice !== oldPrice) {
            await RepairPartPurchaseLog.query().insert({
              part_id,
              old_price: oldPrice,
              new_price: newPrice,
              old_quantity: oldQuantity,
              new_quantity: newQuantity,
              delivery_note,
            });

            await RepairParts.query().where('part_id', part_id).update({
              price: newPrice,
            });
          }

          if (quantity === purchase.quantity) {
            await RepairPartPurchase.query()
              .where('purchase_id', purchase_id)
              .update({
                delivery_note,
                price,
                purchase_status: 'recibido',
                received_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              });

            continue;
          }

          await RepairPartPurchase.query()
            .where('purchase_id', purchase_id)
            .update({
              delivery_note,
              price,
              purchase_status: 'recibido',
              quantity,
              received_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            });

          await RepairPartPurchase.query().insert({
            supplier_id,
            part_id,
            quantity: purchase.quantity - quantity,
            price,
            purchase_status: 'pendiente',
            created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          });
        }

        return true;
      }
    );

    if (!receiveDeliveryNoteTransaction)
      throw new Error('Error al recibir la nota de entrega');

    return res.status(200).json({
      ok: true,
      message: 'Nota de envio recibida correctamente',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function cancelOrder(req: Request, res: Response) {
  try {
    const { purchase_id } = req.query as unknown as {
      purchase_id: number;
    };

    const purchase = await RepairPartPurchase.query()
      .where('purchase_id', purchase_id)
      .where('purchase_status', 'pendiente')
      .select('purchase_id')
      .first()
      .castTo<{ purchase_id: number }>();

    if (!purchase) {
      logger.error(`No se encontro la orden de compra: ${purchase_id}`);

      throw new Error(`No se encontro la orden de compra: ${purchase_id}`);
    }

    await RepairPartPurchase.query().where('purchase_id', purchase_id).update({
      purchase_status: 'cancelado',
    });

    return res.status(200).json({
      ok: true,
      message: 'Orden de compra cancelada correctamente',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function receiveInvoice(req: Request, res: Response) {
  try {
    const { parts, invoice, invoice_type } = req.body as unknown as {
      parts: {
        purchase_id: number;
        supplier_id: number;
        part_id: number;
        quantity: number;
        price: number;
      }[];
      invoice: string;
      invoice_type: string;
    };

    const uniqueSuppliers = new Set(parts.map((p) => p.supplier_id));

    if (uniqueSuppliers.size > 1)
      throw new Error(
        'Todas las repuestos deben pertenecer al mismo proveedor.'
      );

    const receiveInvoiceTransaction = await transaction(
      RepairParts,
      RepairPartPurchase,
      RepairPartPurchaseLog,
      async (RepairParts, RepairPartPurchase, RepairPartPurchaseLog) => {
        for (const part of parts) {
          const { purchase_id, supplier_id, part_id, quantity, price } = part;

          const purchase = await RepairPartPurchase.query()
            .where('purchase_id', purchase_id)
            .where('supplier_id', supplier_id)
            .where('part_id', part_id)
            .where('purchase_status', 'pendiente')
            .first()
            .select('quantity')
            .castTo<{ quantity: number }>();

          if (!purchase) {
            logger.error(`No se encontro la orden de compra: ${purchase_id}`);

            throw new Error(
              `No se encontro la orden de compra: ${purchase_id}`
            );
          }

          if (quantity > purchase.quantity) {
            logger.error(
              `La cantidad recibida es mayor a la cantidad de la orden de compra: ${purchase_id}`
            );

            throw new Error(
              `La cantidad recibida es mayor a la cantidad de la orden de compra: ${purchase_id}`
            );
          }

          const repairPart = await RepairParts.query()
            .where('part_id', part_id)
            .select('part_id', 'quantity', 'price')
            .first()
            .castTo<{ part_id: number; quantity: number; price: number }>();

          if (!repairPart) {
            logger.error(`No se encontro el repuesto: ${part_id}`);

            throw new Error(`No se encontro el repuesto: ${part_id}`);
          }

          const oldQuantity = Number(repairPart.quantity);
          const oldPrice = Number(repairPart.price);

          const newQuantity = oldQuantity + Number(quantity);
          const newPrice =
            parseFloat(price.toFixed(2)) > oldPrice
              ? parseFloat(price.toFixed(2))
              : Number(repairPart.price.toFixed(2));

          await RepairParts.query().where('part_id', part_id).update({
            quantity: newQuantity,
            price: newPrice,
          });

          if (newPrice !== oldPrice) {
            await RepairPartPurchaseLog.query().insert({
              part_id,
              old_price: oldPrice,
              new_price: newPrice,
              old_quantity: oldQuantity,
              new_quantity: newQuantity,
              delivery_note: invoice,
            });

            await RepairParts.query().where('part_id', part_id).update({
              price: newPrice,
            });
          }

          if (quantity === purchase.quantity) {
            await RepairPartPurchase.query()
              .where('purchase_id', purchase_id)
              .update({
                invoice_number: invoice,
                invoice_type: invoice_type,
                price,
                purchase_status: 'recibido',
                received_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              });

            continue;
          }

          await RepairPartPurchase.query()
            .where('purchase_id', purchase_id)
            .update({
              invoice_number: invoice,
              invoice_type: invoice_type,
              price,
              purchase_status: 'recibido',
              quantity,
              received_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            });

          await RepairPartPurchase.query().insert({
            supplier_id,
            part_id,
            quantity: purchase.quantity - quantity,
            price,
            purchase_status: 'pendiente',
            created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          });
        }

        return true;
      }
    );

    if (!receiveInvoiceTransaction)
      throw new Error('Error al recibir la factura');

    return res.status(200).json({
      ok: true,
      message: 'factura recibida correctamente',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function deliveryNoteWithoutInvoice(req: Request, res: Response) {
  try {
    const page = Number(req.query.page ?? 1);
    const limit = Number(req.query.limit ?? 10);
    const delivery_note = req.query.delivery_note as string;

    const deliveryNotes = (await RepairPartPurchase.query()
      .where('purchase_status', 'recibido')
      .andWhere((qb) => {
        qb.whereNull('invoice_number').orWhere('invoice_number', '');
      })
      .modify((qb) => {
        if (delivery_note) {
          qb.where('delivery_note', 'like', `%${delivery_note}%`);
        }
      })
      .select('delivery_note', 'received_at')
      .max('received_at')
      .groupBy('delivery_note')
      .orderBy('received_at', 'asc')
      .page(page - 1, limit)) as unknown as {
      total: number;
      results: {
        delivery_note: string;
        received_at: string;
      }[];
    };

    return res.status(200).json({
      ok: true,
      message: `Notas de envio sin factura obtenidas ${deliveryNotes.results.length}`,
      data: deliveryNotes.results,
      pagination: {
        total: deliveryNotes.total,
        page,
        limit,
        total_pages: Math.ceil(deliveryNotes.total / limit),
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function addInvoiceToDeliveryNote(req: Request, res: Response) {
  try {
    const { invoice, invoice_type, delivery_note } = req.body as unknown as {
      invoice: string;
      invoice_type: string;
      delivery_note: string;
    };

    const deliveryNote = await RepairPartPurchase.query()
      .where('delivery_note', delivery_note)
      .andWhere('purchase_status', 'recibido')
      .andWhere((qb) => {
        qb.whereNull('invoice_number').orWhere('invoice_number', '');
      })
      .first();

    if (!deliveryNote) {
      return res.status(400).json({
        ok: false,
        message: 'La nota de envio no existe',
      });
    }

    await RepairPartPurchase.query()
      .where('delivery_note', delivery_note)
      .andWhere('purchase_status', 'recibido')
      .andWhere((qb) => {
        qb.whereNull('invoice_number').orWhere('invoice_number', '');
      })
      .update({
        invoice_number: invoice,
        invoice_type: invoice_type,
      });

    return res.status(200).json({
      ok: true,
      message: 'Factura agregada correctamente',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function mechanics(req: Request, res: Response) {
  try {
    const page = Number(req.query.page ?? 1);
    const limit = Number(req.query.limit ?? 10);
    const employee_code = req.query.employee_code as string;

    const mechanics = (await Employee.query()
      .modify((qb) => {
        if (employee_code) {
          qb.where('employee_id', 'like', `%${employee_code}%`);
        }
      })
      .where('status', 1)
      .select([
        {
          employee_code: 'employee_id',
        },
        { employee_name: 'first_name' },
      ])
      .page(page - 1, limit)) as unknown as {
      total: number;
      results: {
        employee_code: string;
        employee_name: string;
      }[];
    };

    return res.status(200).json({
      ok: true,
      message: `Mecanicos obtenidos ${mechanics.results.length}`,
      data: mechanics.results,
      pagination: {
        total: mechanics.total,
        page,
        limit,
        total_pages: Math.ceil(mechanics.total / limit),
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function machines(req: Request, res: Response) {
  try {
    const page = Number(req.query.page ?? 1);
    const limit = Number(req.query.limit ?? 10);
    const machine_code = req.query.machine_code as string;

    const machines = (await RepairPartsMachine.query()
      .modify((qb) => {
        if (machine_code) {
          qb.where('code_machine', 'like', `%${machine_code}%`);
        }
      })
      .select('id', 'code_machine', 'model_machine', 'type_machine')
      .page(page - 1, limit)) as unknown as {
      total: number;
      results: {
        id: number;
        code_machine: string;
        model_machine: string;
        type_machine: string;
      }[];
    };

    return res.status(200).json({
      ok: true,
      message: `Maquinas obtenidas ${machines.results.length}`,
      data: machines.results,
      pagination: {
        total: machines.total,
        page,
        limit,
        total_pages: Math.ceil(machines.total / limit),
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function part(req: Request, res: Response) {
  try {
    const { tokenInfo } = req.body as unknown as {
      tokenInfo: { area_id: number };
    };
    const { area_id } = tokenInfo;
    const barcode = req.query.barcode as string;

    const part = await RepairParts.query()
      .where('building_id', area_id)
      .where('part_status', 'active')
      .where('is_active', true)
      .where('barcode', barcode)
      .select([{ part_id: 'part_id' }, 'part_code', 'part_name', 'quantity'])
      .first()
      .castTo<{
        part_id: number;
        part_code: string;
        part_name: string;
        quantity: number;
      }>();

    if (!part) {
      return res.status(404).json({
        ok: false,
        message: 'No se encontro la pieza',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Pieza obtenida',
      data: part,
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function createRequest(req: Request, res: Response) {
  try {
    const { tokenInfo, parts, mechanic_id, machine_id, client } =
      req.body as unknown as {
        tokenInfo: { area_id: number; employee_id: number };
        parts: { part_id: number; quantity: number }[];
        mechanic_id: number;
        machine_id: number;
        client: string;
      };
    const { area_id, employee_id } = tokenInfo;

    if (parts.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'No se encontraron repuestos',
      });
    }

    const machine = await RepairPartsMachine.query()
      .where('id', machine_id)
      .select(
        'code_machine',
        'machine',
        'model_machine',
        'serie_machine',
        'type_machine'
      )
      .first()
      .castTo<{
        code_machine: string;
        machine: string;
        model_machine: string;
        serie_machine: string;
        type_machine: string;
      }>();

    if (!machine) {
      logger.error(`No se encontro la maquina, ${machine_id}`);
      throw new Error('No se encontro la maquina');
    }

    const downloadTransaction = await transaction(
      RepairPartRequests,
      RepairPartRequestItems,
      RepairParts,
      async (RepairPartRequests, RepairPartRequestItems, RepairParts) => {
        for (const part of parts) {
          const partExist = await RepairParts.query()
            .where('building_id', area_id)
            .where('part_id', part.part_id)
            .where('part_status', 'active')
            .where('is_active', true)
            .select('quantity')
            .first()
            .castTo<{ quantity: number }>();

          if (!partExist) {
            logger.error(
              `No se encontro el repuesto, ${part.part_id} del area ${area_id}`
            );

            throw new Error('No se encontro el repuesto');
          }

          if (partExist.quantity < part.quantity) {
            logger.error(
              `No hay suficiente repuesto, ${part.part_id} del area ${area_id}`
            );

            throw new Error('No hay suficiente repuesto');
          }
        }

        const newRequest = await RepairPartRequests.query()
          .insert({
            client,
            created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            machine: machine.machine,
            machine_type: machine.type_machine,
            mechanic_id,
            model: machine.model_machine,
            request_number: 0,
            serial: machine.serie_machine,
            supervisor_id: mechanic_id,
            user_id: employee_id,
          })
          .castTo<{ id: number }>();

        for (const part of parts) {
          await RepairPartRequestItems.query().insert({
            request_id: newRequest.id,
            part_id: part.part_id,
            quantity: part.quantity,
            request_item_status: 'entregado',
          });

          await RepairParts.query()
            .where('building_id', area_id)
            .where('part_id', part.part_id)
            .where('part_status', 'active')
            .where('is_active', true)
            .decrement('quantity', part.quantity);
        }

        return true;
      }
    );

    if (!downloadTransaction) {
      return res.status(400).json({
        ok: false,
        message: 'No se pudo crear la solicitud',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Solicitud creada correctamente',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function requests(req: Request, res: Response) {
  try {
    const page = Number(req.query.page ?? 1);
    const limit = Number(req.query.limit ?? 10);
    const requestNumber = req.query.request_number as string;
    const mechanic_code = req.query.mechanic_code as string;

    const requests = (await RepairPartRequests.query()
      .join(
        'employees',
        'employees.employee_id',
        'repair_part_requests.mechanic_id'
      )
      .select(
        'repair_part_requests.request_id',
        'repair_part_requests.created_at',
        raw("CONCAT(employees.first_name, ' ', employees.last_name)").as(
          'mechanic_name'
        )
      )
      .orderBy('repair_part_requests.created_at', 'desc')
      .page(page - 1, limit)
      .modify((qb) => {
        if (requestNumber) {
          qb.where(
            'repair_part_requests.request_id',
            'like',
            `%${requestNumber}%`
          );
        }

        if (mechanic_code) {
          qb.where(
            'repair_part_requests.mechanic_id',
            'like',
            `%${mechanic_code}%`
          );
        }
      })) as unknown as {
      total: number;
      results: {
        request_id: number;
        created_at: string;
        mechanic_name: string;
      }[];
    };

    return res.status(200).json({
      ok: true,
      message: `Total de requisiciones encontradas ${requests.results.length}`,
      data: requests.results,
      pagination: {
        total: requests.total,
        page,
        limit,
        total_pages: Math.ceil(requests.total / limit),
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function returnPart(req: Request, res: Response) {
  try {
    const { tokenInfo } = req.body as unknown as {
      tokenInfo: {
        area_id: number;
      };
    };
    const { area_id } = tokenInfo;

    const { item_id, part_id, quantity } = req.body as unknown as {
      item_id: number;
      part_id: number;
      quantity: number;
    };

    if (isNaN(item_id) || item_id <= 0) {
      return res.status(400).json({
        ok: false,
        message: 'El item de la requisición no es valido',
      });
    }

    if (isNaN(part_id) || part_id <= 0 || isNaN(quantity) || quantity <= 0) {
      return res.status(400).json({
        ok: false,
        message: 'Todos los campos son obligatorios',
      });
    }

    const itemExists = await RepairPartRequestItems.query()
      .where('item_id', item_id)
      .where('request_item_status', 'entregado')
      .select('quantity')
      .first()
      .castTo<{ quantity: number }>();

    if (!itemExists) {
      return res.status(400).json({
        ok: false,
        message: 'El item de la requisición no existe',
      });
    }

    if (itemExists.quantity < quantity) {
      return res.status(400).json({
        ok: false,
        message: 'La cantidad a devolver es mayor a la cantidad entregada',
      });
    }

    const partExists = await RepairParts.query()
      .where('building_id', area_id)
      .where('part_id', part_id)
      .where('part_status', 'active')
      .where('is_active', true)
      .select('quantity')
      .first()
      .castTo<{ quantity: number }>();

    if (!partExists) {
      return res.status(400).json({
        ok: false,
        message: 'El repuesto no existe',
      });
    }

    const newQuantityItem = itemExists.quantity - quantity;
    const newQuantityPart = partExists.quantity + quantity;

    const returnTransaction = await transaction(
      RepairPartRequestItems,
      RepairParts,
      async () => {
        await RepairPartRequestItems.query()
          .where('item_id', item_id)
          .where('request_item_status', 'entregado')
          .update({
            quantity: newQuantityItem,
          });

        await RepairParts.query()
          .where('building_id', area_id)
          .where('part_id', part_id)
          .where('part_status', 'active')
          .where('is_active', true)
          .update({
            quantity: newQuantityPart,
          });

        return true;
      }
    );

    if (!returnTransaction) {
      return res.status(400).json({
        ok: false,
        message: 'Error al devolver el repuesto',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Repuesto devuelto correctamente',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function itemsRequest(req: Request, res: Response) {
  try {
    console.log('itemsRequest');

    const { tokenInfo } = req.body as unknown as {
      tokenInfo: {
        area_id: number;
      };
    };
    const { area_id } = tokenInfo;
    const requestId = Number(req.query.request_id);

    if (isNaN(requestId) || requestId <= 0) {
      return res.status(400).json({
        ok: false,
        message: 'El id de la requisición no es valido',
      });
    }

    const requestExists = await RepairPartRequests.query()
      .where('request_id', requestId)
      .select('request_id')
      .first()
      .castTo<{ request_id: number }>();

    if (!requestExists) {
      return res.status(400).json({
        ok: false,
        message: 'La requisición no existe',
      });
    }

    const items = await RepairPartRequestItems.query()
      .join(
        'repair_parts',
        'repair_parts.part_id',
        'repair_part_request_items.part_id'
      )
      .where('repair_part_request_items.request_id', requestId)
      .where('repair_parts.building_id', area_id)
      .select(
        'repair_part_request_items.item_id',
        'repair_part_request_items.part_id',
        'repair_part_request_items.quantity',
        'repair_parts.part_code',
        'repair_parts.part_name',
        'repair_parts.description'
      )
      .castTo<
        {
          item_id: number;
          part_id: number;
          quantity: number;
          part_code: string;
          part_name: string;
          description: string;
        }[]
      >();

    if (items.length === 0) {
      return res.status(404).json({
        ok: false,
        message: 'No se encontraron items para la requisición',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Items de la requisición',
      data: items,
      total: items.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}
