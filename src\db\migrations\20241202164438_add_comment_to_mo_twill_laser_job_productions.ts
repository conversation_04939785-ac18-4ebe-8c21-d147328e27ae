import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'mo_twill_laser_job_productions',
    (table): void => {
      table.string('comment');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'mo_twill_laser_job_productions',
    (table): void => {
      table.dropColumn('comment');
    }
  );
}
