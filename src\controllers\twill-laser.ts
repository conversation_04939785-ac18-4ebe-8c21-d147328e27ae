import type { Request, Response } from 'express';
import { transaction } from 'objection';

import { MoNumber, MoScans } from '@app/models/tickets.schema';
import {
  MoTwillLaserDecoration,
  MoTwillLaserJob,
  MoTwillLaserJobConsumption,
  MoTwillLaserJobDecoration,
  MoTwillLaserJobType,
  MoTwillLaserVarsityArtJob,
} from '@app/models/twill-laser';
import { sendScanLog } from '@app/services/discord';
import { createScan } from '@app/services/scanning';
import { buildLogger } from '@app/settings';

const logger = buildLogger('rhinestone.controller.ts');

// interface TypeJob {
//   id: number;
//   name: string;
// }

// interface Consumption {
//   quantity: number;
//   width: number;
//   height: number;
// }

// interface Job {
//   type: TypeJob;
//   comment: string;
//   normalColors: number;
//   specialColors: number;
//   consumptions: Consumption[];
// }
// /** @deprecated */
// export async function createOrdersTwillLaser(req: Request, res: Response) {
//   try {
//     const { jobs, orderMain, orderSecondary } = req.body as unknown as {
//       jobs: Job[];
//       orderMain: number;
//       orderSecondary: number;
//     };

//     if (jobs.length === 0)
//       throw new Error('No se puede crear una orden sin trabajos');

//     if (!orderMain) throw new Error('No se puede crear una orden sin ID');

//     const orderExists = await MoTwillLaserJobs.query()
//       .where('mo_id', orderMain)
//       .where('is_active', 1)
//       .first();

//     if (orderExists) throw new Error('La orden ya existe');

//     const createOrdersTwill = await transaction(
//       MoTwillLaserJobs,
//       MoTwillLaserJobConsumptions,
//       async (MoTwillLaserJobs, MoTwillLaserJobConsumptions) => {
//         for (const job of jobs) {
//           const { type, comment, normalColors, specialColors, consumptions } =
//             job;

//           const newJob = (await MoTwillLaserJobs.query().insert({
//             mo_id: orderMain,
//             sub_mo_id: orderSecondary || null,
//             mo_twill_laser_job_type_id: type.id,
//             comment,
//             layers: normalColors,
//             special_layers: specialColors,
//           })) as unknown as { id: number };

//           if (!newJob) throw new Error('No se pudo crear la orden');

//           for (const consumption of consumptions) {
//             const newConsumption =
//               await MoTwillLaserJobConsumptions.query().insert({
//                 mo_twill_laser_job_id: newJob.id,
//                 height: consumption.height,
//                 width: consumption.width,
//                 quantity: consumption.quantity,
//               });

//             if (!newConsumption) throw new Error('No se pudo crear la orden');
//           }
//         }

//         return true;
//       }
//     );

//     if (!createOrdersTwill) throw new Error('No se pudo crear la orden');

//     return res.status(200).json({
//       ok: true,
//       message: 'Orden creada correctamente',
//       total: 1,
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);

//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');

//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }

// interface IType {
//   id: number;
//   name: string;
// }

// interface IDecorations {
//   type: IType;
//   description: string;
// }
// /** @deprecated */
// export async function createMoTwillLaserJob(req: Request, res: Response) {
//   try {
//     const {
//       comment,
//       consumptions,
//       decorations,
//       employeeID,
//       layers,
//       orderID,
//       specialLayers,
//     } = req.body as unknown as {
//       employeeID: number;
//       decorations: IDecorations[];
//       orderID: number;
//       consumptions: Consumption[];
//       comment: string;
//       layers: number;
//       specialLayers: number;
//     };
//     let main: { mo_id: number } | null = null;

//     if (!orderID) throw new Error('Tienes que enviar el ID de la orden');

//     if (layers === 0)
//       throw new Error('No se puede crear un trabajo sin un tipo de trabajo');

//     if (decorations.length === 0)
//       throw new Error('No se puede crear un trabajo sin decoraciones');

//     if (!employeeID) throw new Error('No se puede crear un trabajo sin un ID');

//     const mo = (await MoNumbers.query()
//       .where('mo_id', orderID)
//       .select('mo_id', 'mo_order_parent_mo', 'quantity')
//       .first()) as unknown as {
//       mo_id: number;
//       mo_order_parent_mo: number;
//       quantity: number;
//     };

//     if (!mo) throw new Error('No se encontro la orden');

//     if (mo.mo_order_parent_mo) {
//       main = (await MoNumbers.query()
//         .where('mo_id', mo.mo_order_parent_mo)
//         .first()) as unknown as { mo_id: number };
//     }

//     const artistConsumptions = (await MoTwillLaserArtistConsumptions.query()
//       .join(
//         'mo_twill_laser_artists',
//         'mo_twill_laser_artist_consumptions.twill_laser_artist_id',
//         'mo_twill_laser_varsity_art_jobs.id'
//       )
//       .where('mo_twill_laser_varsity_art_jobs.child_mo_id', orderID)
//       .where('mo_twill_laser_varsity_art_jobs.is_active', 1)
//       .where('mo_twill_laser_artist_consumptions.is_active', 1)
//       .select([
//         {
//           consumptionID: 'mo_twill_laser_artist_consumptions.id',
//         },
//       ])) as unknown as {
//       consumptionID: number;
//     }[];

//     const createMoTwillLaserJob = await transaction(
//       MoTwillLaserJobs,
//       MoTwillLaserArtistConsumptions,
//       MoTwillLaserDecorations,
//       async (
//         MoTwillLaserJobs,
//         MoTwillLaserArtistConsumptions,
//         MoTwillLaserDecorations
//       ) => {
//         const newJob = (await MoTwillLaserJobs.query().insert({
//           mo_id: orderID,
//           comment: comment,
//           quantity: mo.quantity,
//           employee_id: employeeID,
//           layers: layers,
//           special_layers: specialLayers || 0,
//         })) as unknown as { id: number };

//         if (!newJob) {
//           logger.error(`No se pudo crear el trabajo con la mo ${orderID}`);
//           throw new Error('No se pudo crear el trabajo');
//         }

//         if (consumptions.length > 0) {
//           for (const consumption of consumptions) {
//             const newConsumption =
//               await MoTwillLaserArtistConsumptions.query().insert({
//                 twill_laser_artist_id: null,
//                 mo_twill_laser_job_id: newJob.id,
//                 height: consumption.height,
//                 width: consumption.width,
//                 quantity: consumption.quantity,
//               });

//             if (!newConsumption) {
//               logger.error('No se pudo crear el consumo');
//               throw new Error('No se pudo crear el consumo');
//             }
//           }
//         }

//         if (artistConsumptions.length > 0) {
//           for (const consumption of artistConsumptions) {
//             await MoTwillLaserArtistConsumptions.query()
//               .where('id', consumption.consumptionID)
//               .update({
//                 mo_twill_laser_job_id: newJob.id,
//               });
//           }
//         }

//         for (const decoration of decorations) {
//           const decorationToJob = (await MoTwillLaserDecorations.query().insert(
//             {
//               mo_id: main ? main.mo_id : orderID,
//               child_mo_id: orderID,
//               mo_twill_laser_job_type_id: decoration.type.id,
//               comment: decoration.description,
//             }
//           )) as unknown as { id: number };

//           if (!decorationToJob) {
//             logger.error('No se pudo crear la decoracion');
//             throw new Error('No se pudo crear la decoracion');
//           }
//         }

//         return true;
//       }
//     );

//     if (!createMoTwillLaserJob) {
//       logger.error('No se pudo crear el trabajo');
//       throw new Error('No se pudo crear el trabajo');
//     }

//     return res.status(200).json({
//       ok: true,
//       message: 'Trabajo creado correctamente',
//       total: 1,
//     });
//   } catch (error) {
//     console.log(error);

//     if (error instanceof Error) {
//       logger.error(error.message);

//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');

//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function getOrdersTwillLaser(req: Request, res: Response) {
//   const dataFormated: {
//     id: number;
//     mo_id: number;
//     num: number;
//     required_date: Date;
//     style: string;
//     quantity: number;
//     type: string;
//     normal_colors: number;
//     special_colors: number;
//     consumptions: string;
//   }[] = [];

//   try {
//     const orders = (await MoTwillLaserJobs.query()
//       .join('mo_numbers', 'mo_numbers.mo_id', 'mo_twill_laser_jobs.mo_id')
//       .join(
//         'mo_twill_laser_job_types',
//         'mo_twill_laser_job_types.id',
//         'mo_twill_laser_jobs.mo_twill_laser_job_type_id'
//       )
//       .where('mo_twill_laser_jobs.is_active', 1)
//       .whereNull('mo_twill_laser_jobs.finished_at')
//       .whereNull('mo_twill_laser_jobs.machine_id')
//       .whereNull('mo_twill_laser_jobs.employee_id')
//       .select([
//         'mo_twill_laser_jobs.id',
//         'mo_twill_laser_jobs.mo_id',
//         'mo_twill_laser_jobs.sub_mo_id',
//         'mo_twill_laser_jobs.comment',
//         'mo_numbers.num',
//         'mo_numbers.required_date',
//         'mo_numbers.style',
//         'mo_numbers.quantity',
//         {
//           type: 'mo_twill_laser_job_types.name',
//         },
//         { normal_colors: 'mo_twill_laser_jobs.layers' },
//         { special_colors: 'mo_twill_laser_jobs.special_layers' },
//       ])) as unknown as {
//       id: number;
//       mo_id: number;
//       num: number;
//       required_date: Date;
//       style: string;
//       quantity: number;
//       type: string;
//       normal_colors: number;
//       special_colors: number;
//     }[];

//     if (orders.length === 0) throw new Error('No se encontraron ordenes');

//     for (const order of orders) {
//       const consumptions = (await MoTwillLaserJobConsumptions.query()
//         .where('mo_twill_laser_job_id', order.id)
//         .where('is_active', 1)
//         .select([
//           'mo_twill_laser_job_consumptions.height',
//           'mo_twill_laser_job_consumptions.width',
//           'mo_twill_laser_job_consumptions.quantity',
//         ])) as unknown as { height: number; width: number; quantity: number }[];

//       dataFormated.push({
//         ...order,
//         consumptions: consumptions
//           .map((obj) => `${obj.height} * ${obj.width} / ${obj.quantity}`)
//           .join(', '),
//       });
//     }

//     return res.status(200).json({
//       ok: true,
//       message: 'Ordenes obtenidas',
//       data: dataFormated,
//       total: orders.length,
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);

//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');

//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function getMOTwillLaserJobsByMain(_req: Request, res: Response) {
//   try {
//     const dataFormated: {
//       id: number;
//       mo_id: number;
//       comment: string;
//       finished_at: Date;
//       num: string;
//       required_date: Date;
//       employee: string;
//       style: string;
//       quantity: number;
//       layers: number;
//       special_layers: number;
//       consumptions: string;
//       decorations: string;
//       pieces: string;
//     }[] = [];

//     const jobs = (await MoTwillLaserJobs.query()
//       .join('mo_numbers', 'mo_numbers.mo_id', 'mo_twill_laser_jobs.mo_id')
//       .leftJoin(
//         'employees',
//         'employees.employee_id',
//         'mo_twill_laser_jobs.employee_id'
//       )
//       .where('mo_twill_laser_jobs.is_active', 1)
//       .select([
//         'mo_twill_laser_jobs.id',
//         'mo_twill_laser_jobs.mo_id',
//         'mo_twill_laser_jobs.comment',
//         'mo_twill_laser_jobs.finished_at',
//         'mo_twill_laser_jobs.quantity',
//         { employee: 'employees.first_name' },
//         'mo_numbers.num',
//         'mo_numbers.required_date',
//         'mo_numbers.style',
//         { layers: 'mo_twill_laser_jobs.layers' },
//         { special_layers: 'mo_twill_laser_jobs.special_layers' },
//       ])
//       .orderBy('mo_twill_laser_jobs.id', 'desc')) as unknown as {
//       id: number;
//       mo_id: number;
//       num: string;
//       comment: string;
//       finished_at: Date;
//       quantity: number;
//       employee: string;
//       required_date: Date;
//       style: string;
//       layers: number;
//       special_layers: number;
//     }[];

//     if (jobs.length === 0)
//       throw new Error('No se encontraron trabajos activos');

//     for (const job of jobs) {
//       const consumptions = (await MoTwillLaserArtistConsumptions.query()
//         .where('mo_twill_laser_job_id', job.id)
//         .where('is_active', 1)
//         .select([
//           'mo_twill_laser_artist_consumptions.height',
//           'mo_twill_laser_artist_consumptions.width',
//           'mo_twill_laser_artist_consumptions.quantity',
//         ])) as unknown as { height: number; width: number; quantity: number }[];

//       const productions = (await MoTwillLaserJobProductions.query()
//         .where('mo_twill_laser_job_id', job.id)
//         .where('is_active', 1)
//         .where('is_repo', 0)
//         .select('quantity')) as unknown as { quantity: number }[];

//       const decorations = (await MoTwillLaserDecorations.query()
//         .join(
//           'mo_twill_laser_job_types',
//           'mo_twill_laser_job_types.id',
//           'mo_twill_laser_decorations.mo_twill_laser_job_type_id'
//         )
//         .where('mo_twill_laser_decorations.child_mo_id ', job.mo_id)
//         .where('mo_twill_laser_decorations.is_active', 1)
//         .select([
//           'mo_twill_laser_decorations.comment',
//           'mo_twill_laser_job_types.name',
//         ])) as unknown as { comment: string; name: string }[];

//       const pieces = productions.reduce(
//         (acc, obj): number => acc + obj.quantity,
//         0
//       );

//       dataFormated.push({
//         ...job,
//         consumptions: consumptions
//           .map(
//             (obj): string => `${obj.height} * ${obj.width} / ${obj.quantity}`
//           )
//           .join(', '),
//         decorations: decorations
//           .map((obj): string => `${obj.name} - ${obj.comment}`)
//           .join(', '),
//         pieces: `${pieces} / ${job.quantity}`,
//         employee: job.employee,
//       });
//     }

//     return res.status(200).json({
//       ok: true,
//       message: 'Ordenes obtenidas',
//       data: dataFormated,
//       total: jobs.length,
//     });
//   } catch (error) {
//     console.log(error);

//     if (error instanceof Error) {
//       logger.error(error.message);

//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');

//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function deleteTwillLaserJob(req: Request, res: Response) {
//   try {
//     const { id } = req.params as unknown as { id: number };

//     if (!id) throw new Error('No se puede borrar un trabajo sin ID');

//     const deleteOrder = await MoTwillLaserJobs.query()
//       .update({ is_active: 0 })
//       .where({ id });

//     if (!deleteOrder) throw new Error('No se pudo borrar el trabajo');

//     return res.status(200).json({
//       ok: true,
//       message: 'Trabajo borrado',
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);

//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');

//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function getTwillLaserJobConsumptions(
//   req: Request,
//   res: Response
// ) {
//   try {
//     const { id } = req.params as unknown as { id: number };

//     if (!id)
//       throw new Error('No se puede obtener los consumos sin ID de trabajo');

//     const consumptions = await MoTwillLaserJobConsumptions.query()
//       .where('mo_twill_laser_job_id', id)
//       .where('is_active', 1)
//       .select([
//         'mo_twill_laser_job_consumptions.id',
//         'mo_twill_laser_job_consumptions.height',
//         'mo_twill_laser_job_consumptions.width',
//         'mo_twill_laser_job_consumptions.quantity',
//       ]);

//     if (consumptions.length === 0)
//       throw new Error('No se encontraron consumos');

//     return res.status(200).json({
//       ok: true,
//       message: 'Consumos obtenidos',
//       data: consumptions,
//       total: consumptions.length,
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);

//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');

//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function deleteJobTwillLaserConsumption(
//   req: Request,
//   res: Response
// ) {
//   try {
//     const { id } = req.params as unknown as { id: number };

//     if (!id) throw new Error('No se puede borrar un trabajo sin ID');

//     const deleteJob = await MoTwillLaserJobConsumptions.query()
//       .update({ is_active: 0 })
//       .where({ id });

//     if (!deleteJob) throw new Error('No se pudo borrar el trabajo');

//     return res.status(200).json({
//       ok: true,
//       message: 'Trabajo borrado',
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);

//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');

//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function completeTwillLaserJob(req: Request, res: Response) {
//   try {
//     const { id } = req.params as unknown as { id: number };
//     const { employee, machine, quantity } = req.body as unknown as {
//       employee: string;
//       machine: string;
//       quantity: number;
//     };

//     if (!id || !employee || !machine)
//       throw new Error(
//         'No se puede completar un trabajo sin ID, empleado o maquina'
//       );

//     const job = (await MoTwillLaserJobs.query()
//       .where({ id })
//       .where('is_active', 1)
//       .select(['id', 'quantity'])) as unknown as {
//       id: number;
//       quantity: number;
//     };

//     if (!job) throw new Error('No se encontro el trabajo');

//     if (quantity && job.quantity !== quantity) {
//       const completeJob = await MoTwillLaserJobs.query()
//         .update({
//           finished_at: new Date(),
//           machine_id: machine,
//           employee_id: employee,
//           quantity: quantity,
//         })
//         .where({ id })
//         .where('is_active', 1)
//         .where('finished_at', null);

//       if (!completeJob) throw new Error('No se pudo completar el trabajo');

//       return res.status(200).json({
//         ok: true,
//         message: 'Trabajo completado',
//       });
//     }

//     const completeJob = await MoTwillLaserJobs.query()
//       .update({
//         finished_at: new Date(),
//         machine_id: machine,
//         employee_id: employee,
//       })
//       .where({ id })
//       .where('is_active', 1)
//       .where('finished_at', null);

//     if (!completeJob) throw new Error('No se pudo completar el trabajo');

//     return res.status(200).json({
//       ok: true,
//       message: 'Trabajo completado',
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);

//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');

//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function editMoTwillLaserJobById(req: Request, res: Response) {
//   try {
//     const { id } = req.params as unknown as { id: number };

//     const { layers, layersSpecials, comment } = req.body as unknown as {
//       layers: number;
//       layersSpecials: number;
//       comment: string;
//     };

//     if (!id) throw new Error('No se puede editar un trabajo sin ID');

//     const job = (await MoTwillLaserJobs.query()
//       .where({ id })
//       .where('is_active', 1)
//       .andWhere('finished_at', null)
//       .select(['id', 'layers', 'special_layers', 'comment'])
//       .first()) as unknown as {
//       id: number;
//       layers: number;
//       special_layers: number;
//       comment: string;
//     };

//     if (!job) throw new Error('No se encontro el trabajo');

//     const editJob = await MoTwillLaserJobs.query()
//       .update({
//         layers: layers ? layers : job.layers,
//         special_layers: layersSpecials ? layersSpecials : job.special_layers,
//         comment: comment ? comment : job.comment,
//       })
//       .where({ id })
//       .where('is_active', 1);

//     if (!editJob) throw new Error('No se pudo editar el consumo');

//     return res.status(200).json({
//       ok: true,
//       message: 'Trabajo editado',
//       total: 1,
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);

//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');

//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function editTwillLaserConsumptionById(
//   req: Request,
//   res: Response
// ) {
//   try {
//     const { id } = req.params as unknown as { id: number };

//     const { height, width, quantity } = req.body as unknown as {
//       height: number;
//       width: number;
//       quantity: number;
//     };

//     if (!id) throw new Error('No se puede editar un consumo sin ID');

//     const consumption = (await MoTwillLaserJobConsumptions.query()
//       .where({ id })
//       .where('is_active', 1)
//       .select(['id', 'height', 'width', 'quantity'])
//       .first()) as unknown as {
//       id: number;
//       height: number;
//       width: number;
//       quantity: number;
//     };

//     if (!consumption) throw new Error('No se encontro el consumo');

//     const editConsumption = await MoTwillLaserJobConsumptions.query()
//       .update({
//         height: height ? height : consumption.height,
//         width: width ? width : consumption.width,
//         quantity: quantity ? quantity : consumption.quantity,
//       })
//       .where({ id })
//       .where('is_active', 1);

//     if (!editConsumption) throw new Error('No se pudo editar el consumo');

//     return res.status(200).json({
//       ok: true,
//       message: 'Consumo editado',
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);

//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');

//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function getReportsTwillLaserConsumptions(
//   req: Request,
//   res: Response
// ) {
//   try {
//     const { id: machineID } = req.params as unknown as { id: number };

//     if (!machineID)
//       throw new Error(
//         'No se puede obtener la informacion de la maquina sin ID'
//       );

//     const today = dayjs(new Date().toISOString().split('T')[0]).format(
//       'YYYY-MM-DD'
//     );

//     const totalOrders = await getOrdersByMachine({ machineID, date: today });

//     const totalJobs = await getJobsByOrders(totalOrders);

//     const ordersCompleted = await getOrdersCompletedByMachine({
//       machineID,
//       date: today,
//     });

//     const totalConsumptions: number = ordersCompleted.reduce(
//       (acc, curr) => acc + curr.consumptions,
//       0
//     );

//     return res.status(200).json({
//       ok: true,
//       message: 'Consumos encontrados',
//       data: {
//         totalOrders: totalOrders.length,
//         totalJobs: totalJobs.length,
//         totalConsumptions: totalConsumptions,
//         ordersCompleted: ordersCompleted.map((order) => {
//           return {
//             id: order.id,
//             num: order.num,
//             style: order.style,
//             quantity: order.quantity,
//             required_date: order.required_date,
//           };
//         }),
//       },
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);

//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');

//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }

export async function getOrderInfo(req: Request, res: Response) {
  try {
    const { id } = req.body as unknown as { id: string };

    if (!id) throw new Error('No se puede obtener la informacion de la orden');

    const barcode = id.includes('-') ? id.replace('-', '/') : id;

    const mo = (await MoNumber.query()
      .whereNotIn('mo_status', ['Void', 'Cancelled', 'Hold', 'Complete'])
      .where('company_code', 3)
      .where('num', 'like', `${barcode}%`)
      .select([
        { id: 'mo_id' },
        'style',
        'quantity',
        { order: 'num' },
        { exportDate: 'required_date' },
        { parentMoID: 'parent_mo_id' },
        { isChild: 'is_child' },
      ])
      .first()) as unknown as {
      id: number;
      exportDate: string;
      order: string;
      quantity: number;
      style: string;
      parentMoID: number;
      isChild: number;
    };

    if (!mo) throw new Error('No se encontro la orden');
    if (!mo.parentMoID && mo.isChild === 1) {
      const jobExists = (await MoTwillLaserVarsityArtJob.query()
        .innerJoin(
          'mo_numbers',
          'mo_numbers.mo_id',
          'mo_twill_laser_varsity_art_jobs.mo_id'
        )
        .leftJoin(
          'employees',
          'employees.employee_id',
          'mo_twill_laser_varsity_art_jobs.employee_id'
        )
        .where('mo_twill_laser_varsity_art_jobs.child_mo_id', mo.id)
        .where('mo_twill_laser_varsity_art_jobs.is_active', 1)
        .whereNull('mo_twill_laser_varsity_art_jobs.completed_at')
        .select([
          'mo_twill_laser_varsity_art_jobs.id',
          'mo_twill_laser_varsity_art_jobs.fq',
          'employees.first_name',
          'mo_numbers.parent_mo_id',
        ])
        .first()) as unknown as {
        id: number;
        fq: boolean;
        first_name: string;
        parent_mo_id: number;
      };

      if (jobExists) {
        if (!jobExists.parent_mo_id) {
          await sendScanLog(
            'Orden sin voucher padre',
            `La orden ${mo.id} no tiene voucher padre, no se puede pasar de FQ a Prodcuccion`
          );

          return res.status(400).json({
            ok: false,
            message: 'La orden no tiene voucher padre, no puedes asignarla',
          });
        }

        if (jobExists.fq) {
          await MoTwillLaserVarsityArtJob.query()
            .patch({ comment: `No se encontro el voucher padre` })
            .where('id', jobExists.id);

          return res.status(400).json({
            ok: false,
            message:
              'La orden ya fue asignada a FQ, pero se actualizo el comentario',
          });
        }

        return res.status(400).json({
          ok: false,
          message: 'La orden ya fue asignada',
        });
      }

      await MoTwillLaserVarsityArtJob.query().insert({
        child_mo_id: mo.id,
        comment: `No se encontro el voucher padre`,
        fq: 1,
      });

      await sendScanLog(
        'Orden sin voucher padre',
        `La orden ${mo.id} no tiene voucher padre, se asigno a FQ`
      );

      return res.status(400).json({
        ok: false,
        message: 'No se encontro el voucher padre de la orden y se asigno a FQ',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Orden encontrada',
      data: mo,
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

interface Order {
  id: number;
  exportDate: string;
  order: string;
  quantity: number;
  style: string;
}

interface AssignOrdersToEmployeeReq {
  orders: Order[];
  comment: string;
  priority: boolean;
  fq: boolean;
  assignee: number;
}

export async function assignOrdersToEmployee(req: Request, res: Response) {
  try {
    const { orders, comment, priority, fq, assignee } =
      req.body as unknown as AssignOrdersToEmployeeReq;

    if (!assignee)
      throw new Error('No se puede asignar una orden sin un artista');

    if (fq && assignee !== 1)
      throw new Error('No se puede asignar un FQ a un Artista');

    if (!orders || orders.length === 0)
      throw new Error(
        'Las ordenes no pueden ser vacias, necesita almenos una orden'
      );

    const ordersReversed = orders.reverse();
    const ordersToAssign = await transaction(
      MoTwillLaserVarsityArtJob,
      MoScans,
      async (MoTwillLaserVarsityArtJobs, MoScans) => {
        for (const order of ordersReversed) {
          const scanIn = (await MoScans.query()
            .where('mo_id', order.id)
            .whereNull('removed_at')
            .whereNull('sew')
            .where('work_area_id', 39)
            .where('work_area_group_id', 647)
            .select([{ id: 'scan_id' }])
            .first()) as unknown as {
            id: number;
          };

          if (!scanIn)
            throw new Error(`La orden ${order.order} no tiene scan de ENTRADA`);

          const orderActive = (await MoTwillLaserVarsityArtJobs.query()
            .leftJoin(
              'employees',
              'employees.employee_id',
              'mo_twill_laser_varsity_art_jobs.employee_id'
            )
            .where('mo_twill_laser_varsity_art_jobs.child_mo_id', order.id)
            .where('mo_twill_laser_varsity_art_jobs.is_active', 1)
            .whereNull('mo_twill_laser_varsity_art_jobs.completed_at')
            .select([
              'mo_twill_laser_varsity_art_jobs.id',
              'mo_twill_laser_varsity_art_jobs.fq',
              'employees.first_name',
            ])
            .first()) as unknown as {
            id: number;
            fq: boolean;
            first_name: string;
          };

          if (orderActive) {
            if (orderActive.fq && fq)
              throw new Error('La orden ya esta asignada como FQ');

            if (!orderActive.fq && fq) {
              await MoTwillLaserVarsityArtJobs.query()
                .patch({
                  fq: true,
                  employee_id: null,
                  comment,
                  is_priority: priority,
                })
                .where('id', orderActive.id);

              continue;
            }

            if (orderActive.fq && assignee !== 1) {
              await MoTwillLaserVarsityArtJobs.query()
                .patch({
                  fq: false,
                  employee_id: assignee,
                  comment,
                  is_priority: priority,
                })
                .where('id', orderActive.id);

              continue;
            }

            throw new Error(
              `La orden ${order.order} ya está asignada a ${orderActive.first_name}`
            );
          } else {
            await MoTwillLaserVarsityArtJobs.query().insert({
              child_mo_id: order.id,
              employee_id: assignee === 1 ? null : assignee,
              comment,
              is_priority: priority,
              fq: assignee === 1 ? true : false,
            });
          }
        }

        return true;
      }
    );

    if (!ordersToAssign) throw new Error('No se pudo asignar la orden');

    return res.status(200).json({
      ok: true,
      message: 'Ordenes asignadas',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

interface OrderWithoutConsumption {
  id: number;
  style: string;
  quantity: number;
  order: string;
  exportDate: Date;
  comment: string;
}

export async function getOrdersWithoutConsumption(req: Request, res: Response) {
  try {
    const { id } = req.params as unknown as { id: number };

    if (!id) throw new Error('El id del artista no puede ser vacío');

    let orders: OrderWithoutConsumption[];

    if (Number(id) === 1) {
      orders = await MoTwillLaserVarsityArtJob.query()
        .innerJoin(
          'mo_numbers',
          'mo_numbers.mo_id',
          'mo_twill_laser_varsity_art_jobs.child_mo_id'
        )
        .whereNull('mo_twill_laser_varsity_art_jobs.employee_id')
        .whereNull('mo_twill_laser_varsity_art_jobs.completed_at')
        .where('mo_twill_laser_varsity_art_jobs.fq', 1)
        .where('mo_twill_laser_varsity_art_jobs.is_active', 1)
        .select([
          { id: 'mo_twill_laser_varsity_art_jobs.id' },
          'mo_numbers.style',
          'mo_numbers.quantity',
          { order: 'mo_numbers.num' },
          { exportDate: 'mo_numbers.required_date' },
          { comment: 'mo_twill_laser_varsity_art_jobs.comment' },
        ])
        .orderBy('mo_twill_laser_varsity_art_jobs.created_at', 'asc')
        .castTo<OrderWithoutConsumption[]>();
    } else {
      orders = await MoTwillLaserVarsityArtJob.query()
        .innerJoin(
          'mo_numbers',
          'mo_numbers.mo_id',
          'mo_twill_laser_varsity_art_jobs.child_mo_id'
        )
        .whereNull('mo_twill_laser_varsity_art_jobs.completed_at')
        .where('mo_twill_laser_varsity_art_jobs.employee_id', id)
        .where('mo_twill_laser_varsity_art_jobs.is_active', 1)
        .select([
          { id: 'mo_twill_laser_varsity_art_jobs.id' },
          'mo_numbers.style',
          'mo_numbers.quantity',
          { order: 'mo_numbers.num' },
          { exportDate: 'mo_numbers.required_date' },
          { comment: 'mo_twill_laser_varsity_art_jobs.comment' },
        ])
        .orderBy('mo_twill_laser_varsity_art_jobs.created_at', 'asc')
        .castTo<OrderWithoutConsumption[]>();
    }

    if (!orders)
      throw new Error('No se encontraron ordenes asignadas al artista');

    return res.status(200).json({
      ok: true,
      message: 'Ordenes encontradas',
      data: orders,
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

interface Consumption {
  height: number;
  width: number;
  quantity: number;
}

interface AssignConsumptionRequest {
  consumptions: Consumption[];
  comment: string;
  typeDecorationID: number;
  typeDecorationName: string;
}

export async function addConsumptionsToOrder(req: Request, res: Response) {
  try {
    const { id } = req.params as unknown as { id: number };
    const { consumptions, comment, typeDecorationID, typeDecorationName } =
      req.body as unknown as AssignConsumptionRequest;

    if (!id)
      throw new Error('No se puede agregar un consumo sin ID de trabajo');

    const artistJob = (await MoTwillLaserVarsityArtJob.query()
      .innerJoin(
        'mo_numbers',
        'mo_twill_laser_varsity_art_jobs.child_mo_id',
        'mo_numbers.mo_id'
      )
      .where('mo_twill_laser_varsity_art_jobs.id', id)
      .where('mo_twill_laser_varsity_art_jobs.is_active', 1)
      .whereNull('mo_twill_laser_varsity_art_jobs.completed_at')
      .select([
        'mo_twill_laser_varsity_art_jobs.id',
        'mo_twill_laser_varsity_art_jobs.child_mo_id',
        'mo_twill_laser_varsity_art_jobs.comment',
        'mo_twill_laser_varsity_art_jobs.employee_id',
        'mo_numbers.quantity',
        'mo_numbers.mo_barcode',
        'mo_numbers.parent_mo_id',
        'mo_numbers.lettering',
        'mo_numbers.style',
      ])
      .first()) as unknown as {
      id: number;
      child_mo_id: number;
      comment: string;
      employee_id: number;
      quantity: number;
      mo_barcode: string;
      parent_mo_id: number;
      lettering: string;
      style: string;
    };

    if (!artistJob)
      throw new Error('No se puede agregar un consumo sin una orden activa');

    if (!artistJob.parent_mo_id) {
      await sendScanLog(
        'Orden sin voucher padre',
        `No se encontro el voucher padre de la orden ${artistJob.id}, no se puede agregar un consumo`
      );

      throw new Error(
        'No se encontro el voucher padre de la orden, no se puede agregar un consumo'
      );
    }

    let mainID = 0;
    let quantityOrder = 0;
    let layers = 0;
    let decorationName = '';

    mainID = artistJob.parent_mo_id;
    quantityOrder = artistJob.quantity;
    layers =
      ((JSON.parse(artistJob.lettering) as string[]) &&
        (JSON.parse(artistJob.lettering) as string[]).filter((layer: string) =>
          layer.startsWith('Color')
        ).length) ||
      0;
    decorationName = artistJob.style;

    const createConsumptions = await transaction(
      MoTwillLaserJob,
      MoTwillLaserJobConsumption,
      MoTwillLaserDecoration,
      MoTwillLaserJobDecoration,
      async (
        MoTwillLaserJobs,
        MoTwillLaserJobConsumptions,
        MoTwillLaserDecorations,
        MoTwillLaserJobDecorations
      ) => {
        const job = await MoTwillLaserJobs.query().insert({
          employee_id: artistJob.employee_id,
          is_repo: false,
          layers: layers.toString(),
          mo_id: mainID,
          mo_twill_laser_varsity_art_job_id: id,
          mo_twill_laser_job_status_id: 1,
          quantity: quantityOrder,
          special_layers: '0',
        });

        if (!job) throw new Error('No se pudo agregar el trabajo al operario');

        const decorationExists = (await MoTwillLaserDecorations.query()
          .where('mo_id', mainID)
          .where('child_mo_id', artistJob.child_mo_id)
          .select('id')
          .first()) as unknown as { id: number };

        if (!decorationExists) {
          const decoration = (await MoTwillLaserDecorations.query().insert({
            comment: typeDecorationName || decorationName,
            mo_id: mainID,
            child_mo_id: artistJob.child_mo_id,
            mo_twill_laser_job_type_id: typeDecorationID || 1,
          })) as unknown as { id: number };

          if (!decoration) throw new Error('No se pudo agregar la decoración');

          const decorationsByJob =
            (await MoTwillLaserJobDecorations.query().insert({
              mo_twill_laser_job_id: job.id,
              mo_twill_laser_decoration_id: decoration.id,
            })) as unknown as { id: number };

          if (!decorationsByJob)
            throw new Error('No se pudo agregar la decoración al trabajo');
        } else {
          const decorationsByJob =
            (await MoTwillLaserJobDecorations.query().insert({
              mo_twill_laser_job_id: job.id,
              mo_twill_laser_decoration_id: decorationExists.id,
            })) as unknown as { id: number };

          if (!decorationsByJob)
            throw new Error('No se pudo agregar la decoración al trabajo');
        }

        for (let i = 0; i < consumptions.length; i++) {
          const consumption = consumptions[i];

          const addConsumption =
            await MoTwillLaserJobConsumptions.query().insert({
              mo_twill_laser_job_id: job.id,
              height: consumption.height,
              width: consumption.width,
              quantity: consumption.quantity,
            });

          if (!addConsumption) throw new Error('No se pudo agregar el consumo');
        }

        return true;
      }
    );

    if (!createConsumptions) throw new Error('No se pudo agregar el consumo');

    if (comment !== artistJob.comment) {
      await MoTwillLaserVarsityArtJob.query().update({ comment }).where({ id });
    }

    await MoTwillLaserVarsityArtJob.query()
      .update({ completed_at: new Date() })
      .where({
        id,
      });

    await createScan({
      mo_id: null,
      mo_barcode: artistJob.mo_barcode,
      group_barcode: 'MEGP00647',
      quantity_reported: artistJob.quantity,
      employee_id: artistJob.employee_id,
      type_action: 'FINISH',
      affected_units: null,
      work_area_group_id: null,
      work_area_id: null,
      work_area_line_id: null,
      partial_option: null,
      work_ticket_id: null,
      update_customer: null,
      work_voucher_id: null,
    });

    return res.status(200).json({
      ok: true,
      message: 'Consumo agregado y orden completada',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

interface ConsumptionWithId extends Consumption {
  id: number;
}

export async function getInfoOfConsumption(req: Request, res: Response) {
  try {
    const { orderBarcode } = req.body as unknown as { orderBarcode: string };

    if (!orderBarcode) throw new Error('La orden no puede ser vacia');

    const barcode = orderBarcode.includes('-')
      ? orderBarcode.replace('-', '/')
      : orderBarcode;

    const orderInfo = (await MoTwillLaserVarsityArtJob.query()
      .innerJoin(
        'mo_numbers',
        'mo_twill_laser_varsity_art_jobs.child_mo_id',
        'mo_numbers.mo_id'
      )
      .innerJoin(
        'employees',
        'employees.employee_id',
        'mo_twill_laser_varsity_art_jobs.employee_id'
      )
      .where('mo_numbers.num', barcode)
      .where('mo_twill_laser_varsity_art_jobs.is_active', 1)
      .whereNotNull('mo_twill_laser_varsity_art_jobs.completed_at')
      .select([
        'mo_twill_laser_varsity_art_jobs.id',
        { name: 'employees.first_name' },
        'mo_twill_laser_varsity_art_jobs.comment',
        { isPriority: 'mo_twill_laser_varsity_art_jobs.is_priority' },
        'mo_numbers.style',
        'mo_numbers.quantity',
      ])
      .orderBy('mo_twill_laser_varsity_art_jobs.created_at', 'desc')
      .first()) as unknown as {
      id: number;
      name: string;
      comment: string;
      isPriority: boolean;
      style: string;
      quantity: number;
    };

    if (!orderInfo)
      throw new Error(
        'La orden no existe, no tiene artista asignado o fue eliminada'
      );

    const job = (await MoTwillLaserJob.query()
      .where(
        'mo_twill_laser_jobs.mo_twill_laser_varsity_art_job_id',
        orderInfo.id
      )
      .where('mo_twill_laser_jobs.is_active', 1)
      .select([
        'mo_twill_laser_jobs.id',
        'mo_twill_laser_jobs.mo_twill_laser_job_status_id',
      ])
      .first()) as unknown as {
      id: number;
      mo_twill_laser_job_status_id: number;
    };

    if (!job) throw new Error('La orden no tiene consumos');
    if (job.mo_twill_laser_job_status_id === 3)
      throw new Error(
        'El trabajo ya fue tomado por un operador, no puede ser editado'
      );

    const { id, name, comment, isPriority, style, quantity } = orderInfo;

    const consumptions = (await MoTwillLaserJobConsumption.query()
      .where('mo_twill_laser_job_id', job.id)
      .where('is_active', 1)
      .select([
        'id',
        'height',
        'width',
        'quantity',
      ])) as unknown as ConsumptionWithId[];

    if (consumptions.length === 0)
      throw new Error('La orden no tiene consumos');

    return res.status(200).json({
      ok: true,
      message: 'Consumos obtenidos',
      data: {
        id,
        consumptions,
        comment,
        isPriority,
        assignee: name,
        order: orderBarcode,
        style,
        quantity,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function addConsumptionToOrder(req: Request, res: Response) {
  try {
    const { id } = req.params as unknown as { id: number };
    const { consumption } = req.body as unknown as {
      consumption: Consumption;
    };

    if (!id) throw new Error('No se puede agregar un consumo sin ID');

    const artistJob = (await MoTwillLaserVarsityArtJob.query()
      .innerJoin(
        'mo_numbers',
        'mo_twill_laser_varsity_art_jobs.child_mo_id',
        'mo_numbers.mo_id'
      )
      .innerJoin(
        'mo_twill_laser_jobs',
        'mo_twill_laser_varsity_art_jobs.id',
        'mo_twill_laser_jobs.mo_twill_laser_varsity_art_job_id'
      )
      .where('mo_twill_laser_varsity_art_jobs.id', id)
      .where('mo_twill_laser_jobs.is_active', 1)
      .where('mo_twill_laser_varsity_art_jobs.is_active', 1)
      .first()
      .select(['mo_twill_laser_jobs.id'])) as unknown as {
      id: number;
    };

    if (!artistJob) throw new Error('No hay una orden activa con este ID');

    const newConsumption = (await MoTwillLaserJobConsumption.query().insert({
      mo_twill_laser_job_id: artistJob.id,
      height: consumption.height,
      width: consumption.width,
      quantity: consumption.quantity,
    })) as unknown as {
      id: number;
    };

    return res.status(200).json({
      ok: true,
      message: 'Consumo agregado',
      data: {
        id: newConsumption.id,
        height: consumption.height,
        width: consumption.width,
        quantity: consumption.quantity,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function deleteConsumptionToOrder(req: Request, res: Response) {
  try {
    const { id } = req.params as unknown as { id: number };

    if (!id) throw new Error('No se puede eliminar un consumo sin ID');

    await MoTwillLaserJobConsumption.query()
      .update({ is_active: false })
      .where('id', id);

    return res.status(200).json({
      ok: true,
      message: 'Consumo eliminado',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function deleteOrderAssignment(req: Request, res: Response) {
  try {
    const { id } = req.params as unknown as { id: number };

    if (!id) throw new Error('No se puede eliminar una asignación sin ID');

    await MoTwillLaserVarsityArtJob.query()
      .update({ is_active: 0 })
      .where('id', id);

    return res.status(200).json({
      ok: true,
      message: 'Orden eliminada',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function editCommentOfOrder(req: Request, res: Response) {
  try {
    const { id } = req.params as unknown as { id: number };
    const { comment } = req.body as unknown as { comment: string };

    if (!id)
      throw new Error('No se puede eliminar el comentario sin ID de la orden');
    if (comment.length === 0)
      throw new Error('El comentario no puede ser vacío');

    await MoTwillLaserVarsityArtJob.query()
      .update({ comment })
      .where('id', id)
      .where('is_active', 1);

    return res.status(200).json({
      ok: true,
      message: 'Comentario actualizado',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function getJobTypes(_req: Request, res: Response) {
  try {
    const jobTypes = await MoTwillLaserJobType.query().where('is_active', 1);

    if (!jobTypes) throw new Error('No se encontraron tipos de trabajo');

    return res.status(200).json({
      ok: true,
      message: 'Tipos de trabajo encontrados',
      data: jobTypes,
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

// /** @deprecated */
// export async function getOrderWithInfo(req: Request, res: Response) {
//   try {
//     const { id } = req.params as unknown as { id: number };

//     if (!id) throw new Error('No se puede obtener la orden sin ID');

//     const mo = (await MoNumbers.query()
//       .whereNotIn('mo_status', ['Void', 'Cancelled', 'Hold', 'Complete'])
//       .where('mo_id', id)
//       .select([
//         { id: 'mo_id' },
//         { order: 'num' },
//         { export_date: 'required_date' },
//         'quantity',
//         'style',
//       ])
//       .first()) as unknown as {
//       id: number;
//       order: string;
//       export_date: string;
//       quantity: number;
//       style: string;
//     };

//     if (!mo) throw new Error('No se encontro la orden');

//     const job = (await MoTwillLaserJobs.query()
//       .where('mo_id', id)
//       .where('is_active', 1)
//       .select([
//         {
//           id: 'mo_twill_laser_jobs.id',
//         },
//         {
//           layers: 'mo_twill_laser_jobs.layers',
//         },
//         {
//           special_layers: 'mo_twill_laser_jobs.special_layers',
//         },
//         {
//           comment: 'mo_twill_laser_jobs.comment',
//         },
//         {
//           finished_at: 'mo_twill_laser_jobs.finished_at',
//         },
//       ])
//       .first()) as unknown as {
//       id: number;
//       layers: number;
//       special_layers: number;
//       comment: string;
//       finished_at: string;
//     };

//     const consumptions = (await MoTwillLaserJobConsumptions.query()
//       .innerJoin(
//         'mo_twill_laser_artists',
//         'mo_twill_laser_artist_consumptions.twill_laser_artist_id',
//         'mo_twill_laser_varsity_art_jobs.id'
//       )
//       .where('mo_twill_laser_varsity_art_jobs.child_mo_id', id)
//       .where('mo_twill_laser_varsity_art_jobs.is_active', 1)
//       .where('mo_twill_laser_artist_consumptions.is_active', 1)
//       .select([
//         'mo_twill_laser_artist_consumptions.id',
//         'mo_twill_laser_artist_consumptions.height',
//         'mo_twill_laser_artist_consumptions.width',
//         'mo_twill_laser_artist_consumptions.quantity',
//       ])) as unknown as {
//       id: number;
//       height: number;
//       width: number;
//       quantity: number;
//     }[];

//     const assignedDecorations = (await MoTwillLaserDecorations.query()
//       .join(
//         'mo_twill_laser_job_types',
//         'mo_twill_laser_decorations.mo_twill_laser_job_type_id ',
//         'mo_twill_laser_job_types.id'
//       )
//       .where('mo_twill_laser_decorations.child_mo_id', id)
//       .where('mo_twill_laser_decorations.is_active', 1)
//       .where('mo_twill_laser_job_types.is_active', 1)
//       .select([
//         {
//           id: 'mo_twill_laser_decorations.id',
//           description: 'mo_twill_laser_decorations.comment',
//           name: 'mo_twill_laser_job_types.name',
//         },
//       ])) as unknown as {
//       id: number;
//       description: string;
//       name: string;
//     }[];

//     if (job) {
//       const consumptionsOperators = (await MoTwillLaserJobConsumptions.query()
//         .where(
//           'mo_twill_laser_artist_consumptions.mo_twill_laser_job_id ',
//           job.id
//         )
//         .where('mo_twill_laser_artist_consumptions.is_active', 1)
//         .whereNull('mo_twill_laser_artist_consumptions.twill_laser_artist_id')
//         .select([
//           'mo_twill_laser_artist_consumptions.id',
//           'mo_twill_laser_artist_consumptions.height',
//           'mo_twill_laser_artist_consumptions.width',
//           'mo_twill_laser_artist_consumptions.quantity',
//         ])) as unknown as {
//         id: number;
//         height: number;
//         width: number;
//         quantity: number;
//       }[];

//       const productionsDecorations =
//         (await MoTwillLaserJobProductionDecorations.query()
//           .join(
//             'mo_twill_laser_job_productions',
//             'mo_twill_laser_job_production_decorations.mo_twill_laser_job_production_id',
//             'mo_twill_laser_job_productions.id'
//           )
//           .join(
//             'mo_twill_laser_decorations',
//             'mo_twill_laser_job_production_decorations.mo_twill_laser_decoration_id',
//             'mo_twill_laser_decorations.id'
//           )
//           .join(
//             'mo_twill_laser_job_types',
//             'mo_twill_laser_decorations.mo_twill_laser_job_type_id ',
//             'mo_twill_laser_job_types.id'
//           )
//           .join(
//             'employees',
//             'mo_twill_laser_job_productions.employee_id',
//             'employees.employee_id'
//           )
//           .where('mo_twill_laser_job_productions.mo_twill_laser_job_id', job.id)
//           .where('mo_twill_laser_decorations.is_active', 1)
//           .where('mo_twill_laser_job_productions.is_active', 1)
//           .select([
//             'mo_twill_laser_job_production_decorations.id',
//             { decorationID: 'mo_twill_laser_decorations.id' },
//             { productionID: 'mo_twill_laser_job_productions.id' },
//             { decorationDescription: 'mo_twill_laser_decorations.comment' },
//             { decorationName: 'mo_twill_laser_job_types.name' },
//             'mo_twill_laser_job_productions.quantity',
//             { employee: 'employees.first_name' },
//             { isRepo: 'mo_twill_laser_job_productions.is_repo' },
//           ])) as unknown as {
//           id: number;
//           decorationID: number;
//           productionID: number;
//           decorationDescription: string;
//           decorationName: string;
//           quantity: number;
//           employee: string;
//           isRepo: number;
//         }[];

//       const employeeByDecoration: {
//         decorationID: number;
//         decorationName: string;
//         quantity: number;
//         sumPiecesOfProductions: number;
//         sumPiecesOfProductionsRepo: number;
//         employees: string[];
//       }[] = [];

//       for (const decoration of assignedDecorations) {
//         const decorationID = decoration.id;
//         const decorationName = `${decoration.name} - ${decoration.description}`;
//         const quantity = mo.quantity;

//         const employees = productionsDecorations
//           .filter(
//             (productionDecoration) =>
//               productionDecoration.decorationID === decorationID &&
//               !productionDecoration.isRepo
//           )
//           .map((productionDecoration) => productionDecoration.employee)
//           .filter((employee, index, self) => self.indexOf(employee) === index);

//         const sumPiecesOfProductions = productionsDecorations
//           .filter((productionDecoration) => {
//             if (!productionDecoration.isRepo) {
//               return productionDecoration.decorationID === decorationID;
//             }
//           })
//           .reduce((acc, current) => acc + current.quantity, 0);

//         const sumPiecesOfProductionsRepo = productionsDecorations
//           .filter(
//             (productionDecoration) =>
//               productionDecoration.decorationID === decorationID &&
//               productionDecoration.isRepo
//           )
//           .reduce((acc, current) => acc + current.quantity, 0);

//         employeeByDecoration.push({
//           decorationID,
//           decorationName,
//           quantity,
//           sumPiecesOfProductions,
//           sumPiecesOfProductionsRepo,
//           employees,
//         });
//       }

//       return res.status(200).json({
//         ok: true,
//         message: 'Orden encontrada',
//         data: {
//           mo,
//           job: job || null,
//           assignedDecorations: employeeByDecoration || [],
//           consumptions: [...consumptions, ...consumptionsOperators],
//         },
//       });
//     }

//     return res.status(200).json({
//       ok: true,
//       message: 'Orden encontrada',
//       data: {
//         mo,
//         job: job || null,
//         assignedDecorations: assignedDecorations || [],
//         consumptions: consumptions || [],
//       },
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);
//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');
//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function addProductionToJob(
//   req: Request,
//   res: Response
// ): Promise<Response> {
//   try {
//     const { id } = req.params;
//     const { quantity, decorationID, machineID, employeeID } = req.body;
//     console.log(req.body);

//     if (!id) throw new Error('Falta el id de la orden');
//     if (!decorationID) throw new Error('Falta el id de la decoración');
//     if (!machineID) throw new Error('Falta el id de la maquina');
//     if (!employeeID) throw new Error('Falta el id del empleado');

//     if (quantity < 1) throw new Error('La cantidad debe ser mayor a 0');

//     const totalQuantity = (await MoTwillLaserJobs.query()
//       .where('id', id)
//       .select('quantity')
//       .first()) as unknown as {
//       quantity: number;
//     };

//     const productionsDecorations =
//       (await MoTwillLaserJobProductionDecorations.query()
//         .join(
//           'mo_twill_laser_job_productions',
//           'mo_twill_laser_job_production_decorations.mo_twill_laser_job_production_id',
//           'mo_twill_laser_job_productions.id'
//         )
//         .join(
//           'mo_twill_laser_decorations',
//           'mo_twill_laser_job_production_decorations.mo_twill_laser_decoration_id',
//           'mo_twill_laser_decorations.id'
//         )
//         .join(
//           'mo_twill_laser_job_types',
//           'mo_twill_laser_decorations.mo_twill_laser_job_type_id ',
//           'mo_twill_laser_job_types.id'
//         )
//         .join(
//           'employees',
//           'mo_twill_laser_job_productions.employee_id',
//           'employees.employee_id'
//         )
//         .where('mo_twill_laser_job_productions.mo_twill_laser_job_id', id)
//         .where('mo_twill_laser_decorations.is_active', 1)
//         .where('mo_twill_laser_job_productions.is_active', 1)
//         .select([
//           'mo_twill_laser_job_production_decorations.id',
//           { decorationID: 'mo_twill_laser_decorations.id' },
//           { productionID: 'mo_twill_laser_job_productions.id' },
//           { decorationDescription: 'mo_twill_laser_decorations.comment' },
//           { decorationName: 'mo_twill_laser_job_types.name' },
//           'mo_twill_laser_job_productions.quantity',
//           { employee: 'employees.first_name' },
//           { isRepo: 'mo_twill_laser_job_productions.is_repo' },
//         ])) as unknown as {
//         id: number;
//         decorationID: number;
//         productionID: number;
//         decorationDescription: string;
//         decorationName: string;
//         quantity: number;
//         employee: string;
//         isRepo: number;
//       }[];

//     const sumPiecesOfProductions = productionsDecorations
//       .filter(
//         (productionDecoration) =>
//           productionDecoration.decorationID === decorationID &&
//           !productionDecoration.isRepo
//       )
//       .reduce((acc, current) => acc + current.quantity, 0);

//     if (quantity > totalQuantity.quantity - sumPiecesOfProductions)
//       throw new Error('La cantidad supera la cantidad disponible');

//     const production = (await MoTwillLaserJobProductions.query().insert({
//       mo_twill_laser_job_id: id,
//       quantity,
//       machine_id: machineID,
//       employee_id: employeeID,
//       is_repo: false,
//     })) as unknown as { id: number };

//     if (!production) throw new Error('No se pudo registrar la producción');

//     await MoTwillLaserJobProductionDecorations.query().insert({
//       mo_twill_laser_job_production_id: production.id,
//       mo_twill_laser_decoration_id: decorationID,
//     });

//     return res.status(200).json({
//       ok: true,
//       message: 'Producción registrada',
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);
//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');
//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function addRepositionToJob(
//   req: Request,
//   res: Response
// ): Promise<Response> {
//   try {
//     const { id } = req.params;
//     const { quantity, decorationID, machineID, employeeID } = req.body;

//     if (!id) throw new Error('Falta el id de la orden');
//     if (!decorationID) throw new Error('Falta el id de la decoración');
//     if (!machineID) throw new Error('Falta el id de la maquina');
//     if (!employeeID) throw new Error('Falta el id del empleado');

//     if (quantity <= 0) throw new Error('La cantidad debe ser mayor a 0');

//     const reposition = (await MoTwillLaserJobProductions.query().insert({
//       mo_twill_laser_job_id: id,
//       quantity,
//       machine_id: machineID,
//       employee_id: employeeID,
//       is_repo: true,
//     })) as unknown as {
//       id: number;
//     };

//     if (!reposition.id) throw new Error('No se pudo registrar la reposición');

//     await MoTwillLaserJobProductionDecorations.query().insert({
//       mo_twill_laser_job_production_id: reposition.id,
//       mo_twill_laser_decoration_id: decorationID,
//     });

//     return res.status(200).json({
//       ok: true,
//       message: 'Reposición registrada',
//       data: reposition,
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);
//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');
//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function deleteDecoration(
//   req: Request,
//   res: Response
// ): Promise<Response> {
//   try {
//     const { id } = req.params;

//     if (!id) throw new Error('Falta el id de la decoración');

//     await MoTwillLaserDecorations.query()
//       .update({ is_active: 0 })
//       .where('id', id);

//     return res.status(200).json({
//       ok: true,
//       message: 'Decoración eliminada',
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);
//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');
//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function addConsumptionToJob(
//   req: Request,
//   res: Response
// ): Promise<Response> {
//   try {
//     const { id } = req.params;
//     const { height, width, quantity } = req.body as unknown as {
//       height: number;
//       width: number;
//       quantity: number;
//     };

//     if (!id) throw new Error('Falta el id del trabajo');

//     await MoTwillLaserArtistConsumptions.query().insert({
//       twill_laser_artist_id: null,
//       height,
//       width,
//       quantity,
//       mo_twill_laser_job_id: id,
//     });

//     return res.status(200).json({
//       ok: true,
//       message: 'Consumo registrado correctamente',
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);
//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');
//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function addNewDecoration(
//   req: Request,
//   res: Response
// ): Promise<Response> {
//   try {
//     const { id } = req.params;
//     const { typeID, description } = req.body as unknown as {
//       typeID: number;
//       description: string;
//     };
//     let main: { mo_id: number } | null = null;

//     if (!id) throw new Error('Falta el id de la orden');
//     if (!typeID) throw new Error('Falta el tipo de decoración');
//     if (!description) throw new Error('Falta la descripción de la decoración');

//     const searchType = (await MoTwillLaserJobTypes.query()
//       .where('id', typeID)
//       .where('is_active', 1)
//       .first()) as unknown as {
//       id: number;
//     };

//     if (!searchType) throw new Error('El tipo de decoración no existe');

//     const mo = (await MoNumbers.query()
//       .where('mo_id', id)
//       .select('mo_id', 'mo_order_parent_mo', 'quantity')
//       .first()) as unknown as {
//       mo_id: number;
//       mo_order_parent_mo: number;
//       quantity: number;
//     };

//     if (!mo) throw new Error('No se encontro la orden');

//     if (mo.mo_order_parent_mo) {
//       main = (await MoNumbers.query()
//         .where('mo_id', mo.mo_order_parent_mo)
//         .first()) as unknown as { mo_id: number };
//     }

//     await MoTwillLaserDecorations.query().insert({
//       mo_id: main ? main.mo_id : id,
//       child_mo_id: id,
//       mo_twill_laser_job_type_id: typeID,
//       comment: description,
//     });

//     return res.status(200).json({
//       ok: true,
//       message: 'Decoración agregada correctamente',
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);
//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');
//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function productionsByEmployee(
//   req: Request,
//   res: Response
// ): Promise<Response> {
//   try {
//     const { id } = req.params as unknown as { id: number };

//     if (!id) throw new Error('Falta el id del operador');

//     const last24hours = new Date();
//     last24hours.setDate(last24hours.getDate() - 1);

//     const productionsByEmployee = (await MoTwillLaserJobProductions.query()
//       .join(
//         'machines',
//         'machines.machine_id',
//         'mo_twill_laser_job_productions.machine_id'
//       )
//       .join(
//         'mo_twill_laser_jobs',
//         'mo_twill_laser_jobs.id',
//         'mo_twill_laser_job_productions.mo_twill_laser_job_id'
//       )
//       .join('mo_numbers', 'mo_numbers.mo_id', 'mo_twill_laser_jobs.mo_id')
//       .where('mo_twill_laser_jobs.employee_id', id)
//       .where('mo_twill_laser_jobs.created_at', '>', last24hours)
//       .where('machines.status', 1)
//       .where('mo_twill_laser_job_productions.is_active', 1)
//       .select([
//         { productionID: 'mo_twill_laser_job_productions.id' },
//         { machineName: 'machines.machine' },
//         { isRepo: 'mo_twill_laser_job_productions.is_repo' },
//         { quantity: 'mo_twill_laser_job_productions.quantity' },
//         { order: 'mo_numbers.num' },
//         { style: 'mo_numbers.style' },
//         { orderQuantity: 'mo_numbers.quantity' },
//       ])) as unknown as {
//       productionID: number;
//       machineName: string;
//       isRepo: number;
//       quantity: number;
//       order: string;
//       style: string;
//       orderQuantity: number;
//     }[];

//     const productionIDs = productionsByEmployee.map(
//       (production) => production.productionID
//     );

//     const decorationsByProduction =
//       (await MoTwillLaserJobProductionDecorations.query()
//         .join(
//           'mo_twill_laser_decorations',
//           'mo_twill_laser_decorations.id',
//           'mo_twill_laser_job_production_decorations.mo_twill_laser_decoration_id'
//         )
//         .join(
//           'mo_twill_laser_job_types',
//           'mo_twill_laser_job_types.id',
//           'mo_twill_laser_decorations.mo_twill_laser_job_type_id'
//         )
//         .where(
//           'mo_twill_laser_job_production_decorations.mo_twill_laser_job_production_id',
//           'in',
//           productionIDs
//         )
//         .select([
//           { decorationID: 'mo_twill_laser_decorations.id' },
//           {
//             productionID:
//               'mo_twill_laser_job_production_decorations.mo_twill_laser_job_production_id',
//           },
//           { typeName: 'mo_twill_laser_job_types.name' },
//           { decorationName: 'mo_twill_laser_decorations.comment' },
//         ])) as unknown as {
//         decorationID: number;
//         decorationName: string;
//         productionID: number;
//         typeName: string;
//       }[];

//     const dataFormated: {
//       productionID: number;
//       machineName: string;
//       isRepo: number;
//       quantity: number;
//       order: string;
//       style: string;
//       orderQuantity: number;
//       decoration: string;
//     }[] = [];

//     for (const production of productionsByEmployee) {
//       const searchDecoration = decorationsByProduction.find(
//         (decoration) => decoration.productionID === production.productionID
//       );

//       const decoration = `${searchDecoration.typeName}-${searchDecoration.decorationName}`;

//       dataFormated.push({
//         productionID: production.productionID,
//         machineName: production.machineName,
//         isRepo: production.isRepo,
//         quantity: production.quantity,
//         order: production.order,
//         style: production.style,
//         orderQuantity: production.orderQuantity,
//         decoration,
//       });
//     }

//     const totalOrders = dataFormated
//       .map((order) => order.order)
//       .filter((value, index, self) => self.indexOf(value) === index);

//     const totalOrdersQuantity = dataFormated
//       .map((order) => order.quantity)
//       .reduce((a, b) => a + b, 0);

//     return res.status(200).json({
//       ok: true,
//       message: 'Producciones por operador',
//       data: {
//         productions: dataFormated,
//         orders: totalOrders,
//         pieces: totalOrdersQuantity,
//       },
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);
//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');
//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
// /** @deprecated */
// export async function deleteProduction(req: Request, res: Response) {
//   try {
//     const { id } = req.params as unknown as { id: number };

//     if (!id) throw new Error('Falta el id de la producción');

//     const seachProduction = await MoTwillLaserJobProductions.query()
//       .where('id', id)
//       .first();

//     if (!seachProduction) throw new Error('Producción no encontrada');

//     await MoTwillLaserJobProductions.query()
//       .where('id', id)
//       .patch({ is_active: 0 });

//     return res.status(200).json({
//       ok: true,
//       message: 'Producción eliminada',
//     });
//   } catch (error) {
//     if (error instanceof Error) {
//       logger.error(error.message);
//       return res.status(400).json({
//         ok: false,
//         message: error.message,
//       });
//     }

//     logger.error('Error interno del servidor');
//     return res.status(500).json({
//       ok: false,
//       message: 'Error interno del servidor',
//     });
//   }
// }
