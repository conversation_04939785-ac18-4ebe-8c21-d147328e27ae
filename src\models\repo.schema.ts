import { Model } from '@app/db';

export class WorkRepos extends Model {
  static get tableName(): string {
    return 'work_repos';
  }
  mo_id!: number;
  creator_email!: string;
  work_repo_status_id!: number;
  affected_units!: number;
  estimated_unit_equivalent!: number;
  reported_work_area_id!: number;
  work_issue_id!: number;
  work_reason_note!: string;
  found_work_voucher_id!: number;
  found_work_ticket_id!: number;
  customer_fault!: boolean;
  charge_amount!: number;
  estimated_material_cost!: number;
  estimated_labor_cost!: number;
  comments!: string;
  item_comments!: string;
  services_comments!: string;
  material_comments!: string;
  approval_date!: Date;
  approval_user!: string;
  materials_approver_email!: string;
  materials_approve_date!: Date;
  in_production_date!: Date;
  finish_date!: Date;
  repo_type!: string;
  paper_yds!: number;
  last_status_changed!: Date;
  warehouse_materials_preparred!: boolean;
  warehouse_materials_preparred_date!: Date;
  warehouse_materials_given_email!: string;
  created_at!: Date;
  updated_at!: Date;
  printer_use!: string;
  is_customer_charged!: boolean;
  customer_charge_invoice_number!: string;
  customer_charge_invoice_date!: Date;
}
