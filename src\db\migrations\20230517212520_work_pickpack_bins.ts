import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(
    'work_pickpack_bins',
    (table: Knex.TableBuilder) => {
      table.increments('id').unsigned().primary();
      table
        .timestamp('created_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      table
        .timestamp('updated_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

      table.string('name').notNullable().unique();
      table.boolean('is_container').notNullable().defaultTo(false);
      table.boolean('allow_items').notNullable().defaultTo(true);
      table.boolean('is_pickable').notNullable().defaultTo(true);
      table.integer('sort_station_id').unsigned().nullable().unique();
      table.string('order_number_assigned').nullable();
      table.integer('work_pickpack_bin_type_id').unsigned().nullable();
      table.integer('container_bin_location_id').unsigned().nullable();
      table.integer('container_parent_id').unsigned().nullable();
      table.integer('container_top_parent_id').unsigned().nullable();
      table.jsonb('container_tree_array').nullable();
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('work_pickpack_bins');
}
