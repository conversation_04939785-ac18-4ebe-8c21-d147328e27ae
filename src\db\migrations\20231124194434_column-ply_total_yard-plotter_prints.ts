import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'plotter_prints',
    (table: Knex.TableBuilder) => {
      table.decimal('plys_fabric_yards', 10, 4);
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'plotter_prints',
    (table: Knex.TableBuilder) => {
      table.dropColumn('plys_fabric_yards');
    }
  );
}
