import type { NextFunction, Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import type { ZodIssue, z } from 'zod';
import { ZodError } from 'zod';

export function validateData<TSchema extends z.ZodTypeAny>(
  schema: TSchema
): (
  req: Request<unknown, unknown, z.infer<TSchema>>,
  res: Response,
  next: NextFunction
) => void {
  return (
    req: Request<unknown, unknown, z.infer<TSchema>>,
    res: Response,
    next: NextFunction
  ): void => {
    try {
      const parsedData = schema.parse(req.body);

      req.body = parsedData;

      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const errorMessages = error.errors.map(
          (issue: ZodIssue): { message: string } => ({
            message: `${issue.path.join('.')} is ${issue.message}`,
          })
        );
        res
          .status(StatusCodes.BAD_REQUEST)
          .json({ error: 'Invalid data', details: errorMessages });
      } else {
        res
          .status(StatusCodes.INTERNAL_SERVER_ERROR)
          .json({ error: 'Internal Server Error' });
      }
    }
  };
}
