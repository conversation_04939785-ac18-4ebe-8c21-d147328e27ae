import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.raw(`UPDATE repair_parts
                  SET is_active = CASE
                  WHEN part_status = 'active' THEN 1
                  ELSE 0
                  END;
                `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.raw(`UPDATE repair_parts
                  SET is_active = 1;
                `);
}
