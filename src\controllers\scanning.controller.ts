import { default as dayjs } from 'dayjs';
import type { Request, Response } from 'express';
import type { PartialModelGraph } from 'objection';
import { raw, ref } from 'objection';

import { updateShift } from '@app/controllers/shift.controller';
import { Employee } from '@app/models/employee.schema';
import type { WorkFragmentShape } from '@app/models/fragment.schema';
import { WorkFragmentCustomFields } from '@app/models/fragment.schema';
import {
  MoNumber,
  MoScans,
  Operators,
  WorkActivityLog,
  WorkAreaGroupShifts,
  WorkAreaGroups,
  WorkAreaLines,
  WorkAreaTicketStatuses,
  WorkAreaTickets,
  WorkAreas,
  WorkVouchers,
} from '@app/models/tickets.schema';
import { sendScanLog } from '@app/services/discord';

export async function removeScan(req: Request, res: Response) {
  try {
    const format1 = 'YYYY-MM-DD HH:mm:ss';
    const actualDate = new Date();
    // obtenemos el scan id
    const scanID = req.body.scanid;
    const groupAreaID = req.body.groupid;

    const updateMoScan = await MoScans.query()
      .update({
        removed_at: dayjs(actualDate).format(format1),
      })
      .where('scan_id', scanID);
    // actualizar shift si existe
    const getShift = await WorkAreaGroupShifts.query()
      .where('start_datetime_sv', '<=', dayjs(actualDate).format(format1))
      .where('end_datetime_sv', '>=', dayjs(actualDate).format(format1))
      .where('work_area_group_id', groupAreaID)
      .whereNull('removed_at')
      .select('id', 'operator_count');

    if (getShift.length > 0) {
      await updateShift(+getShift[0].id, +groupAreaID);
    }

    if (updateMoScan > 0) {
      return res.status(200).json({
        ok: true,
        message: `[Removed] - Se elimino el escaneo seleccionado`,
      });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
      data: error,
    });
  }
}

export async function restoreScan(req: Request, res: Response) {
  try {
    // obtenemos el scan id
    const scanID = req.body.scanid;
    const groupAreaID = req.body.groupid;
    const format1 = 'YYYY-MM-DD HH:mm:ss';
    const actualDate = new Date();

    const updateMoScan = await MoScans.query()
      .update({
        removed_at: null,
      })
      .where('scan_id', scanID);

    // actualizar shift si existe
    const getShift = await WorkAreaGroupShifts.query()
      .where('start_datetime_sv', '<=', dayjs(actualDate).format(format1))
      .where('end_datetime_sv', '>=', dayjs(actualDate).format(format1))
      .where('work_area_group_id', groupAreaID)
      .whereNull('removed_at')
      .select('id', 'operator_count');

    if (getShift.length > 0) {
      await updateShift(+getShift[0].id, +groupAreaID);
    }

    if (updateMoScan > 0) {
      return res.status(200).json({
        ok: true,
        message: `[Removed] - Se elimino el escaneo seleccionado`,
      });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
      data: error,
    });
  }
}

export async function updateQuantityScan(req: Request, res: Response) {
  try {
    let updateMoScan;
    // obtenemos el scan id
    const scanID = req.body.scanid;
    const newQuantity = req.body.newquantity;
    const groupAreaID = req.body.groupid;
    const format1 = 'YYYY-MM-DD HH:mm:ss';
    const actualDate = new Date();

    const getScan = await MoScans.query().where('scan_id', scanID);

    if (getScan.length > 0) {
      if (!getScan[0].is_repo) {
        updateMoScan = await MoScans.query()
          .update({
            quantity: newQuantity,
            is_manual_change: true,
            poly_status: 1,
          })
          .where('scan_id', scanID);

        // actualizar shift si existe
        const getShift = await WorkAreaGroupShifts.query()
          .where('start_datetime_sv', '<=', dayjs(actualDate).format(format1))
          .where('end_datetime_sv', '>=', dayjs(actualDate).format(format1))
          .where('work_area_group_id', groupAreaID)
          .whereNull('removed_at')
          .select('id', 'operator_count');

        if (getShift.length > 0) {
          await updateShift(+getShift[0].id, +groupAreaID);
        }
        if (updateMoScan > 0) {
          return res.status(200).json({
            ok: true,
            message: `[Updated] - Se actualizo la cantidad de la MO en este escaneo`,
          });
        }
      } else {
        return res.status(200).json({
          ok: false,
          message: `[Updated] - No se pueden modificar cantidades REPOSICION`,
        });
      }
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
      data: error,
    });
  }
}

export async function createScan(
  req: Request,
  res: Response
): Promise<Response | any> {
  try {
    const format1 = 'YYYY-MM-DD HH:mm:ss';
    const actualDate = new Date();
    const areaId = req.body.work_area_id;
    const moId = req.body.mo_id;
    const voucherId = req.body.voucher_id;
    const groupAreaId = req.body.work_area_group_id;
    const areaTicketId = req.body.work_area_ticket_id;
    const varsitysystem = req.body.varsity_system;
    const action = req.body.type_action;
    const employeeId = req.body.employee_id;
    const operatorId = req.body.operator_id;
    let quantity = req.body.quantity;
    const workLineId = req.body.work_area_line_id; // manual set
    const defaultWorkLineId = req.body.default_work_area_line_id; // default from groups
    // const prev_work_area_id = req.body.last_work_ticket_area_id;
    const moUnitQuantity = req.body.mo_quantity;
    const groupName = req.body.group_name;
    const groupDescription = req.body.group_description;
    const moCustomer = req.body.customer;
    // const moStatus = req.body.mo_status;
    const moNum = req.body.num;
    const areaName = req.body.work_area_name;
    const groupNullBarcode = req.body.badge_barcode;
    const isRepoValue =
      req.body.is_repo !== undefined && req.body.is_repo !== null
        ? req.body.is_repo
        : false;
    const samValue = req.body.sam_value;
    const samId = req.body.sam_id;
    const repoId = req.body.repo_id;
    const affectedUnits = req.body.affected_units;
    const fragmentId = req.body.fragment_id;
    let updatesystem = req.body.update_system;
    let ticketID;
    let operatorBarcode;
    let taskName;
    let workLine;

    // en caso de ser repo no actualizar el sistema
    if (isRepoValue !== undefined && isRepoValue !== null && isRepoValue) {
      updatesystem = 1;
    }

    // obteniendo informacion del operador
    if (operatorId !== undefined && operatorId !== null) {
      // obtener status de area para completo
      const getOperators = await Operators.query()
        .where('operator_id', operatorId)
        .select('barcode', 'task');

      if (getOperators.length > 0) {
        operatorBarcode = getOperators[0].barcode;
        taskName = getOperators[0].task;
      }
    }

    // check workline if added or exist in work group
    if (workLineId !== null && workLineId !== undefined) {
      workLine = workLineId;
    }

    // check si workline esta vacio significa que no existe o no fue enviada en la peticion, en este caso tomar el default en el grupo si no es nulo.
    if (
      (workLine === null || workLine === undefined) &&
      defaultWorkLineId !== undefined &&
      defaultWorkLineId !== null
    ) {
      workLine = defaultWorkLineId;
    }

    if (workLine !== null && workLine !== undefined) {
      // checkear si el id existe y si existe que sea igual al area definida
      const getLine = await WorkAreaLines.query()
        .where('id ', workLine)
        .select('work_area_id');

      if (getLine.length > 0) {
        // check ids
        if (areaId !== getLine[0].work_area_id) {
          // enviar mensaje a discord
          await sendScanLog(
            'LINE DOES NOT BELONG TO THIS AREA',
            `Work Area Line enviada en la peticion no coincide con el area asignada a este grupo. \n MOID : ${moId}, \n MONUM : ${moNum}, \n CUSTOMER: ${moCustomer}, \n AREA DE LINEA: ${getLine[0].work_area_id}, \n AREA DE GRUPO ID : ${areaId}, \n AREA DE GRUPO NAME : ${groupName}, \n AREA DE GRUPO DESCRIPTION : ${groupDescription}, \n GROUPO: ${groupAreaId}`
          );

          return res.status(200).json({
            ok: false,
            data: 'Work Area Line enviada en la peticion no coincide con el area asignada a este grupo',
          });
        }
      }
    }
    // creating scans
    // action RECEIVED
    if (action === 'RECEIVED') {
      if (
        (areaTicketId === undefined || areaTicketId === null) &&
        areaId !== undefined &&
        areaId !== null
      ) {
        const getDisableDate = await WorkAreas.query()
          .where('work_area_id ', areaId)
          .select('disabled_date');

        if (getDisableDate.length > 0) {
          // if disable es nulo, crear ticket en area
          if (getDisableDate[0].disabled_date === null) {
            // obtenemos el id del area para los nuevos
            const getWorkTicketStatuses = await WorkAreaTicketStatuses.query()
              .where('work_area_id', areaId)
              .where('name', 'Nuevo')
              .select('id');

            if (getWorkTicketStatuses.length > 0) {
              const createWorkTicket = await WorkAreaTickets.query().insert({
                work_area_id: areaId,
                work_area_ticket_status_id: getWorkTicketStatuses[0].id,
                work_voucher_id: voucherId,
              });

              // obtener id de ticket recien creado
              if (createWorkTicket.id > 0) {
                ticketID = createWorkTicket.id;
              }
            } else {
              // enviar mensaje a discord
              await sendScanLog(
                'WORKAREATICKETSTATUSES NOT FOUND',
                `status de area no encontrada para completar. \n Area: ${areaId}`
              );

              return res.status(200).json({
                ok: false,
                data: 'status de area no encontrada para completar',
              });
            }
          }
        }

        // create mo scan
        const addMoScans = await MoScans.query().insert({
          mo_id: moId,
          received_at: dayjs(actualDate).format(format1),
          work_area_id: areaId,
          supervisor: operatorBarcode,
          work_voucher_id: voucherId,
          work_area_group_id: groupAreaId,
          work_area_ticket_id: ticketID,
          poly_status: updatesystem,
          varsity_status: varsitysystem,
          employee_id: employeeId,
        });

        return res.status(200).json({
          ok: true,
          message: 'Se ha creado un registro de recibido',
          data: addMoScans,
        });
      } else {
        // enviar mensaje a discord
        await sendScanLog(
          'TICKET ALREADY STARTED IN THIS AREA',
          `Ya existe un ticket en esta area, no se puede recibir (RECEIVED) este MO. \n TICKET: ${areaTicketId}, \n AREA GROUP: ${groupAreaId}`
        );

        return res.status(200).json({
          ok: false,
          data: 'Ya existe un ticket en esta area, no se puede recibir (RECEIVED) este MO',
        });
      }
    }

    // action START
    if (action === 'START') {
      // validar si esta nulo group area id
      if (groupAreaId === undefined || groupAreaId === null) {
        // enviar mensaje a discord
        await sendScanLog(
          'GROUPID NULL',
          `Area group vacio no se puede iniciar(START) el trabajo. \n MOID : ${moId}, \n MO NUM:${moNum}, \n CUSTOMER: ${moCustomer}, \n BARCODE : ${operatorBarcode}`
        );

        return res.status(200).json({
          ok: false,
          data: 'Area group vacio no se puede iniciar(START) el trabajo',
        });
      }
      // validar escaneos anteriores
      const getMoScan = await MoScans.query()
        .where('mo_id', moId)
        .where(
          'work_area_group_id',
          groupAreaId !== undefined && groupAreaId !== null ? groupAreaId : null
        )
        .where(
          'work_voucher_id',
          voucherId !== undefined && voucherId !== null ? voucherId : null
        )
        .where(
          'work_area_ticket_id',
          areaTicketId !== undefined && areaTicketId !== null
            ? areaTicketId
            : null
        )
        .where(
          'work_repo_id',
          repoId !== undefined && repoId !== null ? repoId : null
        )
        .where(
          'work_fragment_id',
          fragmentId !== undefined && fragmentId !== null ? fragmentId : null
        )
        .whereNull('received_at')
        .whereNull('closed_at')
        .whereNull('removed_at')
        .select('sew_ready', 'sew')
        .orderBy('scan_id', 'desc')
        .limit(1);

      if (getMoScan.length > 0) {
        if (getMoScan[0].sew_ready !== null && getMoScan[0].sew === null) {
          await sendScanLog(
            'MO ALREADY STARTED IN THIS AREA',
            `ya existe una produccion iniciada. \n MOID : ${moId}, \n MONUM : ${moNum}, \n CUSTOMER: ${moCustomer}, \n GROUPID : ${groupAreaId}, \n GROUP NAME : ${groupName}, \n GROUP DESCRIPTION : ${groupDescription}`
          );
          const num: any = await MoNumber.query()
            .where('mo_numbers.mo_id', moId)
            .select('mo_numbers.num');

          return res.status(200).json({
            ok: false,
            data: `ya existe una produccion iniciada, Para la MO : ${num[0].num}`,
          });
        }
      }

      // create mo scan
      const addMoScans = await MoScans.query().insert({
        mo_id: moId,
        sew_ready: dayjs(actualDate).format(format1),
        supervisor: operatorBarcode,
        task_name: taskName,
        is_repo:
          isRepoValue !== undefined && isRepoValue != null
            ? isRepoValue
            : false,
        work_area_id: areaId !== undefined && areaId !== null ? areaId : null,
        work_voucher_id:
          voucherId !== undefined && voucherId !== null ? voucherId : null,
        work_area_group_id:
          groupAreaId !== undefined && groupAreaId !== null
            ? groupAreaId
            : null,
        work_area_ticket_id:
          areaTicketId !== undefined && areaTicketId !== null
            ? areaTicketId
            : null,
        work_area_line_id:
          workLine !== undefined && workLine !== null ? workLine : null,
        poly_status: updatesystem,
        varsity_status: varsitysystem,
        employee_id: employeeId,
        work_repo_id: repoId !== undefined && repoId !== null ? repoId : null,
        work_fragment_id:
          fragmentId !== undefined && fragmentId !== null ? fragmentId : null,
      });

      return res.status(200).json({
        ok: true,
        message: 'Se ha creado un registro de inicio de trabajo',
        data: addMoScans,
      });
    }

    // action FINISH
    if (action === 'FINISH') {
      try {
        // validar si esta nulo group area id
        if (groupAreaId === undefined || groupAreaId === null) {
          // enviar mensaje a discord
          await sendScanLog(
            'GROUPID NULL',
            `Area group vacio no se puede finalizar (FINISH) el trabajo. \n MOID : ${moId}, \n MO NUM:${moNum}, \n CUSTOMER: ${moCustomer}, \n BARCODE : ${groupNullBarcode}, \n OPERATORID : ${operatorId}`
          );

          return res.status(200).json({
            ok: false,
            data: 'Area group vacio no se puede finalizar (FINISH) el trabajo',
          });
        }

        // actualizar shift si existe
        const getShift = await WorkAreaGroupShifts.query()
          .where('start_datetime_sv', '<=', dayjs(actualDate).format(format1))
          .where('end_datetime_sv', '>=', dayjs(actualDate).format(format1))
          .where('work_area_group_id', groupAreaId)
          .whereNull('removed_at')
          .select('id', 'operator_count');

        // validar escaneos anteriores
        const getMoScan = await MoScans.query()
          .where('mo_id', moId)
          .where(
            'work_area_group_id',
            groupAreaId !== undefined && groupAreaId !== null
              ? groupAreaId
              : null
          )
          .where(
            'work_voucher_id',
            voucherId !== undefined && voucherId !== null ? voucherId : null
          )
          .where(
            'work_area_ticket_id',
            areaTicketId !== undefined && areaTicketId !== null
              ? areaTicketId
              : null
          )
          .where(
            'work_repo_id',
            repoId !== undefined && repoId !== null ? repoId : null
          )
          .where(
            'work_fragment_id',
            fragmentId !== undefined && fragmentId !== null ? fragmentId : null
          )
          .whereNull('received_at')
          .whereNull('closed_at')
          .whereNull('removed_at')
          .where('is_repo', false)
          .select([
            'sew_ready',
            'sew',
            'scan_id',
            MoScans.query()
              .where('mo_id', moId)
              .where('work_area_group_id', groupAreaId)
              .where('is_repo', false)
              .whereNull('removed_at')
              .sum('quantity')
              .as('qty_reported'),
          ])
          .orderBy('scan_id', 'desc')
          .limit(1)
          .castTo<
            {
              sew_ready: Date;
              sew: Date;
              scan_id: number;
              qty_reported: number | null;
            }[]
          >();

        if (getMoScan.length > 0) {
          // validar cantidades en caso que sea nula la cantidad
          if ((quantity === null || quantity === undefined) && !isRepoValue) {
            quantity = moUnitQuantity - getMoScan[0].qty_reported;

            if (quantity < 0) {
              quantity = 0;
            }
          }
          // encontro escaneos anteriores se validan cantidades
          if (moUnitQuantity === quantity && !isRepoValue) {
            quantity = moUnitQuantity - getMoScan[0].qty_reported;
            if (quantity < 0) {
              quantity = 0;
            }

            if (quantity < 0) {
              quantity = 0;
            }
          }
          // significa que hay un escaneo previo, activo
          if (getMoScan[0].sew_ready !== null && getMoScan[0].sew === null) {
            // validar si creamos otro record
            if (req.body.partial_option !== undefined) {
              // update last record
              const updateMOScan = await MoScans.query()
                .update({
                  sew: dayjs(actualDate).format(format1),
                  poly_status:
                    updatesystem === 1
                      ? 1
                      : moUnitQuantity <=
                        Number(getMoScan[0].qty_reported) + Number(quantity)
                      ? 0
                      : req.body.partial_option === 'partial'
                      ? 1
                      : 0,
                  varsity_status: req.body.partial_option === 'partial' ? 1 : 0,
                  work_area_line_id:
                    workLine !== undefined && workLine !== null
                      ? workLine
                      : null,
                  quantity,
                  employee_id: employeeId,
                  is_repo:
                    isRepoValue !== undefined && isRepoValue == true
                      ? isRepoValue
                      : false,
                  work_area_group_shift_id:
                    getShift.length > 0 ? getShift[0].id : null,
                  style_sam_id:
                    samId !== undefined && samId !== null ? samId : null,
                  sew_sam_value:
                    samValue !== undefined && samValue !== null
                      ? samValue
                      : null,
                  work_fragment_id:
                    fragmentId !== undefined && fragmentId !== null
                      ? fragmentId
                      : null,
                })
                .where('scan_id ', getMoScan[0].scan_id);

              if (getShift.length > 0) {
                await updateShift(+getShift[0].id, +groupAreaId);
              }

              req.body.partial_option =
                moUnitQuantity <=
                Number(getMoScan[0].qty_reported) + Number(quantity)
                  ? null
                  : req.body.partial_option;

              if (updateMOScan > 0) {
                if (req.body.partial_option === 'partial') {
                  // create mo scan
                  const createMO = await MoScans.query().insert({
                    mo_id: moId,
                    sew_ready: dayjs(actualDate).format(format1),
                    supervisor: operatorBarcode,
                    task_name: taskName,
                    work_area_id:
                      areaId !== undefined && areaId !== null ? areaId : null,
                    work_voucher_id:
                      voucherId !== undefined && voucherId !== null
                        ? voucherId
                        : null,
                    work_area_group_id:
                      groupAreaId !== undefined && groupAreaId !== null
                        ? groupAreaId
                        : null,
                    work_area_ticket_id:
                      areaTicketId !== undefined && areaTicketId !== null
                        ? areaTicketId
                        : null,
                    work_area_line_id:
                      workLine !== undefined && workLine !== null
                        ? workLine
                        : null,
                    poly_status: 1,
                    varsity_status: 1,
                    employee_id: employeeId,
                    work_fragment_id:
                      fragmentId !== undefined && fragmentId !== null
                        ? fragmentId
                        : null,
                  });

                  if (createMO.mo_id > 0) {
                    return res.status(200).json({
                      ok: true,
                      message:
                        'Se actualizo un escaneo anterior con su fecha de sew partial',
                      data: updateMOScan,
                    });
                  } else {
                    // enviar mensaje a discord
                    await sendScanLog(
                      'CREATE NEW MOSCAN ERROR. PARTIAL',
                      `Error al querer crear nuevo registro despues de actualizar registro anterior. opcion parcial. \n MOID: ${moId}, \n MO NUM:${moNum}, \n CUSTOMER: ${moCustomer}, \n Operador: ${operatorBarcode}, \n GROUP NAME : ${groupName}, \n GROUP DESCRIPTION: ${groupDescription}`
                    );

                    return res.status(200).json({
                      ok: false,
                      data: 'Error al querer crear nuevo registro despues de actualizar registro anterior. opcion parcial',
                    });
                  }
                } else {
                  return res.status(200).json({
                    ok: true,
                    message:
                      'Se actualizo un registro anterior y se mandara a actualizar sistema. Complete',
                    data: updateMOScan,
                  });
                }
              } else {
                // enviar mensaje a discord
                await sendScanLog(
                  'UPDATE MOSCAN ERROR',
                  `No se logro actualizar el ultimo registro. \n MOID: ${moId}, \n MO NUM:${moNum}, \n CUSTOMER: ${moCustomer}, \n Operador: ${operatorBarcode}, \n GROUP NAME : ${groupName}, \n GROUP DESCRIPTION: ${groupDescription}`
                );

                return res.status(200).json({
                  ok: false,
                  data: 'No se logro actualizar el ultimo registro',
                });
              }
            } else {
              // update last record
              if (
                getMoScan[0].qty_reported !== null &&
                getMoScan[0].qty_reported !== undefined &&
                // getMoScan[0].qty_reported !== '' &&
                !isRepoValue
              ) {
                quantity = moUnitQuantity - getMoScan[0].qty_reported;
                if (quantity < 0) {
                  quantity = 0;
                }
              }
              const updateMOScan = await MoScans.query()
                .update({
                  sew: dayjs(actualDate).format(format1),
                  poly_status: updatesystem,
                  varsity_status: varsitysystem,
                  work_area_line_id:
                    workLine !== undefined && workLine !== null
                      ? workLine
                      : null,
                  quantity:
                    isRepoValue !== undefined && isRepoValue == true
                      ? affectedUnits
                      : quantity,
                  employee_id: employeeId,
                  is_repo:
                    isRepoValue !== undefined && isRepoValue == true
                      ? isRepoValue
                      : false,
                  work_area_group_shift_id:
                    getShift.length > 0 ? getShift[0].id : null,
                  style_sam_id:
                    samId !== undefined && samId !== null ? samId : null,
                  sew_sam_value:
                    samValue !== undefined && samValue !== null
                      ? samValue
                      : null,
                  work_repo_id:
                    repoId !== undefined && repoId !== null ? repoId : null,
                  work_fragment_id:
                    fragmentId !== undefined && fragmentId !== null
                      ? fragmentId
                      : null,
                })
                .where('scan_id', getMoScan[0].scan_id);

              if (getShift.length > 0) {
                await updateShift(+getShift[0].id, +groupAreaId);
              }
              if (updateMOScan > 0) {
                return res.status(200).json({
                  ok: true,
                  message:
                    'Se actualizo un escaneo anterior con su fecha de sew',
                  data: updateMOScan,
                });
              } else {
                // enviar mensaje a discord
                await sendScanLog(
                  'UPDATE MOSCAN ERROR',
                  `No se logro actualizar el ultimo registro. \n MOID: ${moId}, \n MO NUM:${moNum}, \n CUSTOMER: ${moCustomer}, \n Operador: ${operatorBarcode}, \n GROUP NAME : ${groupName}, \n GROUP DESCRIPTION: ${groupDescription}`
                );

                return res.status(200).json({
                  ok: false,
                  data: `No se logro actualizar el ultimo registro. MOID: ${moId}, Operador: ${operatorBarcode}`,
                });
              }
            }
          } else if (getMoScan[0].sew !== null) {
            // calcular 5 minutos despues del ultimo escaneo
            const sew = getMoScan[0].sew;
            let totalMin = (sew.getTime() - actualDate.getTime()) / 1000;

            // let minutes = Math.floor(totalMin / 60) % 60;
            // totalMin -= minutes * 60;
            totalMin /= 60;
            const minutes = Math.abs(Math.round(totalMin));

            if (minutes < 5) {
              const num: any = await MoNumber.query()
                .where('mo_numbers.mo_id', moId)
                .select('mo_numbers.num');

              // enviar mensaje a discord
              await sendScanLog(
                'SCANNED TIME 5 MIN.',
                `Ya ha escaneado espere 5 minutos para volver a escanear. \n MOID: ${moId},\n Operador: ${operatorBarcode},\n AreaName : ${areaName},\n GroupName : ${groupName}, \n Time : ${minutes}`
              );

              return res.status(200).json({
                ok: false,
                data: `Ya ha escaneado la MO: ${num[0].num}, Por Favor 5 minutos para volver a escanear`,
              });
            }
          }
        }
        // validar si creamos otro record
        if (
          req.body.partial_option !== undefined &&
          req.body.partial_option !== null &&
          !isRepoValue
        ) {
          // create mo scan
          const addmo = await MoScans.query().insert({
            mo_id: moId,
            sew: dayjs(actualDate).format(format1),
            supervisor: operatorBarcode,
            task_name: taskName,
            work_area_id:
              areaId !== undefined && areaId !== null ? areaId : null,
            work_voucher_id:
              voucherId !== undefined && voucherId !== null ? voucherId : null,
            work_area_group_id:
              groupAreaId !== undefined && groupAreaId !== null
                ? groupAreaId
                : null,
            work_area_ticket_id:
              ticketID !== undefined && ticketID !== null ? ticketID : null,
            work_area_line_id:
              workLine !== undefined && workLine !== null ? workLine : null,
            poly_status:
              updatesystem === 1
                ? 1
                : moUnitQuantity <= quantity
                ? 0
                : req.body.partial_option === 'partial'
                ? 1
                : 0,
            varsity_status: req.body.partial_option === 'partial' ? 1 : 0,
            is_repo:
              isRepoValue !== undefined && isRepoValue == true
                ? isRepoValue
                : false,
            work_area_group_shift_id:
              getShift.length > 0 ? getShift[0].id : null,
            quantity,
            employee_id: employeeId,
            style_sam_id: samId !== undefined && samId !== null ? samId : null,
            sew_sam_value:
              samValue !== undefined && samValue !== null ? samValue : null,
            work_fragment_id:
              fragmentId !== undefined && fragmentId !== null
                ? fragmentId
                : null,
          });

          //req.body.partial_option = moUnitQuantity <= quantity ? null : req.body.partial_option;

          if (getShift.length > 0) {
            await updateShift(+getShift[0].id, +groupAreaId);
          }

          req.body.partial_option =
            moUnitQuantity <= quantity ? null : req.body.partial_option;

          if (req.body.partial_option === 'partial') {
            // no se encontro registro, se crea nuevo scan con toda la informacion
            const createMO = await MoScans.query().insert({
              mo_id: moId,
              sew_ready: dayjs(actualDate).format(format1),
              supervisor: operatorBarcode,
              task_name: taskName,
              work_area_id:
                areaId !== undefined && areaId !== null ? areaId : null,
              work_voucher_id:
                voucherId !== undefined && voucherId !== null
                  ? voucherId
                  : null,
              work_area_group_id:
                groupAreaId !== undefined && groupAreaId !== null
                  ? groupAreaId
                  : null,
              work_area_ticket_id:
                ticketID !== undefined && ticketID !== null ? ticketID : null,
              work_area_line_id:
                workLine !== undefined && workLine !== null ? workLine : null,
              poly_status: 1,
              varsity_status: 1,
              employee_id: employeeId,
              work_fragment_id:
                fragmentId !== undefined && fragmentId !== null
                  ? fragmentId
                  : null,
            });

            // TODO: was .id, changed to scan_id
            if (createMO.scan_id > 0) {
              return res.status(200).json({
                ok: true,
                message: 'Se creo nuevo registro y se agrego parcial',
                data: addmo,
              });
            } else {
              // enviar mensaje a discord
              await sendScanLog(
                'UPDATE MOSCAN ERROR.',
                `Error al actualizar escaneo anterior. \n MOID: ${moId}, \n MO NUM:${moNum}, \n CUSTOMER: ${moCustomer}, \n Operador: ${operatorBarcode}, \n GROUP NAME : ${groupName}, \n GROUP DESCRIPTION: ${groupDescription}`
              );

              return res.status(200).json({
                ok: false,
                data: 'Error al actualizar escaneo anterior',
              });
            }
          } else {
            return res.status(200).json({
              ok: true,
              message:
                'Se actualizo un registro anterior y se mandara a actualizar sistema. Complete',
              data: addmo,
            });
          }
        } else {
          if (quantity < 0) {
            quantity = 0;
          }
          // no se encontro registro, se crea nuevo scan con toda la informacion
          const addmo = await MoScans.query().insert({
            mo_id: moId,
            sew: dayjs(actualDate).format(format1),
            supervisor: operatorBarcode,
            task_name: taskName,
            work_area_id:
              areaId !== undefined && areaId !== null ? areaId : null,
            work_voucher_id:
              voucherId !== undefined && voucherId !== null ? voucherId : null,
            work_area_group_id:
              groupAreaId !== undefined && groupAreaId !== null
                ? groupAreaId
                : null,
            work_area_ticket_id:
              ticketID !== undefined && ticketID !== null ? ticketID : null,
            work_area_line_id:
              workLine !== undefined && workLine !== null ? workLine : null,
            poly_status: updatesystem,
            varsity_status: varsitysystem,
            is_repo:
              isRepoValue !== undefined && isRepoValue == true
                ? isRepoValue
                : false,
            quantity:
              isRepoValue !== undefined && isRepoValue == true
                ? affectedUnits === null || affectedUnits === undefined
                  ? quantity
                  : affectedUnits
                : quantity,
            employee_id: employeeId,
            work_area_group_shift_id:
              getShift.length > 0 ? getShift[0].id : null,
            style_sam_id: samId !== undefined && samId !== null ? samId : null,
            sew_sam_value:
              samValue !== undefined && samValue !== null ? samValue : null,
            work_repo_id:
              repoId !== undefined && repoId !== null ? repoId : null,
            work_fragment_id:
              fragmentId !== undefined && fragmentId !== null
                ? fragmentId
                : null,
          });

          if (getShift.length > 0) {
            await updateShift(+getShift[0].id, +groupAreaId);
          }

          if (addmo.mo_id > 0) {
            return res.status(200).json({
              ok: true,
              message: 'Se creo nuevo registro',
              data: addmo,
            });
          } else {
            // enviar mensaje a discord
            await sendScanLog(
              'CREATE NEW MOSCAN ERROR.',
              `Error al agregar nuevo registro. \n MOID: ${moId}, \n MO NUM:${moNum}, \n CUSTOMER: ${moCustomer}, \n Operador: ${operatorBarcode}, \n GROUP NAME : ${groupName}, \n GROUP DESCRIPTION: ${groupDescription}`
            );

            return res.status(200).json({
              ok: false,
              data: 'Error al agregar nuevo registro',
            });
          }
        }
      } catch (error) {
        return res.status(500).json({
          ok: false,
          data: error,
        });
      }
    }

    // action CLOSE
    if (action === 'CLOSE') {
      // validar si esta nulo  area id sino es nulo cerrar ticket
      if (areaTicketId !== undefined && areaTicketId !== null) {
        // obtener status de area para completo
        const getWorkTicketStatuses = await WorkAreaTicketStatuses.query()
          .where('work_area_id', areaId)
          .where('name', 'Completo')
          .select('id');

        if (getWorkTicketStatuses.length > 0) {
          // close work_area_ticket_id (finished_ar and status)
          const updateTicket = await WorkAreaTickets.query()
            .update({
              finished_at: dayjs(actualDate).format(format1),
              work_area_ticket_status_id: getWorkTicketStatuses[0].id,
            })
            .where('id', areaTicketId);

          if (updateTicket === 0) {
            // enviar mensaje a discord
            await sendScanLog(
              'UPDATE TICKET ERROR.',
              `Problema al actualizar ticket (CLOSE). TICKET: ${areaTicketId}, AREA: ${areaId}`
            );

            return res.status(200).json({
              ok: false,
              data: 'Problema al actualizar ticket (CLOSE)',
            });
          }
        } else {
          // enviar mensaje a discord
          await sendScanLog(
            'UPDATE TICKET ERROR.',
            `status de area no encontrada para completar. TICKET: ${areaTicketId}, AREA: ${areaId}`
          );

          return res.status(200).json({
            ok: false,
            data: 'status de area no encontrada para completar',
          });
        }
      }

      if (areaId !== undefined && areaId !== null) {
        // obtener antiguo escaneo en caso que se creara anteriormente un receive
        const getMoScan = await MoScans.query()
          .where('mo_id', moId)
          .where('work_area_id', areaId)
          .whereNull('sew')
          .whereNull('sew_ready')
          .whereNotNull('received_at')
          .whereNull('closed_at')
          .select('scan_id');

        // existe un escaneo anterior
        if (getMoScan.length > 0) {
          const updateMOScan = await MoScans.query()
            .update({
              closed_at: dayjs(actualDate).format(format1),
              supervisor: operatorBarcode,
              task_name: taskName,
              work_area_group_id: groupAreaId,
              work_area_ticket_id: areaTicketId,
              work_voucher_id: voucherId,
              poly_status: updatesystem,
              varsity_status: varsitysystem,
            })
            .where('scan_id ', getMoScan[0].scan_id);

          return res.status(200).json({
            ok: true,
            message:
              'Se actualizo un escaneo anterior con su fecha de recibido',
            data: updateMOScan,
          });
        } else {
          // insertar nuevo registro con close date
          const addMoScans = await MoScans.query().insert({
            mo_id: moId,
            closed_at: dayjs(actualDate).format(format1),
            supervisor: operatorBarcode,
            task_name: taskName,
            work_area_id: areaId,
            work_voucher_id: voucherId,
            work_area_group_id: groupAreaId,
            work_area_ticket_id: ticketID,
            poly_status: updatesystem,
            varsity_status: varsitysystem,
            employee_id: employeeId,
          });

          return res.status(200).json({
            ok: true,
            message: 'Se creo nuevo registro con fecha de cerrado',
            data: addMoScans,
          });
        }
      } else {
        // enviar mensaje a discord
        await sendScanLog(
          'UPDATE TICKET ERROR.',
          `No se puede completar ya que area es nula. \n MOID: ${moId}, \n MO NUM:${moNum}, \n CUSTOMER: ${moCustomer}, \n AREA: ${areaId}`
        );

        return res.status(200).json({
          ok: false,
          data: 'No se puede completar ya que area es nula',
        });
      }
    }
  } catch (error) {
    const err: string = error.toString();
    // enviar mensaje a discord
    await sendScanLog('ERROR EN EL SERVIDOR.', err);

    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function getInlineProduction(req: Request, res: Response) {
  try {
    const operator = req.body.barcode_operator;
    const ticketView = req.body.ticket_view;
    const group = req.body.group;
    // evaluar si tiene esta habilitado para ver tickets sino solo mostrar escaneos
    if (ticketView === null || ticketView === undefined) {
      const getAllActiveTickets = await MoScans.query()
        .join('mo_numbers', 'mo_scans.mo_id', '=', 'mo_numbers.mo_id')
        .leftJoin(
          'work_area_lines',
          'mo_scans.work_area_line_id',
          '=',
          'work_area_lines.id'
        )
        .where('mo_scans.work_area_group_id', group)
        .whereNotNull('mo_scans.sew_ready')
        .whereNull('mo_scans.sew')
        .whereNull('mo_scans.removed_at')
        .select(
          'mo_numbers.mo_id',
          'mo_numbers.num',
          'mo_numbers.customer',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.required_date',
          'mo_scans.sew_ready',
          'work_area_lines.name'
        )
        .orderBy('mo_scans.sew_ready', 'desc');

      if (getAllActiveTickets.length > 0) {
        return res.status(200).json({
          ok: true,
          data: getAllActiveTickets,
        });
      } else {
        return res.status(200).json({
          ok: false,
          data: 'No hay información para el operador 1',
        });
      }
    } else {
      // esto es para las estaciones actuales
      if (group !== undefined && group !== null) {
        const getAllActiveTickets = await MoScans.query()
          .join('mo_numbers', 'mo_scans.mo_id', '=', 'mo_numbers.mo_id')
          .leftJoin(
            'work_area_lines',
            'mo_scans.work_area_line_id',
            '=',
            'work_area_lines.id'
          )
          .where('mo_scans.work_area_group_id', group)
          .whereNotNull('mo_scans.sew_ready')
          .whereNull('mo_scans.sew')
          .whereNull('mo_scans.removed_at')
          .select([
            'mo_scans.scan_id',
            'mo_numbers.mo_id',
            'mo_numbers.num',
            'mo_numbers.customer',
            'mo_numbers.style',
            'mo_numbers.quantity',
            'mo_numbers.required_date',
            'mo_scans.sew_ready',
            'work_area_lines.name',
            'mo_scans.work_area_group_id as work_area_group_id',
            MoScans.query()
              .where('mo_id', ref('mo_numbers.mo_id'))
              .where('work_area_group_id', group)
              .whereNull('removed_at')
              .whereNotNull('sew')
              .where('is_repo', false)
              .sum('quantity')
              .as('qtyReported'),
          ])
          .orderBy('mo_scans.sew_ready', 'desc');

        if (getAllActiveTickets.length > 0) {
          return res.status(200).json({
            ok: true,
            data: getAllActiveTickets,
          });
        } else {
          return res.status(200).json({
            ok: false,
            data: 'No hay ordenes de este grupo',
          });
        }
      } else {
        const getAllInlineProduction = await MoScans.query()
          .join('mo_numbers', 'mo_scans.mo_id', '=', 'mo_numbers.mo_id')
          .leftJoin(
            'work_area_lines',
            'mo_scans.work_area_line_id',
            '=',
            'work_area_lines.id'
          )
          .where('mo_scans.supervisor', operator)
          .whereNull('mo_scans.sew')
          .whereNull('mo_scans.removed_at')
          .select(
            'mo_numbers.mo_id',
            'mo_numbers.num',
            'mo_numbers.customer',
            'mo_numbers.style',
            'mo_numbers.quantity',
            'mo_numbers.required_date',
            'mo_scans.sew_ready',
            'work_area_lines.name'
          )

          .orderBy('mo_scans.sew_ready', 'desc');

        if (getAllInlineProduction.length > 0) {
          return res.status(200).json({
            ok: true,
            data: getAllInlineProduction,
          });
        } else {
          return res.status(200).json({
            ok: false,
            data: 'No hay informacion para este operador',
          });
        }
      }
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
      data: error,
    });
  }
}

export async function getCompleteProduction(req: Request, res: Response) {
  try {
    const operator = req.body.barcode_operator;
    const ticketView = req.body.ticket_view;
    const group = req.body.group;
    const today = new Date();

    if (ticketView === null || ticketView === undefined) {
      const getAllProduction = await MoScans.query()
        .join('mo_numbers', 'mo_scans.mo_id', '=', 'mo_numbers.mo_id')
        .leftJoin(
          'work_area_lines',
          'mo_scans.work_area_line_id',
          '=',
          'work_area_lines.id'
        )
        .where('mo_scans.work_area_group_id', group)
        .whereNull('mo_scans.removed_at')
        .whereNotNull('mo_scans.sew')
        .where(
          'mo_scans.sew',
          '>=',
          today.getFullYear() +
            '-' +
            (today.getMonth() + 1) +
            '-' +
            today.getDate() +
            'T00:00:00'
        )
        .select([
          'mo_scans.scan_id',
          'mo_numbers.mo_id',
          'mo_numbers.num',
          'mo_numbers.customer',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.required_date',
          'mo_scans.sew_ready',
          'mo_scans.sew',
          'work_area_lines.name',
          'mo_scans.work_area_group_id',
          'mo_scans.is_repo',
          'mo_scans.removed_at',
          'mo_scans.quantity as dailyReported',
          MoScans.query()
            .where('mo_id', ref('mo_numbers.mo_id'))
            .where('work_area_group_id', group)
            .whereNotNull('sew')
            .whereNull('removed_at')
            .sum('quantity')
            .as('qtyReported'),
        ])
        .groupByRaw('mo_numbers.mo_id, work_area_group_id')
        .orderBy('mo_scans.sew', 'desc');

      if (getAllProduction.length > 0) {
        return res.status(200).json({
          ok: true,
          data: { getAllProduction },
        });
      } else {
        return res.status(200).json({
          ok: false,
          data: 'No hay información para el operador 1',
        });
      }
    } else {
      // para las estaciones actuales
      if (group !== undefined && group !== null) {
        //obtener produccion sin fragmentos
        const getAllProduction = await MoScans.query()
          .join('mo_numbers', 'mo_scans.mo_id', '=', 'mo_numbers.mo_id')
          .leftJoin(
            'work_area_lines',
            'mo_scans.work_area_line_id',
            '=',
            'work_area_lines.id'
          )
          .where('mo_scans.work_area_group_id', group)
          .whereNotNull('mo_scans.sew')
          .whereNull('work_fragment_id')
          .where(
            'mo_scans.sew',
            '>=',
            today.getFullYear() +
              '-' +
              (today.getMonth() + 1) +
              '-' +
              today.getDate() +
              'T00:00:00'
          )
          .select([
            'mo_scans.scan_id',
            'mo_numbers.mo_id',
            'mo_numbers.num',
            'mo_numbers.customer',
            'mo_numbers.style',
            'mo_numbers.quantity',
            'mo_numbers.required_date',
            'mo_scans.sew_ready',
            'mo_scans.sew',
            'work_area_lines.name',
            'mo_scans.work_area_group_id',
            'mo_scans.quantity as dailyReported',
            'mo_scans.removed_at',
            'mo_scans.is_repo',
            MoScans.query()
              .where('mo_id', ref('mo_numbers.mo_id'))
              .where('work_area_group_id', group)
              .whereNotNull('sew')
              .whereNull('removed_at')
              .whereNull('work_fragment_id')
              .sum('quantity')
              .as('qtyReported'),
          ])
          .groupByRaw(
            'mo_scans.scan_id, mo_numbers.mo_id, mo_scans.work_area_group_id'
          )
          .orderBy('mo_scans.removed_at', 'asc')
          .orderBy('mo_scans.sew', 'desc');

        //obtener fragmentos si existe
        const getAllProductionFragments = await MoScans.query()
          .join('mo_numbers', 'mo_scans.mo_id', '=', 'mo_numbers.mo_id')
          .join(
            'work_fragments',
            'mo_scans.work_fragment_id',
            '=',
            'work_fragments.id'
          )
          .leftJoin(
            'work_area_lines',
            'mo_scans.work_area_line_id',
            '=',
            'work_area_lines.id'
          )
          .whereNotNull('mo_scans.work_fragment_id')
          .where('mo_scans.work_area_group_id', group)
          .whereNotNull('mo_scans.sew')
          .where(
            'mo_scans.sew',
            '>=',
            today.getFullYear() +
              '-' +
              (today.getMonth() + 1) +
              '-' +
              today.getDate() +
              'T00:00:00'
          )
          .select([
            'mo_scans.scan_id',
            'mo_numbers.mo_id',
            'mo_scans.work_fragment_id',
            'mo_scans.sew',
            'work_fragments.*',
          ])
          .groupByRaw('mo_scans.scan_id')
          .orderBy('mo_scans.removed_at', 'asc')
          .orderBy('mo_scans.sew', 'desc')
          .castTo<
            ({
              scan_id: number;
              mo_id: number;
              work_fragment_id: number;
              sew: Date;
            } & WorkFragmentShape)[]
          >();

        if (
          getAllProduction.length > 0 ||
          getAllProductionFragments.length > 0
        ) {
          let customFields = [];
          //get all fragments type and his respectives columns if getallproductionfragments is not empty
          if (getAllProductionFragments.length > 0) {
            //getallproductionfragments grouped by fragment_type_id to get distinct fragment_type_id using javascript
            const distinctIds: number[] = [
              ...new Set(
                getAllProductionFragments.map((item) => item.fragment_type_id)
              ),
            ];
            const numberArray: number[] = distinctIds
              // TODO: dont need this map Number since it is a number
              .map((value) => Number(value))
              .filter((value: number) => !isNaN(value));
            //getting custom fields
            customFields = await WorkFragmentCustomFields.query()
              .whereIn('work_fragment_type_id', numberArray)
              .orderBy('id', 'asc');
          }
          return res.status(200).json({
            ok: true,
            data: { getAllProduction, getAllProductionFragments, customFields },
          });
        } else {
          return res.status(200).json({
            ok: false,
            data: 'No hay informacion completa para este grupo',
          });
        }
      } else {
        const getAllProduction = await MoScans.query()
          .join('mo_numbers', 'mo_scans.mo_id', '=', 'mo_numbers.mo_id')
          .leftJoin(
            'work_area_lines',
            'mo_scans.work_area_line_id',
            '=',
            'work_area_lines.id'
          )
          .where('mo_scans.supervisor', operator)
          .whereNull('removed_at')
          .whereNotNull('sew')
          .where(
            'mo_scans.sew',
            '>=',
            today.getFullYear() +
              '-' +
              (today.getMonth() + 1) +
              '-' +
              today.getDate() +
              'T00:00:00'
          )
          .select(
            'mo_numbers.mo_id',
            'mo_numbers.num',
            'mo_numbers.customer',
            'mo_numbers.style',
            'mo_numbers.quantity',
            'mo_numbers.required_date',
            'mo_scans.sew_ready',
            'mo_scans.sew',
            'work_area_lines.name',
            'mo_scans.work_area_group_id'
          )
          .sum('mo_scans.quantity as qtyReported')
          .groupByRaw('mo_numbers.mo_id, work_area_group_id')
          .orderBy('mo_scans.sew', 'desc');

        if (getAllProduction.length > 0) {
          return res.status(200).json({
            ok: true,
            data: getAllProduction,
          });
        } else {
          return res.status(200).json({
            ok: false,
            data: 'No hay información para el operador',
          });
        }
      }
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
      data: error,
    });
  }
}

export async function getCompleteProductionByArea(req: Request, res: Response) {
  try {
    const operator = req.body.barcode_operator;
    const ticketView = req.body.ticket_view;
    const area = req.body.area_id;
    const today = req.body.date;

    //const format1 = 'YYYY-MM-DD HH:mm:ss';
    const separator = '/';
    const dateArray = today.split(separator);

    const startDate = new Date(
      +dateArray[0],
      +dateArray[1] - 1,
      +dateArray[2],
      1,
      1,
      0
    );
    const endDate = new Date(
      +dateArray[0],
      +dateArray[1] - 1,
      +dateArray[2],
      23,
      59,
      0
    );

    if (ticketView === null || ticketView === undefined) {
      const getAllProduction = await MoScans.query()
        .join('mo_numbers', 'mo_scans.mo_id', '=', 'mo_numbers.mo_id')
        .leftJoin(
          'work_area_lines',
          'mo_scans.work_area_line_id',
          '=',
          'work_area_lines.id'
        )
        .where('mo_scans.work_area_id', area)
        .whereNull('removed_at')
        .whereNotNull('sew')
        .where('mo_scans.sew', '>=', startDate)
        .where('mo_scans.sew', '<=', endDate)
        .select([
          'mo_scans.scan_id',
          'mo_numbers.mo_id',
          'mo_numbers.num',
          'mo_numbers.customer',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.required_date',
          'mo_scans.sew_ready',
          'mo_scans.sew',
          'work_area_lines.name',
          'mo_scans.work_area_group_id',
          'mo_scans.is_repo',
          'mo_scans.removed_at',
          'mo_scans.quantity as dailyReported',
          MoScans.query()
            .where('mo_id', ref('mo_numbers.mo_id'))
            .where('mo_scans.work_area_id', area)
            .whereNotNull('sew')
            .whereNull('removed_at')
            .sum('quantity')
            .as('qtyReported'),
        ])
        .groupByRaw('mo_numbers.mo_id, work_area_group_id')
        .orderBy('mo_scans.sew', 'desc');

      if (getAllProduction.length > 0) {
        return res.status(200).json({
          ok: true,
          data: getAllProduction,
        });
      } else {
        return res.status(200).json({
          ok: false,
          data: 'No hay informacion para el area',
        });
      }
    } else {
      // para las estaciones actuales
      if (area !== undefined && area !== null) {
        const getAllProduction = await MoScans.query()
          .join('mo_numbers', 'mo_scans.mo_id', '=', 'mo_numbers.mo_id')
          .leftJoin(
            'work_area_lines',
            'mo_scans.work_area_line_id',
            '=',
            'work_area_lines.id'
          )
          .where('mo_scans.work_area_id', area)
          // .whereNull("removed_at")
          .whereNotNull('sew')
          .where('mo_scans.sew', '>=', startDate)
          .where('mo_scans.sew', '<=', endDate)
          .select([
            'mo_scans.scan_id',
            'mo_numbers.mo_id',
            'mo_numbers.num',
            'mo_numbers.customer',
            'mo_numbers.style',
            'mo_numbers.quantity',
            'mo_numbers.required_date',
            'mo_scans.sew_ready',
            'mo_scans.sew',
            'work_area_lines.name',
            'mo_scans.work_area_group_id',
            'mo_scans.work_area_id',
            'mo_scans.quantity as dailyReported',
            'mo_scans.removed_at',
            'mo_scans.is_repo',
            'mo_scans.is_manual_change',
            'mo_scans.is_repo',
            'mo_scans.is_from_scan',
            MoScans.query()
              .where('mo_id', ref('mo_numbers.mo_id'))
              .where('mo_scans.work_area_id', area)
              .whereNotNull('sew')
              .whereNull('removed_at')
              .sum('quantity')
              .as('qtyReported'),
          ])
          .groupByRaw('mo_scans.scan_id, mo_numbers.mo_id, work_area_group_id')
          .orderBy('mo_scans.removed_at', 'asc')
          .orderBy('mo_scans.sew', 'desc');

        if (getAllProduction.length > 0) {
          return res.status(200).json({
            ok: true,
            data: getAllProduction,
          });
        } else {
          return res.status(200).json({
            ok: false,
            data: 'No hay información para el grupo',
          });
        }
      } else {
        const getAllProduction = await MoScans.query()
          .join('mo_numbers', 'mo_scans.mo_id', '=', 'mo_numbers.mo_id')
          .leftJoin(
            'work_area_lines',
            'mo_scans.work_area_line_id',
            '=',
            'work_area_lines.id'
          )
          .where('mo_scans.supervisor', operator)
          .whereNull('removed_at')
          .whereNotNull('sew')
          .where('mo_scans.sew', '>=', startDate)
          .where('mo_scans.sew', '<=', endDate)
          .select(
            'mo_numbers.mo_id',
            'mo_numbers.num',
            'mo_numbers.customer',
            'mo_numbers.style',
            'mo_numbers.quantity',
            'mo_numbers.required_date',
            'mo_scans.sew_ready',
            'mo_scans.sew',
            'work_area_lines.name',
            'mo_scans.work_area_group_id',
            'mo_scans.work_area_id'
          )
          .sum('mo_scans.quantity as qtyReported')
          .groupByRaw('mo_numbers.mo_id, work_area_group_id')
          .orderBy('mo_scans.sew', 'desc');

        if (getAllProduction.length > 0) {
          return res.status(200).json({
            ok: true,
            data: getAllProduction,
          });
        } else {
          return res.status(200).json({
            ok: false,
            data: 'No hay información para el operador',
          });
        }
      }
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
      data: error,
    });
  }
}

export async function getPendingTaskStation(req: Request, res: Response) {
  const companyCode = req.body.company_code;
  const operator = req.body.barcode_operator;

  try {
    const getPendingWorkAreaTask: any = await MoNumber.query()
      .join('work_vouchers', 'mo_numbers.mo_id', '=', 'work_vouchers.mo_id')
      .join(
        'work_area_tickets',
        'work_vouchers.id',
        '=',
        'work_area_tickets.work_voucher_id'
      )
      .join(
        'work_area_tasks',
        'work_area_tickets.id',
        '=',
        'work_area_tasks.work_area_ticket_id'
      )
      .join(
        'operators',
        'work_area_tasks.operator_id',
        '=',
        'operators.operator_id'
      )
      .whereNull('work_area_tasks.finished_at')
      .whereNull('work_area_tickets.finished_at')
      .where('mo_numbers.company_code', companyCode)
      .where('operators.barcode', operator)
      .select(
        'mo_numbers.mo_id',
        'mo_numbers.num',
        'mo_numbers.customer',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'work_area_tasks.started_at'
      );

    if (getPendingWorkAreaTask.length > 0) {
      return res.status(200).json({
        ok: true,
        data: getPendingWorkAreaTask,
      });
    } else {
      return res.status(200).json({
        ok: false,
        data: 'No hay información para el operador 1',
      });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getCompleteTaskStation(req: Request, res: Response) {
  const companyCode = req.body.company_code;
  const operator = req.body.barcode_operator;

  try {
    const getCompleteWorkAreaTask: any = await MoNumber.query()
      .join('work_vouchers', 'mo_numbers.mo_id', '=', 'work_vouchers.mo_id')
      .join(
        'work_area_tickets',
        'work_vouchers.id',
        '=',
        'work_area_tickets.work_voucher_id'
      )
      .join(
        'work_area_tasks',
        'work_area_tickets.id',
        '=',
        'work_area_tasks.work_area_ticket_id'
      )
      .join(
        'operators',
        'work_area_tasks.operator_id',
        '=',
        'operators.operator_id'
      )
      .whereNotNull('work_area_tasks.finished_at')
      .whereNotNull('work_area_tasks.started_at')
      .whereNotNull('work_area_tickets.finished_at')
      .where('mo_numbers.company_code', companyCode)
      .where('operators.barcode', operator)
      .select(
        'mo_numbers.num',
        'mo_numbers.customer',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'work_area_tasks.started_at',
        'work_area_tickets.finished_at'
      );

    if (getCompleteWorkAreaTask.length > 0) {
      return res.status(200).json({
        ok: true,
        data: getCompleteWorkAreaTask,
      });
    } else {
      return res.status(200).json({
        ok: false,
        data: 'No hay información para el operador 2',
      });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getInfoOperator(req: Request, res: Response) {
  const { operatorBarcode } = req.body;

  if (!operatorBarcode || typeof operatorBarcode !== 'string') {
    return res.status(400).json({
      ok: false,
      message: 'El codigo de operador es requerido',
    });
  }

  try {
    /* verificar si el operador existe en la tabla de workgroups, si existe verificar si en workareas el campo disable_date esta NUL,
    Esta validacion se hace con el fin de mostrar informacion de tickets o informacion solo de escaneos (mo_scan)
    */
    // buscar en work area groups por medio de barcode si existe buscar ese id en work area para obtener area name y customer code 0. Sino existe retornar Falso
    const getWorkArea = await WorkAreas.query()
      .join(
        'work_area_groups',
        'work_areas.work_area_id',
        '=',
        'work_area_groups.work_area_id'
      )
      .leftJoin(
        'employees',
        'work_area_groups.supervisor_employee_id ',
        '=',
        'employees.employee_id '
      )
      .join('work_types', 'work_areas.work_type_id', '=', 'work_types.id')
      .where('work_area_groups.barcode', operatorBarcode)
      .where('work_areas.work_status_id', 50)
      .select(
        'work_area_groups.name as groupname',
        'work_types.name',
        'work_area_groups.id',
        'employees.first_name',
        'employees.last_name',
        'work_area_groups.new_version',
        'work_area_groups.update_customer',
        { type: 'work_areas.work_type_id' },
        { area: 'work_areas.work_area_id' },
        { supervisor: 'work_area_groups.supervisor_employee_id' },
        { manager: 'work_area_groups.manager_employee_id' },
        { ticket_view: 'work_areas.disabled_date' }
      )
      .castTo<
        {
          groupname: string;
          name: string;
          id: number;
          first_name: string;
          last_name: string;
          new_version: boolean;
          update_customer: boolean;
          type: number;
          area: number;
          supervisor: number | null;
          manager: number | null;
          ticket_view: Date | null;
        }[]
      >();

    if (getWorkArea.length > 0) {
      const data = {
        operator_name: getWorkArea[0].groupname,
        task: getWorkArea[0].name,
        group: getWorkArea[0].id,
        company_code: 0,
        ticket_view: getWorkArea[0].ticket_view,
        supervisor_first_name: getWorkArea[0].first_name,
        supervisor_last_name: getWorkArea[0].last_name,
        new_version: getWorkArea[0].new_version,
        area: getWorkArea[0].area,
        update_customer: getWorkArea[0].update_customer,
        type: getWorkArea[0].type,
        user:
          getWorkArea[0].supervisor === null
            ? getWorkArea[0].manager
            : getWorkArea[0].supervisor,
      };
      return res.status(200).json({
        ok: true,
        data,
      });
    } else {
      return res.status(200).json({
        ok: false,
        message: 'No existe el operador',
      });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function getMoInformationByGroup(req: Request, res: Response) {
  const mo_id = req.body.mo_id;
  const group_id = req.body.group_id;

  try {
    const getMoHistory = await MoScans.query()
      .join('mo_numbers', 'mo_numbers.mo_id', '=', 'mo_scans.mo_id')
      .where('mo_scans.mo_id', mo_id)
      .where('mo_scans.work_area_group_id', group_id)
      .whereNull('mo_scans.removed_at')
      .select(
        'mo_numbers.mo_id',
        'mo_scans.scan_id',
        'mo_numbers.num',
        'mo_numbers.customer',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_scans.quantity as qtyReported',
        'mo_numbers.required_date',
        'mo_scans.sew_ready',
        'mo_scans.is_repo',
        'mo_scans.sew'
      )
      .orderBy('mo_scans.sew', 'asc');

    if (getMoHistory.length > 0) {
      return res.status(200).json({
        ok: true,
        data: getMoHistory,
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'empty',
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function getMoInfoByBarcode(req: Request, res: Response) {
  const {
    mo_id,
    mo_quantity,
    customer,
    mo_status,
    num,
    style,
    style_id,
    group_id,
    mo_order,
    required_date,
    style_category,
    product_category,
  } = req.body;

  try {
    let qtyReported = 0;
    let qtyRepoReported = 0;
    // get mo information
    const reportedScan = await MoScans.query()
      .select(raw('sum(quantity)').as('quantity'))
      .where('mo_id', mo_id)
      .where('work_area_group_id ', group_id)
      .whereNotNull('sew')
      .whereNull('removed_at')
      .where('is_repo', false);

    if (reportedScan.length > 0) {
      qtyReported = reportedScan[0].quantity;
    }

    const reportedRepoScan = await MoScans.query()
      .select(raw('sum(quantity)').as('quantity'))
      .where('mo_id', mo_id)
      .where('work_area_group_id ', group_id)
      .whereNotNull('sew')
      .whereNull('removed_at')
      .where('is_repo', true);

    if (reportedRepoScan.length > 0) {
      qtyRepoReported = reportedRepoScan[0].quantity;
    }

    return res.status(200).json({
      ok: true,
      data: {
        mo_id,
        mo_quantity,
        customer,
        mo_status,
        num,
        style,
        style_id,
        group_id,
        qtyReported,
        qtyRepoReported,
        mo_order,
        required_date,
        style_category,
        product_category,
      },
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getEmployeeInfo(req: Request, res: Response) {
  const employee_id = req.body.employee_id;

  try {
    const getEmployee = await Employee.query()
      .where('employee_id', employee_id)
      .select('employee_id', 'first_name', 'last_name');

    if (getEmployee.length > 0) {
      return res.status(200).json({
        ok: true,
        data: getEmployee,
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'empty',
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function getEmployeeOrOperatorFromBarcode(
  req: Request,
  res: Response
) {
  const barcode = req.body.barcode;

  try {
    const getEmployee = await Employee.query()

      .where('barcode', barcode)
      .select('employee_id', 'first_name', 'last_name', 'barcode');

    if (getEmployee.length == 1) {
      return res.status(200).json({
        ok: true,
        data: {
          type: 'employee',
          employee: getEmployee[0],
        },
      });
    }

    const getOperator = await WorkAreaGroups.query()
      .where('barcode', barcode)
      .select('operator_id', 'first_name', 'last_name', 'barcode');

    if (getOperator.length == 1) {
      return res.status(200).json({
        ok: true,
        data: {
          type: 'operator',
          operator: getOperator[0],
        },
      });
    }

    return res.status(400).json({
      ok: false,
      message: 'empty',
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function createVoucherMO(req: Request, res: Response) {
  try {
    const {
      transfers,
      data: { group, line, primary, is_repo, location, mo, voucher_type },
      employee_code,
      area,
      nextArea,
    } = req.body;
    const closeTickets = [100, 105, 110];
    const { value: location_id } = location;

    let company_code: number;
    let barcode: string;
    let ticketToReceiveByMoBarcode: any[];

    if (mo.startsWith('P')) {
      company_code = 1;
      barcode = mo;
    } else if (mo.startsWith('A')) {
      company_code = 2;
      barcode = mo.slice(1);
    } else if (mo.includes('-') || mo.includes('/')) {
      company_code = 3;
      barcode = mo.replace('-', '/');
    }

    // else {

    //   ticketToReceiveByMoBarcode = await WorkVouchers.query()
    //     .leftJoin(
    //       'work_area_tickets',
    //       'work_vouchers.id',
    //       'work_area_tickets.work_voucher_id'
    //     )
    //     .join(
    //       'work_voucher_types',
    //       'work_vouchers.work_voucher_type_id',
    //       'work_voucher_types.id'
    //     )
    //     .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
    //     .join(
    //       'work_areas',
    //       'work_area_tickets.work_area_id',
    //       'work_areas.work_area_id'
    //     )
    //     .leftJoin(
    //       'work_area_ticket_statuses',
    //       'work_area_tickets.work_area_ticket_status_id',
    //       'work_area_ticket_statuses.id'
    //     )
    //     .leftJoin(
    //       'work_statuses',
    //       'work_area_ticket_statuses.work_status_id',
    //       'work_statuses.id'
    //     )
    //     .where('work_vouchers.ignore_next_area', false)
    //     .where('work_statuses.id', '<>', '110')
    //     .where('work_area_tickets.next_work_area_id', area)
    //     .where('mo_numbers.num', mo)
    //     .whereNull('work_vouchers.work_voucher_group_id')
    //     .whereNull('work_vouchers.merge_voucher_id')
    //     .select(
    //       'mo_numbers.mo_id',
    //       'mo_numbers.mo_status',
    //       'mo_numbers.customer',
    //       {
    //         work_status: 'work_statuses.id',
    //       },
    //       { work_voucher_id: 'work_vouchers.id' },
    //       { work_area_ticket_id: 'work_area_tickets.id' },
    //       { work_area_id: 'work_areas.work_area_id' },
    //       { work_area_name: 'work_areas.area_name' },
    //       { status_name: 'work_area_ticket_statuses.name' },
    //       { work_voucher_type_id: 'work_voucher_types.id' },
    //       { voucher_type: 'work_voucher_types.name' },
    //       { forMyArea: 'work_area_tickets.next_work_area_id' }
    //     );

    //   console.log(ticketToReceiveByMoBarcode);

    //   if (ticketToReceiveByMoBarcode.length > 0) {
    //     const getMoInfo = await WorkAreaTickets.query()
    //       .select([
    //         { voucher_id: 'work_vouchers.id' },
    //         { voucher_type: 'work_voucher_types.name' },
    //       ])
    //       .leftJoin(
    //         'work_areas',
    //         'work_area_tickets.work_area_id',
    //         'work_areas.work_area_id'
    //       )
    //       .leftJoin(
    //         'work_vouchers',
    //         'work_area_tickets.work_voucher_id',
    //         'work_vouchers.id'
    //       )
    //       .leftJoin(
    //         'mo_numbers',
    //         'work_vouchers.mo_id',
    //         '=',
    //         'mo_numbers.mo_id'
    //       )
    //       .leftJoin(
    //         'work_voucher_types',
    //         'work_vouchers.work_voucher_type_id',
    //         'work_voucher_types.id'
    //       )
    //       .leftJoin(
    //         'work_area_ticket_statuses',
    //         'work_area_tickets.work_area_ticket_status_id',
    //         '=',
    //         'work_area_ticket_statuses.id'
    //       )
    //       .leftJoin(
    //         'work_statuses',
    //         'work_area_ticket_statuses.work_status_id',
    //         '=',
    //         'work_statuses.id'
    //       )
    //       .leftJoin(
    //         'work_inventory_bins',
    //         'work_area_tickets.work_inventory_location_id',
    //         'work_inventory_bins.id'
    //       )
    //       .where('mo_numbers.num', mo)
    //       .where('work_area_tickets.work_area_id', area)
    //       .whereNotIn('work_statuses.id', closeTickets);

    //     const orderedInfo = [];

    //     const closedMarked = ticketToReceiveByMoBarcode.filter(
    //       (ticket: { forMyArea: string | number; work_status: number }) => {
    //         if (
    //           +ticket.forMyArea === area &&
    //           closeTickets.includes(ticket.work_status)
    //         ) {
    //           return { ...ticket, active: true };
    //         }
    //       }
    //     );

    //     if (closedMarked.length > 0) {
    //       orderedInfo.push({ ...closedMarked[0], active: true });
    //     }

    //     const closed = ticketToReceiveByMoBarcode.filter(
    //       (ticket: { forMyArea: null; work_status: number }) => {
    //         if (
    //           ticket.forMyArea === null &&
    //           closeTickets.includes(ticket.work_status)
    //         ) {
    //           return ticket;
    //         }
    //       }
    //     );

    //     if (closed.length > 0) {
    //       orderedInfo.push({ ...closed[0], active: true });
    //     }

    //     const openMarked = ticketToReceiveByMoBarcode.filter(
    //       (ticket: { forMyArea: string | number; work_status: number }) => {
    //         if (+ticket.forMyArea === area && +ticket.work_status === 50) {
    //           return ticket;
    //         }
    //       }
    //     );

    //     if (openMarked.length > 0) {
    //       orderedInfo.push({ ...openMarked[0], active: false });
    //     }

    //     const open = ticketToReceiveByMoBarcode.filter(
    //       (ticket: { forMyArea: string | number; work_status: number }) => {
    //         if (ticket.forMyArea === null && +ticket.work_status === 50) {
    //           return ticket;
    //         }
    //       }
    //     );

    //     if (open.length > 0) {
    //       orderedInfo.push({ ...open[0], active: false });
    //     }

    //     return res.status(200).json({
    //       ok: true,
    //       message: 'Existen vouchers con la mo que estas ingresando',
    //       vouchersForMe: true,
    //       data: orderedInfo,
    //       vouchersInArea: getMoInfo,
    //     });
    //   } else {
    //     return res.status(200).json({
    //       ok: true,
    //       message: 'No hay voucher para recibir',
    //       vouchersForMe: false,
    //     });
    //   }
    // }

    if (transfers) {
      if (mo.startsWith('AP')) {
        ticketToReceiveByMoBarcode = await WorkVouchers.query()
          .leftJoin(
            'work_area_tickets',
            'work_vouchers.id',
            'work_area_tickets.work_voucher_id'
          )
          .join(
            'work_voucher_types',
            'work_vouchers.work_voucher_type_id',
            'work_voucher_types.id'
          )
          .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
          .join(
            'work_areas',
            'work_area_tickets.work_area_id',
            'work_areas.work_area_id'
          )
          .leftJoin(
            'work_area_ticket_statuses',
            'work_area_tickets.work_area_ticket_status_id',
            'work_area_ticket_statuses.id'
          )
          .leftJoin(
            'work_statuses',
            'work_area_ticket_statuses.work_status_id',
            'work_statuses.id'
          )
          .where('work_vouchers.ignore_next_area', false)
          .where('work_statuses.id', '<>', '110')
          .where('work_area_tickets.next_work_area_id', area)
          .where('mo_numbers.mo_barcode', barcode)
          .whereNull('work_vouchers.work_voucher_group_id')
          .whereNull('work_vouchers.merge_voucher_id')
          .select(
            'mo_numbers.mo_id',
            'mo_numbers.mo_status',
            'mo_numbers.customer',
            {
              work_status: 'work_statuses.id',
            },
            { work_voucher_id: 'work_vouchers.id' },
            { work_area_ticket_id: 'work_area_tickets.id' },
            { work_area_id: 'work_areas.work_area_id' },
            { work_area_name: 'work_areas.area_name' },
            { status_name: 'work_area_ticket_statuses.name' },
            { work_voucher_type_id: 'work_voucher_types.id' },
            { voucher_type: 'work_voucher_types.name' },
            { forMyArea: 'work_area_tickets.next_work_area_id' }
          );
      } else {
        ticketToReceiveByMoBarcode = await WorkVouchers.query()
          .leftJoin(
            'work_area_tickets',
            'work_vouchers.id',
            'work_area_tickets.work_voucher_id'
          )
          .join(
            'work_voucher_types',
            'work_vouchers.work_voucher_type_id',
            'work_voucher_types.id'
          )
          .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
          .join(
            'work_areas',
            'work_area_tickets.work_area_id',
            'work_areas.work_area_id'
          )
          .leftJoin(
            'work_area_ticket_statuses',
            'work_area_tickets.work_area_ticket_status_id',
            'work_area_ticket_statuses.id'
          )
          .leftJoin(
            'work_statuses',
            'work_area_ticket_statuses.work_status_id',
            'work_statuses.id'
          )
          .where('work_vouchers.ignore_next_area', false)
          .where('work_statuses.id', '<>', '110')
          .where('work_area_tickets.next_work_area_id', area)
          .where('mo_numbers.mo_barcode', barcode)
          .where('mo_numbers.company_code', '<>', 2)
          .whereNull('work_vouchers.work_voucher_group_id')
          .whereNull('work_vouchers.merge_voucher_id')
          .select(
            'mo_numbers.mo_id',
            'mo_numbers.mo_status',
            'mo_numbers.customer',
            {
              work_status: 'work_statuses.id',
            },
            { work_voucher_id: 'work_vouchers.id' },
            { work_area_ticket_id: 'work_area_tickets.id' },
            { work_area_id: 'work_areas.work_area_id' },
            { work_area_name: 'work_areas.area_name' },
            { status_name: 'work_area_ticket_statuses.name' },
            { work_voucher_type_id: 'work_voucher_types.id' },
            { voucher_type: 'work_voucher_types.name' },
            { forMyArea: 'work_area_tickets.next_work_area_id' }
          );
      }

      if (ticketToReceiveByMoBarcode.length > 0) {
        const getMoInfo = await WorkAreaTickets.query()
          .select([
            { voucher_id: 'work_vouchers.id' },
            { voucher_type: 'work_voucher_types.name' },
          ])
          .leftJoin(
            'work_areas',
            'work_area_tickets.work_area_id',
            'work_areas.work_area_id'
          )
          .leftJoin(
            'work_vouchers',
            'work_area_tickets.work_voucher_id',
            'work_vouchers.id'
          )
          .leftJoin(
            'mo_numbers',
            'work_vouchers.mo_id',
            '=',
            'mo_numbers.mo_id'
          )
          .leftJoin(
            'work_voucher_types',
            'work_vouchers.work_voucher_type_id',
            'work_voucher_types.id'
          )
          .leftJoin(
            'work_area_ticket_statuses',
            'work_area_tickets.work_area_ticket_status_id',
            '=',
            'work_area_ticket_statuses.id'
          )
          .leftJoin(
            'work_statuses',
            'work_area_ticket_statuses.work_status_id',
            '=',
            'work_statuses.id'
          )
          .leftJoin(
            'work_inventory_bins',
            'work_area_tickets.work_inventory_location_id',
            'work_inventory_bins.id'
          )
          .where('mo_numbers.mo_barcode', barcode)
          .where('mo_numbers.company_code', company_code)
          .where('work_area_tickets.work_area_id', area)
          .whereNotIn('work_statuses.id', closeTickets);

        const orderedInfo = [];

        const closedMarked = ticketToReceiveByMoBarcode.filter(
          (ticket: { forMyArea: string | number; work_status: number }) => {
            if (
              +ticket.forMyArea === area &&
              closeTickets.includes(ticket.work_status)
            ) {
              return { ...ticket, active: true };
            }
          }
        );

        if (closedMarked.length > 0) {
          orderedInfo.push({ ...closedMarked[0], active: true });
        }

        const closed = ticketToReceiveByMoBarcode.filter(
          (ticket: { forMyArea: null; work_status: number }) => {
            if (
              ticket.forMyArea === null &&
              closeTickets.includes(ticket.work_status)
            ) {
              return ticket;
            }
          }
        );

        if (closed.length > 0) {
          orderedInfo.push({ ...closed[0], active: true });
        }

        const openMarked = ticketToReceiveByMoBarcode.filter(
          (ticket: { forMyArea: string | number; work_status: number }) => {
            if (+ticket.forMyArea === area && +ticket.work_status === 50) {
              return ticket;
            }
          }
        );

        if (openMarked.length > 0) {
          orderedInfo.push({ ...openMarked[0], active: false });
        }

        const open = ticketToReceiveByMoBarcode.filter(
          (ticket: { forMyArea: string | number; work_status: number }) => {
            if (ticket.forMyArea === null && +ticket.work_status === 50) {
              return ticket;
            }
          }
        );

        if (open.length > 0) {
          orderedInfo.push({ ...open[0], active: false });
        }

        return res.status(200).json({
          ok: true,
          message: 'Existen vouchers con la mo que estas ingresando',
          vouchersForMe: true,
          data: orderedInfo,
          vouchersInArea: getMoInfo,
        });
      } else {
        return res.status(200).json({
          ok: true,
          message: 'No hay voucher para recibir',
          vouchersForMe: false,
        });
      }
    }

    if (voucher_type?.label) {
      const { value: voucher_type_id } = voucher_type;

      const get_mo_info = await MoNumber.query()
        .whereNotIn('mo_numbers.mo_status', ['Void', 'Cancelled', 'Materials'])
        .where('mo_numbers.mo_barcode', barcode)
        .where('mo_numbers.company_code', company_code)
        .select('mo_numbers.mo_id', 'mo_numbers.num')
        .castTo<
          {
            mo_id: number;
            num: string;
          }[]
        >();

      if (is_repo) {
        if (get_mo_info.length > 0) {
          const get_status = await WorkAreaTicketStatuses.query()
            .where('work_area_id', area)
            .where('name', 'Nuevo')
            .select('id');

          if (get_status.length > 0) {
            // TODO: repeated code in voucher.ts file
            await WorkVouchers.transaction(async (trx) => {
              const voucher = await WorkVouchers.query(trx)
                .insertGraph({
                  mo_id: get_mo_info[0].mo_id,
                  work_voucher_type_id: voucher_type_id,
                  is_primary: primary,
                  is_repo: is_repo,
                  workVouchersWorkTickets: [
                    {
                      work_area_id: area,
                      made_by_mo_scan: 0,
                      notify_company: 1,
                      is_company_notified: 0,
                      exp_work_area_group_id: group?.value || null,
                      next_work_area_id: nextArea || null,
                      exp_work_area_line_id: line?.value || null,
                      work_area_ticket_status_id: get_status[0].id,
                      work_inventory_location_id: location_id
                        ? location_id
                        : null,
                    },
                  ],
                } as PartialModelGraph<WorkVouchers>)
                .castTo<
                  WorkVouchers & { workVouchersWorkTickets: WorkAreaTickets[] }
                >();

              await WorkActivityLog.query().insert({
                employee_id: employee_code,
                work_area_id: area,
                module_name: 'ticket',
                module_id: voucher.workVouchersWorkTickets[0].id,
                activity: 'TicketCreated',
                data: JSON.stringify({}),
              });

              if (voucher) {
                return res.status(200).json({
                  ok: true,
                  mo: get_mo_info[0].num,
                  voucher: voucher.id,
                  is_primary: voucher.is_primary,
                  voucher_type_id: voucher.work_voucher_type_id,
                  message:
                    'Felicidades, se creo el voucher y el ticket en el area',
                });
              } else {
                return res.status(400).json({
                  ok: false,
                  message:
                    'Error, No puede crear el voucher y el ticket en el area',
                });
              }
            });
          } else {
            const create_status = await WorkAreaTicketStatuses.query().insert({
              work_area_id: area,
              name: 'Nuevo',
              work_status_id: 50,
              sequence: 1000,
            });

            if (create_status) {
              await WorkVouchers.transaction(async (trx) => {
                const voucher = await WorkVouchers.query(trx)
                  .insertGraph({
                    mo_id: get_mo_info[0].mo_id,
                    work_voucher_type_id: voucher_type_id,
                    is_primary: primary,
                    is_repo: is_repo,
                    workVouchersWorkTickets: [
                      {
                        work_area_id: area,
                        made_by_mo_scan: 0,
                        notify_company: 1,
                        is_company_notified: 0,
                        exp_work_area_group_id: group?.value || null,
                        next_work_area_id: nextArea || null,
                        exp_work_area_line_id: line?.value || null,
                        work_area_ticket_status_id: create_status.id,
                        work_inventory_location_id: location_id
                          ? location_id
                          : null,
                      },
                    ],
                  } as PartialModelGraph<WorkVouchers>)
                  .castTo<
                    WorkVouchers & {
                      workVouchersWorkTickets: WorkAreaTickets[];
                    }
                  >();

                await WorkActivityLog.query().insert({
                  employee_id: employee_code,
                  work_area_id: area,
                  module_name: 'ticket',
                  module_id: voucher.workVouchersWorkTickets[0].id,
                  activity: 'TicketCreated',
                  data: JSON.stringify({}),
                });

                if (voucher) {
                  return res.status(200).json({
                    ok: true,
                    mo: get_mo_info[0].num,
                    voucher: voucher.id,
                    is_primary: voucher.is_primary,
                    voucher_type_id: voucher.work_voucher_type_id,
                    message:
                      'Felicidades, se creo el voucher y el ticket en el area',
                  });
                } else {
                  return res.status(400).json({
                    ok: false,
                    message:
                      'Error, No puede crear el voucher y el ticket en el area',
                  });
                }
              });
            } else {
              return res.status(400).json({
                ok: false,
                message: "Error, No puede crear el estado del ticket 'NUEVO'",
              });
            }
          }
        } else {
          return res.status(400).json({
            ok: false,
            message:
              'Error en la MO, recuerda que es necesario que la MO este activa',
          });
        }
      } else {
        if (get_mo_info.length > 0) {
          const getVoucherInfo = await WorkVouchers.query()
            .join(
              'work_area_tickets',
              'work_vouchers.id',
              'work_area_tickets.work_voucher_id'
            )
            .join(
              'work_area_ticket_statuses',
              'work_area_tickets.work_area_ticket_status_id',
              'work_area_ticket_statuses.id'
            )
            .join(
              'work_statuses',
              'work_area_ticket_statuses.work_status_id',
              'work_statuses.id'
            )
            .where('work_statuses.id', '<>', '110')
            .where('work_vouchers.mo_id', get_mo_info[0].mo_id)
            .where('work_vouchers.work_voucher_type_id', voucher_type_id)
            .where('work_area_tickets.work_area_id', area)
            .select('work_vouchers.id');

          if (getVoucherInfo.length > 0) {
            return res.status(400).json({
              ok: false,
              message:
                'Error, ya existe un voucher y ticket con el mismo voucher type en el area',
            });
          } else {
            const get_status = await WorkAreaTicketStatuses.query()
              .where('work_area_id', area)
              .where('name', 'Nuevo')
              .select('id');

            if (get_status.length > 0) {
              await WorkVouchers.transaction(async (trx) => {
                const voucher = await WorkVouchers.query(trx)
                  .insertGraph({
                    mo_id: get_mo_info[0].mo_id,
                    work_voucher_type_id: voucher_type_id,
                    is_primary: primary,
                    is_repo: is_repo,
                    workVouchersWorkTickets: [
                      {
                        work_area_id: area,
                        made_by_mo_scan: 0,
                        notify_company: 1,
                        is_company_notified: 0,
                        exp_work_area_group_id: group?.value || null,
                        next_work_area_id: nextArea || null,
                        exp_work_area_line_id: line?.value || null,
                        work_area_ticket_status_id: get_status[0].id,
                        work_inventory_location_id: location_id
                          ? location_id
                          : null,
                      },
                    ],
                  } as PartialModelGraph<WorkVouchers>)
                  .castTo<
                    WorkVouchers & {
                      workVouchersWorkTickets: WorkAreaTickets[];
                    }
                  >();

                await WorkActivityLog.query().insert({
                  employee_id: employee_code,
                  work_area_id: area,
                  module_name: 'ticket',
                  module_id: voucher.workVouchersWorkTickets[0].id,
                  activity: 'TicketCreated',
                  data: JSON.stringify({}),
                });

                if (voucher) {
                  return res.status(200).json({
                    ok: true,
                    mo: get_mo_info[0].num,
                    voucher: voucher.id,
                    is_primary: voucher.is_primary,
                    voucher_type_id: voucher.work_voucher_type_id,
                    message:
                      'Felicidades, se creo el voucher y el ticket en el area',
                  });
                } else {
                  return res.status(400).json({
                    ok: false,
                    message:
                      'Error, No puede crear el voucher y el ticket en el area',
                  });
                }
              });
            } else {
              const create_status = await WorkAreaTicketStatuses.query().insert(
                {
                  work_area_id: area,
                  name: 'Nuevo',
                  work_status_id: 50,
                  sequence: 1000,
                }
              );

              if (create_status) {
                await WorkVouchers.transaction(async (trx) => {
                  const voucher = await WorkVouchers.query(trx)
                    .insertGraph({
                      mo_id: get_mo_info[0].mo_id,
                      work_voucher_type_id: voucher_type_id,
                      is_primary: primary,
                      is_repo: is_repo,
                      workVouchersWorkTickets: [
                        {
                          work_area_id: area,
                          made_by_mo_scan: 0,
                          notify_company: 1,
                          is_company_notified: 0,
                          exp_work_area_group_id: group?.value || null,
                          next_work_area_id: nextArea || null,
                          exp_work_area_line_id: line?.value || null,
                          work_area_ticket_status_id: create_status.id,
                          work_inventory_location_id: location_id
                            ? location_id
                            : null,
                        },
                      ],
                    } as PartialModelGraph<WorkVouchers>)
                    .castTo<
                      WorkVouchers & {
                        workVouchersWorkTickets: WorkAreaTickets[];
                      }
                    >();

                  await WorkActivityLog.query().insert({
                    employee_id: employee_code,
                    work_area_id: area,
                    module_name: 'ticket',
                    module_id: voucher.workVouchersWorkTickets[0].id,
                    activity: 'TicketCreated',
                    data: JSON.stringify({}),
                  });

                  if (voucher) {
                    return res.status(200).json({
                      ok: true,
                      mo: get_mo_info[0].num,
                      voucher: voucher.id,
                      is_primary: voucher.is_primary,
                      voucher_type_id: voucher.work_voucher_type_id,
                      message:
                        'Felicidades, se creo el voucher y el ticket en el area',
                    });
                  } else {
                    return res.status(400).json({
                      ok: false,
                      message:
                        'Error, No puede crear el voucher y el ticket en el area',
                    });
                  }
                });
              } else {
                return res.status(400).json({
                  ok: false,
                  message: "Error, No puede crear el estado del ticket 'NUEVO'",
                });
              }
            }
          }
        } else {
          return res.status(400).json({
            ok: false,
            message:
              'Error en la MO, recuerda que es necesario que la MO este activa',
          });
        }
      }
    } else {
      const voucher_type_id = await WorkAreas.query()
        .where('work_areas.work_area_id', area)
        .select('work_areas.default_work_voucher_type_id');

      const get_mo_info: any = await MoNumber.query()
        .whereNotIn('mo_numbers.mo_status', ['Void', 'Cancelled', 'Materials'])
        .where('mo_numbers.mo_barcode', barcode)
        .where('mo_numbers.company_code', company_code)
        .select('mo_numbers.mo_id', 'mo_numbers.num');

      if (is_repo) {
        if (get_mo_info.length > 0) {
          const get_status = await WorkAreaTicketStatuses.query()
            .where('work_area_id', area)
            .where('name', 'Nuevo')
            .select('id');

          if (get_status.length > 0) {
            await WorkVouchers.transaction(async (trx) => {
              const voucher = await WorkVouchers.query(trx)
                .insertGraph({
                  mo_id: get_mo_info[0].mo_id,
                  work_voucher_type_id:
                    voucher_type_id[0].default_work_voucher_type_id,
                  is_primary: primary,
                  is_repo: is_repo,
                  workVouchersWorkTickets: [
                    {
                      work_area_id: area,
                      made_by_mo_scan: 0,
                      notify_company: 1,
                      is_company_notified: 0,
                      exp_work_area_group_id: group?.value || null,
                      next_work_area_id: nextArea || null,
                      exp_work_area_line_id: line?.value || null,
                      work_area_ticket_status_id: get_status[0].id,
                      work_inventory_location_id: location_id
                        ? location_id
                        : null,
                    },
                  ],
                } as PartialModelGraph<WorkVouchers>)
                .castTo<
                  WorkVouchers & { workVouchersWorkTickets: WorkAreaTickets[] }
                >();

              await WorkActivityLog.query().insert({
                employee_id: employee_code,
                work_area_id: area,
                module_name: 'ticket',
                module_id: voucher.workVouchersWorkTickets[0].id,
                activity: 'TicketCreated',
                data: JSON.stringify({}),
              });

              if (voucher) {
                return res.status(200).json({
                  ok: true,
                  mo: get_mo_info[0].num,
                  voucher: voucher.id,
                  is_primary: voucher.is_primary,
                  voucher_type_id: voucher.work_voucher_type_id,
                  message:
                    'Felicidades, se creo el voucher y el ticket en el area',
                });
              } else {
                return res.status(400).json({
                  ok: false,
                  message:
                    'Error, No puede crear el voucher y el ticket en el area',
                });
              }
            });
          } else {
            const create_status = await WorkAreaTicketStatuses.query().insert({
              work_area_id: area,
              name: 'Nuevo',
              work_status_id: 50,
              sequence: 1000,
            });

            if (create_status) {
              await WorkVouchers.transaction(async (trx) => {
                const voucher = await WorkVouchers.query(trx)
                  .insertGraph({
                    mo_id: get_mo_info[0].mo_id,
                    work_voucher_type_id:
                      voucher_type_id[0].default_work_voucher_type_id,
                    is_primary: primary,
                    is_repo: is_repo,
                    workVouchersWorkTickets: [
                      {
                        work_area_id: area,
                        made_by_mo_scan: 0,
                        notify_company: 1,
                        is_company_notified: 0,
                        exp_work_area_group_id: group?.value || null,
                        next_work_area_id: nextArea || null,
                        exp_work_area_line_id: line?.value || null,
                        work_area_ticket_status_id: create_status.id,
                        work_inventory_location_id: location_id
                          ? location_id
                          : null,
                      },
                    ],
                  } as PartialModelGraph<WorkVouchers>)
                  .castTo<
                    WorkVouchers & {
                      workVouchersWorkTickets: WorkAreaTickets[];
                    }
                  >();

                await WorkActivityLog.query().insert({
                  employee_id: employee_code,
                  work_area_id: area,
                  module_name: 'ticket',
                  module_id: voucher.workVouchersWorkTickets[0].id,
                  activity: 'TicketCreated',
                  data: JSON.stringify({}),
                });

                if (voucher) {
                  return res.status(200).json({
                    ok: true,
                    mo: get_mo_info[0].num,
                    voucher: voucher.id,
                    is_primary: voucher.is_primary,
                    voucher_type_id: voucher.work_voucher_type_id,
                    message:
                      'Felicidades, se creo el voucher y el ticket en el area',
                  });
                } else {
                  return res.status(400).json({
                    ok: false,
                    message:
                      'Error, No puede crear el voucher y el ticket en el area',
                  });
                }
              });
            } else {
              return res.status(400).json({
                ok: false,
                message: "Error, No puede crear el estado del ticket 'NUEVO'",
              });
            }
          }
        } else {
          return res.status(400).json({
            ok: false,
            message:
              'Error en la MO, recuerda que es necesario que la MO este activa',
          });
        }
      } else {
        if (get_mo_info.length > 0) {
          const getVoucherInfo = await WorkVouchers.query()
            .join(
              'work_area_tickets',
              'work_vouchers.id',
              'work_area_tickets.work_voucher_id'
            )
            .join(
              'work_area_ticket_statuses',
              'work_area_tickets.work_area_ticket_status_id',
              'work_area_ticket_statuses.id'
            )
            .join(
              'work_statuses',
              'work_area_ticket_statuses.work_status_id',
              'work_statuses.id'
            )
            .where('work_statuses.id', '<>', '110')
            .where('work_vouchers.mo_id', get_mo_info[0].mo_id)
            .where(
              'work_vouchers.work_voucher_type_id',
              voucher_type_id[0].default_work_voucher_type_id
            )
            .where('work_area_tickets.work_area_id', area)
            .select('work_vouchers.id');

          if (getVoucherInfo.length > 0) {
            return res.status(400).json({
              ok: false,
              message:
                'Error, ya existe un voucher y ticket con el mismo voucher type en el area',
            });
          } else {
            const get_status = await WorkAreaTicketStatuses.query()
              .where('work_area_id', area)
              .where('name', 'Nuevo')
              .select('id');

            if (get_status.length > 0) {
              await WorkVouchers.transaction(async (trx) => {
                const voucher = await WorkVouchers.query(trx)
                  .insertGraph({
                    mo_id: get_mo_info[0].mo_id,
                    work_voucher_type_id:
                      voucher_type_id[0].default_work_voucher_type_id,
                    is_primary: primary,
                    is_repo: is_repo,
                    workVouchersWorkTickets: [
                      {
                        work_area_id: area,
                        made_by_mo_scan: 0,
                        notify_company: 1,
                        is_company_notified: 0,
                        exp_work_area_group_id: group?.value || null,
                        next_work_area_id: nextArea || null,
                        exp_work_area_line_id: line?.value || null,
                        work_area_ticket_status_id: get_status[0].id,
                        work_inventory_location_id: location_id
                          ? location_id
                          : null,
                      },
                    ],
                  } as PartialModelGraph<WorkVouchers>)
                  .castTo<
                    WorkVouchers & {
                      workVouchersWorkTickets: WorkAreaTickets[];
                    }
                  >();

                await WorkActivityLog.query().insert({
                  employee_id: employee_code,
                  work_area_id: area,
                  module_name: 'ticket',
                  module_id: voucher.workVouchersWorkTickets[0].id,
                  activity: 'TicketCreated',
                  data: JSON.stringify({}),
                });

                if (voucher) {
                  return res.status(200).json({
                    ok: true,
                    mo: get_mo_info[0].num,
                    voucher: voucher.id,
                    is_primary: voucher.is_primary,
                    voucher_type_id: voucher.work_voucher_type_id,
                    message:
                      'Felicidades, se creo el voucher y el ticket en el area',
                  });
                } else {
                  return res.status(400).json({
                    ok: false,
                    message:
                      'Error, No puede crear el voucher y el ticket en el area',
                  });
                }
              });
            } else {
              const create_status = await WorkAreaTicketStatuses.query().insert(
                {
                  work_area_id: area,
                  name: 'Nuevo',
                  work_status_id: 50,
                  sequence: 1000,
                }
              );

              if (create_status) {
                await WorkVouchers.transaction(async (trx) => {
                  const voucher = await WorkVouchers.query(trx)
                    .insertGraph({
                      mo_id: get_mo_info[0].mo_id,
                      work_voucher_type_id:
                        voucher_type_id[0].default_work_voucher_type_id,
                      is_primary: primary,
                      is_repo: is_repo,
                      workVouchersWorkTickets: [
                        {
                          work_area_id: area,
                          made_by_mo_scan: 0,
                          notify_company: 1,
                          is_company_notified: 0,
                          exp_work_area_group_id: group?.value || null,
                          next_work_area_id: nextArea || null,
                          exp_work_area_line_id: line?.value || null,
                          work_area_ticket_status_id: create_status.id,
                          work_inventory_location_id: location_id
                            ? location_id
                            : null,
                        },
                      ],
                    } as PartialModelGraph<WorkVouchers>)
                    .castTo<
                      WorkVouchers & {
                        workVouchersWorkTickets: WorkAreaTickets[];
                      }
                    >();

                  await WorkActivityLog.query().insert({
                    employee_id: employee_code,
                    work_area_id: area,
                    module_name: 'ticket',
                    module_id: voucher.workVouchersWorkTickets[0].id,
                    activity: 'TicketCreated',
                    data: JSON.stringify({}),
                  });

                  if (voucher) {
                    return res.status(200).json({
                      ok: true,
                      mo: get_mo_info[0].num,
                      voucher: voucher.id,
                      is_primary: voucher.is_primary,
                      voucher_type_id: voucher.work_voucher_type_id,
                      message:
                        'Felicidades, se creo el voucher y el ticket en el area',
                    });
                  } else {
                    return res.status(400).json({
                      ok: false,
                      message:
                        'Error, No puede crear el voucher y el ticket en el area',
                    });
                  }
                });
              } else {
                return res.status(400).json({
                  ok: false,
                  message: "Error, No puede crear el estado del ticket 'NUEVO'",
                });
              }
            }
          }
        } else {
          return res.status(400).json({
            ok: false,
            message:
              'Error en la MO, recuerda que es necesario que la MO este activa',
          });
        }
      }
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: `Error, ${error}`,
    });
  }
}

/*async function insertEmployeeToShiftFromCreatedShift(
  shiftId: number,
  groupId: number,
  startDate: string,
  endDate: string
) {
  // try {
  const shift_id = shiftId;
  const group_id = groupId;
  const returnArray = [];
  const format1 = 'YYYY-MM-DD HH:mm:ss';
  const isCounted = false;
  // in case shift id was sent
  const getAllEmployees = await WorkAreaGroupEmployees.query().where(
    'work_group_id',
    group_id
  );

  // update shift
  for (const e of getAllEmployees) {
    if (e.employee_id !== null) {
      // check if emp is no in other area in active
      const checkEmployeeInOtherArea = await WorkAreaGroupShiftEmployee.query()
        .join(
          'work_area_group_shifts',
          'work_area_group_shift_employees.work_area_group_shift_id',
          '=',
          'work_area_group_shifts.id'
        )
        .join(
          'work_employee_states',
          'work_area_group_shift_employees.work_employee_state_id',
          '=',
          'work_employee_states.id'
        )
        .where(
          'work_area_group_shifts.start_datetime_sv',
          '>=',
          dayjs(startDate).format(format1)
        )
        .where(
          'work_area_group_shifts.end_datetime_sv ',
          '<=',
          dayjs(endDate).format(format1)
        )
        .where('work_area_group_shift_employees.employee_id', e.employee_id)
        .select(
          'work_area_group_shift_employees.work_area_group_shift_id',
          'work_area_group_shift_employees.employee_id',
          'work_area_group_shift_employees.is_counted',
          'work_employee_states.is_present'
        );

      if (
        checkEmployeeInOtherArea === null ||
        checkEmployeeInOtherArea.length === 0
      ) {
        // not found in other area. insert into shift
        const addEmpShift = await WorkAreaGroupShiftEmployee.query().insert({
          work_area_group_shift_id: shift_id,
          employee_id: e.employee_id,
          is_counted: isCounted,
        });

        if (addEmpShift.id > 0) {
          returnArray.push({
            employee_id: e.employee_id,
            shift_employee_id: addEmpShift.id,
            action: 'added to shift',
          });
        }
      } else {
        // check if is counted is 0 to insert into shift
        if (checkEmployeeInOtherArea[0].is_counted == 0) {
          // is in other area but count is 0, insert into shift
          const addEmpShift = await WorkAreaGroupShiftEmployee.query().insert({
            work_area_group_shift_id: shift_id,
            employee_id: e.employee_id,
            is_counted: isCounted,
          });

          if (addEmpShift.id > 0) {
            returnArray.push({
              employee_id: e.employee_id,
              shift_employee_id: addEmpShift.id,
              action: 'added to shift',
            });
          }
        } else {
          returnArray.push({
            employee_id: e.employee_id,
            shift_employee_id: shift_id,
            action: 'Employee in other shift',
          });
        }
      }
    }
  }

  return returnArray;
}*/

/*export async function CreateMOScans(
  req: Request,
  res: Response
): Promise<Response | any> {
  // data voucher
  const moId: IWorkVouchers['mo_id'] = req.body.moId;

  req.body.work_voucher_group_sort || null;
  // data ticket
  const workAreaId: IWorkAreaTickets['work_area_id'] = req.body.workAreaId;
  let supervisor = req.body.barcode_operator;
  let task = req.body.task;
  const format1 = 'YYYY-MM-DD HH:mm:ss';
  const actualDate = new Date();
  // data de los middlewares
  const data = {
    operatorId: req.body.operatorId,
    typeAction: req.body.typeAction,
    client: req.body.client,
    isVoucher: req.body.voucher,
    voucherCode: req.body.voucherCode,
    workVoucherTypeId: req.body.work_voucher_type_id,
    company_code: req.body.companyCode,
  };

  if (workAreaId === 0) {
    // send to discord message

    await sendScanLog(
      'WorkAreaGroups',
      `[Operador : ${supervisor}] - No se encontro un grupo`
    );
  }

  // validar si existe operador valido para la MO sino buscar en work_area_operator_map basados en el grupo
  if (data.operatorId === 0 && workAreaId === 0) {
    await sendScanLog(
      'Operators',
      `[Operador ${supervisor}] - No concuerda con la produccion escaneada`
    );

    // enviar mensaje a discord
    await sendScanLog(
      'WorkAreaGroups/Operator',
      `[Operador: ${supervisor}, MOID : ${moId}] - No se encontro un grupo, ni operador para esta produccion`
    );

    // retornar error ya que no se encontro ni area ni operador
    return res.status(403).json({
      ok: false,
      message: 'No existe operador, ni area para vincular este escaneo',
    });
  } else if (data.operatorId === 0) {
    // enviar mensaje a discord

    await sendScanLog(
      'Operators',
      `[Operador : ${supervisor}, MOID : ${moId}] - No concuerda con la produccion escaneada, se tomara operador auxiliar`
    );
    // obtener operador de work_area_operator_map
    const getNewOperatorFromMapping = await WorkAreaOperatorMap.query()
      .where('work_area_id', workAreaId)
      .where('company_code', data.company_code)
      .select('operator_id');

    if (getNewOperatorFromMapping.length > 0) {
      // encontro nuevo operador, ahora se validara si se puede escanear en mo_scan
      const getOperator = await Operators.query()
        .where('operator_id', getNewOperatorFromMapping[0].operator_id)
        .where('client', data.client)
        .where('operator_status', 1)
        .select('barcode', 'task');

      // error validar task para ver si es igual, independientemente sea el mismo cliente
      if (getOperator.length > 0) {
        supervisor = getOperator[0].barcode;
        task = getOperator[0].task;
      } else {
        // Operador no concuerda con orden a escanear enviar mensaje a discord
        await sendScanLog(
          'MappedOperatorError',
          `[Nuevo Operador : ${supervisor}, MOID : ${moId}] - No concuerda con la produccion escaneada `
        );

        return res.status(500).json({
          ok: false,
          message:
            'MappedOperatorError, Operador no concuerda con la produccion escaneada',
        });
      }
    } else {
      // enviar mensaje a discord no encontro operador en la tabla de mapeo
      await sendScanLog(
        'MappedOperatorError',
        `[Operador : ${supervisor}], No se encontro en el mapeo`
      );

      return res.status(500).json({
        ok: false,
        message: 'MappedOperatorError, Operador no se encontro en el mapeo',
      });
    }
  }

  // if (workAreaId == 7 || (data.client === "VARPRO" && task === "Sew")) {
  // insertar en mo_scans
  if (data.typeAction === 'ENTRADA') {
    // insertar en mo_scans
    // buscar si existe ya una scan con la mo y el operador
    const getMoScan = await MoScans.query()
      .where('mo_scans.mo_id', moId)
      .where('mo_scans.supervisor', supervisor)
      .where('mo_scans.task_name', task);

    if (getMoScan.length > 0) {
      return res.status(200).json({
        ok: true,
        message: 'Ya existe la tarea, debes realizar una salida',
      });
    } else {
      // insertamos entrada
      const addMoScans = await MoScans.query().insert({
        mo_id: moId,
        sew_ready: dayjs(actualDate).format(format1),
        supervisor,
        supervisor_code: 1,
        task_name: task,
        poly_status: 2,
      });

      return res.status(200).json({
        ok: true,
        message:
          'No se crea voucher y ticket en tu area, se inserto en mo_scans VARSITY(entrada)',
        data: addMoScans,
      });
    }
  } else if (data.typeAction === 'SALIDA') {
    // insertamos salida
    // buscar si existe ya una scan con la mo y el operador
    const getMoScan = await MoScans.query()
      .where('mo_scans.mo_id', moId)
      .where('mo_scans.supervisor', supervisor)
      .where('mo_scans.task_name', task)
      .select('scan_id', 'sew');

    if (getMoScan.length > 0) {
      // validar si sew esta nulo
      if (getMoScan[0].sew !== null) {
        return res.status(200).json({
          ok: true,
          message:
            'Ya existe un escaneo asociado a esta MO para esta area. no se puede escanear dos veces',
          data: getMoScan,
        });
      } else getMoScan[0].sew === null;
      {
        // actualizamos entrada
        const addMoScans = await MoScans.query()
          .update({
            sew: dayjs(actualDate).format(format1),
            poly_status: 0,
          })
          .where('scan_id', getMoScan[0].scan_id);

        return res.status(200).json({
          ok: true,
          message:
            'No se crea voucher y ticket en tu area, se actualizo mo scan',
          data: addMoScans,
        });
      }
    } else {
      // caso que no exista entrada
      const addMoScans = await MoScans.query().insert({
        mo_id: moId,
        sew_ready: dayjs(actualDate).format(format1),
        sew: dayjs(actualDate).format(format1),
        supervisor,
        supervisor_code: 1,
        task_name: task,
        poly_status: 0,
      });

      return res.status(200).json({
        ok: true,
        message:
          'No se crea voucher y ticket en tu area, se inserto en mo_scans ',
        data: addMoScans,
      });
    }
  }
}*/

/*export async function getEmployeeByGroup(req: Request, res: Response) {
  const group_id: number = +req.body.group_id;
  const shift_id: number = +req.body.shift_id;
  const format1 = 'YYYY-MM-DD HH:mm:ss';
  const actualDate = new Date();
  let dayName = actualDate.toLocaleString('es-ES', { weekday: 'long' });
  let startDate = new Date(
    actualDate.getFullYear(),
    actualDate.getMonth(),
    actualDate.getDate(),
    7,
    10,
    0
  );
  let endDate = new Date(
    actualDate.getFullYear(),
    actualDate.getMonth(),
    actualDate.getDate(),
    dayName.toUpperCase() == 'VIERNES' ? 15 : 16,
    50,
    0
  );
  const newTable = [];
  let getSecurityAtten = [];
  const s = new Set();
  let newInsertBySecurity = [];

  const listOfCodes = [];

  try {
    // if shift is empty, create a new one with normal time and group id
     if (shift_id === null) {
      var getShift = await WorkAreaGroupShifts.query()
      .where("start_datetime_sv", "<=", dayjs(actualDate).format(format1))
      .where("end_datetime_sv", ">=", dayjs(actualDate).format(format1))
      .where("work_area_group_id", group_id)
      .select("id");
      
      if(getShift.length === 0)
      {       
        //its empty and create a new shift
        const addNewShift = await WorkAreaGroupShifts.query().insert({
          work_area_group_id: group_id,
          start_datetime_sv : dayjs(startDate).format(format1),
          end_datetime_sv : dayjs(endDate).format(format1),
          normal_minutes: dayName.toUpperCase() == "VIERNES" ? 540 : 480,
          break_minutes: 0,
          working_minutes: dayName.toUpperCase() == "VIERNES" ? 540 : 480,
        });        

        if(addNewShift !== null)
        {
          shift_id = addNewShift.id;
        }
      }
      else
      {
        //use standart shift
        shift_id = getShift[0].id;
      }
    }
    else{
    // in case shift id was sent
    const getShift = await WorkAreaGroupShifts.query().where('id', shift_id);

    if (getShift !== null) {
      dayName = getShift[0].start_datetime_sv.toLocaleString('es-ES', {
        weekday: 'long',
      });
      startDate = new Date(
        +getShift[0].start_datetime_sv.getFullYear(),
        +getShift[0].start_datetime_sv.getMonth(),
        +getShift[0].start_datetime_sv.getDate(),
        7,
        10,
        0
      );
      endDate = new Date(
        +getShift[0].start_datetime_sv.getFullYear(),
        +getShift[0].start_datetime_sv.getMonth(),
        +getShift[0].start_datetime_sv.getDate(),
        dayName.toUpperCase() == 'VIERNES' ? 15 : 16,
        50,
        0
      );
    } else {
      return res.status(200).json({
        ok: false,
        message: 'No Shift for id ' + shift_id,
      });
    }
    // }

    const getAllEmployeesForGroup = await WorkAreaGroupEmployees.query()
      .join(
        'employees',
        'work_area_group_employees.employee_id',
        'employees.employee_id'
      )
      .leftJoin(
        'work_employee_states',
        'work_area_group_employees.work_employee_state_id',
        '=',
        'work_employee_states.id'
      )
      .leftJoin(
        'employees_departments',
        'employees_departments.code',
        '=',
        'employees.department'
      )
      .where('work_area_group_employees.work_group_id', group_id)
      .select(
        'work_area_group_employees.employee_id',
        'work_area_group_employees.work_group_id',
        'work_area_group_employees.work_employee_state_id',
        'work_employee_states.to_count_min',
        'work_employee_states.is_present',
        'employees.first_name',
        'employees.last_name',
        'employees_departments.description',
        'work_employee_states.name',
        'work_employee_states.reason',
        'work_employee_states.is_end_shift'
      );

    const getAllEmployeesForShift = await WorkAreaGroupShiftEmployee.query()
      .join(
        'employees',
        'work_area_group_shift_employees.employee_id',
        'employees.employee_id'
      )
      .leftJoin(
        'work_employee_states',
        'work_area_group_shift_employees.work_employee_state_id',
        '=',
        'work_employee_states.id'
      )
      .leftJoin(
        'employees_departments',
        'employees_departments.code',
        '=',
        'employees.department'
      )
      .where(
        'work_area_group_shift_employees.work_area_group_shift_id',
        shift_id
      )
      .select(
        'work_area_group_shift_employees.id',
        'work_area_group_shift_employees.employee_id',
        'work_area_group_shift_employees.work_area_group_shift_id',
        'work_area_group_shift_employees.work_employee_state_id',
        'work_employee_states.to_count_min',
        'work_employee_states.is_present',
        'employees.first_name',
        'employees.last_name',
        'employees_departments.description',
        'work_employee_states.name',
        'work_employee_states.reason',
        'work_employee_states.is_end_shift'
      );

    getAllEmployeesForGroup.forEach(function (e: any) {
      newTable.push({
        employee_id: e.employee_id,
        group_id: e.work_group_id,
        group_employee_state: e.work_employee_state_id,
        shift_id: null,
        shift_employee_state: null,
        security_scan: null,
        isCounted: e.to_count_min,
        isPresent: e.is_present,
        empShiftId: null,
        first_name: e.first_name,
        last_name: e.last_name,
        description: e.description,
        shift_employee_state_name: null,
        selected: false,
      });
      listOfCodes.push(e.employee_id);
      s.add(e.employee_id);
    });

    if (listOfCodes.length > 0) {
      getSecurityAtten = await EmployeeScanTemp.query()
        .where('time_in', '>=', dayjs(startDate).format(format1))
        .where('time_in', '<=', dayjs(endDate).format(format1))
        .whereIn('employee_id', listOfCodes);

      if (getSecurityAtten.length > 0) {
        getSecurityAtten.forEach(function (e: any) {
          if (s.has(e.employee_id)) {
            const index = newTable
              .map(function (x: any) {
                return x.employee_id;
              })
              .indexOf(e.employee_id);

            newTable[index].security_scan = 1;
          }
        });
      }
    }

    getAllEmployeesForShift.forEach(function (e: any) {
      if (!s.has(e.employee_id)) {
        newTable.push({
          employee_id: e.employee_id,
          group_id: null,
          group_employee_state: null,
          shift_id: e.work_area_group_shift_id,
          shift_employee_state: e.work_employee_state_id,
          security_scan: null,
          isCounted: e.to_count_min,
          isPresent: e.is_present,
          empShiftId: e.id,
          first_name: e.first_name,
          last_name: e.last_name,
          description: e.description,
          shift_employee_state_name: e.name,
          selected: false,
        });
      } else {
        const index = newTable
          .map(function (x: any) {
            return x.employee_id;
          })
          .indexOf(e.employee_id);

        newTable[index].shift_id = e.work_area_group_shift_id;
        newTable[index].shift_employee_state = e.work_employee_state_id;
        newTable[index].isCounted = e.to_count_min;
        newTable[index].isPresent = e.is_present;
        newTable[index].empShiftId = e.id;
        newTable[index].shift_employee_state_name = e.name;
      }
    });

    if (newTable.length > 0) {
      newInsertBySecurity = await insertEmployeeshift(
        shift_id,
        startDate,
        endDate,
        newTable
      );

      if (newInsertBySecurity.length > 0) {
        // update las table to return with updated emp in shift in case we inserted emp
        newInsertBySecurity.forEach(function (e: any) {
          const index = newTable
            .map(function (x: any) {
              return x.employee_id;
            })
            .indexOf(e.employee_id);

          newTable[index].shift_id = shift_id;
          newTable[index].shift_employee_state = 1;
          newTable[index].empShiftId = e.shift_employee_id;
        });
      }

      return res.status(200).json({
        ok: true,
        data: newTable,
        shift: shift_id,
      });
    }

    return res.status(200).json({
      ok: false,
      message: 'empty',
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}*/

/*export async function updateEmployeeShiftState(req: Request, res: Response) {
  try {
    // local variables
    const lateToStart = false;
    const snapLateTime = 10;
    const updateEmpGroup =
      req.body.flag !== undefined && req.body.flag ? req.body.flag : false; // can be update shift only or update shift and employee group
    const listForUpdate = req.body.data;
    const returnArray = [];
    const actualDate = new Date();
    const format1 = 'YYYY-MM-DD HH:mm:ss';

    // update shift
    for (const e of listForUpdate) {
      if (e.id !== null) {
        // If to: Null
        if (e.state_id === null) {
          returnArray.push({
            shiftID: e.id,
            stateID: e.state_id,
            groupID: e.group_id,
            employeeID: e.employee_id,
            action: 'ERROR : Once a employee state is set, it cannot be unset',
          });
        } else {
          // get state values
          const getCurrentStateValues = await WorkEmployeeStates.query()
            .where('id', e.state_id)
            .select(
              'name',
              'reason',
              'to_count_min',
              'is_present',
              'is_end_shift'
            );

          if (getCurrentStateValues.length > 0) {
            const getPreviousEmployeeShiftInformation =
              await WorkAreaGroupShiftEmployee.query()
                .join(
                  'work_area_group_shifts',
                  'work_area_group_shift_employees.work_area_group_shift_id',
                  '=',
                  'work_area_group_shifts.id'
                )
                .leftJoin(
                  'work_employee_states',
                  'work_area_group_shift_employees.work_employee_state_id',
                  '=',
                  'work_employee_states.id'
                )
                .where('work_area_group_shift_employees.id', e.id)
                .select(
                  'work_area_group_shifts.start_datetime_sv',
                  'work_area_group_shifts.end_datetime_sv',
                  'work_area_group_shift_employees.finish_datetime',
                  'work_employee_states.is_present',
                  'work_employee_states.is_end_shift',
                  'work_employee_states.name',
                  'work_area_group_shift_employees.work_employee_state_id'
                );

            let minutes = null;
            let minutesToCount = null;
            let reason = null;
            const finishDateTime: Date =
              getPreviousEmployeeShiftInformation[0].end_datetime_sv;
            let startDateTime: Date =
              getPreviousEmployeeShiftInformation[0].start_datetime_sv;
            const shiftStateCountMin = getCurrentStateValues[0].to_count_min;

            // start checking states changes.
            // from: Null
            if (
              (getPreviousEmployeeShiftInformation[0].work_employee_state_id ===
                null ||
                !getPreviousEmployeeShiftInformation[0].is_present) &&
              getCurrentStateValues[0].is_present
            ) {
              // check if its late.
              let diff =
                (actualDate.getTime() - startDateTime.getTime()) / 1000;

              diff /= 60;
              minutes = Math.round(diff);

              if (!lateToStart && minutes > snapLateTime) {
                startDateTime = actualDate;
                reason = 'Entrada tardia';
              }

              // check if count employee, check if to_count_min is not null, then check if total shiftminutes is greater thatn to_count_min, if is set to true, otherwise is false
              let diffToCount =
                (finishDateTime.getTime() - startDateTime.getTime()) / 1000;

              diffToCount /= 60;
              minutesToCount = Math.round(diffToCount);
              // minutesToCount = minutesToCount - (minutes > 0 && minutes > snapLateTime ? minutes : 0)

              const updateShiftEmployee =
                await WorkAreaGroupShiftEmployee.query()
                  .update({
                    work_employee_state_id: e.state_id,
                    off_min:
                      minutes > 0 && minutes > snapLateTime ? minutes : null,
                    off_reason: reason,
                    start_datetime: dayjs(startDateTime).format(format1),
                    finish_datetime: finishDateTime,
                    is_counted:
                      shiftStateCountMin !== null
                        ? minutesToCount >= shiftStateCountMin
                          ? true
                          : false
                        : false,
                  })
                  .where('id', e.id);

              if (updateShiftEmployee === 1) {
                returnArray.push({
                  shiftID: e.id,
                  stateID: e.state_id,
                  groupID: e.group_id,
                  employeeID: e.employee_id,
                  action: 'Status cambiado',
                });
              } else {
                returnArray.push({
                  shiftID: e.id,
                  stateID: e.state_id,
                  groupID: e.group_id,
                  employeeID: e.employee_id,
                  action: 'ERROR : Shift State Error',
                });
              }
            }
            // cannot go from state of is_present = 0 to is_present = 1 & is_end_shift = 1
            else if (
              !getPreviousEmployeeShiftInformation[0].is_present &&
              getCurrentStateValues[0].is_present &&
              getCurrentStateValues[0].is_end_shift
            ) {
              returnArray.push({
                shiftID: e.id,
                stateID: e.state_id,
                groupID: e.group_id,
                employeeID: e.employee_id,
                action:
                  'ERROR : is_present = 0 to is_present = 1 & is_end_shift = 1',
              });
            }
            // if is_present and is_end_shift are the same for both from and to
            else if (
              getPreviousEmployeeShiftInformation[0].is_present ==
                getCurrentStateValues[0].is_present &&
              getPreviousEmployeeShiftInformation[0].is_end_shift &&
              getCurrentStateValues[0].is_end_shift
            ) {
              const updateShiftEmployee =
                await WorkAreaGroupShiftEmployee.query()
                  .update({
                    work_employee_state_id: e.state_id,
                  })
                  .where('id', e.id);

              if (updateShiftEmployee === 1) {
                returnArray.push({
                  shiftID: e.id,
                  stateID: e.state_id,
                  groupID: e.group_id,
                  employeeID: e.employee_id,
                  action: 'Status cambiado',
                });
              } else {
                returnArray.push({
                  shiftID: e.id,
                  stateID: e.state_id,
                  groupID: e.group_id,
                  employeeID: e.employee_id,
                  action: 'ERROR : Shift State Error',
                });
              }
            }
            // if from status has is_end_shift = true and to status has is_present & is_end_shift = false
            else if (
              getPreviousEmployeeShiftInformation[0].is_end_shift &&
              getCurrentStateValues[0].is_present &&
              !getCurrentStateValues[0].is_end_shift
            ) {
              // get difference between current finish_datetime and now(), set to off_time on shift_employee
              const finishDate =
                getPreviousEmployeeShiftInformation[0].finish_datetime;

              reason = getPreviousEmployeeShiftInformation[0].name;
              let diff = (finishDate.getTime() - actualDate.getTime()) / 1000;

              diff /= 60;
              minutes = Math.abs(Math.round(diff));

              // check if count employee, check if to_count_min is not null, then check if total shiftminutes - minutesoff is greater thatn to_count_min, if is set to true, otherwise is false
              let diffToCount =
                (finishDateTime.getTime() - startDateTime.getTime()) / 1000;

              diffToCount /= 60;
              minutesToCount = Math.round(diffToCount) - minutes;

              const updateShiftEmployee =
                await WorkAreaGroupShiftEmployee.query()
                  .update({
                    work_employee_state_id: e.state_id,
                    off_min: minutes,
                    off_reason: reason,
                    finish_datetime: finishDateTime,
                    is_counted:
                      shiftStateCountMin !== null
                        ? minutesToCount >= shiftStateCountMin
                          ? true
                          : false
                        : false,
                  })
                  .where('id', e.id);
          if (getVoucherInfo.length > 0) {
            return res.status(400).json({
              ok: false,
              message:
                'Error, ya existe un voucher y ticket con el mismo voucher type en el area',
            });
          } else {
            const get_status = await WorkAreaTicketStatuses.query()
              .where('work_area_id', area)
              .where('name', 'Nuevo')
              .select('id');

              if (updateShiftEmployee === 1) {
                returnArray.push({
                  shiftID: e.id,
                  stateID: e.state_id,
                  groupID: e.group_id,
                  employeeID: e.employee_id,
                  action: 'Status cambiado',
                });
              } else {
                returnArray.push({
                  shiftID: e.id,
                  stateID: e.state_id,
                  groupID: e.group_id,
                  employeeID: e.employee_id,
                  action: 'ERROR : Shift State Error',
                });
              }
            }
            // If from is_present = 1 to is_present = 0 | NULL
            else if (
              getPreviousEmployeeShiftInformation[0].is_present &&
              !getCurrentStateValues[0].is_present
            ) {
              const updateShiftEmployee =
                await WorkAreaGroupShiftEmployee.query()
                  .update({
                    work_employee_state_id: e.state_id,
                    off_min: null,
                    off_reason: null,
                    start_datetime: null,
                    finish_datetime: null,
                    is_counted: null,
                    bonus: null,
                  })
                  .where('id', e.id);

              if (updateShiftEmployee === 1) {
                returnArray.push({
                  shiftID: e.id,
                  stateID: e.state_id,
                  groupID: e.group_id,
                  employeeID: e.employee_id,
                  action: 'Status cambiado',
                });
              } else {
                returnArray.push({
                  shiftID: e.id,
                  stateID: e.state_id,
                  groupID: e.group_id,
                  employeeID: e.employee_id,
                  action: 'ERROR : Shift State Error',
                });
              }
            }
            // if from and to: is_present = 1 && from: is_end_shift = 0 && to: is_end_shift = 1 = endtime to now
            else if (
              getPreviousEmployeeShiftInformation[0].is_present &&
              getCurrentStateValues[0].is_present &&
              !getPreviousEmployeeShiftInformation[0].is_end_shift &&
              getCurrentStateValues[0].is_end_shift
            ) {
              const updateShiftEmployee =
                await WorkAreaGroupShiftEmployee.query()
                  .update({
                    work_employee_state_id: e.state_id,
                    finish_datetime: dayjs(actualDate).format(format1),
                  })
                  .where('id', e.id);

              if (updateShiftEmployee === 1) {
                returnArray.push({
                  shiftID: e.id,
                  stateID: e.state_id,
                  groupID: e.group_id,
                  employeeID: e.employee_id,
                  action: 'Status cambiado',
                });
              } else {
                returnArray.push({
                  shiftID: e.id,
                  stateID: e.state_id,
                  groupID: e.group_id,
                  employeeID: e.employee_id,
                  action: 'ERROR : Shift State Error',
                });
              }
            }
          }
        }
      }
    }
    // update group emp if updateEmpGroup is true
    if (updateEmpGroup) {
      for (const e of listForUpdate) {
        if (e.group_id !== null) {
          const updateEmployeeGroup = await WorkAreaGroupEmployees.query()
            .update({
              work_employee_state_id: e.state_id,
            })
            .where('work_group_id ', e.group_id)
            .where('employee_id ', e.employee_id);

          if (updateEmployeeGroup === 1) {
            const index = returnArray
              .map(function (x: any) {
                return x.employeeID;
              })
              .indexOf(e.employee_id);

            returnArray[index].action =
              'Shift State and Group Employee State Changed';
          } else {
            const index = returnArray
              .map(function (x: any) {
                return x.employeeID;
              })
              .indexOf(e.employee_id);

            returnArray[index].action =
              'ERROR : Shift State and Group Employee State Changed';
          }
        }
      }
    }

    return res.status(200).json({
      ok: true,
      message: returnArray,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}*/

/*export async function getWorkEmployeeState(req: Request, res: Response) {
  try {
    const getWorkStatusesName = await WorkEmployeeStates.query();

    return res.status(200).json({
      ok: true,
      data: getWorkStatusesName,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      data: error,
    });
  }
}*/

/*async function insertEmployeeshift(
  shiftId: number,
  startDate: Date,
  endDate: Date,
  newTable: any
) {
  const newArray = [];
  const format1 = 'YYYY-MM-DD HH:mm:ss';

  for (const e of newTable) {
    // add emp scanned in security station and is counted in emp group table where shift id is null. check if is not in another shift for that day
    if (e.shift_id === null && e.isCounted === 1 && e.security_scan === 1) {
      // check if employee is in other area and status is counted = 1
      const checkEmployeeInOtherArea = await WorkAreaGroupShiftEmployee.query()
        .join(
          'work_area_group_shifts',
          'work_area_group_shift_employees.work_area_group_shift_id',
          '=',
          'work_area_group_shifts.id'
        )
        .where(
          'work_area_group_shifts.start_datetime_sv',
          '>=',
          dayjs(startDate).format(format1)
        )
        .where(
          'work_area_group_shifts.end_datetime_sv ',
          '<=',
          dayjs(endDate).format(format1)
        )
        .where('work_area_group_shift_employees.employee_id', e.employee_id)
        .select(
          'work_area_group_shift_employees.work_area_group_shift_id',
          'work_area_group_shift_employees.employee_id',
        );

      if (
        checkEmployeeInOtherArea === null ||
        checkEmployeeInOtherArea.length === 0
      ) {
        // not found in other area. insert into shift
        const addEmpShift = await WorkAreaGroupShiftEmployee.query().insert({
          work_area_group_shift_id: shiftId,
          employee_id: e.employee_id,
        });

        if (addEmpShift.id > 0) {
          newArray.push({
            employee_id: e.employee_id,
            shift_employee_id: addEmpShift.id,
          });
        }
      } else {
        // check if is counted is 0 to insert into shift
        if (checkEmployeeInOtherArea[0].is_counted == 0) {
          // is in other area but count is 0, insert into shift
          const addEmpShift = await WorkAreaGroupShiftEmployee.query().insert({
            work_area_group_shift_id: shiftId,
            employee_id: e.employee_id,
          });

          if (addEmpShift.id > 0) {
            newArray.push({
              employee_id: e.employee_id,
              shift_employee_id: addEmpShift.id,
            });
          }
        }
      }
    }
  }

  return newArray;
}*/
