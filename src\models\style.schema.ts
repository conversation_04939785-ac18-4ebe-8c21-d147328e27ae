import { Model } from '@app/db';

export class Style extends Model {
  static get tableName(): string {
    return 'styles';
  }
  style_id!: number;
  style_number!: string;
  style_name!: string;
  style_category!: string;
  product_category!: string;
  customer!: string;
}

export class StyleCombo extends Model {
  static get tableName(): string {
    return 'style_combos';
  }

  id!: number;
  style_id!: number;
  created_at!: Date;
  updated_at!: Date;
  combo_number!: number;
  piece_count!: number;
  is_laser!: boolean;
  is_facing!: boolean;
  deleted_at!: Date;
  default_part_number!: string;
}

export class StyleDocumentType extends Model {
  static get tableName(): string {
    return 'style_document_types';
  }
  id!: number;
  name!: string;
  created_at!: Date;
  updated_at!: Date;
}

export class StyleDocument extends Model {
  static get tableName(): string {
    return 'style_documents';
  }

  static get relationMappings() {
    return {
      StyleDocumentTypes: {
        relation: Model.HasManyRelation,
        modelClass: StyleDocumentType,
        join: {
          from: 'style_documents.style_document_type_id',
          to: 'style_document_types.id',
        },
      },
      Styles: {
        relation: Model.HasManyRelation,
        modelClass: Style,
        join: {
          from: 'style_documents.style_id',
          to: 'styles.style_id',
        },
      },
    };
  }

  id!: number;
  style_document_type_id!: number;
  comment!: string;
  is_removed!: boolean;
  file_uuid!: string;
  file_extension!: string;
  style_id!: number;
  created_at!: Date;
  updated_at!: Date;
}
