import { Model } from '@app/db';

export class RhinestoneProvider extends Model {
  static get tableName(): string {
    return 'rhinestones_providers';
  }

  id!: number;
  name!: string;
  short_name!: string;
  material_code!: string;
  is_active!: boolean;
  created_at!: string;
  updated_at!: string;
}
export class RhinestoneOrder extends Model {
  static get tableName(): string {
    return 'rhinestones_orders';
  }

  id!: number;
  mo_id!: number;
  size_range!: string;
  placement!: string;
  type_id!: number;
  art_date!: string | Date;
  warehouse_date!: string | Date;
  transfer_date!: string;
  order_status!: string;
  created_at!: string;
  updated_at!: string;
  production_date!: string | Date;
  sheet_count!: number;
  machine_id!: number;
  art_per_sheet!: number;
  sheets_per_art!: number;
  transfers_per_art!: number;
  total_tranfers!: number;
  doubled_art!: boolean;
  panels_per_art!: number;
  film_machine_id!: number;
  film_machine_assigned_date!: string | Date;
  film_finish_date!: string | Date;
  receiving_date!: string;
  is_repo!: boolean;
  is_active!: boolean;
}

export class RhinestoneColor extends Model {
  static get tableName(): string {
    return 'rhinestones_colors';
  }

  id!: number;
  color_name!: string;
  description_color!: string;
  status_color!: string;
  created_at!: string;
  updated_at!: string;
  is_active!: boolean;
}

export class RhinestoneColorProvider extends Model {
  static get tableName(): string {
    return 'rhinestones_colors_providers';
  }

  id!: number;
  rhinestones_provider_id!: number;
  rhinestones_color_id!: number;
  is_active!: boolean;
  created_at!: string;
  updated_at!: string;
}

export class RhinestoneSize extends Model {
  static get tableName(): string {
    return 'rhinestones_sizes';
  }

  id!: number;
  size_name!: string;
  size_description!: string;
  status_size!: string;
  varsity_rate!: number;
  varpro_rate!: number;
  created_at!: string;
  updated_at!: string;
  is_active!: boolean;
}

export class RhinestoneSizeProvider extends Model {
  static get tableName(): string {
    return 'rhinestones_sizes_providers';
  }

  id!: number;
  rhinestones_provider_id!: number;
  rhinestones_size_id!: number;
  is_active!: boolean;
  created_at!: string;
  updated_at!: string;
}

export class RhinestoneStyle extends Model {
  static get tableName(): string {
    return 'rhinestones_styles';
  }

  id!: number;
  style_name!: string;
  style_status!: string;
  created_at!: string;
  updated_at!: string;
}

export class RhinestoneType extends Model {
  static get tableName(): string {
    return 'rhinestones_types';
  }

  id!: number;
  type_name!: string;
  type_status!: string;
  created_at!: string;
  updated_at!: string;
}

export class RhinestoneStyleType extends Model {
  static get tableName(): string {
    return 'rhinestones_styles_types';
  }

  id!: number;
  rhinestones_style_id!: number;
  rhinestones_type_id!: number;
  created_at!: string;
  updated_at!: string;
}

export class RhinestoneInventory extends Model {
  static get tableName(): string {
    return 'rhinestone_inventory';
  }

  id!: number;
  name!: string;
  size_id!: number;
  color_id!: number;
  type_id!: number;
  quantity!: number;
  weight_g_100!: number;
  created_at!: string;
  updated_at!: string;
  inactive_datetime!: string;
}

export class RhinestoneMachineInventory extends Model {
  static get tableName(): string {
    return 'rhinestone_machine_inventory';
  }

  id!: number;
  machine_id!: number;
  rhinestone_inventory_id!: number;
  onhand_quantity!: number;
}

export class RhinestoneOrderItem extends Model {
  static get tableName(): string {
    return 'rhinestones_order_items';
  }

  id!: number;
  order_id!: number;
  size_id!: number;
  color_id!: number;
  item_status!: string;
  quantity!: number;
  type_id!: number;
  created_at!: string;
  updated_at!: string;
  rhinestone_inventory_id!: number;
  is_active!: boolean;
  range_per_order_id!: number;
  file_per_range_id!: number;
}

export class RhinestoneArea extends Model {
  static get tableName(): string {
    return 'rhinestones_areas';
  }

  id!: number;
  name!: string;
  is_active!: boolean;
  created_at!: string;
  updated_at!: string;
}

export class RhinestoneUser extends Model {
  static get tableName(): string {
    return 'rhinestones_users';
  }

  id!: number;
  employee_id!: number;
  rhinestones_area_id!: number;
  password!: string;
  is_active!: boolean;
  created_at!: string;
  updated_at!: string;
}

export class RhinestoneLog extends Model {
  static get tableName(): string {
    return 'rhinestones_logs';
  }

  id!: number;
  rhinestones_area_id!: number;
  employee_id!: number;
  action!: string;
  module_id!: number;
  module_name!: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data!: any;
  created_at!: string;
  updated_at!: string;
}

export class RhinestoneRange extends Model {
  static get tableName(): string {
    return 'rhinestones_ranges';
  }

  id!: string;
  name!: string;
  is_active!: boolean;
  created_at!: string;
  updated_at!: string;
}

export class RhinestoneOrderRange extends Model {
  static get tableName(): string {
    return 'rhinestones_ranges_per_order';
  }

  id!: number;
  range_id!: number;
  order_id!: number;
  quantity!: number;
  is_active!: boolean;
  created_at!: string;
  updated_at!: string;
}
export class RhinestoneOrderRangeFile extends Model {
  static get tableName(): string {
    return 'rhinestones_files_range';
  }

  id!: number;
  rhinestones_range_per_order_id!: number;
  description!: string;
  cute!: number;
  file_url!: string;
  is_active!: boolean;
  created_at!: string;
  updated_at!: string;
}
