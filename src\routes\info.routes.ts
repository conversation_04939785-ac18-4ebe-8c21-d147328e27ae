import { Router } from 'express';

const infoRouter = Router();

infoRouter.route('/info').get((req, res) => {
  const uptime = process.uptime();

  res.json({
    status: 'OK',
    version: '1.23.3',
    environment: process.env.NODE_ENV,
    uptime_seconds: uptime,
    uptime_human: `${Math.floor(uptime / 60)}m ${Math.floor(uptime % 60)}s`,
    timestamp: new Date().toISOString(),
  });
});

export { infoRouter };
