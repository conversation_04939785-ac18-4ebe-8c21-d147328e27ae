import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(
    'work_pickpack_items',
    (table: Knex.TableBuilder) => {
      table.increments('id').unsigned().primary();
      table
        .timestamp('created_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      table
        .timestamp('updated_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

      table.string('customer').notNullable();
      table.string('style').notNullable();
      table.string('division').nullable();
      table.string('size').notNullable();
      table.string('color').nullable();
      table.string('design').nullable();
      table.string('barcode').nullable();
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('work_pickpack_items');
}
