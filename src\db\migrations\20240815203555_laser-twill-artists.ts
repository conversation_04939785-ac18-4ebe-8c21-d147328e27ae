import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(
    'mo_twill_laser_artists',
    (table: Knex.TableBuilder): void => {
      table.increments('id').unsigned().primary();
      table.integer('mo_id').notNullable();
      table
        .foreign('employee_id')
        .references('employee_id')
        .inTable('employees');
      table.text('comment').nullable();
      table.boolean('is_priority').notNullable().defaultTo(true);
      table.boolean('is_active').notNullable().defaultTo(true);
      table.timestamp('completed_at').nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table
        .timestamp('updated_at')
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
      table.integer('employee_id').notNullable();
      table
        .foreign('mo_id', 'mo_id_foreign')
        .references('mo_id')
        .inTable('mo_numbers');
    }
  );

  await knex.schema.createTable(
    'mo_twill_laser_artist_consumptions',
    (table: Knex.TableBuilder): void => {
      table.increments('id').unsigned().primary();
      table.integer('twill_laser_artist_id', 10).notNullable().unsigned();
      table.double('height', 10, 2).notNullable().unsigned();
      table.double('width', 10, 2).notNullable().unsigned();
      table.integer('quantity').notNullable().unsigned();
      table.boolean('is_active').notNullable().defaultTo(true);
      table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
      table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
      table
        .foreign('twill_laser_artist_id', 'tla_id_foreign')
        .references('id')
        .inTable('mo_twill_laser_artists');
    }
  );

  await knex.schema.createTable(
    'mo_twill_laser_operator_consumptions',
    (table: Knex.TableBuilder): void => {
      table.increments('id').unsigned().primary();
      table.integer('laser_artist_consumption_id', 10).notNullable().unsigned();
      table.boolean('is_active').notNullable().defaultTo(true);
      table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
      table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
      table
        .foreign('laser_artist_consumption_id', 'mtloclac_id_foreign')
        .references('id')
        .inTable('mo_twill_laser_artist_consumptions');
    }
  );

  await knex.schema.alterTable(
    'mo_twill_laser_jobs',
    (table: Knex.TableBuilder): void => {
      table.renameColumn('colors_normal', 'layers');
      table.renameColumn('colors_special', 'special_layers');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('mo_twill_laser_artists');
  await knex.schema.dropTable('mo_twill_laser_artist_consumptions');
  await knex.schema.dropTable('mo_twill_laser_operator_consumptions');
  await knex.schema.alterTable(
    'mo_twill_laser_jobs',
    (table: Knex.TableBuilder): void => {
      table.renameColumn('layers', 'colors_normal');
      table.renameColumn('special_layers', 'colors_special');
    }
  );
}
