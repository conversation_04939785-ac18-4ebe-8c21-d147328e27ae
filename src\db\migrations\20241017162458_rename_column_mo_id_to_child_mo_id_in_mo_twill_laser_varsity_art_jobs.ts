import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'mo_twill_laser_varsity_art_jobs',
    (table: Knex.TableBuilder): void => {
      table.renameColumn('mo_id', 'child_mo_id');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'mo_twill_laser_varsity_art_jobs',
    (table: Knex.TableBuilder): void => {
      table.renameColumn('child_mo_id', 'mo_id');
    }
  );
}
