import { Router } from 'express';

import {
  UpdateMachineNumber,
  addPlotterComboToPrint,
  addPlotterPrintToRoll,
  addPlotterPrintToRollByMOController,
  checkOutRoll,
  createAndAddPlotterPrintToRoll,
  createMixMOs,
  createMultiPlotterCombos,
  createPlotterCombo,
  createPlotterPrintFromCombo,
  createPlotterRoll,
  createRollExcel,
  deleteAllComboController,
  deleteComboController,
  employeeMiddleware,
  getMoInfo,
  getMoParts,
  getPlotterCombo,
  getPlotterCombos,
  getPlotterPrint,
  getPlotterPrintByMO,
  getPlotterPrintGroup,
  getPlotterPrintWithoutRoll,
  getPlotterPrintsForMoReq,
  getPlotterRoll,
  getPlotterSuggestedLocations,
  getSuggestedPartNumberController,
  postScanPlotterPrint,
  removePlotterComboFromPrint,
  removePlotterPrintFromRoll,
  scanPlotterPrintReq,
  searchMoByNumAndClient,
  updatePlotsWithCutting,
  updatePlotterCombo,
  updatePlotterPrint,
  updatePlotterPrintByName,
  updatePlotterPrintLocation,
  updateSortRoll,
} from '@app/controllers/plotter.controller';

export const plotterRouter = Router();

plotterRouter.use(employeeMiddleware);

plotterRouter.route('/updateCutPlotsByMoScans').post(updatePlotsWithCutting);

plotterRouter.route('/mo/numandclient').get(searchMoByNumAndClient);
plotterRouter.route('/mo/:mo_id/parts').get(getMoParts);
plotterRouter.route('/mo/:mo_id/plots').get(getPlotterPrintsForMoReq);
plotterRouter.route('/mo/:mo_id').get(getMoInfo);

plotterRouter.route('/combo/create').post(createPlotterCombo);
plotterRouter.route('/combo/create/multiples').post(createMultiPlotterCombos);
plotterRouter
  .route('/combo/suggest/:style_number/:part_number')
  .get(getSuggestedPartNumberController);

plotterRouter.route('/combo/:combo_id').get(getPlotterCombo);
plotterRouter.route('/combo/:combo_id/update').put(updatePlotterCombo);
plotterRouter.route('/combo/:combo_id/delete').put(deleteComboController);
plotterRouter.route('/combo/:mo_id/deleteall').put(deleteAllComboController);

plotterRouter
  .route('/combo/:combo_id/createprint')
  .post(createPlotterPrintFromCombo);
plotterRouter
  .route('/combo/:combo_id/removeprint')
  .delete(removePlotterComboFromPrint);
plotterRouter
  .route('/combo/:combo_id/:print_id/addcombotoprint')
  .put(addPlotterComboToPrint);

plotterRouter.route('/combos/:mo_id/list').get(getPlotterCombos);

plotterRouter.route('/print/updateByDescription').put(updatePlotterPrintByName);
plotterRouter.route('/print/noroll').get(getPlotterPrintWithoutRoll);
plotterRouter.route('/print/bymo').post(getPlotterPrintByMO);
plotterRouter
  .route('/print/addToRollByMO')
  .post(addPlotterPrintToRollByMOController);
plotterRouter.route('/print/scan').post(scanPlotterPrintReq);
plotterRouter.route('/print/:print_id').get(getPlotterPrint);
plotterRouter.route('/print/:print_id/update').put(updatePlotterPrint);
plotterRouter.route('/print-group/:print_group_id').get(getPlotterPrintGroup);
plotterRouter
  .route('/print/:print_id/suggestedBins')
  .get(getPlotterSuggestedLocations);
plotterRouter
  .route('/print/:print_id/saveLocation')
  .put(updatePlotterPrintLocation);

plotterRouter
  .route('/print/:print_id/createroll')
  .post(createAndAddPlotterPrintToRoll);
plotterRouter
  .route('/print/:print_id/:roll_id/addprinttoroll')
  .put(addPlotterPrintToRoll);
plotterRouter
  .route('/print/:print_id/removeroll')
  .delete(removePlotterPrintFromRoll);
plotterRouter.route('/print/:print_id/scan').post(postScanPlotterPrint);

plotterRouter.route('/roll/createroll').post(createPlotterRoll);
plotterRouter.route('/roll/updatesort').post(updateSortRoll);
plotterRouter.route('/roll/machine').post(UpdateMachineNumber);
plotterRouter.route('/roll/checkout').post(checkOutRoll);
plotterRouter.route('/roll/excelFile/:roll_id').get(createRollExcel);
plotterRouter.route('/roll/:roll_id').get(getPlotterRoll);

plotterRouter.route('/mixMO').post(createMixMOs);
