import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'mo_twill_laser_jobs',
    (table: Knex.TableBuilder): void => {
      table.integer('quantity').notNullable().unsigned().defaultTo(0);
      table.boolean('is_repo').defaultTo(false);
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'mo_twill_laser_jobs',
    (table: Knex.TableBuilder): void => {
      table.dropColumn('quantity');
      table.dropColumn('is_repo');
    }
  );
}
