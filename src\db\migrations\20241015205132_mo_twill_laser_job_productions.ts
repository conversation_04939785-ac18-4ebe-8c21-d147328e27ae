import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(
    'mo_twill_laser_job_productions',
    (table: Knex.TableBuilder): void => {
      table.increments('id').unsigned().primary();
      table.integer('mo_twill_laser_job_id', 10).notNullable().unsigned();
      table.integer('quantity').notNullable();
      table.integer('machine_id').notNullable();
      table.integer('employee_id').notNullable();
      table.boolean('is_active').notNullable().defaultTo(true);
      table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
      table
        .timestamp('updated_at')
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
      table
        .foreign('mo_twill_laser_job_id', 'mtlji_mo_twill_laser_jobs_foreign')
        .references('id')
        .inTable('mo_twill_laser_jobs');
      table
        .foreign('employee_id', 'ei_employees_foreign')
        .references('employee_id')
        .inTable('employees');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('mo_twill_laser_job_productions');
}
