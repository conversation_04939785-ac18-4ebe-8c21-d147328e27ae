import type { <PERSON>ind<PERSON> } from '@app/interface/scanning.interfaces';
import { WorkRepos } from '@app/models/repo.schema';
import { MoNumber, WorkVouchers } from '@app/models/tickets.schema';

interface ISearchBy {
  [key: string]: (id: number, moBarcode?: string) => Promise<IFindMO>;
}

export const SEARCH_BY: ISearchBy = {
  MO_ID: async (id: number) => {
    return await MoNumber.query()
      .leftJoin('styles', 'mo_numbers.style', '=', 'styles.style_number')
      .where('mo_numbers.mo_id', id)
      .select(
        'mo_numbers.mo_id',
        'mo_numbers.quantity',
        'mo_numbers.mo_status',
        'mo_numbers.num',
        'mo_numbers.customer',
        'mo_numbers.style',
        'styles.style_id',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        'mo_numbers.style_category',
        'mo_numbers.product_category',
        'mo_numbers.company_code'
      )
      .first()
      .castTo<IFindMO>();
  },
  VOUCHER: async (id: number) => {
    return await WorkVouchers.query()
      .join('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
      .leftJoin('styles', 'mo_numbers.style', '=', 'styles.style_number')
      .where('work_vouchers.id', id)
      .select(
        'mo_numbers.mo_id',
        'mo_numbers.quantity',
        'mo_numbers.mo_status',
        'mo_numbers.num',
        'mo_numbers.customer',
        'mo_numbers.style',
        'styles.style_id',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        'mo_numbers.style_category',
        'mo_numbers.product_category',
        'mo_numbers.company_code'
      )
      .first()
      .castTo<IFindMO>();
  },
  REPO: async (id: number) => {
    return await WorkRepos.query()
      .join('mo_numbers', 'work_repos.mo_id', '=', 'mo_numbers.mo_id')
      .leftJoin('styles', 'mo_numbers.style', '=', 'styles.style_number')
      .where('work_repos.id', id)
      .select(
        'mo_numbers.mo_id',
        'mo_numbers.quantity',
        'mo_numbers.mo_status',
        'mo_numbers.num',
        'mo_numbers.customer',
        'mo_numbers.style',
        'styles.style_id',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        'mo_numbers.style_category',
        'mo_numbers.product_category',
        'mo_numbers.company_code',
        'work_repos.affected_units'
      )
      .first()
      .castTo<IFindMO>();
  },
  BARCODE: async (id: number, moBarcode: string) => {
    return await MoNumber.query()
      .leftJoin('styles', 'mo_numbers.style', '=', 'styles.style_number')
      .where('mo_numbers.mo_barcode', moBarcode)
      .where('mo_numbers.company_code', id)
      .select(
        'mo_numbers.mo_id',
        'mo_numbers.quantity',
        'mo_numbers.mo_status',
        'mo_numbers.num',
        'mo_numbers.customer',
        'mo_numbers.style',
        'styles.style_id',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        'mo_numbers.style_category',
        'mo_numbers.product_category',
        'mo_numbers.company_code'
      )
      .first()
      .castTo<IFindMO>();
  },
  MO_NUMBER: async (id: number, moBarcode: string) => {
    return await MoNumber.query()
      .leftJoin('styles', 'mo_numbers.style', '=', 'styles.style_number')
      .where('mo_numbers.num', moBarcode)
      .where('mo_numbers.company_code', id)
      .select(
        'mo_numbers.mo_id',
        'mo_numbers.quantity',
        'mo_numbers.mo_status',
        'mo_numbers.num',
        'mo_numbers.customer',
        'mo_numbers.style',
        'styles.style_id',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        'mo_numbers.style_category',
        'mo_numbers.product_category',
        'mo_numbers.company_code'
      )
      .first()
      .castTo<IFindMO>();
  },
};

export async function getMoInfoByBarcodeAndCompany(
  barcode: string,
  company_code: number
) {
  const moInfo = await MoNumber.query()
    .whereNotIn('mo_numbers.mo_status', ['Void', 'Cancelled', 'Materials'])
    .where('mo_numbers.mo_barcode', barcode)
    .where('mo_numbers.company_code', company_code)
    .select('mo_numbers.mo_id', 'mo_numbers.num')
    .first()
    .castTo<{ mo_id: number; num: string }>();

  return moInfo;
}

export async function getMoInfoForScan(
  action: string,
  company_code: number,
  barcode?: string
) {
  return await SEARCH_BY[action](company_code, barcode);
}
