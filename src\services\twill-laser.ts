import {
  MoTwillLaserJob,
  MoTwillLaserJobConsumption,
} from '@app/models/twill-laser';
import { buildLogger } from '@app/settings';

const logger = buildLogger('service:twill-laser');

export const completeMoTwillLaserJobService = async ({
  id,
  employee,
  machine,
}: {
  id: number;
  employee: number;
  machine: number;
}): Promise<boolean> => {
  try {
    await MoTwillLaserJob.query()
      .update({
        finished_at: new Date(),
        machine_id: machine,
        employee_id: employee,
      })
      .where({ id })
      .where('is_active', 1)
      .where('finished_at', null);

    return true;
  } catch (error) {
    logger.error(`Error: ${error}`);

    return false;
  }
};

export const getOrdersByMachine = async ({
  machineID,
  date,
}: {
  machineID: number;
  date: string;
}): Promise<number[]> => {
  try {
    const orders = (await MoTwillLaserJob.query()
      .where('machine_id', machineID)
      .where('is_active', 1)
      .andWhereBetween('created_at', [`${date} 00:00:00`, `${date} 23:59:59`])
      .select('id')) as unknown as { id: number }[];

    return orders.map((order) => order.id);
  } catch (error) {
    logger.error(`Error: ${error}`);

    return [];
  }
};

export const getJobsByOrders = async (orders: number[]): Promise<number[]> => {
  try {
    const jobs = (await MoTwillLaserJobConsumption.query()
      .where('is_active', 1)
      .whereIn('mo_twill_laser_job_id', orders)
      .select('mo_twill_laser_job_id')) as unknown as { id: number }[];

    return jobs.map((job) => job.id);
  } catch (error) {
    logger.error(`Error: ${error}`);

    return [];
  }
};

const calculateConsumptions = (
  consumptions: {
    height: number;
    width: number;
    quantity: number;
  }[]
): string => {
  const maxWidth = Math.max(
    ...consumptions.map((consumption) => consumption.width)
  );

  let total = 0;

  consumptions.forEach((consumption) => {
    if (consumption.quantity === 1) {
      if (consumption.width >= maxWidth) {
        total += consumption.height / 36;

        return;
      }

      total += consumption.height / 2 / 36;

      return;
    }

    total += (consumption.height * consumption.quantity) / 36;
  });

  return total.toFixed(2);
};

export const getOrdersCompletedByMachine = async ({
  machineID,
  date,
}: {
  machineID: number;
  date: string;
}): Promise<
  {
    id: number;
    num: string;
    style: string;
    quantity: number;
    required_date: string;
    consumptions: number;
  }[]
> => {
  try {
    const orders = await MoTwillLaserJob.query()
      .innerJoin('mo_numbers', 'mo_numbers.mo_id', 'mo_twill_laser_jobs.mo_id')
      .where('mo_twill_laser_jobs.machine_id', machineID)
      .where('mo_twill_laser_jobs.is_active', 1)
      .andWhereBetween('mo_twill_laser_jobs.created_at', [
        `${date} 00:00:00`,
        `${date} 23:59:59`,
      ])
      .select([
        'mo_twill_laser_jobs.mo_id',
        'mo_twill_laser_jobs.id',
        'mo_numbers.num',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.required_date',
      ])
      .castTo<
        {
          mo_id: number;
          id: number;
          num: string;
          style: string;
          quantity: number;
          required_date: string;
        }[]
      >();

    const consumptions: {
      id: number;
      num: string;
      style: string;
      quantity: number;
      required_date: string;
      consumptions: number;
    }[] = [];

    for (const order of orders) {
      const consumptionsByOrder = await MoTwillLaserJobConsumption.query()
        .where('is_active', 1)
        .where('mo_twill_laser_job_id', order.id)
        .select(['id', 'height', 'width', 'quantity'])
        .castTo<
          {
            id: number;
            height: number;
            width: number;
            quantity: number;
          }[]
        >();

      consumptions.push({
        id: order.id,
        num: order.num,
        style: order.style,
        quantity: order.quantity,
        required_date: order.required_date,
        consumptions: +calculateConsumptions(
          consumptionsByOrder.map((consumption) => ({
            height: consumption.height,
            width: consumption.width,
            quantity: consumption.quantity,
          }))
        ),
      });
    }

    return consumptions;
  } catch (error) {
    logger.error(`Error: ${error}`);

    return [];
  }
};
