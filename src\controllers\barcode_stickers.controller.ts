import type { Request, Response } from 'express';

import { getStickerInfo } from '@app/services/barcode_stickers.service';

export const getBarcodeStickers = async (req: Request, res: Response) => {
  const { option, value } = req.body;
  try {
    const data = await getStickerInfo({
      option,
      value,
    });

    if (data.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'No se encontraron stickers',
      });
    }

    return res.status(200).json({
      ok: true,
      data,
      totalRows: data.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
};
