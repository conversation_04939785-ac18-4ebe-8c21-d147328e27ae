import { Plotter<PERSON>ombo, PlotterPrint } from '@app/models/plotter.schema';
import { WorkAreaGroups, WorkAreaLines } from '@app/models/tickets.schema';
import { getPrint } from './plotter';

interface IPlotterPrintLocationCheck {
  plotter_combo_id: number;
  mo_id: number;
  plotter_print_id: number;
  location: string;
}

const isBin = (binValue: string) => {
  const binRegex = /^[A-Z]-\d{2}-[A-Z](-\d{2})?$/gm;
  return binRegex.test(binValue);
};

export const suggestedBinsForPlot = async (printId: number) => {
  if (!printId) {
    throw new Error('plotId is required');
  }

  const plot = await getPrint(printId);

  const moIds = plot.combos.map((combo) => combo.mo_id);

  // query to get all combos with mo_id in moIds
  const comboMosCheck = await PlotterCombo.query()
    .join(
      'plotter_prints',
      'plotter_prints.id',
      '=',
      'plotter_combos.plotter_print_id'
    )
    .whereIn('plotter_combos.mo_id', moIds)
    .select(
      'plotter_combos.id as plotter_combo_id',
      'plotter_combos.mo_id',
      'plotter_prints.id as plotter_print_id',
      'plotter_prints.location'
    )
    .castTo<IPlotterPrintLocationCheck[]>();

  // loop through and list all distinct locations
  const bins: string[] = [];
  // if plot has location, add to bin
  if (plot.location && !bins.includes(plot.location)) {
    bins.push(plot.location);
  }
  for (const plot_combo of comboMosCheck) {
    if (!plot_combo.location) {
      continue;
    }
    if (plot.combos.find((combo) => combo.id === plot_combo.plotter_combo_id)) {
      continue;
    }
    if (!bins.includes(plot_combo.location)) {
      bins.push(plot_combo.location);
    }
  }

  return {
    plot: plot,
    bins,
  };
};

export const savePlotterPrintLocation = async (
  printId: number,
  location: string
): Promise<void> => {
  if (!printId) {
    throw new Error('printId is required');
  }

  if (!location) {
    throw new Error('location is required');
  }

  if (!isBin(location)) {
    throw new Error('location must be a bin');
  }

  await PlotterPrint.query().findById(printId).patch({ location });

  return;
};

export const scanPlotterPrint = async (
  printCodes: string[],
  work_area_group_id: number,
  options?: {
    work_area_line_id?: number | null;
    location?: string | null;
  }
) => {
  if (!printCodes || !printCodes.length) {
    throw new Error('printCodes is required');
  }

  if (!work_area_group_id) {
    throw new Error('work_area_group_id is required');
  }

  const printIds: number[] = [];
  for (const printCode of printCodes) {
    if (printCode.startsWith('PP')) {
      const printId = Number(printCode.slice(2));
      if (!printId || isNaN(printId)) {
        throw new Error(`printCode is not a number: ${printCode}`);
      }
      printIds.push(printId);
    } else {
      const printId = Number(printCode);
      if (!printId || isNaN(printId)) {
        throw new Error(`printCode is not a number: ${printCode}`);
      }
      printIds.push(printId);
    }
  }

  const prints = await PlotterPrint.query().findByIds(printIds);

  if (!prints || !prints.length || prints.length !== printIds.length) {
    throw new Error('prints not found');
  }

  // check barcode
  const foundWorkAreaGroup = await WorkAreaGroups.query().findById(
    work_area_group_id
  );
  if (!foundWorkAreaGroup) {
    throw new Error('work_area_group_id not found');
  }
  if (foundWorkAreaGroup.work_area_id != 12) {
    throw new Error('Work Area Group is not a cutting area group');
  }

  let foundWorkAreaLine = null;
  if (options?.work_area_line_id) {
    foundWorkAreaLine = await WorkAreaLines.query().findById(
      options.work_area_line_id
    );
    if (!foundWorkAreaLine || foundWorkAreaLine.work_area_id != 12) {
      throw new Error('work_area_line_id not found');
    }
  }

  // update prints
  if (foundWorkAreaGroup.work_area_type === 'CUT') {
    // check prints have not been scanned already
    for (const print of prints) {
      if (print.cut_scanned) {
        throw new Error(`Print ${print.id} has already been scanned`);
      }
    }
    await PlotterPrint.query().findByIds(printIds).patch({
      cut_at: new Date(),
      cut_work_area_group_id: foundWorkAreaGroup.id,
      cut_scanned: true,
      location: options?.location,
      cut_work_area_line_id: foundWorkAreaLine?.id,
    });
    return;
  }
  if (foundWorkAreaGroup.work_area_type === 'SPREAD') {
    // check prints have not been scanned already
    for (const print of prints) {
      if (print.spread_scanned) {
        throw new Error(`Print ${print.id} has already been scanned`);
      }
    }
    await PlotterPrint.query().findByIds(printIds).patch({
      spread_at: new Date(),
      spread_work_area_group_id: foundWorkAreaGroup.id,
      spread_scanned: true,
      location: options?.location,
      spread_work_area_line_id: foundWorkAreaLine?.id,
    });
    return;
  }
  throw new Error('Work Area Group must have a type of CUT or SPREAD');
};
