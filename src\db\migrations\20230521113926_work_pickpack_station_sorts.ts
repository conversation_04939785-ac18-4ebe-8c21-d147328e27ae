import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(
    'work_pickpack_station_sorts',
    (table: Knex.TableBuilder) => {
      table.increments('id').unsigned().primary();
      table
        .timestamp('created_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      table
        .timestamp('updated_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

      table.integer('work_pickpack_station_id').unsigned().notNullable();
      table.string('work_pickpack_bin_id').notNullable();
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('work_pickpack_station_sorts');
}
