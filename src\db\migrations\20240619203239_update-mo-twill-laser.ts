import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'mo_twill_laser_jobs',
    (table: Knex.TableBuilder): void => {
      table.integer('sub_mo_id').nullable();
      table.string('comment', 255).nullable();
      table.foreign('sub_mo_id').references('mo_id').inTable('mo_numbers');
    }
  );

  await knex.schema.alterTable(
    'mo_twill_laser_job_consumptions',
    (table: Knex.TableBuilder): void => {
      table.decimal('height', 10, 2).notNullable().unsigned().alter();
      table.decimal('width', 10, 2).notNullable().unsigned().alter();
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'mo_twill_laser_jobs',
    (table: Knex.TableBuilder): void => {
      table.dropForeign('sub_mo_id');
    }
  );

  await knex.schema.alterTable(
    'mo_twill_laser_jobs',
    (table: Knex.TableBuilder): void => {
      table.dropColumn('sub_mo_id');
      table.dropColumn('comment');
    }
  );

  await knex.schema.alterTable(
    'mo_twill_laser_job_consumptions',
    (table: Knex.TableBuilder): void => {
      table.integer('height').notNullable().unsigned().alter();
      table.integer('width').notNullable().unsigned().alter();
    }
  );
}
