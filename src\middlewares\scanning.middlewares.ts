import type { NextFunction, Request, Response } from 'express';

import type {
  ICheckBadgeBarcodeRequest,
  ICheckJob,
  ICheckJobBarcodeRequest,
  IFindMO,
} from '@app/interface/scanning.interfaces';
import {
  Operators,
  WorkAreaLines,
  WorkAreaTicketStatuses,
} from '@app/models/tickets.schema';
import { sendScanLog } from '@app/services/discord';
import {
  CheckJobRequest,
  checkBadgeBarcodeRequest,
} from '@app/services/scanning';

export const SCAN_ACTION = {
  S: 'START',
  F: 'FINISH',
  C: 'CLOSE',
  R: 'RECEIVED',
};

//seteamos el body para enviar al controlador
async function setBodyRequest(
  req: Request,
  findMO: IFindMO,
  badgeBarcode: string,
  lineName: string,
  checkJob: ICheckJob
): Promise<void> {
  // set mo id
  req.body.mo_id = findMO.mo_id;
  req.body.mo_quantity = findMO.quantity;
  req.body.customer = findMO.customer;
  req.body.mo_status = findMO.mo_status;
  req.body.num = findMO.num;
  req.body.style = findMO.style;
  req.body.style_id = findMO.style_id;
  req.body.mo_order = findMO.mo_order;
  req.body.required_date = findMO.required_date;
  req.body.style_category = findMO.style_category;
  req.body.product_category = findMO.product_category;
  req.body.company_code = findMO.company_code;
  //enviar a discord si la mo no esta en produccion
  //TICKET INFORMATION
  req.body.last_work_area_ticket_id = checkJob.data.lastWorkAreaTicketId;
  req.body.last_work_ticket_area_id = checkJob.data.lastWorkTicketAreaId;
  req.body.last_work_area_ticket_finish =
    checkJob.data.lastWorkAreaTicketFinish;
  req.body.is_repo = checkJob.data.isRepo;
  req.body.repo_id = checkJob.data.repoId;
  req.body.fragment_id = checkJob.data.fragmentId;
  req.body.type_action = checkJob.data.action;
  req.body.quantity = checkJob.data.quantity;
  req.body.voucher_id = checkJob.data.voucherId;

  if (
    findMO.mo_status !== 'Ok to Produce' &&
    findMO.mo_status !== 'In Production' &&
    findMO.mo_status !== 'Materials OK'
  ) {
    await sendScanLog(
      'MO STATUS LOG',
      `La MO ${findMO.num},\n Cliente : ${findMO.customer}, \n Tiene un Status ${findMO.mo_status}, \n Barcode: ${badgeBarcode}, \nLine name : ${lineName}`
    );
  }
}
export async function checkJobBarcode(
  req: Request,
  res: Response,
  next: NextFunction
) {
  try {
    const {
      mo_id: moIDRequest,
      job_barcode: moBarcodeRequest,
      voucher_id: voucherIDRequest,
      type_action: typeActionRequest,
      badge_barcode: badgeBarcode,
      line_name: lineName,
      quantity: quantityRequest,
    }: ICheckJobBarcodeRequest = req.body;

    const checkJob = await CheckJobRequest(
      moIDRequest,
      moBarcodeRequest,
      voucherIDRequest,
      typeActionRequest,
      badgeBarcode,
      lineName,
      quantityRequest
    );
    if (checkJob.ok) {
      const findMo: IFindMO = checkJob.data.findMO;
      setBodyRequest(req, findMo, badgeBarcode, lineName, checkJob);
    } else {
      return res.status(200).json({
        ok: false,
        data: checkJob.message,
      });
    }
    return next();
  } catch (error) {
    const moBarcodeRequest = req.body.job_barcode;
    const badgeBarcode = req.body.badge_barcode;
    const lineName = req.body.line_name;

    // enviar mensaje a discord
    await sendScanLog(
      'MIDDLEWARE  CHECK JOBBARCODE ERROR',
      `checkjobbarcode : ${moBarcodeRequest}, badgebarcode: ${badgeBarcode}, \nLine name : ${lineName}, \nERROR : ${error}`
    );
    next(error);
  }
}

export async function checkBadgeBarcode(
  req: Request,
  res: Response,
  next: NextFunction
) {
  try {
    let workLine: number | null = null;
    const {
      badge_barcode: badgeBarcode,
      work_area_id: areaID,
      work_area_group_id: areaGroupID,
      company_code: companyCode,
      last_work_area_ticket_id: lastTicketID,
      last_work_ticket_area_id: lastTicketAreaID,
      last_work_area_ticket_finish: lastAreaTicketFinish,
      type_action: action,
      update_customer: updateCustomer,
      style_id: styleID,
      fragment_id: fragmentId,
      is_repo: isRepo,
      work_area_line_id: workLineId,
    }: ICheckBadgeBarcodeRequest = req.body;

    const checkBadge = await checkBadgeBarcodeRequest({
      areaGroupID: areaGroupID,
      areaID: areaID,
      styleID: styleID,
      updateCustomer: updateCustomer,
      action: action,
      badgeBarcode: badgeBarcode,
      lastTicketID: lastTicketID,
      lastTicketAreaID: lastTicketAreaID,
      lastAreaTicketFinish: lastAreaTicketFinish,
      companyCode: companyCode,
      fragment_id: fragmentId,
    });
    if (checkBadge.ok) {
      req.body.sam_value = checkBadge.data.sam_value;
      req.body.sam_id = checkBadge.data.sam_id;
      req.body.work_area_id = checkBadge.data.work_area_id; //THIS CAME FROM REQUEST
      req.body.workAreaId = checkBadge.data.workAreaId; //THIS CAME FROM WORK_AREA_GROUPS
      req.body.update_customer = checkBadge.data.update_customer; //USE THIS FOR UPDATE CUSTOMER FROM REQUEST
      req.body.group_name = checkBadge.data.group_name;
      req.body.group_description = checkBadge.data.group_description;
      req.body.work_area_name = checkBadge.data.work_area_name;
      req.body.default_work_area_line_id =
        checkBadge.data.default_work_area_line_id;
      req.body.operator_id = checkBadge.data.operator_id;
      req.body.operator_barcode = checkBadge.data.operator_barcode;
      req.body.task_name = checkBadge.data.task_name;
      req.body.work_area_group_id = checkBadge.data.work_area_group_id;
      req.body.update_system = checkBadge.data.update_system;
      req.body.varsity_system = checkBadge.data.varsity_system;
      req.body.work_area_ticket_id = checkBadge.data.work_area_ticket_id;

      // en caso de ser repo no actualizar el sistema
      if (isRepo !== undefined && isRepo !== null && isRepo) {
        req.body.update_system = 1;
      }

      // obteniendo informacion del operador
      if (
        checkBadge.data.operator_id !== undefined &&
        checkBadge.data.operator_id !== null
      ) {
        // obtener status de area para completo
        const getOperators = await Operators.query()
          .where('operator_id', +checkBadge.data.operator_id)
          .select('barcode', 'task')
          .first()
          .castTo<{ barcode: string; task: string }>();

        if (getOperators) {
          req.body.operatorBarcode = getOperators.barcode;
          req.body.taskName = getOperators.task;
        }
      }

      // check workline if added or exist in work group
      if (workLineId !== null && workLineId !== undefined) {
        workLine = workLineId;
      }

      // check si workline esta vacio significa que no existe o no fue enviada en la peticion, en este caso tomar el default en el grupo si no es nulo.
      if (
        (workLine === null || workLine === undefined) &&
        checkBadge.data.default_work_area_line_id !== undefined &&
        checkBadge.data.default_work_area_line_id !== null
      ) {
        workLine = checkBadge.data.default_work_area_line_id;
      }

      if (workLine !== null && workLine !== undefined) {
        // checkear si el id existe y si existe que sea igual al area definida
        const getLine = await WorkAreaLines.query()
          .where('id ', workLine)
          .select('work_area_id')
          .first()
          .castTo<{ work_area_id: number }>();

        if (getLine) {
          // check ids
          if (checkBadge.data.work_area_id !== getLine.work_area_id) {
            // enviar mensaje a discord
            await sendScanLog(
              'LINE DOES NOT BELONG TO THIS AREA',
              `Work Area Line enviada en la peticion no coincide con el area asignada a este grupo. \n AREA DE LINEA: ${getLine.work_area_id}, \n AREA DE GRUPO ID : ${areaGroupID}, \n AREA DE GRUPO NAME : ${checkBadge.data.group_name}, \n AREA DE GRUPO DESCRIPTION : ${checkBadge.data.group_description}, \n GROUPO: ${checkBadge.data.work_area_group_id}`
            );

            return res.status(200).json({
              ok: false,
              data: 'Work Area Line enviada en la peticion no coincide con el area asignada a este grupo',
            });
          }
        }
      }
    } else {
      return res.status(200).json({
        ok: false,
        data: checkBadge.message,
      });
    }
    return next();
  } catch (error) {
    // enviar mensaje a discord
    const moBarcodeRequest = req.body.job_barcode;
    const badgeBarcode = req.body.badge_barcode;

    await sendScanLog(
      'MIDDLEWARE CHECK BADGEBARCODE ERROR',
      `checkjobbarcode : ${moBarcodeRequest}, \nbadgebarcode: ${badgeBarcode}, \nERROR : ${error}`
    );

    next(error);
  }
}
// funcion para obtener el id de work_area_ticket_statuses
export async function getWorkTicketStatuses(
  req: Request,
  res: Response,
  next: NextFunction
) {
  // evaluar si existe area o no
  if (
    req.body.workAreaId !== 0 &&
    req.body.workAreaId !== undefined &&
    req.body.workAreaId !== null
  ) {
    // obtenemos el workAreaId
    const workAreaId = req.body.workAreaId;

    if (
      workAreaId === undefined ||
      workAreaId === null ||
      typeof workAreaId !== 'number'
    ) {
      return res.status(400).json({
        ok: false,
        data: 'workAreaId no es valido',
      });
    }

    try {
      const getWorkTicketStatuses = await WorkAreaTicketStatuses.query()
        .where('work_area_id', workAreaId)
        .where('name', 'Nuevo')
        .select('id')
        .castTo<{ id: number }[]>();

      if (getWorkTicketStatuses.length > 0) {
        req.body.workTicketStatuses = getWorkTicketStatuses[0].id;
      } else {
        // la siguiente consulta es por si no existe el work_area_ticket_statuses Activo
        // consultamos si existe el work_area_ticket_statuses Completo en el area
        const getWorkTicketStatusesCompleto =
          await WorkAreaTicketStatuses.query()
            .where('work_area_id', workAreaId)
            .where('name', 'Completo')
            .select('id', 'work_status_id');

        // si no existe el work_area_ticket_statuses Completo en el area lo creamos junto al work_area_ticket_statuses Activo
        if (getWorkTicketStatusesCompleto.length === 0) {
          const dataCompleto = {
            work_area_id: workAreaId,
            name: 'Completo',
            work_status_id: 100,
            sequence: 2,
          };

          const createWorkTicketStatusesCompleto =
            await WorkAreaTicketStatuses.query().insert(dataCompleto);

          const dataNuevo = {
            work_area_id: workAreaId,
            name: 'Nuevo',
            work_status_id: 50,
            sequence: 1,
          };

          const createWorkTicketStatusesNuevo =
            await WorkAreaTicketStatuses.query().insert(dataNuevo);

          // verificamos que se ingreso correctamente en la base de datos
          if (
            createWorkTicketStatusesNuevo.name.length > 0 &&
            createWorkTicketStatusesCompleto.name.length > 0
          ) {
            // retornamos el id del estado completo
            req.body.workTicketStatuses = createWorkTicketStatusesNuevo.id;
            req.body.workStatuses =
              createWorkTicketStatusesNuevo.work_status_id;
          } else {
            return res.status(403).json({
              ok: false,
              data: 'Ocurrio un error al ingresar los estados del area',
            });
          }
        } else {
          // si existe el work_area_ticket_statuses Completo en el area solo crearemos el work_area_ticket_statuses Activo
          const createWorkTicketStatusesNuevo =
            await WorkAreaTicketStatuses.query().insert({
              work_area_id: workAreaId,
              name: 'Nuevo',
              work_status_id: 50,
              sequence: 1,
            });

          // verificamos que se ingreso correctamente en la base de datos
          if (createWorkTicketStatusesNuevo.name.length > 0) {
            req.body.workTicketStatuses = createWorkTicketStatusesNuevo.id;
            req.body.workStatuses =
              createWorkTicketStatusesNuevo.work_status_id;
          } else {
            return res.status(403).json({
              ok: false,
              data: 'Ocurrio un error al ingresar el estado activo del area',
            });
          }
        }
      }
    } catch (error) {
      // enviar mensaje a discord
      await sendScanLog('MIDDLEWARE ERROR', 'ticket statuses_   ');
      next(error);
    }
  }

  return next();
}

/*
//OLD CODE MOVED TO SCANNING SERVICES
//checkear el barcode del job enviado, para saber que hacer con el luego
export async function checkJobBarcodeBackUp(
  req: Request,
  res: Response,
  next: NextFunction
) {
  try {    
    const {
      mo_id: moIDRequest,
      job_barcode: moBarcodeRequest,
      voucher_id: voucherIDRequest,
      type_action: typeActionRequest,
      badge_barcode: badgeBarcode,
      line_name: lineName,
      // company_code: companyCode,
    }: checkJobBarcodeRequest = req.body;    

    // *** revisar si moid ha sido seteado ***
    if (
      moIDRequest !== undefined &&
      moIDRequest !== null &&
      typeof moIDRequest === 'number'
    ) {
      const getMoInformation = await getMoInfoForScan('MO_ID', moIDRequest)

      if (getMoInformation) {
        // enviar mensaje a discord
        await sendScanLog(
          'MOID SET IN REQUEST',
          `No se encontró la MO enviada en la peticion ${moIDRequest}, Line name : ${lineName}, por medio de MO ID`
        );

        // no se encontro MO enviar error
        return res.status(200).json({
          ok: false,
          data: 'No se encontró la MO enviada en la peticion, por medio del MO ID',
        });
      }
    }

    // *** obtener voucher y accion si ha sido enviado ***
    let action: string;
    let actionFound = false;

    if (moBarcodeRequest && typeof moBarcodeRequest === 'string') {
      if (moBarcodeRequest.startsWith('MEVB')) {
        actionFound = true;
        // obtener voucher id, revisar si lleva accion o no en el voucher
        const getAction = Number(moBarcodeRequest.charAt(4));
        const voucherID = isNaN(getAction)
          ? +moBarcodeRequest.slice(5)
          : +moBarcodeRequest.slice(4);

        // verificar si voucher id a sido seteado y es diferente del obtenido en el codigo de barra
        if (
          voucherIDRequest !== undefined &&
          voucherIDRequest !== null &&
          voucherID !== voucherIDRequest
        ) {
          // enviar mensaje a discord
          await sendScanLog(
            'VOUCHERID SET IN REQUEST',
            `No se encontró la MO enviada en la peticion ${voucherIDRequest}, Line name : ${lineName}`
          );

          // send to discord and return error
          return res.status(200).json({
            ok: false,
            data: 'Voucher no es igual al enviado en la peticion',
          });
        }
        // set voucher id
        req.body.voucher_id = voucherID;
        // obtener mo_id a partir del voucher
        const findMO = await getMoInfoForScan('VOUCHER', voucherID);

        if (findMO) {
          setBodyRequest(req, findMO, badgeBarcode, lineName);        
          // set last_work_area_ticket_id WorkAreaTickets
          const getWorkAreaTickets = await WorkAreaTickets.query()
            .where('work_voucher_id ', voucherID)
            .select('id', 'work_area_id', 'finished_at')
            .orderBy('id', 'desc')
            .first()
            .castTo<{
              id: number;
              work_area_id: number;
              finished_at: string;
            }>();

          if (getWorkAreaTickets) {
            req.body.last_work_area_ticket_id = getWorkAreaTickets.id;
            req.body.last_work_ticket_area_id = getWorkAreaTickets.work_area_id;
            req.body.last_work_area_ticket_finish =
              getWorkAreaTickets.finished_at;
          }
        } else {
          // enviar mensaje a discord
          await sendScanLog(
            'VOUCHER NOT FOUND',
            `Voucher no existe en la tabla ${voucherID}, Line name : ${lineName}`
          );

          // send to discord and return error
          return res
            .status(200)
            .json({ ok: false, data: 'Voucher no existe en la tabla' });
        }
        const getScanAction = SCAN_ACTION[moBarcodeRequest.charAt(4)];
        if (getScanAction) {
          action = getScanAction;
        }
        else{
          if (typeActionRequest === undefined || typeActionRequest === null) {
            // enviar mensaje a discord
            await sendScanLog(
              'ACTION NOT FOUND',
              `Se necesita action cuando se envia voucher principal ${moBarcodeRequest}, Barcode : ${badgeBarcode}, Line name : ${lineName}`
            );
            return res.status(200).json({
              ok: false,
              data: 'Falta setear accion ya que se esta usando voucher principal',
            });
          }
          action = typeActionRequest;
        }
        req.body.voucher_code = moBarcodeRequest;
      } else if (moBarcodeRequest.startsWith('MMRP')) {
        actionFound = true;
        // obtener repo id
        const repoID = Number(moBarcodeRequest.slice(4));
        // obtener mo_id a partir del repo
        const findMO =  await getMoInfoForScan('REPO', repoID)

        if (findMO) {
          setBodyRequest(req, findMO, badgeBarcode, lineName);    
          req.body.is_repo = true;
          req.body.repo_id = repoID;
          // obtener accion si ha sido seteado
          if (typeActionRequest !== undefined && typeActionRequest !== null) {
            action = typeActionRequest;
          } else {
            action = SCAN_ACTION['F'];
          }
        } else {
          // enviar mensaje a discord
          await sendScanLog(
            'REPO NOT FOUND',
            `Repo no existe en la tabla ${repoID}, Line name : ${lineName}`
          );

          // send to discord and return error
          return res
            .status(200)
            .json({ ok: false, data: 'Repo no existe en la tabla' });
        }
      } else {
        // *** Revisar mo barcode para todos los clientes ***
        // verificar si es varsity
        if (moBarcodeRequest.includes('/')) {
          actionFound = true;
          // obtener accion si ha sido seteado
          if (typeActionRequest !== undefined && typeActionRequest !== null) {
            action = typeActionRequest;
          } else {
            action = SCAN_ACTION['F'];
          }
          req.body.company_code = 3;
          req.body.job_barcode = moBarcodeRequest;
        }

        // verificamos si es adidas
        if (moBarcodeRequest.startsWith('AINPPMO')) {
          actionFound = true;
          action = SCAN_ACTION['S'];
          req.body.job_barcode = moBarcodeRequest.slice(3);
          req.body.company_code = 2;
        } else if (moBarcodeRequest.startsWith('APPMO')) {
          actionFound = true;
          action = SCAN_ACTION['F'];
          req.body.job_barcode = moBarcodeRequest.slice(1);
          req.body.company_code = 2;
        }

        // verificamos si es varpro
        if (moBarcodeRequest.startsWith('INPPMO')) {
          actionFound = true;
          action = SCAN_ACTION['S'];
          req.body.job_barcode = moBarcodeRequest.slice(2);
          req.body.company_code = 1;
        } else if (moBarcodeRequest.startsWith('PPMO')) {
          actionFound = true;
          action = SCAN_ACTION['F'];
          req.body.company_code = 1;
        }

        // obtener mo id
        // obtenemos el job_barcode y el company_code enviado
        const moBarcode: string = req.body.job_barcode;
        const companyCode = Number(req.body.company_code);        
        if (!isNaN(companyCode)) {
          const findMO =  await getMoInfoForScan('BARCODE', companyCode, moBarcode)
          if (findMO) {
            //es codigo mo asi que se setea body
            setBodyRequest(req, findMO, badgeBarcode, lineName);
          }
          else {
            // buscar en mo numbers en caso que lo proporcionado sea un numero de MO
            const findMO = await getMoInfoForScan('MO_NUMBER', companyCode, moBarcode)
            if (findMO) {
              actionFound = true;
              // set mo id
              setBodyRequest(req, findMO, badgeBarcode, lineName)
              // set action finish if is empty
              if (typeActionRequest === undefined || typeActionRequest === null) {
                action = SCAN_ACTION['F'];
              }
            } 
          }
        }
          
         //search for fragments
         const findFragment = await WorkFragments.query()
         .where('barcode', moBarcode)
         .first();

       if (findFragment) {
         const findMO = await getMoInfoForScan('MO_ID', findFragment.mo_id)

         if (findMO) {
            actionFound = true;
            // get fragments id and send to scan
            req.body.fragment_id = findFragment.id;
            // set mo id
            setBodyRequest(req, findMO, badgeBarcode, lineName);
            // set action finish if is empty
            if (typeActionRequest === undefined || typeActionRequest === null) {
              action = SCAN_ACTION['F'];
            }
          } else {
            // enviar mensaje a discord
            await sendScanLog(
              'MO NOT FOUND',
              `No se encontró la MO ${moBarcodeRequest},\nBadgeOperator : ${badgeBarcode},\nCompanyCode: ${companyCode}, \nLine name : ${lineName}`
            );

            // no existe mandar a discord y retornar error
            return res
              .status(200)
              .json({ ok: false, data: 'No se encontró la MO' });
          }
        } 
      }
    }

    // reviasr si es un barcode valido
    if (!actionFound) {
      // enviar mensaje a discord
      await sendScanLog(
        'BARCODE FORMAT ERROR',
        `Formato incorrecto: ${moBarcodeRequest}, \nOperador : ${badgeBarcode}, \nLine name : ${lineName}`
      );

      // no son iguales enviar discord y retornar error
      return res.status(200).json({
        ok: false,
        data: `Formato incorrecto: ${moBarcodeRequest}, \nOperador : ${badgeBarcode}, \nLine name : ${lineName}`,
      });
    }

    // revisar si mo id ya ha sido seteado   y es distinto al resultado
    if (
      moIDRequest !== undefined &&
      moIDRequest !== null &&
      moIDRequest !== req.body.mo_id
    ) {
      // enviar mensaje a discord
      await sendScanLog(
        'MOID SET IN REQUEST',
        `MO no coincide con la MO enviada en la peticion ${moIDRequest}`
      );

      // no son iguales enviar discord y retornar error
      return res.status(200).json({
        ok: false,
        data: 'MO no coincide con la MO enviada en la peticion',
      });
    }

    // verificar si type_action ya ha sido seteado y es distinto al resultado
    if (
      typeActionRequest !== undefined &&
      typeActionRequest !== null &&
      typeActionRequest !== action
    ) {
      // enviar mensaje a discord
      await sendScanLog(
        'ACTION SET IN REQUEST',
        `Action no coincide con la Action enviada en la peticion ${typeActionRequest}, \nLine name : ${lineName}`
      );
      // retornar error
      return res.status(200).json({
        ok: false,
        data: 'Action no coincide con la Action enviada en la peticion',
      });
    } else {
      // set action
      req.body.type_action = action;
    }

    // revisar si action y mo_id no son nulos
    if (req.body.mo_id === null) {
      // enviar mensaje a discord
      await sendScanLog(
        'MOID REQUIRED',
        `MO ID NULO, \n Operator : ${badgeBarcode}\n `
      );

      // retornar el error
      return res.status(400).json({ ok: false, data: 'MO ID NULO' });
    }

    if (req.body.type_action === null) {
      // enviar mensaje a discord
      await sendScanLog('ACTION REQUIRED', `ACTION NULO`);
      // retornar el error
      return res.status(200).json({
        ok: false,
        data: 'ACTION ENVIADA EN LA LLAMADA Y NO TIENE FORMATO CORRECTO',
      });
    }

    // revisar cantidad proporcionada mayor que la cantidad de MO
    if (req.body.quantity !== undefined && req.body.quantity !== null) {
      if (req.body.quantity > req.body.mo_quantity) {
        // enviar mensaje a discord
        await sendScanLog(
          'QUANTITY SET',
          `EN PARCIAL SE REPORTO MAS DE LO QUE TIENE LA MO, \n Operator : ${badgeBarcode}, \nLine name : ${lineName} `
        );

        return res.status(200).json({
          ok: false,
          data: 'CANTIDAD PROPORCIONADA ES MAYOR QUE LA MO',
        });
      }
    } else {
      req.body.quantity = req.body.mo_quantity;
    }

    return next();
  } catch (error) {  
    const moBarcodeRequest = req.body.job_barcode;
    const badgeBarcode = req.body.badge_barcode;
    const lineName = req.body.line_name;

    // enviar mensaje a discord
    await sendScanLog(
      'MIDDLEWARE  CHECK JOBBARCODE ERROR',
      `checkjobbarcode : ${moBarcodeRequest}, badgebarcode: ${badgeBarcode}, \nLine name : ${lineName}, \nERROR : ${error}`
    );
    next(error);
  }
}

export async function checkBadgeBarcodeBackUP(
  req: Request,
  res: Response,
  next: NextFunction
) {
  try {
    const badgeBarcode: string = req.body.badge_barcode;
    const areaID = req.body.work_area_id;
    const areaGroupID: number = req.body.work_area_group_id;
    const companyCode = req.body.company_code;
    const lastTicketID = req.body.last_work_area_ticket_id;
    const lastTicketAreaID = req.body.last_work_ticket_area_id;
    const lastAreaTicketFinish = req.body.last_work_area_ticket_finish;
    const action = req.body.type_action;
    const updateCustomer = req.body.update_customer;
    const styleID: number = req.body.style_id;

    let workType = 0;    
    // revisar si se ha proporcionado work_area_group_id y revisar si existe en la tabla
    if (
      areaGroupID !== undefined &&
      areaGroupID !== null &&
      typeof areaGroupID === 'number'
    ) {
      const getWorkAreaGroups = await WorkAreaGroups.query()
        .where('id', areaGroupID)
        .select('work_area_id')
        .first()
        .castTo<
          {
            work_area_id: number;
          }
        >();

      if (getWorkAreaGroups) {
        // no se encontro work group con el work_group_id establecido en la peticion
        // enviar mensaje a discord
        await sendScanLog(
          'ACTION NULO',
          `grupo no encontrado con el id de grupo enviado en la peticion. GROUPID: ${areaGroupID}`
        );

        return res.status(200).json({
          ok: false,
          data: 'grupo no encontrado con el id de grupo enviado en la peticion',
        });
      }
    }

    // obtener area group por medio del codigo de barra
    if (badgeBarcode && typeof badgeBarcode === 'string') {
      const getWorkAreaGroups = await WorkAreaGroups.query()
        .join(
          'work_areas',
          'work_area_groups.work_area_id',
          '=',
          'work_areas.work_area_id'
        )
        .where('barcode', badgeBarcode)
        .select(
          'work_area_groups.work_area_id',
          'work_area_groups.id',
          'work_area_groups.default_work_area_line_id',
          'work_area_groups.update_customer',
          'work_area_groups.name',
          'work_area_groups.description',
          'work_area_groups.style_sam_group_id',
          'work_areas.work_type_id',
          'work_areas.area_name'
        )
        .first()
        .castTo<{
          work_area_id: number;
          id: number;
          default_work_area_line_id: number;
          update_customer: number;
          name: string;
          description: string;
          style_sam_group_id: number;
          work_type_id: number;
          area_name: string;
        }>();        
      if (getWorkAreaGroups) {
        // reivsar si cuadra con el enviado
        if (
          areaID !== undefined &&
          areaID !== null &&
          areaID !== getWorkAreaGroups.work_area_id
        ) {
          // enviar mensaje a discord
          await sendScanLog(
            'AREAID SET IN REQUEST',
            `Area enviada en la peticion no coincide con el area asignada a este grupo. ${areaID}`
          );

          return res.status(200).json({
            ok: false,
            data: 'Area enviada en la peticion no coincide con el area asignada a este grupo',
          });
        }

        if (
          getWorkAreaGroups.style_sam_group_id !== null &&
          styleID !== null &&
          styleID !== undefined &&
          typeof styleID === 'number'
        ) {
          // get sam value and sam id from style_sam_group_id in the group
          const getStyleSamValue = await StyleSams.query()
            .where('style_id', styleID)
            .where('style_sam_group_id', getWorkAreaGroups.style_sam_group_id)
            .select('sam', 'id ');

          if (getStyleSamValue.length === 1) {
            req.body.sam_value = getStyleSamValue[0].sam;
            req.body.sam_id = getStyleSamValue[0].id;
          }
        }

        req.body.work_area_id = getWorkAreaGroups.work_area_id;
        req.body.workAreaId = req.body.work_area_id;
        req.body.update_customer = getWorkAreaGroups.update_customer;
        req.body.group_name = getWorkAreaGroups.name;
        req.body.group_description = getWorkAreaGroups.description;

        workType = getWorkAreaGroups.work_type_id;
        req.body.work_area_name = getWorkAreaGroups.area_name;
        // obteniendo  default work arealine id en caso de que sea null
        if (getWorkAreaGroups.default_work_area_line_id !== null) {
          req.body.default_work_area_line_id =
            getWorkAreaGroups.default_work_area_line_id;
        }

        if (
          updateCustomer !== undefined &&
          updateCustomer !== null &&
          !updateCustomer
        ) {
          req.body.update_customer = !updateCustomer;
        }

        // reivsar si cuadra el grupo si ha sido establecido con el enviado
        if (
          areaGroupID !== undefined &&
          areaGroupID !== null &&
          areaGroupID !== getWorkAreaGroups.id
        ) {
          // enviar mensaje a discord
          await sendScanLog(
            'GROUPID SET IN REQUEST',
            `Group Area enviada en la peticion no coincide con grupo encontrado por medio de barcode. ${areaGroupID}`
          );

          return res.status(200).json({
            ok: false,
            data: 'Group Area enviada en la peticion no coincide con grupo encontrado por medio de barcode',
          });
        }

        req.body.work_area_group_id = getWorkAreaGroups.id;        
        // obteniendo operator_id de work_area_operator_map        
        if (
          companyCode !== undefined &&
          companyCode !== null &&
          typeof companyCode === 'number'
        ) {
          const getOperatorMap = await WorkAreaOperatorMap.query()
            .where('work_area_id ', getWorkAreaGroups.work_area_id)
            .where('company_code  ', companyCode)
            .select('operator_id')
            .first()
            .castTo<{ operator_id: number }>();          
          if (getOperatorMap) {
            req.body.operator_id = getOperatorMap.operator_id;
          } else {
            await sendScanLog(
              'OPERATOR MAP ERROR',
              `Problemas con operator map no existe operador para area: ${getWorkAreaGroups.work_area_id}, company: ${companyCode}`
            );

            return res.status(200).json({
              ok: false,
              data: `Problemas con operator map no existe operador para area: ${getWorkAreaGroups.work_area_id}, company: ${companyCode}`,
            });
          }
        }
      } else {
        if (
          companyCode !== undefined &&
          companyCode !== null &&
          typeof companyCode === 'number'
        ) {
          // search operator id  in operator table
          const getOperator = await Operators.query()
            .where('barcode', badgeBarcode)
            .where('company_code  ', companyCode)
            .where('operator_status', 1)
            .select('operator_id', 'task')
            .castTo<{ operator_id: number; task: string }[]>();

          // operator found
          if (getOperator.length > 0) {
            req.body.operator_id = getOperator[0].operator_id;
            req.body.operator_barcode = badgeBarcode;
            req.body.task_name = getOperator[0].task;
          }
        }
      }
    }

    // validad areas en last work area ticket
    if (
      lastTicketID !== null &&
      req.body.work_area_id !== null &&
      lastTicketAreaID === req.body.work_area_id &&
      lastAreaTicketFinish === null
    ) {
      req.body.work_area_ticket_id = lastTicketID;
    }

    req.body.update_system = 1;
    req.body.varsity_system = 1;

    // setting update_system
    if (req.body.operator_id === null) {
      req.body.update_system = 1;
      req.body.varsity_system = 1;
    }

    if (
      action === 'START' &&
      (workType === 12 || req.body.task_name === 'Sew')
    ) {
      req.body.update_system = 2;
      req.body.varsity_system = 1;
    }

    if (action === 'FINISH' || action === 'CLOSE') {
      // se evalua que no sea costura se actualiza y si es costura se evalua si tiene el campo update customer igual a 0 para actualizar, sirve para las secciones
      // workType !== 12 no necesario, pero se deja para evitar inconveniente
      if (req.body.update_customer === 0) {
        req.body.update_system = 0;
        req.body.varsity_system = 0;
      } else {
        req.body.update_system = 1;
        req.body.varsity_system = 1;
      }

      if (
        action === 'FINISH' &&
        req.body.work_area_ticket_id !== undefined &&
        req.body.work_area_ticket_id !== null
      ) {
        req.body.update_system = 1;
        req.body.varsity_system = 1;
      }

      if (req.body.fragment_id !== undefined && req.body.fragment_id !== null) {
        req.body.update_system = 1;
        req.body.varsity_system = 1;
      }
    }

    return next();
  } catch (error) {
    // enviar mensaje a discord
    const moBarcodeRequest = req.body.job_barcode;
    const badgeBarcode = req.body.badge_barcode;

    await sendScanLog(
      'MIDDLEWARE CHECK BADGEBARCODE ERROR',
      `checkjobbarcode : ${moBarcodeRequest}, \nbadgebarcode: ${badgeBarcode}, \nERROR : ${error}`
    );

    next(error);
  }
}
*/
