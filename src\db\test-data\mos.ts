export interface Size {
  size: string;
  qty: number;
}

export interface Mo {
  id: number;
  number: string;
  style: string;
  qty: number;
  sizes: Size[];
}

export const mos: Mo[] = [
  {
    id: 1,
    number: 'MO-0001',
    style: 'OAR1',
    qty: 17,
    sizes: [
      {
        size: 'S',
        qty: 5,
      },
      {
        size: 'M',
        qty: 7,
      },
      {
        size: 'L',
        qty: 5,
      },
    ],
  },
  {
    id: 2,
    number: 'MO-0002',
    style: 'OAR2',
    qty: 5,
    sizes: [
      {
        size: 'S',
        qty: 1,
      },
      {
        size: 'M',
        qty: 2,
      },
      {
        size: 'L',
        qty: 2,
      },
    ],
  },
  {
    id: 3,
    number: 'MO-0003',
    style: 'OAR3',
    qty: 6,
    sizes: [
      {
        size: 'S',
        qty: 2,
      },
      {
        size: 'M',
        qty: 3,
      },
      {
        size: 'L',
        qty: 1,
      },
    ],
  },
  {
    id: 4,
    number: 'MO-0004',
    style: 'OAR4',
    qty: 9,
    sizes: [
      {
        size: 'S',
        qty: 4,
      },
      {
        size: 'M',
        qty: 4,
      },
      {
        size: 'L',
        qty: 1,
      },
    ],
  },
  {
    id: 5,
    number: 'MO-0005',
    style: 'OAR5',
    qty: 1,
    sizes: [
      {
        size: 'S',
        qty: 1,
      },
    ],
  },
];
