import axios from 'axios';

const EXTERNAL_URL = 'https://restfulapi.varpro.org/PolyService.svc/poly/table';

export const getPolyTable = async <T>(
  query: string,
  columns: string,
  filter: string
) => {
  try {
    const response = await axios.post<T>(EXTERNAL_URL, {
      query,
      columns,
      filter,
    });
    return response.data;
  } catch (error) {
    console.error('Axios poly service error: ', error);
    throw error;
  }
};

export interface PolyMoWithdrawel {
  RawTransactionID: string;
  ManufactureNumber: string;
  CategoryName: string;
  SubcategoryName: string;
  ComponentFabricContent_: string;
  Comments3: string;
  DatabaseUnitSymbol: string;
  Quantity: string;
  ComponentName: string;
  PartNumber: string;
  ContainerCode: string;
  Bin: string;
  OrigInvoiceNumber: string;
}

export const getPolyMosWithdrawels = async (mos: string[]) => {
  if (mos.length === 0) {
    return Promise.resolve<PolyMoWithdrawel[]>([]);
  }
  const moFilter = mos
    .map((mo) => `[ManufactureNumber] = '${mo}'`)
    .join(' OR ');
  return getPolyTable<PolyMoWithdrawel[]>(
    'ShowRawTransactions',
    'ManufactureNumber, CategoryName, SubcategoryName, ComponentFabricContent_, Comments3, DatabaseUnitSymbol, Quantity, ComponentName, PartNumber, ContainerCode, Bin, OrigInvoiceNumber',
    `[CategoryName] = 'Fabric' AND [Quantity] < 0 AND (${moFilter})`
  );
};

export interface PolyActiveOrderDetails {
  OrderDetailsID: string;
  CustomerNumber: string;
  OrderNumber: string;
  ItemNumber: string;
  OrderStatus: string;
  ShipToName: string;
  ShipToCountry_: string;
  StatusName: string;
  PONumber: string;
  RetailerPONumber: string;
  CreateDate: string;
  OrderDate: string;
  RequiredDate: string;
  OrderTypeName: string;
  OrderType2: string;
  OrderType3: string;
  StyleNumber: string;
  GarmentSize: string;
  ItemDescription8_: string;
  StyleCategoryName: string;
  StyleSubcategoryName: string;
  ActualCount: string;
  RequestCount: string;
  UnallocateCount: string;
  AllocateCount: string;
  CommitCount: string;
  PackCount: string;
  ShipCount: string;
}

interface GetPolyActiveOrderDetailsFilters {
  customerNumbers?: string;
  poNumbers?: string;
}

export const getPolyActiveOrderDetails = async (
  filters?: GetPolyActiveOrderDetailsFilters
) => {
  const queryOptions = {
    query: 'ShowAllOrderDetails',
    columns:
      'OrderDetailsID, CustomerNumber, OrderNumber, ItemNumber, OrderStatus, ShipToName, ShipToCountry_, StatusName, PONumber, RetailerPONumber, CreateDate, OrderDate, RequiredDate, OrderTypeName, OrderType2, OrderType3, StyleNumber, GarmentSize, ItemDescription8_, StyleCategoryName, StyleSubcategoryName, ActualCount, AllocateCount, UnallocateCount, CanPack_, ShipCount, RequestCount, CommitCount, PackCount',
    filter:
      "[OrderStatus] <> 'Complete' AND [OrderStatus] <> 'Void' AND [OrderStatus] <> 'Forecast' AND [ActualCount] > 0",
  };
  if (filters?.customerNumbers && filters.customerNumbers.trim().length > 0) {
    // split customer numbers by comma and add to filter
    queryOptions.filter += ` AND (${filters.customerNumbers
      .split(',')
      .map((value) => `[CustomerNumber] = '${value}'`)
      .join(' OR ')})`;
  }
  if (filters?.poNumbers && filters.poNumbers.trim().length > 0) {
    queryOptions.filter += ` AND (${filters.poNumbers
      .split(',')
      .map((value) => `[PONumber] = '${value}'`)
      .join(' OR ')})`;
  }

  console.log('queryOptions', queryOptions);

  const data = getPolyTable<PolyActiveOrderDetails[]>(
    queryOptions.query,
    queryOptions.columns,
    queryOptions.filter
  );

  if (typeof data === 'string') {
    return [];
  }
  return data;
};

export interface PolyGoodsAllocation {
  GoodsDetailsID: string;
  OrderDetailsID: string;
  CustomerNumber: string;
  OrderNumber: string;
  ItemNumber: string;
  GarmentSize: string;
  ManufactureNumber: string;
  QuantityAllocated: string;
  QuantityAvailable: string;
  MfgStatus_: string;
  TargetDate_: string;
  MfgOriginalRequiredDate_: string;
  RunningTasks_: string;
  RunningTask_: string;
  MO_to_SO_Allocated: string;
  Inventory_to_SO_Allocated: string;
}

export const getPolyGoodsAllocations = async (
  filters?: GetPolyActiveOrderDetailsFilters
) => {
  const queryOptions = {
    query: 'ShowGoodsAllocation',
    columns:
      'GoodsDetailsID, OrderDetailsID, CustomerNumber, OrderNumber, ItemNumber, GarmentSize,ManufactureNumber, QuantityAllocated, QuantityAvailable, MfgStatus_,TargetDate_,MfgOriginalRequiredDate_, RunningTasks_,RunningTask_, MO_to_SO_Allocated,Inventory_to_SO_Allocated',
    filter: '',
  };
  if (filters?.customerNumbers && filters.customerNumbers.trim().length > 0) {
    // split customer numbers by comma and add to filter
    queryOptions.filter += `${
      queryOptions.filter != '' ? ' AND ' : ''
    }(${filters.customerNumbers
      .split(',')
      .map((value) => `[CustomerNumber] = '${value}'`)
      .join(' OR ')})`;
  }
  if (filters?.poNumbers && filters.poNumbers.trim().length > 0) {
    queryOptions.filter += `${
      queryOptions.filter != '' ? ' AND ' : ''
    }(${filters.poNumbers
      .split(',')
      .map((value) => `[PONumber] = '${value}'`)
      .join(' OR ')})`;
  }

  console.log('queryOptions', queryOptions);

  const data = getPolyTable<PolyGoodsAllocation[]>(
    queryOptions.query,
    queryOptions.columns,
    queryOptions.filter
  );

  if (typeof data === 'string') {
    return [];
  }
  return data;
};

export interface PolyManufactureDetails {
  ManufactureItem: string;
  ManufactureNumber: string;
  MfgStatusName: string;
  TargetDate: string;
  SchedFinish: string;
  QuantityOrdered: string;
  GarmentSize: string;
  ManufactureUnitID: string;
  ItemOrderNumber: string;
  ItemOrderStatus_: string;
  ItemNumber2: string;
  StyleNumber: string;
  RunningTask_: string;
}

export const getPolyManufactureDetails = async (
  filters?: GetPolyActiveOrderDetailsFilters
) => {
  const queryOptions = {
    query: 'ShowAllManufactureDetails',
    columns:
      'ManufactureItem, ManufactureNumber, MfgStatusName, TargetDate, SchedFinish, QuantityOrdered, GarmentSize, ManufactureUnitID, ItemOrderNumber, ItemOrderStatus_, ItemNumber2, StyleNumber, RunningTask_',
    filter:
      "[ItemOrderStatus_] <> 'Complete' AND [ItemOrderStatus_] <> 'Void' AND [ItemOrderStatus_] <> 'Forecast' AND [MfgStatusName] <> 'Void' AND [QuantityOrdered] > 0",
  };

  if (filters?.customerNumbers && filters.customerNumbers.trim().length > 0) {
    // split customer numbers by comma and add to filter
    queryOptions.filter += ` AND (${filters.customerNumbers
      .split(',')
      .map((value) => `[CustomerNumber] = '${value}'`)
      .join(' OR ')})`;
  }
  if (filters?.poNumbers && filters.poNumbers.trim().length > 0) {
    queryOptions.filter += ` AND (${filters.poNumbers
      .split(',')
      .map((value) => `[PONumber] = '${value}'`)
      .join(' OR ')})`;
  }

  console.log('queryOptions', queryOptions);

  const data = getPolyTable<PolyManufactureDetails[]>(
    queryOptions.query,
    queryOptions.columns,
    queryOptions.filter
  );

  if (typeof data === 'string') {
    return [];
  }
  return data;
};
