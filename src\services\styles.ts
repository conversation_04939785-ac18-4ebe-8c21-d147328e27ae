import type { Knex } from 'knex';
import type multer from 'multer';
import { raw, transaction } from 'objection';
import path from 'path';
import Client from 'ssh2-sftp-client';
import { v4 as uuidv4 } from 'uuid';

import {
  Style,
  StyleCombo,
  StyleDocument,
  StyleDocumentType,
} from '@app/models/style.schema';

export const getSuggestedStyles = async (
  style: string,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  const trx = options?.trx ?? undefined;
  if (!style) {
    throw Error('Agregue un estilo');
  }
  return await Style.query(trx)
    .where(raw('LOWER(style_number)'), 'like', `%${style.toLowerCase()}%`)
    .limit(50);
};

export const getStyleCombos = async (
  styleId: number,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  const trx = options?.trx ?? undefined;
  if (!styleId) {
    throw Error('Agregue un estilo');
  }
  return await StyleCombo.query(trx)
    .where('style_id', styleId)
    .whereNull('deleted_at');
};

export const updateStyleCombos = async (
  id: number,
  input: Partial<StyleCombo>[]
) => {
  //run 3 times to update if not error just one

  let attempt = 0;
  let success = false;
  const insertData = [];
  while (!success && attempt < 3) {
    attempt++;
    const trx = await transaction.start(StyleCombo.knex());
    try {
      //update all styles combos deleted_at where style_id = id and deleted_at is null
      //get all combos
      const combos = await StyleCombo.query(trx)
        .where('style_id', id)
        .whereNull('deleted_at');

      if (combos.length > 0) {
        const updateDeleted = await StyleCombo.query(trx)
          .where('style_id', id)
          .whereNull('deleted_at')
          .update({ deleted_at: new Date() });

        if (!updateDeleted) {
          throw Error('No se pudo actualizar los combos del estilo');
        }
      }

      //insert new style combos for each input

      for (const item of input) {
        if (item.combo_number < 1) {
          throw Error('Numero de combo es requerido');
        }

        if (isNaN(item.combo_number)) {
          throw Error('Numero de combo no tiene formato correcto');
        }

        const insert = await StyleCombo.query(trx).insert({
          style_id: id,
          created_at: new Date(),
          updated_at: new Date(),
          combo_number: item.combo_number,
          piece_count: item.piece_count,
          is_laser: item.is_laser,
          is_facing: item.is_facing,
          default_part_number:
            item.default_part_number === '' ? null : item.default_part_number,
        });

        if (!insert) {
          throw Error('No se pudo insertar los combos del estilo');
        }

        insertData.push(insert);
      }
    } catch (error) {
      await trx.rollback();
      throw error;
    } finally {
      success = true;
      await trx.commit();
    }
  }

  if (!success) {
    throw Error('No se pudo insertar los combos del estilo');
  }
  return insertData;
};

export const uploadStyleDocuments = async (
  file: multer.File,
  comment: string,
  type: string,
  styleName: string
) => {
  //save database if everything is ok
  const trx = await transaction.start(StyleDocument.knex());

  try {
    const sftpHost = process.env.SFTP_HOST;
    const sftpPort = process.env.SFTP_PORT;
    const sftpUsername = process.env.SFTP_USERNAME;
    const sftpPassword = process.env.SFTP_PASSWORD;
    const sftpRemotePath = process.env.SFTP_REMOTE_DOCUMENT_PATH;

    if (
      !sftpHost ||
      !sftpUsername ||
      !sftpPassword ||
      !sftpRemotePath ||
      !sftpPort
    ) {
      throw new Error(
        'SFTP configuration is not defined in environment variables'
      );
    }

    const documentType = await StyleDocumentType.query(trx)
      .where('name', type)
      .first();

    if (!documentType) {
      await trx.rollback();
      throw Error('Tipo de documento no encontrado');
    }

    const style = await Style.query(trx)
      .where('style_number', styleName)
      .first();

    if (!style) {
      await trx.rollback();
      throw Error('Estilo no encontrado');
    }

    //check if file type exist by documenttypeid
    const styleDocument = await StyleDocument.query(trx)
      .joinRelated({ StyleDocumentTypes: true })
      .joinRelated({ Styles: true })
      .where('StyleDocumentTypes.name', type)
      .where('Styles.style_number', styleName)
      .where('is_removed', false)
      .first();

    if (styleDocument) {
      const updateIsRemoved = await StyleDocument.query(trx)
        .where('id', styleDocument.id)
        .update({ is_removed: true });

      if (!updateIsRemoved) {
        await trx.rollback();
        throw Error('No se pudo actualizar el documento');
      }
    }

    const fileUuid = uuidv4();
    const ext = path.extname(file.originalname);

    const insert = await StyleDocument.query(trx).insertGraph({
      style_document_type_id: documentType.id,
      comment,
      file_uuid: fileUuid,
      file_extension: ext,
      style_id: style.style_id,
    });

    if (!insert) {
      await trx.rollback();
      throw Error('No se pudo insertar el documento');
    }

    const sftp = new Client();
    await sftp.connect({
      host: sftpHost,
      port: sftpPort,
      username: sftpUsername,
      password: sftpPassword,
    });
    const remoteFilePath = path.posix.join(sftpRemotePath, fileUuid + ext);
    await sftp.put(file.buffer, remoteFilePath);
    await trx.commit();

    return insert;
  } catch (error) {
    await trx.rollback();
    throw error;
  }

  //save to sftp
};

export const getSingleStyleDocument = async (
  styleId: number,
  type: string,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  const trx = options?.trx ?? undefined;
  if (!styleId) {
    throw Error('Agregue un estilo');
  }
  return await StyleDocument.query(trx)
    .joinRelated({ StyleDocumentTypes: true })
    .joinRelated({ Styles: true })
    .where('StyleDocumentTypes.name', type)
    .where('Styles.style_id', styleId)
    .where('is_removed', false)
    .first();
};
