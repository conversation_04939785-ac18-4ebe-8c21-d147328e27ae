import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'plotter_combos',
    (table: Knex.TableBuilder) => {
      table.string('style_combos', 255);
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'plotter_combos',
    (table: Knex.TableBuilder) => {
      table.dropColumn('style_combos');
    }
  );
}
