import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('plotter_roll_log', (table: Knex.TableBuilder) => {
    table.increments('id').unsigned().primary();
    table
      .timestamp('created_at')
      .notNullable()
      .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    table
      .timestamp('updated_at')
      .notNullable()
      .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

    table.integer('plotter_roll_id').unsigned().notNullable();
    table.integer('created_by_employee_id').notNullable();
    table.string('type').notNullable();
    table.jsonb('data').notNullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('plotter_roll_log');
}
