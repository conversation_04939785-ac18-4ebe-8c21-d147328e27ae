import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('warehouse_pull_sessions', (table): void => {
    table.string('customer').notNullable().after('status');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('warehouse_pull_sessions', (table): void => {
    table.dropColumn('customer');
  });
}
