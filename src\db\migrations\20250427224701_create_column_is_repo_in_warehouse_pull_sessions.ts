import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('warehouse_pull_sessions', (table) => {
    table.boolean('is_reposition').defaultTo(false).after('customer');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('warehouse_pull_sessions', (table) => {
    table.dropColumn('is_reposition');
  });
}
