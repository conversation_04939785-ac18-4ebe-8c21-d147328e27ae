import dayjs from 'dayjs';
import type { Request, Response } from 'express';
import { transaction } from 'objection';

import {
  FabricAdjustment,
  FabricIssue,
  FabricsRevision,
  FabricsRevisionItems,
} from '@app/models/calidad.schema';

interface dataNewRevision {
  auditor: number;
  comments: string;
  container: string;
  customer: string;
  cut_width: string;
  data_base_unit_symbol: string;
  dyelot: string;
  end_quantity: number;
  fabric_adjustment: number;
  invoice_number: string;
  lot_number: string;
  observation_1: string;
  observation_2: string;
  part_number: string;
  po_number: string;
  provider_number: string;
  purchase_unit_symbol: string;
  real_width: string;
  receive_number: string;
  receive_way_bill: string;
  roll_number: string;
  season: number;
  start_quantity: number;
  status_roll: string;
  tone: string;
  weight: string;
  yardasComentadas: string;
}

export async function CreateNewRollRevision(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const {
    auditor,
    comments,
    container,
    customer,
    cut_width,
    data_base_unit_symbol,
    dyelot,
    end_quantity,
    fabric_adjustment,
    invoice_number,
    lot_number,
    observation_1,
    observation_2,
    part_number,
    po_number,
    provider_number,
    purchase_unit_symbol,
    real_width,
    receive_number,
    receive_way_bill,
    roll_number,
    season,
    start_quantity,
    status_roll,
    tone,
    weight,
    yardasComentadas: revision,
  } = req.body as dataNewRevision;

  try {
    // crear transaccion para insertar en la tabla de revisiones y en la tabla de ajustes
    const roll = await transaction(
      FabricsRevision,
      FabricAdjustment,
      async (FabricsRevision: any, FabricAdjustment: any) => {
        await FabricsRevision.query().insertGraph({
          auditor,
          comments,
          container,
          customer,
          cut_width,
          data_base_unit_symbol,
          dyelot,
          end_quantity,
          invoice_number,
          lot_number,
          part_number,
          observation_1,
          observation_2,
          po_number,
          provider_number,
          purchase_unit_symbol,
          real_width,
          receive_number,
          receive_way_bill,
          revision,
          roll_number,
          season,
          start_quantity,
          status_roll,
          tone,
          weight,
        });

        await FabricAdjustment.query().insert({
          revision_date: dayjs().format('YYYYMMDD'),
          hours: dayjs().format('HHmmss'),
          number: 1,
          barcode: container,
          quantity: fabric_adjustment,
          status: 0,
        });

        return true;
      }
    );

    if (roll) {
      return res.status(201).json({
        ok: true,
        message: 'Se ingreso correctamente',
      });
    }

    return res.status(400).json({
      ok: false,
      message: 'Error, datos no validos',
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetAllFabricsIssues(
  _req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const getFabricsIssues = await FabricIssue.query()
      .where('issue_status', 1)
      .orderBy('issue', 'desc');

    return res.status(200).json({ ok: true, data: getFabricsIssues });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

interface Ipprc {
  auditor: string;
  created_at: string;
  status_roll: string;
  container: string;
  customer: string;
  end_quantity: string;
  lot_number: string;
  part_number: string;
  po_number: string;
  real_width: string;
  id: string;
}

export async function GetPPRC(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { pprc }: { pprc: string } = req.body;

  try {
    const getPPRC = (await FabricsRevision.query()
      .join('employees', 'fabrics_revision.auditor', 'employees.employee_id')
      .where('container', pprc)
      .orderBy('created_at', 'desc')
      .select([
        {
          auditor: 'employees.first_name',
        },
        'fabrics_revision.created_at',
        'fabrics_revision.status_roll',
        'fabrics_revision.container',
        'fabrics_revision.customer',
        'fabrics_revision.end_quantity',
        'fabrics_revision.lot_number',
        'fabrics_revision.part_number',
        'fabrics_revision.po_number',
        'fabrics_revision.real_width',
        'fabrics_revision.tone',
        'fabrics_revision.comments',
        'fabrics_revision.weight',
        'fabrics_revision.id',
      ])
      .limit(1)) as unknown as Ipprc[];

    if (getPPRC.length > 0) {
      const getPPRCItems = await FabricsRevisionItems.query()
        .join(
          'fabric_issue',
          'fabrics_revision_items.id_issue',
          'fabric_issue.id'
        )
        .where('fabrics_revision_id ', getPPRC[0].id)
        .select([
          {
            yard: 'fabrics_revision_items.yard_number',
          },
          {
            points: 'fabrics_revision_items.point_number',
          },
          {
            issue: 'fabric_issue.issue',
          },
        ]);

      return res.status(200).json({
        ok: true,
        data: {
          ...getPPRC[0],
          items: getPPRCItems,
        },
      });
    }

    return res.status(200).json({
      ok: false,
      data: 'No hay informacion para mostrar',
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

interface IRollInfo {
  id: number;
  status_roll: string;
  customer: string;
  container: string;
  po_number: string;
  season: number;
  provider_number: string;
  lot_number: string;
  roll_number: string;
  auditor: number;
  start_quantity: number;
  end_quantity: number;
  part_number: string;
  real_width: number;
  cut_width: number;
  comments: string;
  dyelot: string;
  created_at: string;
}
export async function GetRollInfo(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { pprc }: { pprc: string } = req.body;

  try {
    const getRollInfo = (await FabricsRevision.query()
      .join('employees', 'fabrics_revisions.auditor', 'employees.employee_id')
      .select([
        'fabrics_revisions.id',
        'fabrics_revisions.status_roll',
        'fabrics_revisions.customer',
        'fabrics_revisions.container',
        'fabrics_revisions.po_number',
        'fabrics_revisions.season',
        'fabrics_revisions.provider_number',
        'fabrics_revisions.lot_number',
        'fabrics_revisions.roll_number',
        'employees.first_name',
        'fabrics_revisions.start_quantity',
        'fabrics_revisions.end_quantity',
        'fabrics_revisions.part_number',
        'fabrics_revisions.real_width',
        'fabrics_revisions.cut_width',
        'fabrics_revisions.comment',
        'fabrics_revisions.observation',
        'fabrics_revisions.weight',
        'fabrics_revisions.tone',
        'fabrics_revisions.dyelot',
        'fabrics_revisions.created_at',
      ])
      .where('container', pprc)
      .where('is_delete', 1) // TODO: cambiar a 0
      .orderBy('created_at', 'desc')
      .limit(1)) as unknown as IRollInfo[];

    return res.status(200).json({
      ok: true,
      data: getRollInfo,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      error,
    });
  }
}

export async function GetYardInfo(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { id }: { id: number } = req.body;

  try {
    // ? Retornamos el objeto
    const getYardInfo = await FabricsRevisionItems.query()
      .joinRelated('yardIssue')
      .where('fabrics_revision_items.fabrics_revision_id', id)
      .select(
        'fabrics_revision_items.yard_number',
        'fabrics_revision_items.point_number',
        'yardIssue.issue'
      );

    return res.status(200).json({
      ok: true,
      getYardInfo,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      error,
    });
  }
}
