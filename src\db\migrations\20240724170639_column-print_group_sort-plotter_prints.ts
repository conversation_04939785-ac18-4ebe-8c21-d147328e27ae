import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('plotter_prints', (table: Knex.TableBuilder) => {
    table.smallint('print_group_sort').nullable().defaultTo(null);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('plotter_prints', (table: Knex.TableBuilder) => {
    table.dropColumn('print_group_sort');
  });
}
