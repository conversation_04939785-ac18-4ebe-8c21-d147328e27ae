import { Router } from 'express';

import {
  getBatchTicketData,
  getWarehouseBatchSuggestions,
} from '@app/services/warehouse_batching';

const batchRouter = Router();

batchRouter.route('/testTickets').get(async (req, res) => {
  const materialCategories = ['Trim', 'Supplies'];

  const tickets = await getBatchTicketData(
    9,
    [18],
    [18], // Nuevo, 641,  18 = Impresseo, 2571
    {
      material_categories: materialCategories,
    }
  );

  console.log(tickets.length);
  res.send(tickets);
});

getWarehouseBatchSuggestions;

batchRouter.route('/batchTest').get(async (req, res) => {
  const materialCategories = ['Trim', 'Supplies'];
  const voucher_type_ids = [18];
  const status_ids = [17, 18, 19];
  const suggestions = await getWarehouseBatchSuggestions(
    9,
    status_ids,
    voucher_type_ids,
    // Nuevo, 641,  18 = Impresseo, 2571
    {
      material_categories: materialCategories,
    }
  );

  console.log(suggestions.length);
  res.send(suggestions);
});

export { batchRouter };
