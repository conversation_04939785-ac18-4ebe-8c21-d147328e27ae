import currency from 'currency.js';
import xl from 'excel4node';

import type { MadeItem, ReportData, SummaryData } from './config';

export const getCPRXlsx = (
  reportData: ReportData,
  summaryData: SummaryData[],
  itemJson: MadeItem[]
) => {
  try {
    console.log('creating excel file of pcr items');
    const wb = new xl.Workbook();
    const dataSheet = wb.addWorksheet('Data');
    const summarySheet = wb.addWorksheet('Summary');

    summarySheet.cell(1, 1).string('Invoice');
    summarySheet.cell(1, 2).string(reportData.invoice_number);

    summarySheet.cell(2, 1).string('Date Range');
    summarySheet.cell(2, 2).string(reportData.report_start);
    summarySheet.cell(2, 3).string(reportData.report_end);

    summarySheet.cell(3, 1).string('Pay Date');
    summarySheet.cell(3, 2).string(reportData.pay_date);

    summarySheet.cell(4, 1).string('Run Date');
    summarySheet
      .cell(4, 2)
      .string(`${reportData.run_date} ${reportData.run_time}`);

    let curRow = 7;
    summarySheet.cell(curRow, 1).string('Class Code');
    summarySheet.cell(curRow, 2).string('Class Name');
    summarySheet.cell(curRow, 3).string('Item Count');
    summarySheet.cell(curRow, 4).string('Unit Count');
    summarySheet.cell(curRow, 5).string('Labor Additional');
    summarySheet.cell(curRow, 6).string('Cut');
    summarySheet.cell(curRow, 7).string('Sew');
    summarySheet.cell(curRow, 8).string('Total');
    summarySheet.cell(curRow, 9).string('Page');
    summarySheet.cell(curRow, 10).string('Row');
    summarySheet.cell(curRow, 11).string('Summed Total');
    summarySheet.cell(curRow, 12).string('Diff');
    curRow++;

    for (const record of summaryData) {
      const diff = currency(record.summed_items_total ?? 0).subtract(
        record.sum_total ?? 0
      ).value;
      summarySheet.cell(curRow, 1).string(record.class_code);
      summarySheet.cell(curRow, 2).string(record.class_name);
      summarySheet.cell(curRow, 3).number(record.sum_item_count ?? 0);
      summarySheet.cell(curRow, 4).number(record.sum_unit_count ?? 0);
      summarySheet.cell(curRow, 5).number(record.sum_labor ?? 0);
      summarySheet.cell(curRow, 6).number(record.sum_cut ?? 0);
      summarySheet.cell(curRow, 7).number(record.sum_sew ?? 0);
      summarySheet.cell(curRow, 8).number(record.sum_total ?? 0);
      summarySheet.cell(curRow, 9).number(record.page);
      summarySheet.cell(curRow, 10).number(record.row);
      summarySheet.cell(curRow, 11).number(record.summed_items_total ?? 0);
      summarySheet.cell(curRow, 12).number(diff);
      curRow++;
    }

    const headingColumnNames = [
      'page',
      'row',
      'class_code',
      'class_name',
      'class',
      'style',
      'order',
      'vch',
      'mo',
      'style_text_overlap_order',
      'sub_code',
      'ltr',
      'size',
      'CP',
      'qty',
      'unit',
      'cut',
      'sew',
      'sub_labor',
      'prt',
      'prs',
      'art',
      'total',
    ];
    //Write Column Title in Excel file
    let headingColumnIndex = 1;
    headingColumnNames.forEach((heading) => {
      dataSheet.cell(1, headingColumnIndex++).string(heading);
    });
    dataSheet.cell(1, headingColumnIndex).string('Diff Total');

    //Write Data in Excel file
    let rowIndex = 2;
    console.log('itemJson', itemJson.length);
    for (const record of itemJson) {
      // console.log('record', record);
      let columnIndex = 0;
      const total = currency(record.total ?? 0).value;
      const summedValues = currency(record.cut ?? 0)
        .add(record.sew ?? 0)
        .add(record.sub_labor ?? 0)
        .add(record.prt ?? 0)
        .add(record.prs ?? 0)
        .add(record.art ?? 0).value;
      const diff = summedValues - total;

      const moString = `${record.order}/${record.vch
        .toString()
        .padStart(3, '0')}`;

      for (const columnName of headingColumnNames) {
        columnIndex++;
        if (columnName === 'mo') {
          dataSheet.cell(rowIndex, columnIndex).string(moString);
          continue;
        }
        const value = record[columnName];
        // console.log('cell', rowIndex, columnName, value);
        if (!value) {
          continue;
        }
        if (typeof value === 'string') {
          dataSheet.cell(rowIndex, columnIndex).string(value);
          continue;
        }
        if (typeof value === 'number') {
          dataSheet.cell(rowIndex, columnIndex).number(value);
          continue;
        }
      }
      dataSheet.cell(rowIndex, columnIndex + 1).number(diff);
      rowIndex++;
    }

    return wb;
  } catch (e) {
    console.log('error creating excel file', e);
    throw e;
  }
};
