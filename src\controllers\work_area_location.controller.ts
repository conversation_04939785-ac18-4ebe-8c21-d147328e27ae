import type { Request, Response } from 'express';

import { WorkActivityLog } from '@app/models/tickets.schema';

const {
  WorkInventoryBins,
  WorkZones,
  WorkAreaZones,
  WorkAreaTickets,
} = require('../models/tickets.schema');
const { raw } = require('objection');

export async function updateLocationArea(req: Request, res: Response) {
  try {
    const {
      id,
      isle,
      locationBarcode,
      name,
      rack,
      shelf,
      workInventoryZoneId,
    } = req.body;

    if (isle) {
      const updateIsle = await WorkInventoryBins.query()
        .update({
          isle,
        })
        .where('work_inventory_bins.id', id);
    }
    if (locationBarcode) {
      const updateBarcode = await WorkInventoryBins.query()
        .update({
          barcode: locationBarcode,
        })
        .where('work_inventory_bins.id', id);
    }
    if (name) {
      const updateName = await WorkInventoryBins.query()
        .update({
          name,
        })
        .where('work_inventory_bins.id', id);
    }
    if (rack) {
      const updateRack = await WorkInventoryBins.query()
        .update({
          rack,
        })
        .where('work_inventory_bins.id', id);
    }
    if (shelf) {
      const updateShelf = await WorkInventoryBins.query()
        .update({
          shelf,
        })
        .where('work_inventory_bins.id', id);
    }
    if (workInventoryZoneId) {
      const updateWorkInventoryZoneId = await WorkInventoryBins.query()
        .update({
          work_zone_id: workInventoryZoneId,
        })
        .where('work_inventory_bins.id', id);
    }

    return res.status(200).json({
      ok: true,
      data: req.body,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

// ! actualizar datos
export async function getAllLocationsArea(req: Request, res: Response) {
  try {
    const { workAreaId } = req.query;

    const getLocations = await WorkInventoryBins.query()
      .join('work_zones', 'work_inventory_bins.work_zone_id', 'work_zones.id')
      .join('work_area_zones', 'work_zones.id', 'work_area_zones.work_zone_id')
      .join('buildings', 'work_zones.building_id', 'buildings.building_id')
      .where('work_area_zones.work_area_id', workAreaId)
      .select(
        { idLocation: 'work_inventory_bins.id' },
        { idInventoryZone: 'work_zones.id' },
        { nameLocation: 'work_inventory_bins.name' },
        { BarcodeLocation: 'work_inventory_bins.barcode' },
        { nameZone: 'work_zones.name' },
        { build: 'buildings.building' }
      );

    return res.status(200).json({
      ok: true,
      data: getLocations,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getAllZonesArea(req: Request, res: Response) {
  try {
    const { workAreaId } = req.body;

    const getZonesArea = await WorkAreaZones.query()
      .join('work_zones', 'work_area_zones.work_zone_id', 'work_zones.id')
      .where('work_area_zones.work_area_id', workAreaId)
      .select({ idZone: 'work_zones.id' }, { nameZone: 'work_zones.name' });

    return res.status(200).json({
      ok: true,
      data: getZonesArea,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function addNewZone(req: Request, res: Response) {
  try {
    const { workAreaId, zoneName, zoneBarcode, buildingId } = req.body;
    const addNewAreaLocation = await WorkZones.query().insert({
      work_factory_id: 1,
      name: zoneName,
      code: zoneBarcode,
      building_id: buildingId,
    });

    const addNewZoneArea = await WorkAreaZones.query().insert({
      work_area_id: workAreaId,
      work_zone_id: addNewAreaLocation.id,
    });
    return res.status(200).json({
      ok: true,
      data: { addNewAreaLocation, addNewZoneArea },
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function addAreaLocation(req: Request, res: Response) {
  try {
    const { isle, locationBarcode, name, rack, shelf, workInventoryZoneId } =
      req.body as unknown as {
        isle: string;
        locationBarcode: string;
        name: string;
        rack: string;
        shelf: string;
        workInventoryZoneId: number;
      };

    if (locationBarcode?.length > 0) {
      const addNewLocationArea = await WorkInventoryBins.query().insert({
        work_zone_id: workInventoryZoneId,
        name: name,
        barcode: locationBarcode,
        isle: isle || null,
        rack: rack || null,
        shelf: shelf || null,
      });

      return res.status(200).json({
        ok: true,
        data: addNewLocationArea,
      });
    } else {
      const addNewLocationArea = await WorkInventoryBins.query().insert({
        work_zone_id: workInventoryZoneId,
        name: name,
        isle: isle || null,
        rack: rack || null,
        shelf: shelf || null,
      });

      const insertBarcodeLocation = await WorkInventoryBins.query()
        .update({
          barcode: `MBN0${addNewLocationArea.id}`,
        })
        .where('id', addNewLocationArea.id);

      return res.status(200).json({
        ok: true,
        data: { addNewLocationArea, insertBarcodeLocation },
      });
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getAllTicketsLocationsArea(req: Request, res: Response) {
  try {
    const { workAreaId, location } = req.body;

    const getAllTickets = await WorkAreaTickets.query()
      .select([
        { ticket: 'work_area_tickets.id' },
        { voucher: 'work_vouchers.id' },
        { voucherType: 'work_voucher_types.name' },
        { last_activity_status_date: 'last_activity.updated_at' },
        { mo: 'mo_numbers.num' },
        'mo_numbers.customer',
        'mo_numbers.quantity',
        'mo_numbers.style',
        raw("CASE WHEN work_vouchers.is_repo = 1 THEN 'SI' ELSE 'NO' END").as(
          'isRepo'
        ),
      ])
      .leftJoin(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .leftJoin('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
      .leftJoin(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .leftJoin(
        'work_area_ticket_statuses',
        'work_area_ticket_statuses.id',
        'work_area_tickets.work_area_ticket_status_id'
      )
      .leftJoin(
        WorkActivityLog.query()
          .select('employees.first_name', 'work_activity_log.updated_at', {
            ticket_id: 'latest_work_activity.module_id',
          })
          .join(
            WorkActivityLog.query()
              .select('work_activity_log.module_id')
              // @ts-ignore
              .max({ max_activity_id: 'work_activity_log.id' })
              .where('work_activity_log.activity', 'TicketStatusChanged')
              .groupBy('work_activity_log.module_id')
              .as('latest_work_activity'),
            'latest_work_activity.max_activity_id',
            'work_activity_log.id'
          )
          .leftJoin(
            'employees',
            'employees.employee_id',
            'work_activity_log.employee_id'
          )
          .whereNotNull('latest_work_activity.module_id')
          .as('last_activity'),
        'last_activity.ticket_id',
        'work_area_tickets.id'
      )
      .whereNull('work_area_tickets.finished_at')
      .where('work_area_tickets.work_area_id', workAreaId)
      .where('work_area_tickets.work_inventory_location_id', location)
      .orderBy('work_area_tickets.created_at', 'DESC');

    if (getAllTickets.length === 0) {
      return res.status(400).json({
        ok: false,
        data: [],
      });
    }

    return res.status(200).json({
      ok: true,
      data: getAllTickets,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getAllTicketsStatusArea(req: Request, res: Response) {
  try {
    const { workAreaId, status } = req.body;

    const getAllTickets = await WorkAreaTickets.query()
      .select([
        { ticket: 'work_area_tickets.id' },
        { voucher: 'work_vouchers.id' },
        { voucherType: 'work_voucher_types.name' },
        { last_activity_status_date: 'last_activity.updated_at' },
        { mo: 'mo_numbers.num' },
        'mo_numbers.customer',
        'mo_numbers.quantity',
        'mo_numbers.style',
        raw("CASE WHEN work_vouchers.is_repo = 1 THEN 'SI' ELSE 'NO' END").as(
          'isRepo'
        ),
      ])
      .leftJoin(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .leftJoin('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
      .leftJoin(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .leftJoin(
        'work_area_ticket_statuses',
        'work_area_ticket_statuses.id',
        'work_area_tickets.work_area_ticket_status_id'
      )
      .leftJoin(
        WorkActivityLog.query()
          .select('employees.first_name', 'work_activity_log.updated_at', {
            ticket_id: 'latest_work_activity.module_id',
          })
          .join(
            WorkActivityLog.query()
              .select('work_activity_log.module_id')
              // @ts-ignore
              .max({ max_activity_id: 'work_activity_log.id' })
              .where('work_activity_log.activity', 'TicketStatusChanged')
              .groupBy('work_activity_log.module_id')
              .as('latest_work_activity'),
            'latest_work_activity.max_activity_id',
            'work_activity_log.id'
          )
          .leftJoin(
            'employees',
            'employees.employee_id',
            'work_activity_log.employee_id'
          )
          .whereNotNull('latest_work_activity.module_id')
          .as('last_activity'),
        'last_activity.ticket_id',
        'work_area_tickets.id'
      )
      .whereNull('work_area_tickets.finished_at')
      .where('work_area_tickets.work_area_id', workAreaId)
      .where('work_area_tickets.work_area_ticket_status_id ', status)
      .orderBy('work_area_tickets.created_at', 'DESC');

    if (getAllTickets.length === 0) {
      return res.status(200).json({
        ok: false,
        data: [],
      });
    }

    return res.status(200).json({
      ok: true,
      data: getAllTickets,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}
