interface Pagination {
  page: number;
  size: number;
  count: number;
}

interface PaginationResult {
  totalItems: number;
  totalPages: number;
  currentPage: number;
  limit: number;
  offset: number;
}

export const getPagination = ({
  page,
  size,
  count,
}: Pagination): PaginationResult => {
  const limit = size;
  const currentPage = page;
  const totalPages = Math.ceil(count / limit);

  // if page is greater than totalpages the offset is 0

  // return void
  if (currentPage > totalPages) {
    return {
      totalItems: count,
      totalPages,
      currentPage: totalPages,
      limit,
      offset: 0,
    };
  }

  return {
    totalItems: count,
    totalPages,
    currentPage,
    limit,
    offset: (page - 1) * limit,
  };
};
