// Importamos el modulo Router de express
import { Router } from 'express';

// Importamos nuestro controlador
import {
  getBraidReception,
  getUserinformation,
  updateDatabase,
} from '@app/controllers/warehouse.controller';

// Instanciamos el método Router
const warehousesRouter = Router();

// Establecemos las rutas pasando como parametros las funciones establecidas en el controlador
warehousesRouter.route('/getBraidReception').post(getBraidReception);

warehousesRouter.route('/updateDatabase').post(updateDatabase);

warehousesRouter.route('/employee').post(getUserinformation);

// Exportamos nuestra configuración de rutas
export { warehousesRouter };
