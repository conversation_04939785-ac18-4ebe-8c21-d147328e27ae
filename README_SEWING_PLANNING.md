# Sewing Planning Module

This module provides intelligent sewing line planning capabilities for manufacturing orders (MOs). It analyzes MO requirements, sewing line capabilities, and historical performance to recommend optimal line assignments.

## Features

- **Eligible MO Detection**: Identifies MOs that are not complete/void/cancelled and have been scanned at least once
- **Sewing Line Analysis**: Tracks which sewing lines have worked on specific styles in the past month
- **Efficiency Tracking**: Monitors latest efficiency metrics for each sewing line
- **Intelligent Planning**: Recommends optimal line assignments based on style experience, efficiency, and capacity

## API Endpoints

### Base URL: `/sewing-planning`

### 1. Get Eligible MOs

**GET** `/eligible-mos`

Returns manufacturing orders that are eligible for sewing planning.

**Query Parameters:**

- `company_codes` (array): Filter by company codes
- `style_categories` (array): Filter by style categories
- `customers` (array): Filter by customer names
- `required_date_from` (string): Filter by required date from (YYYY-MM-DD)
- `required_date_to` (string): Filter by required date to (YYYY-MM-DD)
- `min_quantity` (number): Minimum quantity filter
- `max_quantity` (number): Maximum quantity filter
- `sections` (array): Filter by sewing line sections

**Response:**

```json
{
  "ok": true,
  "data": [
    {
      "mo_id": 12345,
      "mo_number": "MO-2024-001",
      "style": "STYLE123",
      "style_id": 456,
      "customer": "Customer A",
      "quantity": 1000,
      "required_date": "2024-03-15",
      "mo_status": "In Production",
      "style_category": "Apparel",
      "product_category": "Shirts",
      "company_code": 1,
      "scan_count": 5,
      "last_scan_date": "2024-02-20T10:30:00Z"
    }
  ],
  "count": 1
}
```

### 2. Get Sewing Line Efficiency

**GET** `/sewing-lines/efficiency`

Returns efficiency data for active sewing lines.

**Query Parameters:**

- `sections` (array): Filter by sewing line sections

**Response:**

```json
{
  "ok": true,
  "data": [
    {
      "work_area_group_id": 101,
      "work_area_group_name": "Sewing Line A",
      "latest_efficiency": 0.85,
      "shift_date": "2024-02-20T08:00:00Z",
      "operator_count": 12,
      "usable_minutes": 4800,
      "est_production_minutes": 4080,
      "section": "Main Floor",
      "planning_name": "Line A - Main"
    }
  ],
  "count": 1
}
```

### 3. Get Sewing Line Style History

**POST** `/sewing-lines/style-history`

Returns historical data about which sewing lines have worked on specific styles.

**Request Body:**

```json
{
  "styles": ["STYLE123", "STYLE456"]
}
```

**Response:**

```json
{
  "ok": true,
  "data": [
    {
      "work_area_group_id": 101,
      "work_area_group_name": "Sewing Line A",
      "style": "STYLE123",
      "style_id": 456,
      "scan_count": 15,
      "last_scan_date": "2024-02-18T14:30:00Z",
      "total_quantity_scanned": 2500,
      "avg_efficiency": 0.82
    }
  ],
  "count": 1
}
```

### 4. Generate Sewing Plan

**GET** `/generate-plan`

Generates a complete sewing plan with MO-to-line recommendations.

**Query Parameters:** Same as `/eligible-mos`

**Response:**

```json
{
  "ok": true,
  "data": {
    "eligible_mos": [...],
    "sewing_lines": [...],
    "mo_plans": [
      {
        "mo_id": 12345,
        "mo_number": "MO-2024-001",
        "style": "STYLE123",
        "customer": "Customer A",
        "quantity": 1000,
        "required_date": "2024-03-15",
        "recommended_lines": [
          {
            "work_area_group_id": 101,
            "work_area_group_name": "Sewing Line A",
            "recommendation_score": 85.5,
            "style_experience_score": 25.0,
            "efficiency_score": 25.5,
            "capacity_score": 18.0,
            "style_similarity_score": 17.0,
            "last_worked_style": "2024-02-18T14:30:00Z",
            "latest_efficiency": 0.85,
            "section": "Main Floor",
            "planning_name": "Line A - Main"
          }
        ],
        "priority_score": 75.5
      }
    ],
    "summary": {
      "total_mos": 25,
      "total_lines": 8,
      "avg_efficiency": 82.5,
      "total_quantity": 15000
    }
  }
}
```

### 5. Get Dashboard Data

**GET** `/dashboard`

Returns summary data for the sewing planning dashboard.

**Query Parameters:**

- `company_codes` (array): Filter by company codes
- `sections` (array): Filter by sewing line sections

**Response:**

```json
{
  "ok": true,
  "data": {
    "summary": {
      "total_mos": 25,
      "total_lines": 8,
      "total_quantity": 15000,
      "avg_efficiency": 82.5,
      "urgent_mos": 3
    },
    "urgent_mos": [...],
    "top_performing_lines": [...],
    "recent_mos": [...]
  }
}
```

## Recommendation Algorithm

The system uses a weighted scoring algorithm to recommend optimal sewing lines for each MO:

### Recommendation Score Components:

1. **Style Experience Score (30% weight)**: Based on how many times the line has worked on the specific style
2. **Efficiency Score (30% weight)**: Based on the line's latest efficiency rating
3. **Capacity Score (20% weight)**: Based on operator count and available minutes
4. **Style Similarity Score (20% weight)**: **NEW** - Bonus for similar styles that can be batched together

### Style Similarity Algorithm:

The system analyzes style codes to identify similar styles that can be efficiently scheduled together:

- **Same Prefix (40% similarity)**: Styles with identical prefixes (e.g., "ABC-123" and "ABC-456")
- **Same Category (30% similarity)**: Styles with matching category codes
- **Similar Variants (20% similarity)**: Styles with related size/variant codes (e.g., "XL" vs "L")
- **String Similarity (10% similarity)**: Overall character matching for prefix alignment

**Batching Logic**:

- Identifies other MOs with similar styles within 7 days of the target MO's required date
- Compares with styles the line has previous experience with
- Awards higher scores when similar styles can be grouped together
- Reduces setup time and maintains workflow consistency

### Priority Score for MOs:

- **Urgency Score**: Based on days until required date
- **Quantity Score**: Based on order quantity (normalized)

## Database Tables Used

- `mo_numbers`: Manufacturing orders
- `mo_scans`: Scanning records for tracking MO progress
- `work_area_groups`: Sewing lines/work groups
- `work_area_group_shifts`: Efficiency and shift data
- `styles`: Style information

## Usage Examples

### Get all eligible MOs for company 1:

```
GET /sewing-planning/eligible-mos?company_codes=1
```

### Generate plan for urgent orders:

```
GET /sewing-planning/generate-plan?required_date_to=2024-03-01
```

### Get efficiency for main floor lines:

```
GET /sewing-planning/sewing-lines/efficiency?sections=Main Floor
```

## Error Handling

All endpoints return standardized error responses:

```json
{
  "ok": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

Common HTTP status codes:

- `200`: Success
- `400`: Bad Request (invalid parameters)
- `500`: Internal Server Error
