import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'mo_twill_laser_jobs',
    (table: Knex.TableBuilder): void => {
      table.renameColumn(
        'mo_twill_laser_job_type_id',
        'depricated_mo_twill_laser_job_type_id'
      );
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'mo_twill_laser_jobs',
    (table: Knex.TableBuilder): void => {
      table.renameColumn(
        'depricated_mo_twill_laser_job_type_id',
        'mo_twill_laser_job_type_id'
      );
    }
  );
}
