import type { <PERSON><PERSON> } from 'knex';
import type { Multer } from 'multer';
import type { PartialModelObject } from 'objection';
import path from 'path';
import Client from 'ssh2-sftp-client';

import { WorkFactories } from '@app/models/factory.schema';

export const savePolyFile = async (
  polyExcelFile: Multer.File,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  const trx = options?.trx ?? undefined;

  const sftpHost = process.env.SFTP_HOST;
  const sftpPort = process.env.SFTP_PORT;
  const sftpUsername = process.env.SFTP_USERNAME;
  const sftpPassword = process.env.SFTP_PASSWORD;
  const sftpRemotePath = process.env.SFTP_REMOTE_PATH;

  if (
    !sftpHost ||
    !sftpUsername ||
    !sftpPassword ||
    !sftpRemotePath ||
    !sftpPort
  ) {
    throw new Error(
      'SFTP configuration is not defined in environment variables'
    );
  }

  const timestamp = new Date().toISOString().replace(/[:.-]/g, '');
  const ext = path.extname(polyExcelFile.originalname);
  const baseName = path.basename(polyExcelFile.originalname, ext);
  const newFileName = `${baseName}_${timestamp}${ext}`;
  const remoteFilePath = path.posix.join(sftpRemotePath, newFileName);
  const sftp = new Client();

  try {
    await sftp.connect({
      host: sftpHost,
      port: sftpPort,
      username: sftpUsername,
      password: sftpPassword,
    });

    await sftp.put(polyExcelFile.buffer, remoteFilePath);
  } catch (err) {
    console.error('Error during SFTP upload:', err.message);
    throw err;
  } finally {
    await sftp.end();
  }

  const getWorkFactory = await WorkFactories.query(trx).where('id', 1).first();

  if (!getWorkFactory) {
    throw new Error('WorkFactory not found');
  }

  const wf = await WorkFactories.query(trx).updateAndFetchById(
    getWorkFactory.id,
    {
      latest_poly_excel_file: newFileName,
    } as PartialModelObject<WorkFactories>
  );

  if (!wf) {
    throw new Error('Error updating latest_poly_excel_file');
  }

  return remoteFilePath;
};
