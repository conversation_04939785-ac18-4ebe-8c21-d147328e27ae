import dayjs from 'dayjs';
import type { Request, Response } from 'express';
import { fn, raw, ref, transaction } from 'objection';
import { v4 as uuidv4 } from 'uuid';

import { createScan } from './scanning.controller';

const {
  WorkActivityLog,
  WorkAreas,
  WorkAreaTickets,
  WorkAreaTicketStatuses,
  WorkInventoryBins,
  MoVouchers,
  WorkVouchers,
  WorkNotes,
  WorkVoucherGroups,
  MoScans,
  MoNumbers,
} = require('../models/tickets.schema');

export async function CreateWorkVoucher(
  req: Request,
  res: Response
): Promise<Response | any> {
  const moId = req.body.moId;
  const workAreaId = req.body.workAreaId;
  //let supervisor = req.body.barcode_operator;
  //let task = req.body.task;
  const format1 = 'YYYY-MM-DD HH:mm:ss';
  const actualDate = new Date();
  // data de los middlewares
  const data = {
    moQuantity: req.body.moQuantity,
    operatorId: req.body.operatorId,
    typeAction: req.body.typeAction,
    client: req.body.client,
    isVoucher: req.body.voucher,
    voucherCode: req.body.voucherCode,
    workVoucherTypeId: req.body.work_voucher_type_id,
    company_code: req.body.companyCode,
  };

  //obtener disable areas si estan disables solo se creara el mo scan sino se hara logica de tickets
  const getDisableArea = await WorkAreas.query()
    .where('work_area_id', workAreaId)
    .select('disabled_date');

  try {
    if (workAreaId === 0 || getDisableArea[0].disabled_date !== null) {
      return await createScan(req, res);
    } else {
      //obtener status de area, tanto completo como activo
      //obtenemos status activo en el area
      const getWorkTicketActiveStatus = await WorkAreaTicketStatuses.query()
        .where('work_area_id', workAreaId)
        .where('name', 'Nuevo')
        .select('id');
      //obtenemos status completo en el area
      const getWorkTicketCompleteStatus = await WorkAreaTicketStatuses.query()
        .where('work_area_id', workAreaId)
        .where('name', 'Completo')
        .select('id');

      //revisar si es un voucher escaneado para obtener su informacion
      if (data.isVoucher) {
        const voucherId = data.voucherCode.slice(5);

        if (data.typeAction === 'ENTRADA') {
          //revisar si ya existe un ticket con ese voucher en el area
          const checkTicketExist = await WorkAreaTickets.query()
            .where('work_voucher_id', voucherId)
            .where('work_area_id', workAreaId)
            .select('id', 'work_area_id')
            .limit(1);
          if (checkTicketExist.length > 0) {
            //ya existe un ticket con este voucher en el area
            return res.status(201).json({
              ok: true,
              message: `Ya existe un ticket en el area para este voucher ${voucherId}`,
            });
          } else {
            //no existe revisar mas informacion
            //evaluar si el voucher esta en el area anterior como cerrado sino cerrarlo
            const checkOpenVoucherPreArea = await WorkAreaTickets.query()
              .where('work_voucher_id', voucherId)
              .select('id', 'work_area_id', 'finished_at')
              .orderBy('id', 'desc')
              .limit(1);

            //si existe cambiaremos el status a completo
            if (checkOpenVoucherPreArea.length > 0) {
              //si esta nulo el campo de finished_at, completar el ticket anterior
              if (checkOpenVoucherPreArea[0].finished_at === null) {
                //obtener status completo en base al area
                const getWorkPreviewTicketStatuses =
                  await WorkAreaTicketStatuses.query()
                    .where(
                      'work_area_id',
                      checkOpenVoucherPreArea[0].work_area_id
                    )
                    .where('name', 'Completo')
                    .select('id');

                //actualizamos tickets antes de ingresarlo
                await WorkAreaTickets.query()
                  .update({
                    work_area_ticket_status_id:
                      getWorkPreviewTicketStatuses[0].id,
                    finished_at: dayjs(actualDate).format(format1),
                  })
                  .where('id', checkOpenVoucherPreArea[0].id);
              }
            }
            //se creara un nuevo ticket para ese voucher si es que no existe
            const createTickets = await WorkAreaTickets.query().insert({
              work_area_id: workAreaId,
              work_voucher_id: voucherId,
              work_area_ticket_status_id: getWorkTicketActiveStatus[0].id,
              prev_work_area_id:
                checkOpenVoucherPreArea.length > 0
                  ? checkOpenVoucherPreArea[0].work_area_id
                  : null,
              notify_company: 0,
            });
            return res.status(200).json({
              ok: true,
              message:
                'Se ha creado ticket a partir de un voucher existente en otra area ',
              data: createTickets,
            });
          }
        } else if (data.typeAction === 'SALIDA') {
          //es salida de un voucher en el area
          const checkTicketExist = await WorkAreaTickets.query()
            .where('work_voucher_id', voucherId)
            .where('work_area_id', workAreaId)
            .select('id', 'work_area_id', 'finished_at')
            .orderBy('id', 'desc')
            .limit(1);

          //evaluar si existe
          if (checkTicketExist.length > 0) {
            //existe, evaluar si la fecha de fin esta nula
            if (checkTicketExist[0].finished_at === null) {
              //actualizar status y fecha de fin
              await WorkAreaTickets.query()
                .update({
                  work_area_ticket_status_id: getWorkTicketCompleteStatus[0].id,
                  notify_company: 0,
                  finished_at: dayjs(actualDate).format(format1),
                })
                .where('id', checkTicketExist[0].id);
              //AGREGAR MO SCAN

              // se insertara en mo_scans
              return await createScan(req, res);
              ///return res.status(200).json({
              //ok: true,
              //message: `Se realizo la salida del ticket en el area`,
              //});
            } else {
              return res.status(201).json({
                ok: true,
                message: `Ya existe un ticket en el area para este voucher ${voucherId}`,
              });
            }
          } else {
            //voucher no existe, evaluar si esta en area previa y completarla antes de completarla en nuestra area
            //actualizar area previa
            //evaluar si el voucher esta en el area anterior como cerrado sino cerrarlo
            const checkOpenVoucherPreArea = await WorkAreaTickets.query()
              .where('work_voucher_id', voucherId)
              .select('id', 'work_area_id', 'finished_at')
              .orderBy('id', 'desc')
              .limit(1);

            //si existe cambiaremos el status a completo
            if (checkOpenVoucherPreArea.length > 0) {
              //si esta nulo el campo de finished_at, completar el ticket anterior
              if (checkOpenVoucherPreArea[0].finished_at === null) {
                //obtener status completo en base al area
                const getWorkPreviewTicketStatuses =
                  await WorkAreaTicketStatuses.query()
                    .where(
                      'work_area_id',
                      checkOpenVoucherPreArea[0].work_area_id
                    )
                    .where('name', 'Completo')
                    .select('id');

                //actualizamos tickets antes de ingresarlo
                await WorkAreaTickets.query()
                  .update({
                    work_area_ticket_status_id:
                      getWorkPreviewTicketStatuses[0].id,
                    finished_at: dayjs(actualDate).format(format1),
                  })
                  .where('id', checkOpenVoucherPreArea[0].id);
              }
            }

            //crear ticket y cerrarlo
            //se creara un nuevo ticket para ese voucher si es que no existe
            const createTickets = await WorkAreaTickets.query().insert({
              work_area_id: workAreaId,
              work_voucher_id: voucherId,
              work_area_ticket_status_id: getWorkTicketActiveStatus[0].id,
              prev_work_area_id:
                checkOpenVoucherPreArea.length > 0
                  ? checkOpenVoucherPreArea[0].work_area_id
                  : null,
              notify_company: 0,
              finished_at: dayjs(actualDate).format(format1),
            });
            //SAVE TO MO SCAN
            // se insertara en mo_scans
            return await createScan(req, res);

            //return res.status(200).json({
            //ok: true,
            //message:
            //"Se ha creado ticket a partir de un voucher existente en otra area ",
            //data: createTickets,
            //});
          }
        }
      } else {
        //revisar si ya existe un ticket con ese voucher en el area
        const checkTicketExist = await WorkAreaTickets.query()
          .join(
            'work_vouchers',
            'work_area_tickets.work_voucher_id',
            '=',
            'work_vouchers.id'
          )
          .where('work_area_tickets.work_area_id', workAreaId)
          .where('work_vouchers.mo_id', moId)
          .select(
            'work_area_tickets.id',
            'work_area_tickets.work_area_id',
            'work_area_tickets.finished_at'
          )
          .orderBy('work_area_tickets.id', 'desc')
          .limit(1);
        //no es voucher y es una producion normal
        if (data.typeAction === 'ENTRADA') {
          //obtener el voucher partiendo de mo
          if (checkTicketExist.length > 0) {
            //ya existe un ticket con este voucher en el area
            return res.status(201).json({
              ok: true,
              message: `Ya existe un ticket para mo ${moId}`,
            });
          } else {
            await WorkVouchers.transaction(async (trx: any) => {
              const voucher = await WorkVouchers.query(trx).insertGraph({
                mo_id: moId,
                work_voucher_type_id: 1,
                workVouchersWorkTickets: [
                  {
                    notify_company: 0,
                    work_area_id: workAreaId,
                    work_area_ticket_status_id: getWorkTicketActiveStatus[0].id,
                  },
                ],
              });
              if (voucher.id !== null) {
                return res.status(200).json({
                  ok: true,
                  message: 'Se ha creado ticket a partir de un MO',
                  data: voucher,
                });
              } else {
                return res.status(500).json({
                  ok: false,
                  message: 'Ocurrio un error al insertar ticket y voucher',
                });
              }
            });
          }
        } else if (data.typeAction === 'SALIDA') {
          //revisar si ya existe un ticket en el area para esta MO
          if (checkTicketExist.length > 0) {
            //revisar si la fecha de finalizar esta null
            if (checkTicketExist[0].finished_at === null) {
              //actualizar ticket
              const closeTicket = await WorkAreaTickets.query()
                .update({
                  work_area_ticket_status_id: getWorkTicketCompleteStatus[0].id,
                  notify_company: 0,
                  finished_at: dayjs(actualDate).format(format1),
                })
                .where('id', checkTicketExist[0].id);
              //AGREGAR MO SCAN
              // se insertara en mo_scans

              return await createScan(req, res);

              //return res.status(200).json({
              //ok: true,
              //message:
              //"Se ha cerrado ticket a partir de un MO",
              //data: closeTicket,
              //});
            } else {
              //error ya existe un ticket cerrado para el area
              return res.status(201).json({
                ok: false,
                message: 'Ya existe un ticket cerrado para esta mo en el area',
              });
            }
          } else {
            //no existe, crear ticket y voucher
            await WorkVouchers.transaction(async (trx: any) => {
              const voucher = await WorkVouchers.query(trx).insertGraph({
                mo_id: moId,
                work_voucher_type_id: 1,
                workVouchersWorkTickets: [
                  {
                    notify_company: 0,
                    work_area_id: workAreaId,
                    work_area_ticket_status_id:
                      getWorkTicketCompleteStatus[0].id,
                    finished_at: dayjs(actualDate).format(format1),
                  },
                ],
              });
              if (voucher.id !== null) {
                //MO SCAN SALIDA
                // se insertara en mo_scans

                return await createScan(req, res);
                //return res.status(200).json({
                //ok: true,
                //message:
                //"Se ha creado ticket a partir de un MO",
                //data: voucher,
                //});
              } else {
                return res.status(500).json({
                  ok: false,
                  message: 'Ocurrio un error al insertar ticket y voucher',
                });
              }
            });
          }
        }
      }
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function createVoucherByMo(req: Request, res: Response) {
  //fix:todo
  try {
    const { mo, workAreaId, codeUser } = req.body;
    const format1 = 'YYYY-MM-DD HH:mm:ss';
    const actualDate = new Date();

    // variables globales
    const dataVoucher: any[] = [];
    const updateDataVoucher: any[] = [];
    const dataNewVoucherPlate: any[] = [];
    const dataNewVoucher: any[] = [];
    const dataErrorsVoucher: any[] = [];

    // data de los middlewares
    const dataMiddlewares = {
      workTicketStatuses: req.body.workTicketStatuses,
    };

    for (let i = 0; i < mo.length; i++) {
      if (mo[i].newGroup) {
        const searchIdVoucherGroup = await WorkVoucherGroups.query()
          .where('work_voucher_groups.name', mo[i].newGroup)
          .where('work_voucher_groups.work_area_id_ref', workAreaId)
          .select([{ workVoucherGroupId: 'work_voucher_groups.id' }]);

        if (searchIdVoucherGroup.length > 0) {
          // ? verificamos si es un voucher action el que procesamos
          if (mo[i].voucherScan) {
            const updateVoucher = await WorkVouchers.query()
              .update({
                work_voucher_group_id:
                  searchIdVoucherGroup[0].workVoucherGroupId,
              })
              .where('work_vouchers.id', mo[i].voucherId);

            if (updateVoucher.length > 0) {
              console.log(
                'se actualizo el voucher, agregandolo al grupo',
                mo[i].newGroup
              );

              const voucherLog = await WorkActivityLog.query().insert({
                work_voucher_id: mo[i].voucherId,
                work_area_id: workAreaId,
                employee_id: codeUser,
                module_name: 'voucherGroup',
                module_id: searchIdVoucherGroup[0].workVoucherGroupId,
                activity: 'AddedVoucherToGroup',
                data: JSON.stringify({ value: mo[i].voucherId }),
              });
              if (voucherLog) {
                if (mo[i].location === 'N/A') {
                  const ticketArea = await WorkAreaTickets.query().insert({
                    work_area_id: workAreaId,
                    work_voucher_id: mo[i].voucherId,
                    made_by_mo_scan: 0,
                    notify_company: 1,
                    is_company_notified: 0,
                    work_inventory_location_id: null,
                    prev_work_area_id: mo[i].areaPrev,
                    exp_finish_date: mo[i].expDate || null,
                    exp_work_area_line_id: mo[i].lineId || null,
                    exp_work_area_group_id: mo[i].groupId || null,
                    next_work_area_id: mo[i].nextAreaId || null,
                    work_area_ticket_status_id:
                      dataMiddlewares.workTicketStatuses,
                  });

                  const ticketLog = await WorkActivityLog.query().insert({
                    employee_id: codeUser,
                    work_area_id: workAreaId,
                    module_name: 'ticket',
                    module_id: ticketArea.id,
                    activity: 'TicketCreated',
                    data: JSON.stringify({}),
                  });
                  if (mo[i].commentVoucher?.length > 0) {
                    const addNewComment = await WorkNotes.query().insert({
                      mo_id: mo[i].mo_id,
                      work_area_ticket_id: ticketArea.id,
                      note: mo[i].commentVoucher,
                      employee_id: codeUser,
                    });
                  }

                  const ticketPrevArea = await WorkAreaTickets.query()
                    .update({
                      work_area_ticket_status_id: mo[i].statusCompleteAreaPrev,
                      is_company_notified: 0,
                      finished_at: dayjs(actualDate).format(format1),
                    })
                    .where('work_area_tickets.id', mo[i].ticketId);

                  if (ticketArea.length > 0 && ticketPrevArea.length > 0) {
                    dataNewVoucherPlate.push({
                      ...ticketArea,
                      ...ticketLog,
                    });
                  }
                } else {
                  // * obtenermos la informacion de la ubicacion
                  const getIdLocation = await WorkInventoryBins.query()
                    .join(
                      'work_inventory_zones',
                      'work_inventory_bins.work_inventory_zone_id',
                      'work_inventory_zones.id'
                    )
                    .join(
                      'work_area_inventory_zones',
                      'work_inventory_zones.id',
                      'work_area_inventory_zones.work_inventory_zone_id'
                    )
                    .where('work_area_inventory_zones.work_area_id', workAreaId)
                    .where('work_inventory_bins.name', mo[i].location)
                    .select('work_inventory_bins.id');

                  const ticketArea = await WorkAreaTickets.query().insert({
                    work_area_id: workAreaId,
                    work_voucher_id: mo[i].voucherId,
                    made_by_mo_scan: 0,
                    notify_company: 1,
                    is_company_notified: 0,
                    prev_work_area_id: mo[i].areaPrev,
                    work_inventory_location_id: getIdLocation[0].id,
                    exp_finish_date: mo[i].expDate || null,
                    exp_work_area_line_id: mo[i].lineId || null,
                    exp_work_area_group_id: mo[i].groupId || null,
                    next_work_area_id: mo[i].nextAreaId || null,
                    work_area_ticket_status_id:
                      dataMiddlewares.workTicketStatuses,
                  });

                  const ticketLog = await WorkActivityLog.query().insert({
                    employee_id: codeUser,
                    work_area_id: workAreaId,
                    module_name: 'ticket',
                    module_id: ticketArea.id,
                    activity: 'TicketCreated',
                    data: JSON.stringify({}),
                  });
                  if (mo[i].commentVoucher?.length > 0) {
                    const addNewComment = await WorkNotes.query().insert({
                      mo_id: mo[i].mo_id,
                      work_area_ticket_id: ticketArea.id,
                      note: mo[i].commentVoucher,
                      employee_id: codeUser,
                    });
                  }

                  const ticketPrevArea = await WorkAreaTickets.query()
                    .update({
                      work_area_ticket_status_id: mo[i].statusCompleteAreaPrev,
                      is_company_notified: 0,
                      finished_at: dayjs(actualDate).format(format1),
                    })
                    .where('work_area_tickets.id', mo[i].ticketId);

                  if (ticketArea.length > 0 && ticketPrevArea.length > 0) {
                    dataNewVoucherPlate.push({
                      ...ticketArea,
                      ...ticketLog,
                    });
                  }
                }
              }
            }
          } else if (mo[i].voucherGroup) {
            // verificar si el grupo de voucher es igual al que se va a actualizar
            const getVoucherGroupOfVoucher = await WorkVoucherGroups.query()
              .where('id', mo[i].voucherGroupId)
              .select([{ workAreaOfVoucherGroup: 'work_area_id_ref' }]);

            if (
              getVoucherGroupOfVoucher[0].workAreaOfVoucherGroup === workAreaId
            ) {
              // 1- buscar el ultimo ticket creado y activo para poder cerrarlo
              const getTicket = await WorkAreaTickets.query()
                .join(
                  'work_area_ticket_statuses',
                  'work_area_tickets.work_area_ticket_status_id',
                  'work_area_ticket_statuses.id'
                )
                .where('work_voucher_id', mo[i].voucherId)
                .where('work_area_ticket_statuses.work_status_id', '<>', 100)
                .andWhere('work_area_ticket_statuses.work_status_id', '<>', 110)

                .select(
                  'work_area_tickets.id',
                  {
                    prevArea: 'work_area_tickets.work_area_id',
                  },
                  { nameStatus: 'work_area_ticket_statuses.name' },
                  { colorStatus: 'work_area_ticket_statuses.color_hex' }
                )
                .orderBy('work_area_tickets.id', 'desc')
                .limit(1);

              // 2- obtenemos el id del status completo del area anterior para cerrar el ticket
              const getStatusCompletoPrevArea =
                await WorkAreaTicketStatuses.query()
                  .where(
                    'work_area_ticket_statuses.work_area_id',
                    getTicket[0].prevArea
                  )
                  .where('work_area_ticket_statuses.name', 'Completo')
                  .orWhere('work_area_ticket_statuses.work_status_id', 100)
                  .select([
                    'work_area_ticket_statuses.id',
                    'work_area_ticket_statuses.name',
                    'work_area_ticket_statuses.color_hex',
                  ])
                  .limit(1);

              // buscar la ubicacion del ticket
              if (getTicket.length > 0) {
                const getIdLocation = await WorkInventoryBins.query()
                  .join(
                    'work_zones',
                    'work_inventory_bins.work_zone_id',
                    'work_zones.id'
                  )
                  .join(
                    'work_area_zones',
                    'work_zones.id',
                    'work_area_zones.work_zone_id'
                  )
                  .where('work_area_zones.work_area_id', workAreaId)
                  .where('work_inventory_bins.name', mo[i].location || 'N/A')
                  .select('work_inventory_bins.id');

                // cerrar los tickets del area anterior
                if (getIdLocation.length > 0) {
                  const ticketPrevArea = await WorkAreaTickets.query()
                    .update({
                      work_area_ticket_status_id:
                        getStatusCompletoPrevArea[0].id,
                      finished_at: dayjs(actualDate).format(format1),
                    })
                    .where('work_area_tickets.id', getTicket[0].id);

                  // log del ticket cerrado
                  if (ticketPrevArea) {
                    const ticketLog = await WorkActivityLog.query().insert({
                      employee_id: codeUser,
                      work_area_id: workAreaId,
                      module_name: 'ticket',
                      module_id: getTicket[0].id,
                      activity: 'TicketStatusChanged',
                      data: JSON.stringify({
                        old_status_name: getTicket[0].nameStatus,
                        old_color: getTicket[0].colorStatus,
                        new_status_name: getStatusCompletoPrevArea[0].name,
                        new_color: getStatusCompletoPrevArea[0].color_hex,
                      }),
                    });

                    // crear el nuevo ticket
                    if (ticketLog) {
                      const ticketAreaNew =
                        await WorkAreaTickets.query().insert({
                          work_area_id: workAreaId,
                          work_voucher_id: mo[i].voucherId,
                          made_by_mo_scan: 0,
                          notify_company: 1,
                          is_company_notified: 0,
                          work_inventory_location_id:
                            getIdLocation.length > 0
                              ? getIdLocation[0].id
                              : null,
                          work_area_ticket_status_id:
                            dataMiddlewares.workTicketStatuses,
                          exp_finish_date: mo[i].expDate || null,
                          exp_work_area_line_id: mo[i].lineId || null,
                          exp_work_area_group_id: mo[i].groupId || null,
                          next_work_area_id: mo[i].nextAreaId || null,
                        });

                      if (ticketAreaNew) {
                        const ticketLogNew =
                          await WorkActivityLog.query().insert({
                            employee_id: codeUser,
                            work_area_id: workAreaId,
                            module_name: 'ticket',
                            module_id: ticketAreaNew.id,
                            activity: 'TicketCreated',
                            data: JSON.stringify({}),
                          });
                        if (mo[i].commentVoucher?.length > 0) {
                          const addNewComment = await WorkNotes.query().insert({
                            mo_id: mo[i].mo_id,
                            work_area_ticket_id: ticketAreaNew.id,
                            note: mo[i].commentVoucher,
                            employee_id: codeUser,
                          });
                        }
                      }
                    }
                  }
                } else {
                  const ticketPrevArea = await WorkAreaTickets.query()
                    .update({
                      work_area_ticket_status_id:
                        getStatusCompletoPrevArea[0].id,
                      finished_at: dayjs(actualDate).format(format1),
                    })
                    .where('work_area_tickets.id', getTicket[0].id);

                  // log del ticket cerrado
                  if (ticketPrevArea) {
                    const ticketLog = await WorkActivityLog.query().insert({
                      employee_id: codeUser,
                      work_area_id: workAreaId,
                      module_name: 'ticket',
                      module_id: getTicket[0].id,
                      activity: 'TicketStatusChanged',
                      data: JSON.stringify({
                        old_status_name: getTicket[0].nameStatus,
                        old_color: getTicket[0].colorStatus,
                        new_status_name: getStatusCompletoPrevArea[0].name,
                        new_color: getStatusCompletoPrevArea[0].color_hex,
                      }),
                    });

                    // crear el nuevo ticket
                    if (ticketLog) {
                      const ticketAreaNew =
                        await WorkAreaTickets.query().insert({
                          work_area_id: workAreaId,
                          work_voucher_id: mo[i].voucherId,
                          made_by_mo_scan: 0,
                          notify_company: 1,
                          is_company_notified: 0,
                          work_inventory_location_id: null,
                          work_area_ticket_status_id:
                            dataMiddlewares.workTicketStatuses,
                          exp_finish_date: mo[i].expDate || null,
                          exp_work_area_line_id: mo[i].lineId || null,
                          exp_work_area_group_id: mo[i].groupId || null,
                          next_work_area_id: mo[i].nextAreaId || null,
                        });
                      if (ticketAreaNew) {
                        const ticketLogNew =
                          await WorkActivityLog.query().insert({
                            employee_id: codeUser,
                            work_area_id: workAreaId,
                            module_name: 'ticket',
                            module_id: ticketAreaNew.id,
                            activity: 'TicketCreated',
                            data: JSON.stringify({}),
                          });
                        if (mo[i].commentVoucher?.length > 0) {
                          const addNewComment = await WorkNotes.query().insert({
                            mo_id: mo[i].mo_id,
                            work_area_ticket_id: ticketAreaNew.id,
                            note: mo[i].commentVoucher,
                            employee_id: codeUser,
                          });
                        }
                      }
                    }
                  }
                }
              }
            } else {
              // * actualizar el area del grupo de vouchers
              const updateAreaVoucherGroup = await WorkVoucherGroups.query()
                .update({
                  work_area_id_ref: workAreaId,
                })
                .where('id', mo[i].voucherGroupId);

              if (updateAreaVoucherGroup) {
                // log del cambio de area
                const logVoucherGroup = await WorkActivityLog.query().insert({
                  work_area_id: workAreaId,
                  employee_id: codeUser,
                  module_name: 'voucherGroup',
                  module_id: mo[i].voucherGroupId,
                  activity: 'ChangedAreaVoucherGroup',
                  data: JSON.stringify({}),
                });
                if (logVoucherGroup) {
                  // 1- buscar el ultimo ticket creado y activo para poder cerrarlo
                  const getTicket = await WorkAreaTickets.query()
                    .join(
                      'work_area_ticket_statuses',
                      'work_area_tickets.work_area_ticket_status_id',
                      'work_area_ticket_statuses.id'
                    )
                    .where('work_voucher_id', mo[i].voucherId)
                    .where(
                      'work_area_ticket_statuses.work_status_id',
                      '<>',
                      100
                    )
                    .andWhere(
                      'work_area_ticket_statuses.work_status_id',
                      '<>',
                      110
                    )

                    .select(
                      'work_area_tickets.id',
                      {
                        prevArea: 'work_area_tickets.work_area_id',
                      },
                      { nameStatus: 'work_area_ticket_statuses.name' },
                      { colorStatus: 'work_area_ticket_statuses.color_hex' }
                    )
                    .orderBy('work_area_tickets.id', 'desc')
                    .limit(1);

                  // 2- obtenermos el id del status completo del area anterior para cerrar el ticket
                  const getStatusCompletoPrevArea =
                    await WorkAreaTicketStatuses.query()
                      .where(
                        'work_area_ticket_statuses.work_area_id',
                        getTicket[0].prevArea
                      )
                      .where('work_area_ticket_statuses.name', 'Completo')
                      .orWhere('work_area_ticket_statuses.work_status_id', 100)
                      .select([
                        'work_area_ticket_statuses.id',
                        'work_area_ticket_statuses.name',
                        'work_area_ticket_statuses.color_hex',
                      ])
                      .limit(1);

                  // buscar la ubicacion del ticket
                  if (getTicket.length > 0) {
                    const getIdLocation = await WorkInventoryBins.query()
                      .join(
                        'work_zones',
                        'work_inventory_bins.work_zone_id',
                        'work_zones.id'
                      )
                      .join(
                        'work_area_zones',
                        'work_zones.id',
                        'work_area_zones.work_zone_id'
                      )
                      .where('work_area_zones.work_area_id', workAreaId)
                      .where(
                        'work_inventory_bins.name',
                        mo[i].location || 'N/A'
                      )
                      .select('work_inventory_bins.id');

                    // cerrar los tickets del area anterior
                    if (getIdLocation.length > 0) {
                      const ticketPrevArea = await WorkAreaTickets.query()
                        .update({
                          work_area_ticket_status_id:
                            getStatusCompletoPrevArea[0].id,
                          finished_at: dayjs(actualDate).format(format1),
                        })
                        .where('work_area_tickets.id', getTicket[0].id);

                      // log del ticket cerrado
                      if (ticketPrevArea) {
                        const ticketLog = await WorkActivityLog.query().insert({
                          employee_id: codeUser,
                          work_area_id: workAreaId,
                          module_name: 'ticket',
                          module_id: getTicket[0].id,
                          activity: 'TicketStatusChanged',
                          data: JSON.stringify({
                            old_status_name: getTicket[0].nameStatus,
                            old_color: getTicket[0].colorStatus,
                            new_status_name: getStatusCompletoPrevArea[0].name,
                            new_color: getStatusCompletoPrevArea[0].color_hex,
                          }),
                        });

                        // crear el nuevo ticket
                        if (ticketLog) {
                          const ticketAreaNew =
                            await WorkAreaTickets.query().insert({
                              work_area_id: workAreaId,
                              work_voucher_id: mo[i].voucherId,
                              made_by_mo_scan: 0,
                              notify_company: 1,
                              is_company_notified: 0,
                              work_inventory_location_id:
                                getIdLocation.length > 0
                                  ? getIdLocation[0].id
                                  : null,
                              work_area_ticket_status_id:
                                dataMiddlewares.workTicketStatuses,
                              exp_finish_date: mo[i].expDate || null,
                              exp_work_area_line_id: mo[i].lineId || null,
                              exp_work_area_group_id: mo[i].groupId || null,
                              next_work_area_id: mo[i].nextAreaId || null,
                            });

                          if (ticketAreaNew) {
                            const ticketLogNew =
                              await WorkActivityLog.query().insert({
                                employee_id: codeUser,
                                work_area_id: workAreaId,
                                module_name: 'ticket',
                                module_id: ticketAreaNew.id,
                                activity: 'TicketCreated',
                                data: JSON.stringify({}),
                              });
                            if (mo[i].commentVoucher?.length > 0) {
                              const addNewComment =
                                await WorkNotes.query().insert({
                                  mo_id: mo[i].mo_id,
                                  work_area_ticket_id: ticketAreaNew.id,
                                  note: mo[i].commentVoucher,
                                  employee_id: codeUser,
                                });
                            }
                          }
                        }
                      }
                    } else {
                      const ticketPrevArea = await WorkAreaTickets.query()
                        .update({
                          work_area_ticket_status_id:
                            getStatusCompletoPrevArea[0].id,
                          finished_at: dayjs(actualDate).format(format1),
                        })
                        .where('work_area_tickets.id', getTicket[0].id);

                      // log del ticket cerrado
                      if (ticketPrevArea) {
                        const ticketLog = await WorkActivityLog.query().insert({
                          employee_id: codeUser,
                          work_area_id: workAreaId,
                          module_name: 'ticket',
                          module_id: getTicket[0].id,
                          activity: 'TicketStatusChanged',
                          data: JSON.stringify({
                            old_status_name: getTicket[0].nameStatus,
                            old_color: getTicket[0].colorStatus,
                            new_status_name: getStatusCompletoPrevArea[0].name,
                            new_color: getStatusCompletoPrevArea[0].color_hex,
                          }),
                        });

                        // crear el nuevo ticket
                        if (ticketLog) {
                          const ticketAreaNew =
                            await WorkAreaTickets.query().insert({
                              work_area_id: workAreaId,
                              work_voucher_id: mo[i].voucherId,
                              made_by_mo_scan: 0,
                              notify_company: 1,
                              is_company_notified: 0,
                              work_inventory_location_id: null,
                              work_area_ticket_status_id:
                                dataMiddlewares.workTicketStatuses,
                              exp_finish_date: mo[i].expDate || null,
                              exp_work_area_line_id: mo[i].lineId || null,
                              exp_work_area_group_id: mo[i].groupId || null,
                              next_work_area_id: mo[i].nextAreaId || null,
                            });
                          if (ticketAreaNew) {
                            const ticketLogNew =
                              await WorkActivityLog.query().insert({
                                employee_id: codeUser,
                                work_area_id: workAreaId,
                                module_name: 'ticket',
                                module_id: ticketAreaNew.id,
                                activity: 'TicketCreated',
                                data: JSON.stringify({}),
                              });
                            if (mo[i].commentVoucher?.length > 0) {
                              const addNewComment =
                                await WorkNotes.query().insert({
                                  mo_id: mo[i].mo_id,
                                  work_area_ticket_id: ticketAreaNew.id,
                                  note: mo[i].commentVoucher,
                                  employee_id: codeUser,
                                });
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          } else {
            //? si es una mo
            const getIdLocation = await WorkInventoryBins.query()
              .join(
                'work_zones',
                'work_inventory_bins.work_zone_id',
                'work_zones.id'
              )
              .join(
                'work_area_zones',
                'work_zones.id',
                'work_area_zones.work_zone_id'
              )
              .where('work_area_zones.work_area_id', workAreaId)
              .where('work_inventory_bins.name', mo[i].location || 'N/A')
              .select('work_inventory_bins.id');

            if (mo[i].repo === 'SI') {
              const voucher = await WorkVouchers.query().insertGraph({
                mo_id: mo[i].mo_id,
                is_primary: mo[i].isMain === 'true' ? 1 : 0,
                is_repo: 1,
                work_voucher_type_id: mo[i].voucherTypeId,
                work_voucher_group_id:
                  searchIdVoucherGroup[0].workVoucherGroupId,
                workVouchersWorkTickets: [
                  {
                    work_area_id: workAreaId,
                    made_by_mo_scan: 0,
                    notify_company: 1,
                    is_company_notified: 0,
                    work_inventory_location_id:
                      getIdLocation.length > 0 ? getIdLocation[0].id : null,
                    work_area_ticket_status_id:
                      dataMiddlewares.workTicketStatuses,
                    exp_finish_date: mo[i].expDate || null,
                    exp_work_area_line_id: mo[i].lineId || null,
                    exp_work_area_group_id: mo[i].groupId || null,
                    next_work_area_id: mo[i].nextAreaId || null,
                  },
                ],
              });
              const ticketLog = await WorkActivityLog.query().insert({
                employee_id: codeUser,
                work_area_id: workAreaId,
                module_name: 'ticket',
                module_id: voucher.workVouchersWorkTickets[0].id,
                activity: 'TicketCreated',
                data: JSON.stringify({}),
              });

              if (mo[i].commentVoucher?.length > 0) {
                const addNewComment = await WorkNotes.query().insert({
                  mo_id: mo[i].mo_id,
                  work_area_ticket_id: voucher.workVouchersWorkTickets[0].id,
                  note: mo[i].commentVoucher,
                  employee_id: codeUser,
                });
              }

              if (voucher) {
                dataNewVoucher.push({
                  ...voucher,
                  ...ticketLog,
                });
              }
            } else {
              //// se crea un nuevo ticket y se actualiza el voucher type si el ticket

              const voucher = await WorkVouchers.query().insertGraph({
                mo_id: mo[i].mo_id,
                is_primary: mo[i].isMain === 'true' ? 1 : 0,
                work_voucher_type_id: mo[i].voucherTypeId,
                work_voucher_group_id:
                  searchIdVoucherGroup[0].workVoucherGroupId,
                workVouchersWorkTickets: [
                  {
                    work_area_id: workAreaId,
                    made_by_mo_scan: 0,
                    notify_company: 1,
                    is_company_notified: 0,
                    work_inventory_location_id:
                      getIdLocation.length > 0 ? getIdLocation[0].id : null,
                    work_area_ticket_status_id:
                      dataMiddlewares.workTicketStatuses,
                    exp_finish_date: mo[i].expDate || null,
                    exp_work_area_line_id: mo[i].lineId || null,
                    exp_work_area_group_id: mo[i].groupId || null,
                    next_work_area_id: mo[i].nextAreaId || null,
                  },
                ],
              });
              const ticketLog = await WorkActivityLog.query().insert({
                employee_id: codeUser,
                work_area_id: workAreaId,
                module_name: 'ticket',
                module_id: voucher.workVouchersWorkTickets[0].id,
                activity: 'TicketCreated',
                data: JSON.stringify({}),
              });
              if (mo[i].commentVoucher?.length > 0) {
                const addNewComment = await WorkNotes.query().insert({
                  mo_id: mo[i].mo_id,
                  work_area_ticket_id: voucher.workVouchersWorkTickets[0].id,
                  note: mo[i].commentVoucher,
                  employee_id: codeUser,
                });
              }
              if (voucher) {
                dataNewVoucher.push({
                  ...voucher,
                  ...ticketLog,
                });
              }
            }
          }
        } else {
          dataErrorsVoucher.push(mo[i]);
        }
      } else {
        // ? verificamos si es un voucher action el que procesamos
        if (mo[i].voucherScan) {
          const searchVoucherInfo = await WorkVouchers.query()
            .where('work_vouchers.id', mo[i].voucherId)
            .whereNull('work_vouchers.work_voucher_group_id');

          if (searchVoucherInfo.length > 0) {
            if (mo[i].location === 'N/A') {
              const ticketArea = await WorkAreaTickets.query().insert({
                work_area_id: workAreaId,
                work_voucher_id: mo[i].voucherId,
                made_by_mo_scan: 0,
                notify_company: 1,
                is_company_notified: 0,
                work_inventory_location_id: null,
                prev_work_area_id: mo[i].areaPrev,
                exp_finish_date: mo[i].expDate || null,
                exp_work_area_line_id: mo[i].lineId || null,
                exp_work_area_group_id: mo[i].groupId || null,
                next_work_area_id: mo[i].nextAreaId || null,
                work_area_ticket_status_id: dataMiddlewares.workTicketStatuses,
              });

              const ticketLog = await WorkActivityLog.query().insert({
                employee_id: codeUser,
                work_area_id: workAreaId,
                module_name: 'ticket',
                module_id: ticketArea.id,
                activity: 'TicketCreated',
                data: JSON.stringify({}),
              });

              if (mo[i].commentVoucher?.length > 0) {
                const addNewComment = await WorkNotes.query().insert({
                  mo_id: mo[i].mo_id,
                  work_area_ticket_id: ticketArea.id,
                  note: mo[i].commentVoucher,
                  employee_id: codeUser,
                });
              }

              const ticketPrevArea = await WorkAreaTickets.query()
                .update({
                  work_area_ticket_status_id: mo[i].statusCompleteAreaPrev,
                  is_company_notified: 0,
                  finished_at: dayjs(actualDate).format(format1),
                })
                .where('work_area_tickets.id', mo[i].ticketId);

              if (ticketArea.length > 0 && ticketPrevArea.length > 0) {
                dataNewVoucherPlate.push({
                  ...ticketArea,
                  ...ticketLog,
                });
              }
            } else {
              // * obtenemos la informacion de la ubicacion
              const getIdLocation = await WorkInventoryBins.query()
                .join(
                  'work_inventory_zones',
                  'work_inventory_bins.work_inventory_zone_id',
                  'work_inventory_zones.id'
                )
                .join(
                  'work_area_inventory_zones',
                  'work_inventory_zones.id',
                  'work_area_inventory_zones.work_inventory_zone_id'
                )
                .where('work_area_inventory_zones.work_area_id', workAreaId)
                .where('work_inventory_bins.name', mo[i].location)
                .select('work_inventory_bins.id');

              const ticketArea = await WorkAreaTickets.query().insert({
                work_area_id: workAreaId,
                work_voucher_id: mo[i].voucherId,
                made_by_mo_scan: 0,
                notify_company: 1,
                is_company_notified: 0,
                prev_work_area_id: mo[i].areaPrev,
                work_inventory_location_id: getIdLocation[0].id,
                exp_finish_date: mo[i].expDate || null,
                exp_work_area_line_id: mo[i].lineId || null,
                exp_work_area_group_id: mo[i].groupId || null,
                next_work_area_id: mo[i].nextAreaId || null,
                work_area_ticket_status_id: dataMiddlewares.workTicketStatuses,
              });

              const ticketLog = await WorkActivityLog.query().insert({
                employee_id: codeUser,
                work_area_id: workAreaId,
                module_name: 'ticket',
                module_id: ticketArea.id,
                activity: 'TicketCreated',
                data: JSON.stringify({}),
              });
              if (mo[i].commentVoucher?.length > 0) {
                const addNewComment = await WorkNotes.query().insert({
                  mo_id: mo[i].mo_id,
                  work_area_ticket_id: ticketArea.id,
                  note: mo[i].commentVoucher,
                  employee_id: codeUser,
                });
              }

              const ticketPrevArea = await WorkAreaTickets.query()
                .update({
                  work_area_ticket_status_id: mo[i].statusCompleteAreaPrev,
                  is_company_notified: 0,
                  finished_at: dayjs(actualDate).format(format1),
                })
                .where('work_area_tickets.id', mo[i].ticketId);

              if (ticketArea.length > 0 && ticketPrevArea.length > 0) {
                dataNewVoucherPlate.push({
                  ...ticketArea,
                  ...ticketLog,
                });
              }
            }
          } else {
            const getVoucherInfo = await WorkVouchers.query()
              .select('work_voucher_group_id')
              .where('id', mo[i].voucherId);

            const updateVoucher = await WorkVouchers.query()
              .update({
                work_voucher_group_id: null,
              })
              .where('id', mo[i].voucherId);

            if (updateVoucher) {
              const voucherLog = await WorkActivityLog.query().insert({
                employee_id: codeUser,
                work_area_id: workAreaId,
                module_name: 'voucherGroup',
                work_area_group_id: getVoucherInfo[0].work_voucher_group_id,
                module_id: mo[i].voucherId,
                activity: 'VoucherDeletedGroup',
                data: JSON.stringify({}),
              });
              if (voucherLog) {
                if (mo[i].location === 'N/A') {
                  const ticketArea = await WorkAreaTickets.query().insert({
                    work_area_id: workAreaId,
                    work_voucher_id: mo[i].voucherId,
                    made_by_mo_scan: 0,
                    notify_company: 1,
                    is_company_notified: 0,
                    work_inventory_location_id: null,
                    prev_work_area_id: mo[i].areaPrev,
                    exp_finish_date: mo[i].expDate || null,
                    exp_work_area_line_id: mo[i].lineId || null,
                    exp_work_area_group_id: mo[i].groupId || null,
                    next_work_area_id: mo[i].nextAreaId || null,
                    work_area_ticket_status_id:
                      dataMiddlewares.workTicketStatuses,
                  });

                  const ticketLog = await WorkActivityLog.query().insert({
                    employee_id: codeUser,
                    work_area_id: workAreaId,
                    module_name: 'ticket',
                    module_id: ticketArea.id,
                    activity: 'TicketCreated',
                    data: JSON.stringify({}),
                  });
                  if (mo[i].commentVoucher?.length > 0) {
                    const addNewComment = await WorkNotes.query().insert({
                      mo_id: mo[i].mo_id,
                      work_area_ticket_id: ticketArea.id,
                      note: mo[i].commentVoucher,
                      employee_id: codeUser,
                    });
                  }

                  const ticketPrevArea = await WorkAreaTickets.query()
                    .update({
                      work_area_ticket_status_id: mo[i].statusCompleteAreaPrev,
                      is_company_notified: 0,
                      finished_at: dayjs(actualDate).format(format1),
                    })
                    .where('work_area_tickets.id', mo[i].ticketId);

                  if (ticketArea.length > 0 && ticketPrevArea.length > 0) {
                    dataNewVoucherPlate.push({ ...ticketArea, ...ticketLog });
                  }
                } else {
                  // * obtenermos la informacion de la ubicacion
                  const getIdLocation = await WorkInventoryBins.query()
                    .join(
                      'work_inventory_zones',
                      'work_inventory_bins.work_inventory_zone_id',
                      'work_inventory_zones.id'
                    )
                    .join(
                      'work_area_inventory_zones',
                      'work_inventory_zones.id',
                      'work_area_inventory_zones.work_inventory_zone_id'
                    )
                    .where('work_area_inventory_zones.work_area_id', workAreaId)
                    .where('work_inventory_bins.name', mo[i].location)
                    .select('work_inventory_bins.id');

                  const ticketArea = await WorkAreaTickets.query().insert({
                    work_area_id: workAreaId,
                    work_voucher_id: mo[i].voucherId,
                    made_by_mo_scan: 0,
                    notify_company: 1,
                    is_company_notified: 0,
                    prev_work_area_id: mo[i].areaPrev,
                    work_inventory_location_id: getIdLocation[0].id,
                    exp_finish_date: mo[i].expDate || null,
                    exp_work_area_line_id: mo[i].lineId || null,
                    exp_work_area_group_id: mo[i].groupId || null,
                    next_work_area_id: mo[i].nextAreaId || null,
                    work_area_ticket_status_id:
                      dataMiddlewares.workTicketStatuses,
                  });

                  const ticketLog = await WorkActivityLog.query().insert({
                    employee_id: codeUser,
                    work_area_id: workAreaId,
                    module_name: 'ticket',
                    module_id: ticketArea.id,
                    activity: 'TicketCreated',
                    data: JSON.stringify({}),
                  });
                  if (mo[i].commentVoucher?.length > 0) {
                    const addNewComment = await WorkNotes.query().insert({
                      mo_id: mo[i].mo_id,
                      work_area_ticket_id: ticketArea.id,
                      note: mo[i].commentVoucher,
                      employee_id: codeUser,
                    });
                  }

                  const ticketPrevArea = await WorkAreaTickets.query()
                    .update({
                      work_area_ticket_status_id: mo[i].statusCompleteAreaPrev,
                      is_company_notified: 0,
                      finished_at: dayjs(actualDate).format(format1),
                    })
                    .where('work_area_tickets.id', mo[i].ticketId);

                  if (ticketArea.length > 0 && ticketPrevArea.length > 0) {
                    dataNewVoucherPlate.push({
                      ...ticketArea,
                      ...ticketLog,
                    });
                  }
                }
              }
            }
          }
        } else if (mo[i].voucherGroup) {
          // verificar si el grupo de voucher es igual al que se va a actualizar
          const getVoucherGroupOfVoucher = await WorkVoucherGroups.query()
            .where('id', mo[i].voucherGroupId)
            .select([{ workAreaOfVoucherGroup: 'work_area_id_ref' }]);

          if (
            getVoucherGroupOfVoucher[0].workAreaOfVoucherGroup === workAreaId
          ) {
            // 1- buscar el ultimo ticket creado y activo para poder cerrarlo
            const getTicket = await WorkAreaTickets.query()
              .join(
                'work_area_ticket_statuses',
                'work_area_tickets.work_area_ticket_status_id',
                'work_area_ticket_statuses.id'
              )
              .where('work_voucher_id', mo[i].voucherId)
              .where('work_area_ticket_statuses.work_status_id', '<>', 100)
              .andWhere('work_area_ticket_statuses.work_status_id', '<>', 110)

              .select(
                'work_area_tickets.id',
                {
                  prevArea: 'work_area_tickets.work_area_id',
                },
                { nameStatus: 'work_area_ticket_statuses.name' },
                { colorStatus: 'work_area_ticket_statuses.color_hex' }
              )
              .orderBy('work_area_tickets.id', 'desc')
              .limit(1);

            // 2- obtenemos el id del status completo del area anterior para cerrar el ticket
            const getStatusCompletoPrevArea =
              await WorkAreaTicketStatuses.query()
                .where(
                  'work_area_ticket_statuses.work_area_id',
                  getTicket[0].prevArea
                )
                .where('work_area_ticket_statuses.name', 'Completo')
                .orWhere('work_area_ticket_statuses.work_status_id', 100)
                .select([
                  'work_area_ticket_statuses.id',
                  'work_area_ticket_statuses.name',
                  'work_area_ticket_statuses.color_hex',
                ])
                .limit(1);

            // buscar la ubicacion del ticket
            if (getTicket.length > 0) {
              const getIdLocation = await WorkInventoryBins.query()
                .join(
                  'work_zones',
                  'work_inventory_bins.work_zone_id',
                  'work_zones.id'
                )
                .join(
                  'work_area_zones',
                  'work_zones.id',
                  'work_area_zones.work_zone_id'
                )
                .where('work_area_zones.work_area_id', workAreaId)
                .where('work_inventory_bins.name', mo[i].location || 'N/A')
                .select('work_inventory_bins.id');

              // cerrar los tickets del area anterior
              if (getIdLocation.length > 0) {
                const ticketPrevArea = await WorkAreaTickets.query()
                  .update({
                    work_area_ticket_status_id: getStatusCompletoPrevArea[0].id,
                    finished_at: dayjs(actualDate).format(format1),
                  })
                  .where('work_area_tickets.id', getTicket[0].id);

                // log del ticket cerrado
                if (ticketPrevArea) {
                  const ticketLog = await WorkActivityLog.query().insert({
                    employee_id: codeUser,
                    work_area_id: workAreaId,
                    module_name: 'ticket',
                    module_id: getTicket[0].id,
                    activity: 'TicketStatusChanged',
                    data: JSON.stringify({
                      old_status_name: getTicket[0].nameStatus,
                      old_color: getTicket[0].colorStatus,
                      new_status_name: getStatusCompletoPrevArea[0].name,
                      new_color: getStatusCompletoPrevArea[0].color_hex,
                    }),
                  });

                  // crear el nuevo ticket
                  if (ticketLog) {
                    const ticketAreaNew = await WorkAreaTickets.query().insert({
                      work_area_id: workAreaId,
                      work_voucher_id: mo[i].voucherId,
                      made_by_mo_scan: 0,
                      notify_company: 1,
                      is_company_notified: 0,
                      work_inventory_location_id:
                        getIdLocation.length > 0 ? getIdLocation[0].id : null,
                      work_area_ticket_status_id:
                        dataMiddlewares.workTicketStatuses,
                      exp_finish_date: mo[i].expDate || null,
                      exp_work_area_line_id: mo[i].lineId || null,
                      exp_work_area_group_id: mo[i].groupId || null,
                      next_work_area_id: mo[i].nextAreaId || null,
                    });

                    if (ticketAreaNew) {
                      const ticketLogNew = await WorkActivityLog.query().insert(
                        {
                          employee_id: codeUser,
                          work_area_id: workAreaId,
                          module_name: 'ticket',
                          module_id: ticketAreaNew.id,
                          activity: 'TicketCreated',
                          data: JSON.stringify({}),
                        }
                      );
                      if (mo[i].commentVoucher?.length > 0) {
                        const addNewComment = await WorkNotes.query().insert({
                          mo_id: mo[i].mo_id,
                          work_area_ticket_id: ticketAreaNew.id,
                          note: mo[i].commentVoucher,
                          employee_id: codeUser,
                        });
                      }
                    }
                  }
                }
              } else {
                const ticketPrevArea = await WorkAreaTickets.query()
                  .update({
                    work_area_ticket_status_id: getStatusCompletoPrevArea[0].id,
                    finished_at: dayjs(actualDate).format(format1),
                  })
                  .where('work_area_tickets.id', getTicket[0].id);

                // log del ticket cerrado
                if (ticketPrevArea) {
                  const ticketLog = await WorkActivityLog.query().insert({
                    employee_id: codeUser,
                    work_area_id: workAreaId,
                    module_name: 'ticket',
                    module_id: getTicket[0].id,
                    activity: 'TicketStatusChanged',
                    data: JSON.stringify({
                      old_status_name: getTicket[0].nameStatus,
                      old_color: getTicket[0].colorStatus,
                      new_status_name: getStatusCompletoPrevArea[0].name,
                      new_color: getStatusCompletoPrevArea[0].color_hex,
                    }),
                  });

                  // crear el nuevo ticket
                  if (ticketLog) {
                    const ticketAreaNew = await WorkAreaTickets.query().insert({
                      work_area_id: workAreaId,
                      work_voucher_id: mo[i].voucherId,
                      made_by_mo_scan: 0,
                      notify_company: 1,
                      is_company_notified: 0,
                      work_inventory_location_id: null,
                      work_area_ticket_status_id:
                        dataMiddlewares.workTicketStatuses,
                      exp_finish_date: mo[i].expDate || null,
                      exp_work_area_line_id: mo[i].lineId || null,
                      exp_work_area_group_id: mo[i].groupId || null,
                      next_work_area_id: mo[i].nextAreaId || null,
                    });
                    if (ticketAreaNew) {
                      const ticketLogNew = await WorkActivityLog.query().insert(
                        {
                          employee_id: codeUser,
                          work_area_id: workAreaId,
                          module_name: 'ticket',
                          module_id: ticketAreaNew.id,
                          activity: 'TicketCreated',
                          data: JSON.stringify({}),
                        }
                      );
                      if (mo[i].commentVoucher?.length > 0) {
                        const addNewComment = await WorkNotes.query().insert({
                          mo_id: mo[i].mo_id,
                          work_area_ticket_id: ticketAreaNew.id,
                          note: mo[i].commentVoucher,
                          employee_id: codeUser,
                        });
                      }
                    }
                  }
                }
              }
            }
          } else {
            // * actualizar el area del grupo de vouchers
            const updateAreaVoucherGroup = await WorkVoucherGroups.query()
              .update({
                work_area_id_ref: workAreaId,
              })
              .where('id', mo[i].voucherGroupId);

            if (updateAreaVoucherGroup) {
              // log del cambio de area
              const logVoucherGroup = await WorkActivityLog.query().insert({
                work_area_id: workAreaId,
                employee_id: codeUser,
                module_name: 'voucherGroup',
                module_id: mo[i].voucherGroupId,
                activity: 'ChangedAreaVoucherGroup',
                data: JSON.stringify({}),
              });
              if (logVoucherGroup) {
                // 1- buscar el ultimo ticket creado y activo para poder cerrarlo
                const getTicket = await WorkAreaTickets.query()
                  .join(
                    'work_area_ticket_statuses',
                    'work_area_tickets.work_area_ticket_status_id',
                    'work_area_ticket_statuses.id'
                  )
                  .where('work_voucher_id', mo[i].voucherId)
                  .where('work_area_ticket_statuses.work_status_id', '<>', 100)
                  .andWhere(
                    'work_area_ticket_statuses.work_status_id',
                    '<>',
                    110
                  )

                  .select(
                    'work_area_tickets.id',
                    {
                      prevArea: 'work_area_tickets.work_area_id',
                    },
                    { nameStatus: 'work_area_ticket_statuses.name' },
                    { colorStatus: 'work_area_ticket_statuses.color_hex' }
                  )
                  .orderBy('work_area_tickets.id', 'desc')
                  .limit(1);

                // 2- obtenermos el id del status completo del area anterior para cerrar el ticket
                const getStatusCompletoPrevArea =
                  await WorkAreaTicketStatuses.query()
                    .where(
                      'work_area_ticket_statuses.work_area_id',
                      getTicket[0].prevArea
                    )
                    .where('work_area_ticket_statuses.name', 'Completo')
                    .orWhere('work_area_ticket_statuses.work_status_id', 100)
                    .select([
                      'work_area_ticket_statuses.id',
                      'work_area_ticket_statuses.name',
                      'work_area_ticket_statuses.color_hex',
                    ])
                    .limit(1);

                // buscar la ubicacion del ticket
                if (getTicket.length > 0) {
                  const getIdLocation = await WorkInventoryBins.query()
                    .join(
                      'work_zones',
                      'work_inventory_bins.work_zone_id',
                      'work_zones.id'
                    )
                    .join(
                      'work_area_zones',
                      'work_zones.id',
                      'work_area_zones.work_zone_id'
                    )
                    .where('work_area_zones.work_area_id', workAreaId)
                    .where('work_inventory_bins.name', mo[i].location || 'N/A')
                    .select('work_inventory_bins.id');

                  // cerrar los tickets del area anterior
                  if (getIdLocation.length > 0) {
                    const ticketPrevArea = await WorkAreaTickets.query()
                      .update({
                        work_area_ticket_status_id:
                          getStatusCompletoPrevArea[0].id,
                        finished_at: dayjs(actualDate).format(format1),
                      })
                      .where('work_area_tickets.id', getTicket[0].id);

                    // log del ticket cerrado
                    if (ticketPrevArea) {
                      const ticketLog = await WorkActivityLog.query().insert({
                        employee_id: codeUser,
                        work_area_id: workAreaId,
                        module_name: 'ticket',
                        module_id: getTicket[0].id,
                        activity: 'TicketStatusChanged',
                        data: JSON.stringify({
                          old_status_name: getTicket[0].nameStatus,
                          old_color: getTicket[0].colorStatus,
                          new_status_name: getStatusCompletoPrevArea[0].name,
                          new_color: getStatusCompletoPrevArea[0].color_hex,
                        }),
                      });

                      // crear el nuevo ticket
                      if (ticketLog) {
                        const ticketAreaNew =
                          await WorkAreaTickets.query().insert({
                            work_area_id: workAreaId,
                            work_voucher_id: mo[i].voucherId,
                            made_by_mo_scan: 0,
                            notify_company: 1,
                            is_company_notified: 0,
                            work_inventory_location_id:
                              getIdLocation.length > 0
                                ? getIdLocation[0].id
                                : null,
                            work_area_ticket_status_id:
                              dataMiddlewares.workTicketStatuses,
                            exp_finish_date: mo[i].expDate || null,
                            exp_work_area_line_id: mo[i].lineId || null,
                            exp_work_area_group_id: mo[i].groupId || null,
                            next_work_area_id: mo[i].nextAreaId || null,
                          });

                        if (ticketAreaNew) {
                          const ticketLogNew =
                            await WorkActivityLog.query().insert({
                              employee_id: codeUser,
                              work_area_id: workAreaId,
                              module_name: 'ticket',
                              module_id: ticketAreaNew.id,
                              activity: 'TicketCreated',
                              data: JSON.stringify({}),
                            });
                          if (mo[i].commentVoucher?.length > 0) {
                            const addNewComment =
                              await WorkNotes.query().insert({
                                mo_id: mo[i].mo_id,
                                work_area_ticket_id: ticketAreaNew.id,
                                note: mo[i].commentVoucher,
                                employee_id: codeUser,
                              });
                          }
                        }
                      }
                    }
                  } else {
                    const ticketPrevArea = await WorkAreaTickets.query()
                      .update({
                        work_area_ticket_status_id:
                          getStatusCompletoPrevArea[0].id,
                        finished_at: dayjs(actualDate).format(format1),
                      })
                      .where('work_area_tickets.id', getTicket[0].id);

                    // log del ticket cerrado
                    if (ticketPrevArea) {
                      const ticketLog = await WorkActivityLog.query().insert({
                        employee_id: codeUser,
                        work_area_id: workAreaId,
                        module_name: 'ticket',
                        module_id: getTicket[0].id,
                        activity: 'TicketStatusChanged',
                        data: JSON.stringify({
                          old_status_name: getTicket[0].nameStatus,
                          old_color: getTicket[0].colorStatus,
                          new_status_name: getStatusCompletoPrevArea[0].name,
                          new_color: getStatusCompletoPrevArea[0].color_hex,
                        }),
                      });

                      // crear el nuevo ticket
                      if (ticketLog) {
                        const ticketAreaNew =
                          await WorkAreaTickets.query().insert({
                            work_area_id: workAreaId,
                            work_voucher_id: mo[i].voucherId,
                            made_by_mo_scan: 0,
                            notify_company: 1,
                            is_company_notified: 0,
                            work_inventory_location_id: null,
                            work_area_ticket_status_id:
                              dataMiddlewares.workTicketStatuses,
                            exp_finish_date: mo[i].expDate || null,
                            exp_work_area_line_id: mo[i].lineId || null,
                            exp_work_area_group_id: mo[i].groupId || null,
                            next_work_area_id: mo[i].nextAreaId || null,
                          });
                        if (ticketAreaNew) {
                          const ticketLogNew =
                            await WorkActivityLog.query().insert({
                              employee_id: codeUser,
                              work_area_id: workAreaId,
                              module_name: 'ticket',
                              module_id: ticketAreaNew.id,
                              activity: 'TicketCreated',
                              data: JSON.stringify({}),
                            });
                          if (mo[i].commentVoucher?.length > 0) {
                            const addNewComment =
                              await WorkNotes.query().insert({
                                mo_id: mo[i].mo_id,
                                work_area_ticket_id: ticketAreaNew.id,
                                note: mo[i].commentVoucher,
                                employee_id: codeUser,
                              });
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        } else {
          // ? si es una mo
          const getIdLocation = await WorkInventoryBins.query()
            .join(
              'work_zones',
              'work_inventory_bins.work_zone_id',
              'work_zones.id'
            )
            .join(
              'work_area_zones',
              'work_zones.id',
              'work_area_zones.work_zone_id'
            )
            .where('work_area_zones.work_area_id', workAreaId)
            .where('work_inventory_bins.name', mo[i].location || 'N/A')
            .select('work_inventory_bins.id');

          if (mo[i].repo === 'SI') {
            const voucher = await WorkVouchers.query().insertGraph({
              mo_id: mo[i].mo_id,
              is_primary: mo[i].isMain === 'true' ? 1 : 0,
              is_repo: 1,
              work_voucher_type_id: mo[i].voucherTypeId,
              workVouchersWorkTickets: [
                {
                  work_area_id: workAreaId,
                  made_by_mo_scan: 0,
                  notify_company: 1,
                  is_company_notified: 0,
                  work_inventory_location_id:
                    getIdLocation.length > 0 ? getIdLocation[0].id : null,
                  work_area_ticket_status_id:
                    dataMiddlewares.workTicketStatuses,
                  exp_finish_date: mo[i].expDate || null,
                  exp_work_area_line_id: mo[i].lineId || null,
                  exp_work_area_group_id: mo[i].groupId || null,
                  next_work_area_id: mo[i].nextAreaId || null,
                },
              ],
            });
            const ticketLog = await WorkActivityLog.query().insert({
              employee_id: codeUser,
              work_area_id: workAreaId,
              module_name: 'ticket',
              module_id: voucher.workVouchersWorkTickets[0].id,
              activity: 'TicketCreated',
              data: JSON.stringify({}),
            });

            if (mo[i].commentVoucher?.length > 0) {
              await WorkNotes.query().insert({
                mo_id: mo[i].mo_id,
                work_area_ticket_id: voucher.workVouchersWorkTickets[0].id,
                note: mo[i].commentVoucher,
                employee_id: codeUser,
              });
            }

            if (voucher.length > 0) {
              dataNewVoucher.push({
                ...voucher,
                ...ticketLog,
              });
            }
          } else {
            //// se crea un nuevo ticket y se actualiza el voucher type si el ticket
            const voucher = await WorkVouchers.query().insertGraph({
              mo_id: mo[i].mo_id,
              is_primary: mo[i].isMain === 'true' ? 1 : 0,
              work_voucher_type_id: mo[i].voucherTypeId,
              workVouchersWorkTickets: [
                {
                  work_area_id: workAreaId,
                  made_by_mo_scan: 0,
                  notify_company: 1,
                  is_company_notified: 0,
                  work_inventory_location_id:
                    getIdLocation.length > 0 ? getIdLocation[0].id : null,
                  work_area_ticket_status_id:
                    dataMiddlewares.workTicketStatuses,
                  exp_finish_date: mo[i].expDate || null,
                  exp_work_area_line_id: mo[i].lineId || null,
                  exp_work_area_group_id: mo[i].groupId || null,
                  next_work_area_id: mo[i].nextAreaId || null,
                },
              ],
            });

            const ticketLog = await WorkActivityLog.query().insert({
              employee_id: codeUser,
              work_area_id: workAreaId,
              module_name: 'ticket',
              module_id: voucher.workVouchersWorkTickets[0].id,
              activity: 'TicketCreated',
              data: JSON.stringify({}),
            });
            if (mo[i].commentVoucher?.length > 0) {
              await WorkNotes.query().insert({
                mo_id: mo[i].mo_id,
                work_area_ticket_id: voucher.workVouchersWorkTickets[0].id,
                note: mo[i].commentVoucher,
                employee_id: codeUser,
              });
            }

            if (voucher.length > 0) {
              dataNewVoucher.push({
                ...voucher,
                ...ticketLog,
              });
            }
          }
        }
      }
    }
    // retornamos la data
    return res.status(200).json({
      ok: true,
      data: {
        dataVoucher,
        updateDataVoucher,
        dataNewVoucherPlate,
        dataNewVoucher,
        dataErrorsVoucher,
      },
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getAllVouchers(req: Request, res: Response) {
  try {
    const { customers, fromDate, mos, statuses, style, workAreaId } =
      req.body as unknown as {
        customers: number[];
        fromDate: string;
        mos: string[];
        style: string;
        statuses: number[];
        workAreaId: number;
      };

    if (customers.length === 0)
      return res.status(200).json({
        ok: true,
        data: [],
        totalRows: 0,
      });

    const today = new Date().toISOString().split('T')[0];

    let getMoInfo = WorkAreaTickets.query()
      .select([
        { id: 'work_area_tickets.id' },
        { voucher_id: 'work_vouchers.id' },
        { voucher_type: 'work_voucher_types.name' },
        'work_area_tickets.created_at',
        { group: 'work_area_groups.name' },
        { line: 'work_area_lines.name' },
        { last_activity_status_employee: 'last_activity.first_name' },
        { last_activity_status_date: 'last_activity.updated_at' },
        'mo_numbers.mo_id',
        'mo_numbers.mo_status',
        'mo_numbers.material_date',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        'mo_numbers.ItemDescription8',
        'mo_numbers.num',
        'mo_numbers.po_numbers',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.customer',
        { voucher_group: 'work_voucher_groups.name' },
        raw(
          "CASE WHEN work_vouchers.is_repo = 1 THEN 'ES REPOSICION' ELSE 'NO ES REPOSICION' END"
        ).as('is_repo'),
        raw(
          "CASE WHEN work_vouchers.is_primary = 1 THEN 'PRINCIPAL' ELSE 'NO PRINCIPAL' END"
        ).as('is_primary'),
        'work_area_tickets.work_inventory_location_id',
        'work_area_tickets.work_voucher_id',
        fn
          .coalesce(
            WorkNotes.query()
              .select('work_notes.note')
              .orderBy('work_notes.id', 'desc')
              .where(
                'work_notes.work_area_ticket_id',
                ref('work_area_tickets.id')
              )
              .limit(1),
            'Sin comentario'
          )
          .as('last_note'),
        'last_packet.file_uuid',
        'voucher_ticket_counts.ticket_count',
        'work_area_tickets.exp_finish_date',
        'work_area_tickets.prev_work_area_id',
        'work_area_tickets.next_work_area_id',
        { voucher_plate_name: 'work_voucher_plates.name' },
        { ticket_status_name: 'work_area_ticket_statuses.name' },
        { status_global: 'work_statuses.name' },
        { bin_location_name: 'work_inventory_bins.name' },
        { next_area_name: 'next_work_area.area_name' },
        { prev_area_name: 'prev_work_area.area_name' },
        { voucher_plate_name: 'work_voucher_plates.name' },
        MoScans.query()
          .select('mo_scans.sew')
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .where('mo_scans.work_area_id', workAreaId)
          .whereNull('mo_scans.removed_at')
          .limit(1)
          .as('mo_scan'),
        MoScans.query()
          .select('work_area_groups.name')
          .as('group_scan')
          .leftJoin(
            'work_area_groups',
            'mo_scans.work_area_group_id',
            'work_area_groups.id'
          )
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .where('mo_scans.work_area_id', workAreaId)
          .whereNull('mo_scans.removed_at')
          .limit(1),
        MoScans.query()
          .select('work_area_lines.name')
          .as('line_scan')
          .leftJoin(
            'work_area_lines',
            'mo_scans.work_area_line_id',
            'work_area_lines.id'
          )
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .where('mo_scans.work_area_id', workAreaId)
          .whereNull('mo_scans.removed_at')
          .limit(1),
      ])
      .leftJoin(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .leftJoin('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
      .leftJoin(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .leftJoin(
        'work_voucher_groups',
        'work_vouchers.work_voucher_group_id',
        'work_voucher_groups.id'
      )
      .leftJoin(
        'work_area_groups',
        'work_area_tickets.exp_work_area_group_id',
        'work_area_groups.id'
      )
      .leftJoin(
        'work_area_lines',
        'work_area_tickets.exp_work_area_line_id',
        'work_area_lines.id'
      )
      .leftJoin(
        'work_voucher_plates',
        'work_vouchers.work_voucher_plate_id',
        'work_voucher_plates.id'
      )
      .leftJoin(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id',
        '=',
        'work_area_ticket_statuses.id'
      )
      .leftJoin(
        'work_statuses',
        'work_area_ticket_statuses.work_status_id',
        '=',
        'work_statuses.id'
      )
      .leftJoin(
        'work_inventory_bins',
        'work_area_tickets.work_inventory_location_id',
        'work_inventory_bins.id'
      )
      .leftJoin(
        raw(`work_areas next_work_area ON
          next_work_area.work_area_id =
          work_area_tickets.next_work_area_id`)
      )
      .leftJoin(
        raw(`work_areas prev_work_area ON
          prev_work_area.work_area_id =
          work_area_tickets.prev_work_area_id`)
      )
      .leftJoin(
        WorkActivityLog.query()
          .select('employees.first_name', 'work_activity_log.updated_at', {
            ticket_id: 'latest_work_activity.module_id',
          })
          .join(
            WorkActivityLog.query()
              .select('work_activity_log.module_id')
              .max({ max_activity_id: 'work_activity_log.id' })
              .where('work_activity_log.activity', 'TicketStatusChanged')
              .groupBy('work_activity_log.module_id')
              .as('latest_work_activity'),
            'latest_work_activity.max_activity_id',
            'work_activity_log.id'
          )
          .leftJoin(
            'employees',
            'employees.employee_id',
            'work_activity_log.employee_id'
          )
          .whereNotNull('latest_work_activity.module_id')
          .as('last_activity'),
        'last_activity.ticket_id',
        'work_area_tickets.id'
      )
      .leftJoin(
        MoVouchers.query()
          .join(
            MoVouchers.query()
              .select('mo_vouchers.mo_id')
              .max({ last_id: 'mo_vouchers.id' })
              .where('file_status', 'Active')
              .groupBy('mo_vouchers.mo_id')
              .as('latest_voucher'),
            'latest_voucher.last_id',
            'mo_vouchers.id'
          )
          .whereNotNull('latest_voucher.last_id')
          .as('last_packet'),
        'last_packet.mo_id',
        'work_vouchers.mo_id'
      )
      .leftJoin(
        WorkAreaTickets.query()
          .select('work_area_tickets.work_voucher_id')
          .count({ ticket_count: 'work_area_tickets.id' })
          .groupBy('work_area_tickets.work_voucher_id')
          .as('voucher_ticket_counts'),
        'voucher_ticket_counts.work_voucher_id',
        'work_area_tickets.work_voucher_id'
      )
      .where('work_area_tickets.work_area_id', workAreaId);

    if (customers.length > 0)
      getMoInfo.whereIn('mo_numbers.company_code', customers);

    if (statuses.length > 0)
      getMoInfo.whereIn('work_area_ticket_statuses.id', statuses);

    if (mos.length > 0) {
      getMoInfo.whereIn('mo_numbers.num', mos);
    } else if (style) {
      getMoInfo.where('mo_numbers.style', style);
    } else if (fromDate) {
      getMoInfo.whereBetween('work_area_tickets.finished_at', [
        `${dayjs(fromDate).format('YYYY-MM-DD')} 00:00:00`,
        `${dayjs(today).format('YYYY-MM-DD')} 23:59:59`,
      ]);
    }

    getMoInfo = await getMoInfo;

    if (getMoInfo.length > 0) {
      return res.status(200).json({
        ok: true,
        data: getMoInfo,
        totalRows: getMoInfo.length,
      });
    } else {
      return res.status(200).json({
        ok: false,
        data: [],
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getVouchers(req: Request, res: Response) {
  try {
    const { workAreaId } = req.body;

    const getMoInfo = await WorkAreaTickets.query()
      .select([
        { id: 'work_area_tickets.id' },
        { voucher_id: 'work_vouchers.id' },
        { voucher_type: 'work_voucher_types.name' },
        'work_area_tickets.created_at',
        { group: 'work_area_groups.name' },
        { line: 'work_area_lines.name' },
        { last_activity_status_employee: 'last_activity.first_name' },
        { last_activity_status_date: 'last_activity.updated_at' },
        'mo_numbers.mo_id',
        'mo_numbers.mo_status',
        'mo_numbers.material_date',
        'mo_numbers.mo_order',
        'mo_numbers.company_code',
        'mo_numbers.required_date',
        'mo_numbers.ItemDescription8',
        'mo_numbers.num',
        'mo_numbers.po_numbers',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.customer',
        { voucher_group: 'work_voucher_groups.name' },
        { voucher_group_id: 'work_voucher_groups.id' },
        { batch: 'work_area_batches.name' },
        { batch_id: 'work_area_batches.id' },
        raw(
          "CASE WHEN work_vouchers.is_repo = 1 THEN 'ES REPOSICION' ELSE 'NO ES REPOSICION' END"
        ).as('is_repo'),
        raw(
          "CASE WHEN work_vouchers.is_primary = 1 THEN 'PRINCIPAL' ELSE 'NO PRINCIPAL' END"
        ).as('is_primary'),
        'work_area_tickets.work_inventory_location_id',
        'work_area_tickets.work_voucher_id',
        fn
          .coalesce(
            WorkNotes.query()
              .select('work_notes.note')
              .orderBy('work_notes.id', 'desc')
              .where(
                'work_notes.work_area_ticket_id',
                ref('work_area_tickets.id')
              )
              .limit(1),
            'Sin comentario'
          )
          .as('last_note'),
        MoScans.query()
          .select('mo_scans.sew')
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .where('mo_scans.work_area_id', workAreaId)
          .whereNull('mo_scans.removed_at')
          .limit(1)
          .as('mo_scan'),
        MoScans.query()
          .select('work_area_groups.name')
          .as('group_scan')
          .leftJoin(
            'work_area_groups',
            'mo_scans.work_area_group_id',
            'work_area_groups.id'
          )
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .where('mo_scans.work_area_id', workAreaId)
          .whereNull('mo_scans.removed_at')
          .limit(1),
        MoScans.query()
          .select('work_area_lines.name')
          .as('line_scan')
          .leftJoin(
            'work_area_lines',
            'mo_scans.work_area_line_id',
            'work_area_lines.id'
          )
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .where('mo_scans.work_area_id', workAreaId)
          .whereNull('mo_scans.removed_at')
          .limit(1),
        'last_packet.file_uuid',
        'voucher_ticket_counts.ticket_count',
        'work_area_tickets.exp_finish_date',
        'work_area_tickets.prev_work_area_id',
        'work_area_tickets.next_work_area_id',
        { voucher_plate_name: 'work_voucher_plates.name' },
        { ticket_status_name: 'work_area_ticket_statuses.name' },
        { status_global: 'work_statuses.name' },
        { bin_location_name: 'work_inventory_bins.name' },
        { next_area_name: 'next_work_area.area_name' },
        { prev_area_name: 'prev_work_area.area_name' },
      ])
      .leftJoin(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .leftJoin('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
      .leftJoin(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .leftJoin(
        'work_voucher_groups',
        'work_vouchers.work_voucher_group_id',
        'work_voucher_groups.id'
      )
      .leftJoin(
        'work_area_batches',
        'work_area_tickets.work_batch_id',
        'work_area_batches.id'
      )
      .leftJoin(
        'work_area_groups',
        'work_area_tickets.exp_work_area_group_id',
        'work_area_groups.id'
      )
      .leftJoin(
        'work_area_lines',
        'work_area_tickets.exp_work_area_line_id',
        'work_area_lines.id'
      )
      .leftJoin(
        'work_voucher_plates',
        'work_vouchers.work_voucher_plate_id',
        'work_voucher_plates.id'
      )
      .leftJoin(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id',
        '=',
        'work_area_ticket_statuses.id'
      )
      .leftJoin(
        'work_statuses',
        'work_area_ticket_statuses.work_status_id',
        '=',
        'work_statuses.id'
      )
      .leftJoin(
        'work_inventory_bins',
        'work_area_tickets.work_inventory_location_id',
        'work_inventory_bins.id'
      )
      .leftJoin(
        raw(`work_areas next_work_area ON
          next_work_area.work_area_id =
          work_area_tickets.next_work_area_id`)
      )
      .leftJoin(
        raw(`work_areas prev_work_area ON
          prev_work_area.work_area_id =
          work_area_tickets.prev_work_area_id`)
      )
      .leftJoin(
        WorkActivityLog.query()
          .select('employees.first_name', 'work_activity_log.updated_at', {
            ticket_id: 'latest_work_activity.module_id',
          })
          .join(
            WorkActivityLog.query()
              .select('work_activity_log.module_id')
              .max({ max_activity_id: 'work_activity_log.id' })
              .where('work_activity_log.activity', 'TicketStatusChanged')
              .groupBy('work_activity_log.module_id')
              .as('latest_work_activity'),
            'latest_work_activity.max_activity_id',
            'work_activity_log.id'
          )
          .leftJoin(
            'employees',
            'employees.employee_id',
            'work_activity_log.employee_id'
          )
          .whereNotNull('latest_work_activity.module_id')
          .as('last_activity'),
        'last_activity.ticket_id',
        'work_area_tickets.id'
      )
      .leftJoin(
        MoVouchers.query()
          .join(
            MoVouchers.query()
              .select('mo_vouchers.mo_id')
              .max({ last_id: 'mo_vouchers.id' })
              .where('file_status', 'Active')
              .groupBy('mo_vouchers.mo_id')
              .as('latest_voucher'),
            'latest_voucher.last_id',
            'mo_vouchers.id'
          )
          .whereNotNull('latest_voucher.last_id')
          .as('last_packet'),
        'last_packet.mo_id',
        'work_vouchers.mo_id'
      )
      .leftJoin(
        WorkAreaTickets.query()
          .select('work_area_tickets.work_voucher_id')
          .count({ ticket_count: 'work_area_tickets.id' })
          .groupBy('work_area_tickets.work_voucher_id')
          .as('voucher_ticket_counts'),
        'voucher_ticket_counts.work_voucher_id',
        'work_area_tickets.work_voucher_id'
      )
      .whereNull('work_area_tickets.finished_at')
      .where('work_area_tickets.work_area_id', workAreaId)
      .orderBy('work_area_tickets.created_at', 'DESC');

    if (getMoInfo.length > 0) {
      return res
        .status(200)
        .json({ ok: true, data: getMoInfo, totalRows: getMoInfo.length });
    } else {
      return res.status(200).json({ ok: false, data: [] });
    }
  } catch (error) {
    console.error(error);
    let message: any = 'Unknown';
    let stack: any = null;
    if (error instanceof Error) {
      message = error.message;
      stack = error.stack;
    }
    return res.status(500).json({ ok: false, message: message, stack: stack });
  }
}

export async function getVoucherInfo(req: Request, res: Response) {
  try {
    const { voucherId } = req.body;

    const voucher = voucherId.substr(4);

    const voucherInfo = await WorkVouchers.query()
      .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
      .where('work_vouchers.id', voucher)
      .select(
        'work_vouchers.id',
        'mo_numbers.num',
        'mo_numbers.mo_id',
        'mo_numbers.customer'
      );

    return res.status(200).json({
      ok: true,
      mo: voucherInfo[0].num,
      uuidVoucher: uuidv4(),
      voucherId: voucherInfo[0].id,
      mo_id: voucherInfo[0].mo_id,
      customer: voucherInfo[0].customer,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function mergeVouchers(req: Request, res: Response) {
  try {
    // variables
    const {
      vouchersMerge,
      area,
      disabledRadio,
      isRepo,
      voucherTypeBarcode,
      codeUser,
    } = req.body;

    const format = 'YYYY-MM-DD HH:mm:ss';
    const actualDate = new Date();

    if (disabledRadio) {
      // se creara un nuevo voucher para hacer el merge
      const mo_id = vouchersMerge[0].mo_id;

      //search status complete of the area
      const completeStatus = await WorkAreaTicketStatuses.query()
        .where('work_area_ticket_statuses.work_area_id', area)
        .where('work_area_ticket_statuses.work_status_id', 100)
        .orderBy('work_area_ticket_statuses.sequence')
        .limit(1)
        .select('work_area_ticket_statuses.id');

      // search status new of the area
      const newStatus = await WorkAreaTicketStatuses.query()
        .where('work_area_ticket_statuses.work_area_id', area)
        .where('work_area_ticket_statuses.work_status_id', 50)
        .orderBy('work_area_ticket_statuses.sequence')
        .limit(1)
        .select('work_area_ticket_statuses.id');

      const newVoucher = await WorkVouchers.query().insert({
        mo_id: mo_id,
        work_voucher_type_id: voucherTypeBarcode,
        is_primary: 1,
        ignore_next_area: 0,
        is_repo: isRepo,
      });

      await WorkAreaTickets.query().insert({
        work_area_id: area,
        work_voucher_id: newVoucher.id,
        work_area_ticket_status_id: newStatus[0].id,
        made_by_mo_scan: 0,
        notify_company: 1,
        is_company_notified: 0,
      });

      //loop to vouchers
      for (let i = 0; i < vouchersMerge.length; i++) {
        //vouchers
        await WorkVouchers.query()
          .update({
            merge_voucher_id: newVoucher.id,
          })
          .where('work_vouchers.id', vouchersMerge[i].voucherId);

        // tickets
        await WorkAreaTickets.query()
          .update({
            work_area_ticket_status_id: completeStatus[0].id,
            is_company_notified: 0,
            finished_at: dayjs(actualDate).format(format),
          })
          .where(
            'work_area_tickets.work_voucher_id',
            vouchersMerge[i].voucherId
          )
          .where('work_area_tickets.work_area_id', area);

        const ticketLog = await WorkActivityLog.query().insert({
          employee_id: codeUser,
          work_area_id: area,
          module_name: 'ticket',
          module_id: vouchersMerge[i].voucherId,
          activity: 'MergeTicket',
          data: JSON.stringify({}),
        });
      }
    } else {
      const searchMainVoucher = vouchersMerge.find(
        (voucher: { main: boolean }) => voucher.main === true
      );

      const getVouchersMerge = vouchersMerge.filter(
        (voucher: { main: boolean }) => voucher.main === false
      );

      //search status complete of the area
      const completeStatus = await WorkAreaTicketStatuses.query()
        .where('work_area_ticket_statuses.work_area_id', area)
        .where('work_area_ticket_statuses.work_status_id', 100)
        .orderBy('work_area_ticket_statuses.sequence')
        .limit(1)
        .select('work_area_ticket_statuses.id');

      //loop to vouchers
      for (let i = 0; i < getVouchersMerge.length; i++) {
        //vouchers
        await WorkVouchers.query()
          .update({
            merge_voucher_id: searchMainVoucher.voucherId,
          })
          .where('work_vouchers.id', getVouchersMerge[i].voucherId);

        // tickets
        await WorkAreaTickets.query()
          .update({
            work_area_ticket_status_id: completeStatus[0].id,
            is_company_notified: 0,
            finished_at: dayjs(actualDate).format(format),
          })
          .where(
            'work_area_tickets.work_voucher_id',
            getVouchersMerge[i].voucherId
          )
          .where('work_area_tickets.work_area_id', area);
      }
    }

    return res.status(200).json({ ok: true });
  } catch (error) {
    console.log(error);

    return res.status(500).json({ ok: false });
  }
}

export async function getVouchersMo(req: Request, res: Response) {
  try {
    const { mo } = req.body;

    const getMoInfo = await WorkAreaTickets.query()
      .join(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        '=',
        'work_vouchers.id'
      )
      .join(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .leftJoin(
        'work_voucher_plates',
        'work_vouchers.work_voucher_plate_id',
        'work_voucher_plates.id'
      )
      .join('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
      .join(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id',
        '=',
        'work_area_ticket_statuses.id'
      )
      .join(
        'work_statuses',
        'work_area_ticket_statuses.work_status_id',
        '=',
        'work_statuses.id'
      )
      .join(
        'work_areas',
        'work_area_tickets.work_area_id',
        '=',
        'work_areas.work_area_id'
      )
      .orderBy('work_vouchers.created_at', 'desc')
      .select([
        'mo_numbers.mo_id',
        'mo_numbers.mo_status',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        'mo_numbers.num',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.customer',
        { id: 'work_area_tickets.id' },
        'work_vouchers.created_at',
        'work_vouchers.is_repo',
        'work_area_tickets.work_inventory_location_id',
        'work_area_tickets.work_voucher_id',
        WorkAreaTickets.query()
          .where('work_area_tickets.work_voucher_id', ref('work_vouchers.id'))
          .groupBy('work_area_tickets.work_voucher_id')
          .count('work_area_tickets.id')
          .as('ticketByMo'),
        MoVouchers.query()
          .where('mo_vouchers.mo_id', ref('mo_numbers.mo_id'))
          .where('mo_vouchers.file_status', 'Active')
          .select('mo_vouchers.file_uuid')
          .limit(1)
          .as('file_uuid'),
        WorkInventoryBins.query()
          .where(
            'work_inventory_bins.id',
            ref('work_area_tickets.work_inventory_location_id')
          )
          .select('work_inventory_bins.name')
          .as('nameLocation'),
        'work_area_tickets.exp_finish_date',
        { voucherId: 'work_vouchers.id' },
        { voucherType: 'work_voucher_types.name' },
        'work_area_tickets.prev_work_area_id',
        'work_area_tickets.next_work_area_id',
        { voucherPlateName: 'work_voucher_plates.name' },
        'work_area_ticket_statuses.name',
        { statusGlobal: 'work_statuses.name' },
        WorkAreas.query()
          .where(
            'work_areas.work_area_id',
            ref('work_area_tickets.next_work_area_id')
          )
          .select('work_areas.area_name')
          .as('areaNameNext'),
        WorkAreas.query()
          .where(
            'work_areas.work_area_id',
            ref('work_area_tickets.prev_work_area_id')
          )
          .select('work_areas.area_name')
          .as('areaNamePrev'),
        WorkAreas.query()
          .where(
            'work_areas.work_area_id',
            ref('work_area_tickets.work_area_id')
          )
          .select('work_areas.area_name')
          .as('areaTicket'),
      ])
      .whereNotIn('mo_numbers.mo_status', [
        'Void',
        'Cancelled',
        'Materials',
        'Complete',
      ])
      .where('work_area_ticket_statuses.work_status_id', '<>', 100)
      .where('mo_numbers.mo_id', mo);

    const dataFiltering = getMoInfo.map((moInfo: any) => {
      return {
        ...moInfo,
        required_date: moInfo.required_date
          ? (moInfo.required_date = dayjs(moInfo.required_date).format(
              'DD-MM-YYYY'
            ))
          : '00-00-0000',
        is_repo: moInfo.is_repo === 1 ? 'REPO' : 'NO REPO',
        created_at: (moInfo.created_at = dayjs(moInfo.created_at).format(
          'DD-MM-YYYY HH:mm'
        )),
        exp_finish_date: moInfo.exp_finish_date
          ? (moInfo.exp_finish_date = dayjs(moInfo.exp_finish_date).format(
              'YYYY-MM-DD'
            ))
          : '',
      };
    });

    if (getMoInfo.length > 0) {
      return res.status(200).json({
        ok: true,
        data: dataFiltering,
      });
    } else {
      return res.status(200).json({
        ok: false,
        data: [],
      });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getVouchersInfoFilter(req: Request, res: Response) {
  try {
    const { mo, area, voucherType } = req.body;

    const dataResponse = [];

    for (let i = 0; i < mo.length; i++) {
      if (area && voucherType) {
        const infoVouchers = await WorkAreaTickets.query()
          .join(
            'work_vouchers',
            'work_area_tickets.work_voucher_id',
            'work_vouchers.id'
          )
          .join(
            'work_areas',
            'work_area_tickets.work_area_id',
            'work_areas.work_area_id'
          )
          .join(
            'work_area_ticket_statuses',
            'work_area_tickets.work_area_ticket_status_id',
            'work_area_ticket_statuses.id'
          )
          .join(
            'work_voucher_types',
            'work_vouchers.work_voucher_type_id',
            'work_voucher_types.id'
          )
          .where('work_area_tickets.work_area_id', area)
          .where('work_vouchers.work_voucher_type_id', voucherType)
          .where('work_vouchers.mo_id', mo[i])
          .select(
            WorkInventoryBins.query()
              .where(
                'work_inventory_bins.id',
                ref('work_area_tickets.work_inventory_location_id')
              )
              .select('work_inventory_bins.name')
              .as('location'),
            { workAreaTicketsId: 'work_area_tickets.id' },
            { workVoucherId: 'work_vouchers.id' },
            { num: 'mo_numbers.num' },
            { customer: 'mo_numbers.customer' },
            { quantity: 'mo_numbers.quantity' },
            { areaTicket: 'work_areas.area_name' },
            { ticketStatus: 'work_area_ticket_statuses.name' },
            { expDate: 'work_area_tickets.exp_finish_date' },
            { finishedAtTicket: 'work_area_tickets.finished_at' },
            { isRepo: 'work_vouchers.is_repo' },
            WorkAreas.query()
              .where(
                'work_areas.work_area_id',
                ref('work_area_tickets.next_work_area_id')
              )
              .select('work_areas.area_name')
              .as('nextArea'),
            { voucherType: 'work_voucher_types.name' }
          );

        if (infoVouchers.length > 0) {
          const dataFiltering = [];

          for (let i = 0; i < infoVouchers.length; i++) {
            const searchTicketOtherArea = await WorkAreaTickets.query()
              .join(
                'work_vouchers',
                'work_area_tickets.work_voucher_id',
                'work_vouchers.id'
              )
              .join(
                'work_area_ticket_statuses',
                'work_area_tickets.work_area_ticket_status_id ',
                'work_area_ticket_statuses.id'
              )
              .join(
                'work_areas',
                'work_area_tickets.work_area_id ',
                'work_areas.work_area_id'
              )
              .where(
                'work_area_tickets.work_voucher_id',
                infoVouchers[i].workVoucherId
              )
              .where('work_area_ticket_statuses.work_status_id', 50)
              .orWhere('work_area_ticket_statuses.work_status_id', 10)
              .select(['work_areas.area_name']);

            dataFiltering.push({
              ...infoVouchers[i],
              lastUpdateStatusDate: infoVouchers[i]?.lastUpdateStatusDate
                ? dayjs(infoVouchers[i]?.lastUpdateStatusDate).format(
                    'DD-MM-YYYY'
                  )
                : '00-00-0000',
              finishedAtTicket: infoVouchers[i].finishedAtTicket
                ? dayjs(infoVouchers[i].finishedAtTicket).format('DD-MM-YYYY')
                : '00-00-0000',
              AreaActual: searchTicketOtherArea[0]?.area_name,
              isRepo: infoVouchers[i].isRepo === 1 ? 'REPO' : 'NO REPO',
            });
          }
          const dataLogSearch = [];
          let activityLog;
          for (let j = 0; j < dataFiltering.length; j++) {
            activityLog = await WorkActivityLog.query()
              .join(
                'employees',
                'work_activity_log.employee_id',
                'employees.employee_id'
              )
              .where('work_activity_log.activity', 'TicketStatusChanged')
              .where(
                'work_activity_log.module_id',
                dataFiltering[j].workAreaTicketsId
              )
              .select([
                { lastUpdateStatusDate: 'work_activity_log.updated_at' },
                { lastUpdateStatusEmployee: 'employees.first_name' },
              ]);

            dataLogSearch.push({
              ...dataFiltering[j],
              ...activityLog[0],
            });
          }
          dataResponse.push(...dataLogSearch);
        }
      } else if (area) {
        const infoVouchers = await WorkAreaTickets.query()
          .join(
            'work_vouchers',
            'work_area_tickets.work_voucher_id',
            'work_vouchers.id'
          )
          .join(
            'work_areas',
            'work_area_tickets.work_area_id',
            'work_areas.work_area_id'
          )
          .join(
            'work_area_ticket_statuses',
            'work_area_tickets.work_area_ticket_status_id',
            'work_area_ticket_statuses.id'
          )
          .join(
            'work_voucher_types',
            'work_vouchers.work_voucher_type_id',
            'work_voucher_types.id'
          )
          .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
          .join(
            'work_activity_log',
            'work_area_tickets.id',
            'work_activity_log.module_id'
          )
          .join(
            'employees',
            'work_activity_log.employee_id',
            'employees.employee_id'
          )
          .where('work_activity_log.activity', 'TicketStatusChanged')
          .where('work_area_tickets.work_area_id', area)
          .where('work_vouchers.mo_id', mo[i])
          .select(
            WorkInventoryBins.query()
              .where(
                'work_inventory_bins.id',
                ref('work_area_tickets.work_inventory_location_id')
              )
              .select('work_inventory_bins.name')
              .as('location'),
            { workAreaTicketsId: 'work_area_tickets.id' },
            { workVoucherId: 'work_vouchers.id' },
            { num: 'mo_numbers.num' },

            { customer: 'mo_numbers.customer' },
            { quantity: 'mo_numbers.quantity' },
            { areaTicket: 'work_areas.area_name' },
            { ticketStatus: 'work_area_ticket_statuses.name' },
            { expDate: 'work_area_tickets.exp_finish_date' },
            { isRepo: 'work_vouchers.is_repo' },
            { finishedAtTicket: 'work_area_tickets.finished_at' },
            { lastUpdateStatusDate: 'work_activity_log.updated_at' },
            { lastUpdateStatusEmployee: 'employees.first_name' },
            WorkAreas.query()
              .where(
                'work_areas.work_area_id',
                ref('work_area_tickets.next_work_area_id')
              )
              .select('work_areas.area_name')
              .as('nextArea'),
            { voucherType: 'work_voucher_types.name' }
          );

        if (infoVouchers.length > 0) {
          const dataFiltering = [];
          for (let i = 0; i < infoVouchers.length; i++) {
            const searchTicketOtherArea = await WorkAreaTickets.query()
              .join(
                'work_vouchers',
                'work_area_tickets.work_voucher_id',
                'work_vouchers.id'
              )
              .join(
                'work_area_ticket_statuses',
                'work_area_tickets.work_area_ticket_status_id ',
                'work_area_ticket_statuses.id'
              )
              .join(
                'work_areas',
                'work_area_tickets.work_area_id ',
                'work_areas.work_area_id'
              )
              .where(
                'work_area_tickets.work_voucher_id',
                infoVouchers[i].workVoucherId
              )
              .where('work_area_ticket_statuses.work_status_id', 50)
              .orWhere('work_area_ticket_statuses.work_status_id', 10)
              .select(['work_areas.area_name']);

            dataFiltering.push({
              ...infoVouchers[i],
              finishedAtTicket: infoVouchers[i].finishedAtTicket
                ? dayjs(infoVouchers[i].finishedAtTicket).format('DD-MM-YYYY')
                : '00-00-0000',
              AreaActual: searchTicketOtherArea[0]?.area_name,
              isRepo: infoVouchers[i].isRepo === 1 ? 'REPO' : 'NO REPO',
            });
          }
          const dataLogSearch = [];
          let activityLog;
          for (let j = 0; j < dataFiltering.length; j++) {
            activityLog = await WorkActivityLog.query()
              .join(
                'employees',
                'work_activity_log.employee_id',
                'employees.employee_id'
              )
              .where('work_activity_log.activity', 'TicketStatusChanged')
              .where(
                'work_activity_log.module_id',
                dataFiltering[j].workAreaTicketsId
              )
              .select([
                { lastUpdateStatusDate: 'work_activity_log.updated_at' },
                { lastUpdateStatusEmployee: 'employees.first_name' },
              ]);

            dataLogSearch.push({
              ...dataFiltering[j],
              ...activityLog[0],
            });
          }
          dataResponse.push(...dataLogSearch);
        }
      } else if (voucherType) {
        const infoVouchers = await WorkAreaTickets.query()
          .join(
            'work_vouchers',
            'work_area_tickets.work_voucher_id',
            'work_vouchers.id'
          )
          .join(
            'work_areas',
            'work_area_tickets.work_area_id',
            'work_areas.work_area_id'
          )
          .join(
            'work_area_ticket_statuses',
            'work_area_tickets.work_area_ticket_status_id',
            'work_area_ticket_statuses.id'
          )
          .join(
            'work_voucher_types',
            'work_vouchers.work_voucher_type_id',
            'work_voucher_types.id'
          )
          .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
          .where('work_vouchers.work_voucher_type_id', voucherType)
          .where('work_vouchers.mo_id', mo[i])
          .select([
            WorkInventoryBins.query()
              .where(
                'work_inventory_bins.id',
                ref('work_area_tickets.work_inventory_location_id')
              )
              .select('work_inventory_bins.name')
              .as('location'),
            { workAreaTicketsId: 'work_area_tickets.id' },
            { workVoucherId: 'work_vouchers.id' },
            { num: 'mo_numbers.num' },
            { customer: 'mo_numbers.customer' },
            { quantity: 'mo_numbers.quantity' },
            { areaTicket: 'work_areas.area_name' },
            { ticketStatus: 'work_area_ticket_statuses.name' },
            { expDate: 'work_area_tickets.exp_finish_date' },
            { finishedAtTicket: 'work_area_tickets.finished_at' },
            { isRepo: 'work_vouchers.is_repo' },
            WorkAreas.query()
              .where(
                'work_areas.work_area_id',
                ref('work_area_tickets.next_work_area_id')
              )
              .select('work_areas.area_name')
              .as('nextArea'),
            { voucherType: 'work_voucher_types.name' },
          ]);

        if (infoVouchers.length > 0) {
          const dataFiltering = [];
          for (let i = 0; i < infoVouchers.length; i++) {
            const searchTicketOtherArea = await WorkAreaTickets.query()
              .join(
                'work_vouchers',
                'work_area_tickets.work_voucher_id',
                'work_vouchers.id'
              )
              .join(
                'work_area_ticket_statuses',
                'work_area_tickets.work_area_ticket_status_id ',
                'work_area_ticket_statuses.id'
              )
              .join(
                'work_areas',
                'work_area_tickets.work_area_id ',
                'work_areas.work_area_id'
              )
              .where(
                'work_area_tickets.work_voucher_id',
                infoVouchers[i].workVoucherId
              )
              .where('work_area_ticket_statuses.work_status_id', 50)
              .orWhere('work_area_ticket_statuses.work_status_id', 10)
              .select(['work_areas.area_name']);

            dataFiltering.push({
              ...infoVouchers[i],
              lastUpdateStatusDate: infoVouchers[i]?.lastUpdateStatusDate
                ? dayjs(infoVouchers[i]?.lastUpdateStatusDate).format(
                    'DD-MM-YYYY'
                  )
                : '00-00-0000',
              finishedAtTicket: infoVouchers[i].finishedAtTicket
                ? dayjs(infoVouchers[i].finishedAtTicket).format('DD-MM-YYYY')
                : '00-00-0000',
              AreaActual: searchTicketOtherArea[0]?.area_name,
              isRepo: infoVouchers[i].isRepo === 1 ? 'REPO' : 'NO REPO',
            });
          }

          const dataLogSearch = [];
          let activityLog;
          for (let j = 0; j < dataFiltering.length; j++) {
            activityLog = await WorkActivityLog.query()
              .join(
                'employees',
                'work_activity_log.employee_id',
                'employees.employee_id'
              )
              .where('work_activity_log.activity', 'TicketStatusChanged')
              .where(
                'work_activity_log.module_id',
                dataFiltering[j].workAreaTicketsId
              )
              .select([
                { lastUpdateStatusDate: 'work_activity_log.updated_at' },
                { lastUpdateStatusEmployee: 'employees.first_name' },
              ]);

            dataLogSearch.push({
              ...dataFiltering[j],
              ...activityLog[0],
            });
          }

          dataResponse.push(...dataLogSearch);
        }
      }
    }

    return res.status(200).json({ ok: true, data: dataResponse });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function createNewVouchers(req: Request, res: Response) {
  try {
    const { mos, voucherType, isRepo = 0, area, main = 0, codeUser } = req.body;
    let getDefaultVoucherType;

    if (!voucherType) {
      getDefaultVoucherType = await WorkAreas.query()
        .where('work_areas.work_area_id', area)
        .select('work_areas.default_work_voucher_type_id');
    }

    for (let i = 0; i < mos.length; i++) {
      const voucher = await WorkVouchers()
        .query()
        .insert({
          mo_id: mos[i].mo_id,
          is_primary: main ? 1 : 0,
          is_repo: isRepo,
          work_voucher_type_id:
            getDefaultVoucherType[0].default_work_voucher_type_id,
        });
      const ticketLog = await WorkActivityLog.query().insert({
        employee_id: codeUser,
        work_area_id: area,
        module_name: 'ticket',
        module_id: voucher.id,
        activity: 'TicketCreated',
        data: JSON.stringify({}),
      });
    }
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function get_vouchers_of_mo(req: Request, res: Response) {
  try {
    const { area, mo } = req.body;
    let companyCode = 0;
    let barcode = '';

    if (mo.startsWith('PPMO') || mo.startsWith('APPMO') || mo.includes('-')) {
      if (mo.startsWith('P')) {
        companyCode = 1;
        barcode = mo;
      } else if (mo.startsWith('A')) {
        companyCode = 2;
        barcode = mo.slice(1);
      } else {
        companyCode = 3;
        barcode = mo.replace('-', '/');
      }

      const get_vouchers = await WorkVouchers.query()
        .leftJoin('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
        .leftJoin(
          'work_area_tickets',
          'work_vouchers.id',
          'work_area_tickets.work_voucher_id'
        )
        .leftJoin(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .leftJoin(
          'work_inventory_bins',
          'work_area_tickets.work_inventory_location_id',
          'work_inventory_bins.id'
        )
        .leftJoin(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .where('work_area_tickets.work_area_id', area)
        .where('mo_numbers.mo_barcode', barcode)
        .where('mo_numbers.company_code', companyCode)
        .whereNull('work_area_tickets.finished_at')
        .select(
          { voucher_id: 'work_vouchers.id' },
          { creado: 'work_vouchers.created_at' },
          { ticket_id: 'work_area_tickets.id' },
          { voucher_type: 'work_voucher_types.name' },
          'mo_numbers.mo_order',
          'mo_numbers.required_date',
          'mo_numbers.num',
          'mo_numbers.po_numbers',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.customer',
          raw(
            "CASE WHEN work_vouchers.is_repo = 1 THEN 'ES REPOSICION' ELSE 'NO ES REPOSICION' END"
          ).as('is_repo'),
          fn
            .coalesce(
              WorkNotes.query()
                .select('work_notes.note')
                .orderBy('work_notes.id', 'desc')
                .where(
                  'work_notes.work_area_ticket_id',
                  ref('work_area_tickets.id')
                )
                .limit(1),
              'Sin comentario'
            )
            .as('last_note'),
          { bin_location_name: 'work_inventory_bins.name' },
          fn
            .coalesce(ref('work_inventory_bins.name'), 'Sin ubicación')
            .as('bin_location_name'),
          { ticket_status_name: 'work_area_ticket_statuses.name' }
        );

      if (get_vouchers.length > 0) {
        return res
          .status(200)
          .json({ ok: true, isVoucher: true, data: get_vouchers });
      } else {
        const getMoInfo: any = await MoNumbers.query()
          .whereNotIn('mo_numbers.mo_status', [
            'Void',
            'Cancelled',
            'Materials',
            'Complete',
          ])
          .where('mo_numbers.mo_barcode', barcode)
          .where('mo_numbers.company_code', companyCode)
          .select(
            'mo_numbers.mo_order',
            'mo_numbers.required_date',
            'mo_numbers.num',
            'mo_numbers.po_numbers',
            'mo_numbers.style',
            'mo_numbers.quantity',
            'mo_numbers.customer'
          );

        if (getMoInfo.length > 0) {
          return res
            .status(200)
            .json({ ok: true, isVoucher: false, data: getMoInfo });
        } else {
          return res
            .status(400)
            .json({ ok: false, data: 'No se encontro la MO' });
        }
      }
    } else if (mo.startsWith('MEVB')) {
      const get_vouchers = await WorkVouchers.query()
        .leftJoin('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
        .leftJoin(
          'work_area_tickets',
          'work_vouchers.id',
          'work_area_tickets.work_voucher_id'
        )
        .leftJoin(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .leftJoin(
          'work_inventory_bins',
          'work_area_tickets.work_inventory_location_id',
          'work_inventory_bins.id'
        )
        .leftJoin(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .where('work_area_tickets.work_area_id', area)
        .where('work_vouchers.id', mo.substr(4))
        .whereNull('work_area_tickets.finished_at')
        .select(
          { voucher_id: 'work_vouchers.id' },
          { creado: 'work_vouchers.created_at' },
          { ticket_id: 'work_area_tickets.id' },
          { voucher_type: 'work_voucher_types.name' },
          'mo_numbers.mo_order',
          'mo_numbers.required_date',
          'mo_numbers.num',
          'mo_numbers.po_numbers',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.customer',
          raw(
            "CASE WHEN work_vouchers.is_repo = 1 THEN 'ES REPOSICION' ELSE 'NO ES REPOSICION' END"
          ).as('is_repo'),
          fn
            .coalesce(
              WorkNotes.query()
                .select('work_notes.note')
                .orderBy('work_notes.id', 'desc')
                .where(
                  'work_notes.work_area_ticket_id',
                  ref('work_area_tickets.id')
                )
                .limit(1),
              'Sin comentario'
            )
            .as('last_note'),
          { bin_location_name: 'work_inventory_bins.name' },
          fn
            .coalesce(ref('work_inventory_bins.name'), 'Sin ubicación')
            .as('bin_location_name'),
          { ticket_status_name: 'work_area_ticket_statuses.name' }
        );

      if (get_vouchers.length > 0) {
        return res
          .status(200)
          .json({ ok: true, isVoucher: true, data: get_vouchers });
      } else {
        return res
          .status(400)
          .json({ ok: false, data: 'No se encontro el voucher' });
      }
    } else {
      const get_vouchers = await WorkVouchers.query()
        .leftJoin('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
        .leftJoin(
          'work_area_tickets',
          'work_vouchers.id',
          'work_area_tickets.work_voucher_id'
        )
        .leftJoin(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .leftJoin(
          'work_inventory_bins',
          'work_area_tickets.work_inventory_location_id',
          'work_inventory_bins.id'
        )
        .leftJoin(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .where('work_area_tickets.work_area_id', area)
        .where('mo_numbers.num', mo)
        .whereNull('work_area_tickets.finished_at')
        .select(
          { voucher_id: 'work_vouchers.id' },
          { creado: 'work_vouchers.created_at' },
          { ticket_id: 'work_area_tickets.id' },
          { voucher_type: 'work_voucher_types.name' },
          'mo_numbers.mo_order',
          'mo_numbers.required_date',
          'mo_numbers.num',
          'mo_numbers.po_numbers',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.customer',
          raw(
            "CASE WHEN work_vouchers.is_repo = 1 THEN 'ES REPOSICION' ELSE 'NO ES REPOSICION' END"
          ).as('is_repo'),
          fn
            .coalesce(
              WorkNotes.query()
                .select('work_notes.note')
                .orderBy('work_notes.id', 'desc')
                .where(
                  'work_notes.work_area_ticket_id',
                  ref('work_area_tickets.id')
                )
                .limit(1),
              'Sin comentario'
            )
            .as('last_note'),
          { bin_location_name: 'work_inventory_bins.name' },
          fn
            .coalesce(ref('work_inventory_bins.name'), 'Sin ubicación')
            .as('bin_location_name'),
          { ticket_status_name: 'work_area_ticket_statuses.name' }
        );

      if (get_vouchers.length > 0) {
        return res
          .status(200)
          .json({ ok: true, isVoucher: true, data: get_vouchers });
      } else {
        const getMoInfo: any = await MoNumbers.query()
          .whereNotIn('mo_numbers.mo_status', [
            'Void',
            'Cancelled',
            'Materials',
            'Complete',
          ])
          .where('mo_numbers.num', mo)
          .select(
            'mo_numbers.mo_order',
            'mo_numbers.required_date',
            'mo_numbers.num',
            'mo_numbers.po_numbers',
            'mo_numbers.style',
            'mo_numbers.quantity',
            'mo_numbers.customer'
          );

        if (getMoInfo.length > 0) {
          return res
            .status(200)
            .json({ ok: true, isVoucher: false, data: getMoInfo });
        } else {
          return res
            .status(400)
            .json({ ok: false, data: 'No se encontro la MO' });
        }
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({ ok: false });
  }
}

export async function cloneVouchers(req: Request, res: Response) {
  try {
    const { workAreaId, data } = req.body;
    const infoVouchers: any[] = [];

    for (let i = 0; i < data.length; i++) {
      const voucher = await WorkVouchers.query()
        .leftJoin(
          'work_area_tickets',
          'work_vouchers.id',
          'work_area_tickets.work_voucher_id'
        )
        .where('work_vouchers.id', data[i].voucher_id)
        .where('work_area_tickets.id', data[i].id)
        .whereNull('work_area_tickets.finished_at')
        .whereNull('work_vouchers.work_voucher_group_id')
        .select(
          { voucher_id: 'work_vouchers.id' },
          { voucher_type_id: 'work_vouchers.work_voucher_type_id' },
          'work_vouchers.mo_id',
          'work_vouchers.is_repo',
          { bin_location_id: 'work_area_tickets.work_inventory_location_id' },
          { ticket_status_id: 'work_area_tickets.work_area_ticket_status_id' },
          { voucher_group_id: 'work_area_tickets.exp_work_area_group_id ' },
          { line_id: 'work_area_tickets.exp_work_area_line_id ' },
          'work_vouchers.is_primary',
          'work_area_tickets.next_work_area_id',
          'work_area_tickets.prev_work_area_id',
          'work_area_tickets.exp_finish_date'
        );

      if (voucher.length > 0) {
        infoVouchers.push(voucher[0]);
      }
    }

    if (infoVouchers.length > 0) {
      for (let j = 0; j < infoVouchers.length; j++) {
        await transaction(
          WorkVouchers,
          WorkActivityLog,
          async (WorkVouchers, WorkActivityLog) => {
            const voucher = await WorkVouchers.query().insertGraph({
              mo_id: infoVouchers[j].mo_id,
              is_primary: infoVouchers[j].is_primary,
              is_repo: infoVouchers[j].is_repo,
              work_voucher_type_id: infoVouchers[j].voucher_type_id,
              workVouchersWorkTickets: [
                {
                  work_area_id: workAreaId,
                  made_by_mo_scan: 0,
                  notify_company: 1,
                  is_company_notified: 0,
                  exp_finish_date: infoVouchers[j].exp_finish_date,
                  exp_work_area_line_id: infoVouchers[j].line_id,
                  exp_work_area_group_id: infoVouchers[j].voucher_group_id,
                  next_work_area_id: infoVouchers[j].next_work_area_id,
                  prev_work_area_id: infoVouchers[j].prev_work_area_id,
                  work_inventory_location_id: infoVouchers[j].bin_location_id,
                  work_area_ticket_status_id: infoVouchers[j].ticket_status_id,
                },
              ],
            });

            const ticketLog = await WorkActivityLog.query().insert({
              module_name: 'ticket',
              module_id: voucher.workVouchersWorkTickets[0].id,
              activity: 'TicketCreated',
              data: JSON.stringify({ cloned_from: voucher.voucher_id }),
            });

            return {
              voucher,
              ticketLog,
            };
          }
        );
      }

      return res.status(200).json({ ok: true, data: infoVouchers });
    } else {
      return res.status(500).json({
        ok: false,
        message: 'No se encontró la información de los vouchers',
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({ ok: false });
  }
}

export async function cloneVouchersGroup(req: Request, res: Response) {
  try {
    const { name, voucherGroupId, workAreaId, codeEmployee } = req.body;

    const vouchers = await WorkAreaTickets.query()
      .leftJoin(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .where('work_vouchers.work_voucher_group_id', voucherGroupId)
      .where('work_area_tickets.work_area_id', workAreaId)
      .whereNull('work_area_tickets.finished_at')
      .select(
        { voucher_id: 'work_vouchers.id' },
        { voucher_type_id: 'work_vouchers.work_voucher_type_id' },
        'work_vouchers.mo_id',
        'work_vouchers.is_repo',
        { bin_location_id: 'work_area_tickets.work_inventory_location_id' },
        { ticket_status_id: 'work_area_tickets.work_area_ticket_status_id' },
        { voucher_group_id: 'work_area_tickets.exp_work_area_group_id ' },
        { line_id: 'work_area_tickets.exp_work_area_line_id ' },
        'work_vouchers.is_primary',
        'work_area_tickets.next_work_area_id',
        'work_area_tickets.prev_work_area_id',
        'work_area_tickets.exp_finish_date'
      );

    const searchNameGroup = await WorkVoucherGroups.query()
      .where('name', name)
      .where('work_area_id_ref', workAreaId);

    if (searchNameGroup.length > 0) {
      return res
        .status(200)
        .json({ ok: false, data: 'Ya existe un grupo con ese nombre' });
    } else {
      const newVoucherGroup = await WorkVoucherGroups.query().insert({
        name,
        employee_id_ref: codeEmployee,
        work_area_id_ref: workAreaId,
      });

      const addBarcodeToVoucherGroup = await WorkVoucherGroups.query()
        .update({
          barcode: `MEGV${newVoucherGroup.id}`,
        })
        .where('id', newVoucherGroup.id);

      // iniciar el clonado de los vouchers
      if (addBarcodeToVoucherGroup) {
        for (let j = 0; j < vouchers.length; j++) {
          const newVouchers = await transaction(
            WorkVouchers,
            WorkActivityLog,
            async (WorkVouchers, WorkActivityLog) => {
              const voucher = await WorkVouchers.query().insertGraph({
                mo_id: vouchers[j].mo_id,
                is_primary: vouchers[j].is_primary,
                is_repo: vouchers[j].is_repo,
                work_voucher_type_id: vouchers[j].voucher_type_id,
                workVouchersWorkTickets: [
                  {
                    work_area_id: workAreaId,
                    made_by_mo_scan: 0,
                    notify_company: 1,
                    is_company_notified: 0,
                    exp_finish_date: vouchers[j].exp_finish_date,
                    exp_work_area_line_id: vouchers[j].line_id,
                    exp_work_area_group_id: vouchers[j].voucher_group_id,
                    next_work_area_id: vouchers[j].next_work_area_id,
                    prev_work_area_id: vouchers[j].prev_work_area_id,
                    work_inventory_location_id: vouchers[j].bin_location_id,
                    work_area_ticket_status_id: vouchers[j].ticket_status_id,
                  },
                ],
              });

              await WorkActivityLog.query().insert({
                module_name: 'ticket',
                module_id: voucher.workVouchersWorkTickets[0].id,
                activity: 'TicketCreated',
                data: JSON.stringify({ cloned_from: voucher.voucher_id }),
              });

              return voucher;
            }
          );

          // añadir al grupo
          const searchLastOrderNumber = await WorkVouchers.query()
            .where('work_vouchers.work_voucher_group_id', newVoucherGroup.id)
            .select('work_voucher_group_sort')
            .orderBy('work_voucher_group_sort', 'DESC')
            .limit(1);

          const updateVoucher = await WorkVouchers.query()
            .update({
              work_voucher_group_id: newVoucherGroup.id,
              work_voucher_group_sort:
                +searchLastOrderNumber[0]?.work_voucher_group_sort + 1 || 1,
            })
            .where('work_vouchers.id', newVouchers.id);

          if (updateVoucher) {
            await WorkActivityLog.query().insert({
              work_voucher_id: newVouchers.id,
              work_area_id: workAreaId,
              employee_id: codeEmployee,
              module_name: 'voucherGroup',
              module_id: newVoucherGroup.id,
              activity: 'AddedVoucherToGroup',
              data: JSON.stringify({ value: newVouchers.id }),
            });
          }
        }
      }
    }

    return res.status(200).json({ ok: true, message: name });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ ok: false });
  }
}

export async function getAllVouchersByMo(req: Request, res: Response) {
  const { moId, workAreaId } = req.query as {
    moId: string;
    workAreaId: string;
  };

  try {
    const vouchers = await WorkVouchers.query()
      .join(
        'work_area_tickets',
        'work_vouchers.id',
        'work_area_tickets.work_voucher_id'
      )
      .join(
        'work_areas',
        'work_area_tickets.work_area_id',
        'work_areas.work_area_id'
      )
      .join(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .join(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id',
        'work_area_ticket_statuses.id'
      )
      .join(
        'work_statuses',
        'work_area_ticket_statuses.work_status_id',
        'work_statuses.id'
      )
      .leftJoin(
        raw(`work_areas next_work_area ON
          next_work_area.work_area_id =
          work_area_tickets.next_work_area_id`)
      )
      .where('work_vouchers.mo_id', moId)
      .where('work_area_tickets.work_area_id', '<>', workAreaId)
      .where('work_vouchers.ignore_next_area', false)
      .where('work_statuses.id', '<>', '110')
      .select([
        'work_vouchers.id',
        { ticketId: 'work_area_tickets.id' },
        { status: 'work_area_ticket_statuses.name' },
        { type: 'work_voucher_types.name' },
        { typeId: 'work_vouchers.work_voucher_type_id' },
        { moId: 'work_vouchers.mo_id' },
        { area: 'work_areas.area_name' },
        { areaId: 'work_area_tickets.work_area_id' },
        { nextArea: 'next_work_area.area_name' },
        {
          globalStatusId: 'work_area_ticket_statuses.work_status_id',
        },
      ]);

    if (vouchers.length === 0) {
      return res
        .status(400)
        .json({ ok: false, message: 'No se encontraron vouchers para la MO' });
    }

    return res
      .status(200)
      .json({ ok: true, data: vouchers, totalItems: vouchers.length });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ ok: false });
  }
}
