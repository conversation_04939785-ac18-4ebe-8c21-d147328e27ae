import type { ModelObject, Raw } from 'objection';

import { Model } from '@app/db';

export const enum BraidOrderJobStatus {
  PENDING = 'pending',
  PARTIAL = 'partial',
  PRODUCING = 'producing',
  COMPLETED = 'completed',
  VOID = 'void',
}

export class BraidOrder extends Model {
  static tableName = 'braid_orders';
  static idColumn = 'id';

  id!: number;
  braid_num!: string;
  parent_mo_id!: number;
  required_date!: string;
  pattern!: string;
  colors!: string;
  yards!: number;
  size!: number;
  created_at!: string;
  updated_at!: string;
  fabric!: string;
}
export type BraidOrderShape = ModelObject<BraidOrder>;

export class BraidOrderJob extends Model {
  static tableName = 'braid_order_jobs';
  static idColumn = 'id';

  id!: number;
  braid_order_id!: number;
  created_at!: string;
  updated_at!: string;
  required_yards!: number;
  is_repo!: boolean;
  status!: BraidOrderJobStatus;
  completed_at!: string | Raw;
  removed_at!: string | Raw;
}
export type BraidOrderJobShape = ModelObject<BraidOrderJob>;

export class BraidOrderJobProduction extends Model {
  static tableName = 'braid_order_job_productions';
  static idColumn = 'id';

  id!: number;
  braid_order_job_id!: number;
  employee_id!: number;
  created_at!: string;
  updated_at!: string;
  machine_id!: number;
  yards!: number;
  marked_bad_at!: boolean;
  marked_bad_reason!: string;
  started_at!: string;
  finished_at!: string;
  braid_inventory_item_id!: number;
  use_inventory!: boolean;
  split_from_production_id!: number;
  goal_difference!: number;
  removed_at!: string;
}
export type BraidOrderJobProductionShape = ModelObject<BraidOrderJobProduction>;

export class BraidInventoryItem extends Model {
  static tableName = 'braid_inventory_items';
  static idColumn = 'id';

  id!: number;
  created_at!: string;
  updated_at!: string;
  pattern!: string;
  colors!: string;
  size!: number;
  fabric!: string;
  yards!: number;
  from_braid_order_job_production_id!: number;
  location!: string;
  notes!: string;
}
export type BraidInventoryItemShape = ModelObject<BraidInventoryItem>;

export class BraidInventoryItemAdjustment extends Model {
  static tableName = 'braid_inventory_item_adjustments';
  static idColumn = 'id';

  id!: number;
  braid_inventory_item_id!: number;
  created_at!: string;
  updated_at!: string;
  employee_id!: number;
  yards!: number;
  note!: string;
  braid_order_job_production_id!: number;
}

export class BraidPattern extends Model {
  static tableName = 'braid_patterns';
  static idColumn = 'id';

  id!: number;
  created_at!: string;
  updated_at!: string;
  pattern!: string;
  color_count!: number;
}
export type BraidPatternShape = ModelObject<BraidPattern>;

export class BraidColor extends Model {
  static tableName = 'braid_colors';
  static idColumn = 'id';

  id!: number;
  created_at!: string;
  updated_at!: string;
  name!: string;
}
export type BraidColorShape = ModelObject<BraidColor>;

export class BraidFabric extends Model {
  static tableName = 'braid_fabrics';
  static idColumn = 'id';

  id!: number;
  created_at!: string;
  updated_at!: string;
  name!: string;
}
export type BraidFabricShape = ModelObject<BraidFabric>;
