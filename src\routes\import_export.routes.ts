import { Router } from 'express';

import {
  getImportExportMoData,
  getMoListPage,
  orderDetailsMosCondensedShippingFile,
  orderDetailsMosFile,
  orderDetailsMosShippingFile,
} from '@app/controllers/import_export.controller';
import { employeeMiddleware } from '@app/controllers/plotter.controller';

export const importExportRouter = Router();

importExportRouter.route('/mos').get(getMoListPage);
importExportRouter.route('/orderDetailsMos').get(orderDetailsMosFile);
importExportRouter
  .route('/orderDetailsMosShipping')
  .get(orderDetailsMosShippingFile);
importExportRouter
  .route('/orderDetailMosCondensed')
  .get(orderDetailsMosCondensedShippingFile);

importExportRouter.use(employeeMiddleware);

importExportRouter.route('/moExport').post(getImportExportMoData);
