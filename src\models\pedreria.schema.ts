import type { ModelObject } from 'objection';

import { Model } from '@app/db';

export class ProductionType extends Model {
  static get tableName(): string {
    return 'production_types';
  }
}

export class MoNumber extends Model {
  static get tableName(): string {
    return 'mo_numbers';
  }

  static get idColumn() {
    return 'mo_id';
  }

  mo_id!: number; //": 1172380,
  poly_manufacture_id!: number; //": 274683,
  customer!: string; //": "JL",
  customers!: string; //": null,
  mo_order!: string; //": "",
  mo_order_item_num!: string; //": null,
  mo_order_parent_mo!: string; //": null,
  mo_status!: string; //": "Released",
  num!: string; //": "244570",
  required_date!: string; //": null,
  scheduled_xfac_date!: string; //": null,
  plan_xfac_date!: string; //": null,
  create_date!: string; //": "2023-02-05T05:00:00.000Z",
  finish_date!: string; //": null,
  style!: string; //": "JLMUN210",
  internal_style_id!: number; //": null,
  style_category!: string; //": "UNISUIT",
  product_category!: string; //": "ROWING",
  quantity!: number; //": 7,
  comment!: string; //": null,
  created_at!: string; //": "2023-02-06T14:00:21.000Z",
  updated_at!: string; //": "2023-02-06T19:59:03.000Z",
  po_number!: string; //": "",
  running_task!: string; //": "",
  mo_barcode!: string; //": "PPMO1274683",
  order_date!: string; //": null,
  order_required_date!: string; //": "2023-03-03T05:00:00.000Z",
  style_id!: number; //": 15522,
  order_status!: string; //": "",
  material_date!: string; //": "2023-02-05T05:00:00.000Z",
  company_code!: number; //": 1,
  ItemDescription8!: string; //": "136373",
  retail_po!: string; //": "",
  sched_start!: string; //": null,
  material_comments!: string; //": "",
  withdraw_comments!: string; //": null,
  order_ship_date!: string; //": null,
  order_numbers!: string; //": "118507, 118509, 118511, 118512, 118517, 118519, 118520",
  status_change_date!: string; //": "2023-02-05T05:00:00.000Z",
  production_status!: string; //": "",
  retailer_po_numbers!: string; //": "P36827 - For Stars Winter 2023",
  po_numbers!: string; //": "P36827-1, P36827-10, P36827-13, P36827-5, P36827-6, P36827-7, P36827-9",
  ItemDescription14!: string; //": "",
  voucher_ready!: string; //": null,
  order_type!: string; //": "New",
  original_required_date!: string; //": null,
  order_type_name!: string; //": null,
  order_type_2!: string; //": null,
  order_type_3!: string; //": null,
  cut_date!: string; //": null,
  missing_on_sync!: number; //": 1,
  plan_sew_line!: string; //": null,
  plan_sew_manager!: string; //": null,
  plan_sew_location!: string; //": null,
  plan_notes!: string; //": null,
  plan_new_xfac_text!: string; //": null,
  plan_proceso!: string; //": null,
  plan_comment!: string; //": null,
  plan_tipo!: string; //": null,
  plan_corte_mesa!: string; //": null,
  plan_sublimado_comment!: string; //": null,
  plan_decoration_comment!: string; //": null,
  plan_status_comment!: string; //": null,
  plan_leadtime_comment!: string; //": null
}

export class MoSize extends Model {
  static get tableName(): string {
    return 'mo_sizes';
  }

  static get idColumn() {
    return 'mo_size_id';
  }

  mo_size_id!: number;
  mo_id!: number;
  poly_manufacture_detail_id!: number;
  size_name!: string;
  alt_size_name!: string;
  size_quantity!: number;
  number_detail!: number;
  style_option5!: string;
  style_option6!: string;
  style_option7!: string;
  style_option16!: string;
  style_option12!: string;
  manufacture_id!: number;
  company_code!: number;
  style_option9!: string;
  style_option25!: string;
}

export type MoNumberShape = ModelObject<MoNumber>;
