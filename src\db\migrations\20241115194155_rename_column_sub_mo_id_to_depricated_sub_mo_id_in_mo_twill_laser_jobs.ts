import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'mo_twill_laser_jobs',
    (table: Knex.TableBuilder): void => {
      table.renameColumn('sub_mo_id', 'depricated_sub_mo_id');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'mo_twill_laser_jobs',
    (table: Knex.TableBuilder): void => {
      table.renameColumn('depricated_sub_mo_id', 'sub_mo_id');
    }
  );
}
