import type { Request, Response } from 'express';
import fs from 'fs';

import { parseForm } from '@app/helpers/promisifys';
import { readSaveReportCprFile } from '@app/services/varsityCPR/varsityCPRpdf';

export const getVarsityCPRFile = async (req: Request, res: Response) => {
  try {
    const [fields, files] = await parseForm(req);
    // console.log('fields', fields);
    // console.log('files', files);
    const filenames = Object.keys(files);
    const force = fields?.force === 'true' ? true : false;
    let showPages: number[] | undefined = undefined;
    if (fields?.showPages) {
      if (typeof fields.showPages === 'string') {
        showPages = fields.showPages.split(',').map((p) => +p);
      }
      if (Array.isArray(fields.showPages)) {
        showPages = fields.showPages.map((p) => +p);
      }
    }

    if (filenames.length === 0) {
      throw Error('No files uploaded');
    }
    if (filenames.length > 1) {
      throw Error('Only one file allowed');
    }
    const fileName = filenames[0];
    const file = files[fileName];
    // console.log('got file named ' + fileName);
    if (Array.isArray(file)) {
      throw Error('File in file is not allowed');
    }
    console.log(file.filepath, file.newFilename, file.originalFilename);
    const [cprWorkbook, filenameToSave] = await readSaveReportCprFile(file, {
      force,
      showPages,
    });
    // console.log('form cpr read done');

    // removes file
    fs.unlinkSync(file.filepath);
    // console.log('form cpr file removed');

    try {
      cprWorkbook.write(filenameToSave, res);
    } catch (e) {
      throw Error('Error writing excel file');
    }
  } catch (e) {
    console.log('varsity cpr error', e);
    res.status(400).send(e.message);
    return;
  }
};
