import { PlotterPrint } from '@app/models/plotter.schema';
import { WorkAreaGroups } from '@app/models/tickets.schema';

// Uses plotter apps data as base
export const scanPlotterPrintWithGroup = async (
  plotter_print_id: number | string,
  work_area_group_id: number | string
) => {
  const plotterPrint = await PlotterPrint.query().findById(plotter_print_id);
  if (!plotterPrint) {
    return null;
  }

  const cuttingGroup = await WorkAreaGroups.query().findById(
    work_area_group_id
  );
  if (!cuttingGroup) {
    return null;
  }

  if (cuttingGroup.work_area_type === 'CUT') {
    if (plotterPrint.cut_at !== null) {
      throw new Error('Plotter print already cut');
    }
    plotterPrint.cut_at = new Date();
    plotterPrint.cut_work_area_group_id = cuttingGroup.id;
    plotterPrint.cut_scanned = true;
    plotterPrint.$query().patch(plotterPrint);

    return plotterPrint;
  }

  if (cuttingGroup.work_area_type === 'SPREAD') {
    if (plotterPrint.spread_at !== null) {
      throw new Error('Plotter print already spread');
    }
    plotterPrint.spread_at = new Date();
    plotterPrint.spread_work_area_group_id = cuttingGroup.id;
    plotterPrint.spread_scanned = true;
    plotterPrint.$query().patch(plotterPrint);

    return plotterPrint;
  }

  throw new Error('Invalid work area type');
};
