import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('rhinestones_orders', (table): void => {
    table.timestamp('receiving_date').notNullable().defaultTo(knex.fn.now());
    table.tinyint('is_repo').nullable().defaultTo(0);
    table.tinyint('is_active').nullable().defaultTo(1);
  });

  await knex.schema.createTable('rhinestones_areas', (table): void => {
    table.increments('id').primary();
    table.string('name', 150).notNullable().unique();
    table.tinyint('is_active').nullable().defaultTo(1);
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
  });

  await knex.schema.createTable('rhinestones_users', (table): void => {
    table.increments('id').primary();
    table.integer('employee_id').notNullable();
    table.integer('rhinestones_area_id', 10).notNullable().unsigned();
    table.string('password', 150).notNullable();
    table.tinyint('is_active').nullable().defaultTo(1);
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    table
      .foreign('rhinestones_area_id')
      .references('id')
      .inTable('rhinestones_areas');
    table.foreign('employee_id').references('employee_id').inTable('employees');
  });

  await knex.schema.alterTable('rhinestones_sizes', (table): void => {
    table.string('name', 100).notNullable();
    table.string('description', 150).notNullable();
    table.tinyint('is_active').nullable().defaultTo(1);
  });

  await knex.schema.alterTable('rhinestones_colors', (table): void => {
    table.string('name', 100).notNullable();
    table.string('description', 150).notNullable();
    table.tinyint('is_active').nullable().defaultTo(1);
  });

  await knex.schema.createTable('rhinestones_providers', (table): void => {
    table.increments('id').primary();
    table.string('name', 150).notNullable();
    table.string('short_name', 50).notNullable();
    table.string('material_code', 20).notNullable();
    table.tinyint('is_active').nullable().defaultTo(1);
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
  });

  await knex.schema.createTable(
    'rhinestones_sizes_providers',
    (table): void => {
      table.increments('id').primary();
      table.integer('rhinestones_provider_id', 10).notNullable().unsigned();
      table.integer('rhinestones_size_id').notNullable().unsigned();
      table.tinyint('is_active').nullable().defaultTo(1);
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').defaultTo(knex.fn.now());
      table
        .foreign('rhinestones_provider_id')
        .references('id')
        .inTable('rhinestones_providers');
      table
        .foreign('rhinestones_size_id')
        .references('id')
        .inTable('rhinestones_sizes');
    }
  );

  await knex.schema.createTable(
    'rhinestones_colors_providers',
    (table): void => {
      table.increments('id').primary();
      table.integer('rhinestones_provider_id', 10).notNullable().unsigned();
      table.integer('rhinestones_color_id').notNullable().unsigned();
      table.tinyint('is_active').nullable().defaultTo(1);
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').defaultTo(knex.fn.now());
      table
        .foreign('rhinestones_provider_id')
        .references('id')
        .inTable('rhinestones_providers');
      table
        .foreign('rhinestones_color_id')
        .references('id')
        .inTable('rhinestones_colors');
    }
  );

  await knex.schema.createTable('rhinestones_ranges', (table): void => {
    table.increments('id').primary();
    table.string('name', 150).notNullable().unique();
    table.tinyint('is_active').nullable().defaultTo(1);
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
  });

  await knex.schema.createTable(
    'rhinestones_ranges_per_order',
    (table): void => {
      table.increments('id').primary();
      table.integer('range_id', 10).notNullable().unsigned();
      table.integer('order_id').notNullable().unsigned();
      table.integer('quantity').notNullable().defaultTo(1);
      table.integer('art_per_sheet').notNullable().defaultTo(1);
      table.integer('cute').notNullable().defaultTo(1);
      table.string('description', 150).nullable();
      table.string('file_url', 150).nullable();
      table.tinyint('is_active').nullable().defaultTo(1);
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').defaultTo(knex.fn.now());
      table.foreign('range_id').references('id').inTable('rhinestones_ranges');
      table.foreign('order_id').references('id').inTable('rhinestones_orders');
    }
  );

  await knex.schema.alterTable('rhinestones_order_items', (table): void => {
    table.tinyint('is_active').nullable().defaultTo(1);
    table.integer('original_quantity').nullable().defaultTo(null);
    table.integer('range_per_order_id').nullable().defaultTo(null);
  });

  await knex.schema.createTable('rhinestones_logs', (table): void => {
    table.increments('id').primary();
    table.integer('rhinestones_area_id', 10).notNullable().unsigned();
    table.integer('employee_id').notNullable();
    table.string('action', 150).notNullable();
    table.integer('module_id').notNullable();
    table.string('module_name', 150).notNullable();
    table.text('data').notNullable();
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    table
      .foreign('rhinestones_area_id')
      .references('id')
      .inTable('rhinestones_areas');
    table.foreign('employee_id').references('employee_id').inTable('employees');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('rhinestones_orders', (table): void => {
    table.dropColumn('receiving_date');
    table.dropColumn('is_repo');
    table.dropColumn('is_active');
  });

  await knex.schema.dropTableIfExists('rhinestones_areas');
  await knex.schema.dropTableIfExists('rhinestones_users');
  await knex.schema.alterTable('rhinestones_sizes', (table): void => {
    table.dropColumn('name');
    table.dropColumn('description');
    table.dropColumn('is_active');
  });

  await knex.schema.alterTable('rhinestones_colors', (table): void => {
    table.dropColumn('name');
    table.dropColumn('description');
    table.dropColumn('is_active');
  });

  await knex.schema.dropTableIfExists('rhinestones_providers');
  await knex.schema.dropTableIfExists('rhinestones_sizes_providers');
  await knex.schema.dropTableIfExists('rhinestones_colors_providers');
  await knex.schema.dropTableIfExists('rhinestones_ranges');
  await knex.schema.dropTableIfExists('rhinestones_ranges_per_order');
  await knex.schema.alterTable('rhinestones_order_items', (table): void => {
    table.dropColumn('is_active');
    table.dropColumn('original_quantity');
    table.dropColumn('range_per_order_id');
  });

  await knex.schema.dropTableIfExists('rhinestones_logs');
}
