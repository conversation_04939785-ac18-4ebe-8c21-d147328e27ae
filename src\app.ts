// Importacion de librerías express, morgan
import cors from 'cors';
import express, {
  type Application,
  json,
  static as staticExpress,
  urlencoded,
} from 'express';
import morgan from 'morgan';
import path from 'path';
import favicon from 'serve-favicon';

// Importación de las rutas
import { auth } from './routes/auth.routes';
import { barcodeRouter } from './routes/barcode.routes';
import { stickersRouter } from './routes/barcode_stickers.routes';
import { batchRouter } from './routes/batch.routes';
import { braidRouter } from './routes/braid.routes';
import { calidadRouter } from './routes/calidad.routes';
import { employeesRouter } from './routes/employee.routes';
import { fragmentsRouter } from './routes/fragment.routes';
import { fragmentGroupsRouter } from './routes/fragments_group.routes';
import { importExportRouter } from './routes/import_export.routes';
import { indexRouter } from './routes/index.routes';
import { infoRouter } from './routes/info.routes';
import { laserOperatorRouter } from './routes/laser_operator.routes';
import { machinesRouter } from './routes/machine.routes';
import { mailerRouter } from './routes/mailer.routes';
import { vouchersRouter } from './routes/monumbers.routes';
import { ordersRouter } from './routes/pedreria.routes';
import { pickpackRouter } from './routes/pickpack.routes';
import { planningRouter } from './routes/planning.routes';
import { plotterRouter } from './routes/plotter.routes';
import { printRouter } from './routes/printer.routes';
import { qrcodeRouter } from './routes/qrcode.routes';
import { repairPartsRouter } from './routes/repair_parts.routes';
import { reposRouter } from './routes/repo.routes';
import { reportsRouter } from './routes/reports.routes';
import { rfidRouter } from './routes/rfid.routes';
import { rhinestonesRouter } from './routes/rhinestone.routes';
import { scansRouter } from './routes/scans.routes';
import { sewingPlanningRouter } from './routes/sewingPlanning.routes';
import { shiftsRouter } from './routes/shift.routes';
import { smithRouter } from './routes/smith.routes';
import { styleRouter } from './routes/style.routes';
import { ticketsRouter } from './routes/tickets.routes';
import { laserRouter } from './routes/twill-laser';
import { varsityCprRouter } from './routes/varsitycpr.routes';
import { warehousesRouter } from './routes/warehouse.routes';
import { warehousePullRouter } from './routes/warehouse_pull.routes';
import { buildLogger } from './settings';

const logger = buildLogger('app.ts');

// Creación de la clase App
export class App {
  // Propiedades de la clase App
  private app: Application;

  // Método constructor de la clase, la propiedad port es opcional
  constructor(private port?: number | string) {
    // Ejecutamos express
    this.app = express();
    // Ejecución de métodos
    this.settings();
    this.middlewares();
    this.routes();
  }

  // Establecemos la configuración del puerto para nuestro servidor
  private settings(): void {
    this.app.set('port', this.port || process.env.PORT || 3000);
  }

  private middlewares(): void {
    // Para poder mostrar por consola mensajes de desarrollo
    this.app.use(morgan('dev'));

    // Para poder recibir JSON desde una REQ
    this.app.use(urlencoded({ extended: true }));
    this.app.use(json({ limit: '50mb' }));

    // Carga del directorio público
    this.app.use('/pages', staticExpress(path.resolve(__dirname, '../public')));

    // ICO de la página
    this.app.use(favicon(path.join(__dirname, '../public/ico', 'js.ico')));
    // Comunicación entre servidores
    this.app.use(
      cors({
        exposedHeaders: ['Content-Disposition'],
      })
    );
  }

  // Establecemos el acceso a las rutas
  private routes(): void {
    // Ruta principal
    this.app.use(indexRouter);
    this.app.use(infoRouter);
    // Ruta de voucher
    this.app.use('/voucher', vouchersRouter);
    // Ruta de warehouse
    this.app.use('/warehouse', warehousesRouter);
    // Ruta de calidad
    this.app.use('/calidad', calidadRouter);
    // Ruta de tickets
    this.app.use('/tickets', ticketsRouter);
    // Ruta de Sew Rfid
    this.app.use('/rfid', rfidRouter);
    // Ruta de pedreria
    this.app.use('/pedreria', ordersRouter);
    // Ruta de scans
    this.app.use('/scans', scansRouter);
    // Ruta de employee
    this.app.use('/employee', employeesRouter);
    // Ruta de maquinaria
    this.app.use('/machines', machinesRouter);

    this.app.use('/repos', reposRouter);

    this.app.use('/mailer', mailerRouter);

    this.app.use('/reports', reportsRouter);

    this.app.use('/shift', shiftsRouter);
    this.app.use('/fragments', fragmentsRouter);

    // deprecated
    this.app.use('/qrcode', qrcodeRouter);

    this.app.use('/barcode', barcodeRouter);

    this.app.use('/pickpack', pickpackRouter);
    this.app.use('/fragments-group', fragmentGroupsRouter);
    this.app.use('/batch', batchRouter);
    this.app.use('/varsitycpr', varsityCprRouter);
    this.app.use('/rhinestones', rhinestonesRouter);
    this.app.use('/auth', auth);
    this.app.use('/plotter', plotterRouter);
    this.app.use('/print', printRouter);
    this.app.use('/smith', smithRouter);
    this.app.use('/barcode-stickers', stickersRouter);

    this.app.use('/importexport', importExportRouter);

    this.app.use('/planning', planningRouter);
    this.app.use('/sewing-planning', sewingPlanningRouter);
    this.app.use('/twill-laser', laserRouter);
    this.app.use('/style', styleRouter);
    this.app.use('/twill-laser-operators', laserOperatorRouter);
    this.app.use('/braid', braidRouter);
    this.app.use('/warehouse-pull', warehousePullRouter);
    this.app.use('/repair-parts', repairPartsRouter);
  }

  async listen(): Promise<string> {
    // Obtenemos y establecemos la configuración del puerto para el servidor
    this.app.listen(this.app.get('port'));

    logger.log(`Server on port ${this.app.get('port')}`);

    return `Server on port ${this.app.get('port')}`;
  }
}
