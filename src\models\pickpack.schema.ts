import type { ModelObject } from 'objection';

import { Model } from '@app/db';

export class GoodsAllocations extends Model {
  static get tableName(): string {
    return 'good_allocations';
  }

  static get idColumn() {
    return 'good_allocation_id';
  }

  good_allocation_id!: number;
  ItemDescription7_!: string;
  UPCNumber2_!: string;
  ItemDescription_!: string;
  ItemDescription3_!: string;
  ShipToName2_!: string;
  CustomerNumber!: string;
  DetailSpec4_!: string;
  DetailSpec3_!: string;
  DetailSpec2_!: string;
  DetailDescription_!: string;
  DivisionName_!: string;
  ItemNumber!: number;
  OrderNumber!: number;
  OrderDetailsID!: number;
  QuantityAllocated!: number;
  GarmentSize!: string;
  GarmentSize1!: string;
  GarmentSize2!: string;
  ItemDescription8_!: string;
  PONumber_!: string;
  RetailerPONumber_!: string;
  ManufactureID!: number;
  ManufactureNumber!: string;
  ManufactureBarcode_!: string;
  StyleNumber!: string;
  StyleName!: string;
  SKUNumber_!: string;
  StyleCategoryName_!: string;
  StyleOption9_!: string;
  StyleDescription2_!: string;
  StyleColor!: string;
  ItemDescription14_!: string;
  StyleOption2_!: string;
  CancelDate_!: string;
  Inventory_to_SO_Allocated!: number;
  MO_to_SO_Allocated!: number;
  Last_MO_Used!: string;
  OrderStatusReal_!: string;
  MfgStatus_!: string;
}

export class PickPackItem extends Model {
  static tableName = 'work_pickpack_items';

  id!: string;
  created_at!: Date;
  updated_at!: Date;
  customer!: string;
  style!: string;
  division!: string;
  size!: string;
  color!: string;
  design!: string;
  barcode!: string;
}

export class PickPackBinTypes extends Model {
  static tableName = 'work_pickpack_bin_types';

  id!: number;
  created_at!: Date;
  updated_at!: Date;
  name!: string;
}

export class PickPackBin extends Model {
  static tableName = 'work_pickpack_bins';

  static relationMappings = {
    binType: {
      relation: Model.BelongsToOneRelation,
      modelClass: PickPackBinTypes,
      join: {
        // persons_movies is the join table.
        from: 'work_pickpack_bins.work_pickpack_bin_type_id',
        to: 'work_pickpack_bin_types.id',
      },
    },
  };

  id!: number;
  name!: string;
  created_at!: Date;
  updated_at!: Date;
  is_container!: boolean;
  allow_items!: boolean;
  is_pickable!: boolean;
  sort_station_id: number | null;
  order_number_assigned: string | null;
  work_pickpack_bin_type_id!: number | null;
  container_bin_location_id!: number | null;
  container_parent_id!: number | null;
  container_top_parent_id!: number | null;
  container_tree_array!: string[] | null;
}

export type PickPackBinShape = ModelObject<PickPackBin>;

export class PickPackBinContent extends Model {
  static get tableName(): string {
    return 'work_pickpack_bin_contents';
  }

  created_at!: Date;
  updated_at!: Date;

  id!: number; // increments
  work_pickpack_bin_id!: number; // integer
  work_pickpack_item_id!: number; // integer
  quantity!: number; // integer
  order_number!: string | null; // string
  order_item_number!: number | null; // integer
  mo_id!: number | null; // integer
  good_allocation_id!: number | null; // integer
  rosters!: any[] | null; // jsonb

  static relationMappings = {
    bin: {
      relation: Model.BelongsToOneRelation,
      modelClass: PickPackBin,
      join: {
        // persons_movies is the join table.
        from: 'work_pickpack_bin_contents.work_pickpack_bin_id',
        to: 'work_pickpack_bins.id',
      },
    },
  };
}

export class PickPackStation extends Model {
  static tableName = 'work_pickpack_stations';

  id!: number;
  created_at!: Date;
  updated_at!: Date;
  name!: string;
}

export type PickPackStationShape = ModelObject<PickPackStation>;

export class PickPackStationSort extends Model {
  static tableName = 'work_pickpack_station_sorts';

  id!: number;
  created_at!: Date;
  updated_at!: Date;
  work_pickpack_station_id!: number;
  work_pickpack_bin_id!: number;
}

export class PickPackMoPick extends Model {
  static tableName = 'work_pickpack_mo_pick';

  id!: number;
  created_at!: Date;
  updated_at!: Date;
  mo_id!: number;
}

export class PickPackMoPickItem extends Model {
  static tableName = 'work_pickpack_mo_pick_items';

  id!: number;
  created_at!: Date;
  updated_at!: Date;
  work_pickpack_mo_pick_id!: number;
  work_pickpack_item_id!: number;
  quantity!: number;
}
