import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('mo_invoices', (table: Knex.TableBuilder) => {
    table.string('accepted_format_type', 128);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('mo_invoices', (table: Knex.TableBuilder) => {
    table.dropColumn('accepted_format_type');
  });
}
