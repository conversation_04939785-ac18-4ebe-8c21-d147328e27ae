import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('plotter_rolls', (table: Knex.TableBuilder) => {
    table.integer('recipient_employee_id').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('plotter_rolls', (table: Knex.TableBuilder) => {
    table.dropColumn('recipient_employee_id');
  });
}
