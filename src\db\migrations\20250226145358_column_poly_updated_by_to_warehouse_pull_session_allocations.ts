import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'warehouse_pull_session_allocations',
    (table): void => {
      table.integer('poly_updated_by', 11).nullable().after('poly_updated_at');
      table
        .foreign('poly_updated_by', 'pub_employees_fk')
        .references('employee_id')
        .inTable('employees');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'warehouse_pull_session_allocations',
    (table): void => {
      table.dropForeign('poly_updated_by', 'pub_employees_fk');
      table.dropColumn('poly_updated_by');
    }
  );
}
