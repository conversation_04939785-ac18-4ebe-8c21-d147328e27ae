import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'warehouse_pull_session_order_parts',
    (table): void => {
      table
        .float('quantity_rounded', 10, 2)
        .notNullable()
        .defaultTo(0)
        .after('quantity_bom');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'warehouse_pull_session_order_parts',
    (table): void => {
      table.dropColumn('quantity_rounded');
    }
  );
}
