import dayjs from 'dayjs';
import type { Request, Response } from 'express';
import { ref } from 'objection';

import type { Machine } from '@app/models/machines.schema';
import { MoNumber } from '@app/models/tickets.schema';
import {
  MoTwillLaserDecoration,
  MoTwillLaserJob,
  MoTwillLaserJobConsumption,
  MoTwillLaserJobDecoration,
  MoTwillLaserJobProduction,
  MoTwillLaserJobStatus,
} from '@app/models/twill-laser';
import {
  newDecoration,
  newJob,
  newOperatorConsumption,
} from '@app/services/laser_operator.service';
import { buildLogger } from '@app/settings';

const logger = buildLogger('rhinestone.controller.ts');

export async function createLaserTwillJob(req: Request, res: Response) {
  try {
    const {
      clientID,
      comment,
      employeeID,
      isRepo,
      layers,
      moID,
      moTwillLaserJobStatusID = 3,
      quantity,
      specialLayers,
      consumptions,
      decorations,
      activeDecorations = [],
    } = req.body as unknown as {
      clientID: number;
      comment?: string;
      employeeID: number;
      isRepo?: boolean;
      layers: number;
      moID: number;
      moTwillLaserJobStatusID?: number;
      quantity?: number;
      specialLayers?: number;
      consumptions: {
        height: number;
        width: number;
        quantity: number;
      }[];
      decorations: {
        typeID: number;
        description: string;
      }[];
      activeDecorations?: number[];
    };

    let mainID: number | null = null;
    let quantityOrder = 0;
    let decoration: MoTwillLaserDecoration | null = null;

    if (decorations.length === 0 && activeDecorations.length === 0)
      throw new Error('Debes de agregar al menos una decoración');

    if (consumptions.length === 0)
      throw new Error('Para crear un trabajo, se requiere consumo');

    if (!clientID || !employeeID || !moID || !layers)
      throw new Error('Todos los datos son requeridos');

    const order = await MoNumber.query()
      .where('mo_id', moID)
      .select('quantity')
      .first()
      .castTo<{
        quantity: number;
      }>();

    if (!order) throw new Error('No se encontro la orden');

    quantityOrder = order.quantity;

    if (Number(clientID) === 3) {
      // TODO: Duplicate grab from pevious query??
      const main = await MoNumber.query().where('mo_id', moID).first();

      if (!main.parent_mo_id)
        throw new Error(
          'VARSITY, no se encontro la orden principal. Por favor, contacte con el administrador'
        );

      mainID = main.parent_mo_id;
    } else {
      mainID = null;
    }

    if (quantity > quantityOrder)
      throw new Error(
        'La cantidad solicitada es mayor a la cantidad de la orden'
      );

    const job = await newJob({
      moTwillLaserJobStatusID,
      comment,
      employeeID,
      isRepo,
      layers,
      moID: mainID || moID,
      quantity: quantity || quantityOrder,
      specialLayers,
    });

    if (!job) throw new Error('No se pudo crear el trabajo');

    if (decorations.length > 0) {
      for (const dataDecoration of decorations) {
        if (!dataDecoration.typeID || !dataDecoration.description)
          throw new Error(
            'El tipo de decoración y la descripción son requeridos'
          );

        if (Number(clientID) === 3) {
          const searchDecoration = await MoTwillLaserDecoration.query()
            .where('mo_id', mainID)
            .andWhere('child_mo_id', moID)
            .select('id')
            .first()
            .castTo<{ id: number }>();

          if (searchDecoration)
            throw new Error('Ya existe una decoración para la MO');

          decoration = await newDecoration({
            comment: dataDecoration.description,
            moID: mainID,
            subMoID: moID,
            typeID: dataDecoration.typeID,
          });
        } else {
          decoration = await newDecoration({
            comment: dataDecoration.description,
            moID,
            subMoID: null,
            typeID: dataDecoration.typeID,
          });
        }

        if (!decoration)
          throw new Error('No se pudo crear la decoración de la mo');

        const jobDecoration = await MoTwillLaserJobDecoration.query().insert({
          mo_twill_laser_job_id: job.id,
          mo_twill_laser_decoration_id: decoration.id,
        });

        if (!jobDecoration)
          throw new Error(
            'No se pudo crear la decoración del trabajo correspondiente'
          );
      }
    }

    if (activeDecorations.length > 0) {
      for (const activeDecoration of activeDecorations) {
        const jobDecoration = await MoTwillLaserJobDecoration.query().insert({
          mo_twill_laser_job_id: job.id,
          mo_twill_laser_decoration_id: activeDecoration,
        });

        if (!jobDecoration)
          throw new Error(
            'No se pudo crear la decoración del trabajo correspondiente'
          );
      }
    }

    if (consumptions.length > 0) {
      for (const consumption of consumptions) {
        const consumptionJob = await newOperatorConsumption({
          height: consumption.height,
          moTwillLaserJobID: job.id,
          quantity: consumption.quantity,
          width: consumption.width,
        });

        if (!consumptionJob)
          throw new Error('No se pudo crear el consumo del trabajo');
      }
    }

    logger.log(
      `Se ha creado los trabajos correctamente, por el empleado ${employeeID} para la orden ${moID}`
    );

    return res.status(201).json({
      ok: true,
      message: 'Se ha creado el trabajo correctamente',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }
  }
}

interface Jobs {
  id: number;
  layers: number;
  special_layers: number;
  comment: string;
  finished_at: string;
  quantity: string;
  is_repo: boolean;
  child_mo_id: number;
  consumptions: {
    id: number;
    height: number;
    width: number;
    quantity: number;
  }[];
  decorations: {
    id: number;
    description: string;
    voucher: string;
    name: string;
  }[];
}

export async function getLaserTwillJobs(req: Request, res: Response) {
  try {
    const { moID, childMoID, companyCode } = req.params as unknown as {
      moID: number;
      childMoID: number;
      companyCode: number;
    };
    const jobsResponse: Jobs[] = [];

    let moDecorations: {
      id: number;
      child_mo_id: number;
      description: string;
      typeID: number;
      name: string;
    }[];

    const jobs = await MoTwillLaserJob.query()
      .where('mo_twill_laser_jobs.mo_id', moID)
      .where('mo_twill_laser_jobs.is_active', 1)
      .select([
        {
          id: 'mo_twill_laser_jobs.id',
        },
        {
          layers: 'mo_twill_laser_jobs.layers',
        },
        {
          special_layers: 'mo_twill_laser_jobs.special_layers',
        },
        {
          comment: 'mo_twill_laser_jobs.comment',
        },
        {
          finished_at: 'mo_twill_laser_jobs.finished_at',
        },
        {
          quantity: 'mo_twill_laser_jobs.quantity',
        },
        {
          isRepo: 'mo_twill_laser_jobs.is_repo',
        },
      ])
      .castTo<
        {
          comment: string;
          finished_at: string;
          id: number;
          isRepo: boolean;
          layers: number;
          quantity: number;
          special_layers: number;
        }[]
      >();

    if (+companyCode === 3) {
      moDecorations = await MoTwillLaserDecoration.query()
        .join(
          'mo_numbers',
          'mo_twill_laser_decorations.mo_id',
          'mo_numbers.mo_id'
        )
        .join(
          'mo_twill_laser_job_types',
          'mo_twill_laser_decorations.mo_twill_laser_job_type_id ',
          'mo_twill_laser_job_types.id'
        )
        .where('mo_twill_laser_decorations.child_mo_id', childMoID)
        .select([
          'mo_twill_laser_decorations.id',
          'mo_twill_laser_decorations.child_mo_id',
          { description: 'mo_twill_laser_decorations.comment' },
          { typeID: 'mo_twill_laser_job_types.id' },
          { name: 'mo_twill_laser_job_types.name' },
        ])
        .castTo<
          {
            id: number;
            child_mo_id: number;
            description: string;
            typeID: number;
            name: string;
          }[]
        >();
    } else {
      moDecorations = await MoTwillLaserDecoration.query()
        .join(
          'mo_numbers',
          'mo_twill_laser_decorations.mo_id',
          'mo_numbers.mo_id'
        )
        .join(
          'mo_twill_laser_job_types',
          'mo_twill_laser_decorations.mo_twill_laser_job_type_id ',
          'mo_twill_laser_job_types.id'
        )
        .where('mo_twill_laser_decorations.mo_id', moID)
        .select([
          'mo_twill_laser_decorations.id',
          'mo_twill_laser_decorations.child_mo_id',
          { description: 'mo_twill_laser_decorations.comment' },
          { typeID: 'mo_twill_laser_job_types.id' },
          { name: 'mo_twill_laser_job_types.name' },
        ])
        .castTo<
          {
            id: number;
            child_mo_id: number;
            description: string;
            typeID: number;
            name: string;
          }[]
        >();
    }

    for (const job of jobs) {
      const consumptions = await MoTwillLaserJobConsumption.query()
        .where('mo_twill_laser_job_consumptions.mo_twill_laser_job_id', job.id)
        .where('mo_twill_laser_job_consumptions.is_active', 1)
        .select([
          'mo_twill_laser_job_consumptions.id',
          'mo_twill_laser_job_consumptions.height',
          'mo_twill_laser_job_consumptions.width',
          'mo_twill_laser_job_consumptions.quantity',
        ])
        .castTo<
          {
            id: number;
            height: number;
            width: number;
            quantity: number;
          }[]
        >();

      if (!consumptions)
        throw new Error(`No se encontraron consumos de ${job.id}`);

      const productions = await MoTwillLaserJobProduction.query()
        .where('mo_twill_laser_job_id', job.id)
        .where('is_active', 1)
        .select([
          {
            id: 'mo_twill_laser_job_productions.id',
          },
          { quantity: 'mo_twill_laser_job_productions.quantity' },
        ])
        .castTo<
          {
            id: number;
            quantity: number;
            isRepo: boolean;
          }[]
        >();

      const sumPieces = productions
        .map((production) => production.quantity)
        .reduce((a, b) => a + b, 0);

      const decorations = await MoTwillLaserDecoration.query()
        .join(
          'mo_twill_laser_job_types',
          'mo_twill_laser_decorations.mo_twill_laser_job_type_id ',
          'mo_twill_laser_job_types.id'
        )
        .join(
          'mo_twill_laser_job_decorations',
          'mo_twill_laser_decorations.id',
          'mo_twill_laser_job_decorations.mo_twill_laser_decoration_id'
        )
        .where('mo_twill_laser_job_decorations.mo_twill_laser_job_id', job.id)
        .where('mo_twill_laser_decorations.is_active', 1)
        .where('mo_twill_laser_job_types.is_active', 1)
        .select([
          { id: 'mo_twill_laser_job_decorations.id' },
          { child_mo_id: 'mo_twill_laser_decorations.child_mo_id' },
          MoNumber.query()
            .select('mo_order_item_num')
            .where('mo_id', ref('mo_twill_laser_decorations.child_mo_id'))
            .first()
            .as('voucher'),
          { decorationID: 'mo_twill_laser_decorations.id' },
          { description: 'mo_twill_laser_decorations.comment' },
          { name: 'mo_twill_laser_job_types.name' },
        ])
        .castTo<
          {
            id: number;
            voucher: string;
            child_mo_id: number;
            decorationID: number;
            description: string;
            name: string;
          }[]
        >();

      const searchChildMo = decorations.find(
        (decoration) => +decoration.child_mo_id === +childMoID
      );

      jobsResponse.push({
        id: job.id,
        layers: job.layers,
        special_layers: job.special_layers,
        comment: job.comment,
        is_repo: job.isRepo,
        quantity: `${sumPieces}/${job.quantity}`,
        finished_at: job.finished_at,
        child_mo_id: (searchChildMo && searchChildMo.child_mo_id) || null,
        consumptions: consumptions || [],
        decorations: decorations || [],
      });
    }

    return res.status(200).json({
      ok: true,
      mesage: 'Trabajos encontrados',
      data: {
        jobs: jobsResponse,
        decorations: moDecorations,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function createJobProduction(req: Request, res: Response) {
  try {
    const { jobID } = req.params as unknown as { jobID: number };
    const { employeeID, machineID, quantity, comment } =
      req.body as unknown as {
        employeeID: number;
        machineID: number;
        quantity: number;
        comment: string;
      };

    if (!jobID) throw new Error('Falta el id del trabajo');
    if (!employeeID || !machineID || !quantity)
      throw new Error('Faltan los datos de empleado, maquina o cantidad');
    if (quantity <= 0) throw new Error('La producción debe ser mayor a 0');

    const totalPieces = await MoTwillLaserJob.query()
      .where('id', jobID)
      .where('is_active', 1)
      .select('quantity')
      .first()
      .castTo<{
        quantity: number;
      }>();

    if (!totalPieces) throw new Error('No se encontro el trabajo');

    const allProductions = await MoTwillLaserJobProduction.query()
      .where('mo_twill_laser_job_id', jobID)
      .where('is_active', 1)
      .select([
        {
          id: 'mo_twill_laser_job_productions.id',
        },
        {
          quantity: 'mo_twill_laser_job_productions.quantity',
        },
      ])
      .castTo<
        {
          id: number;
          quantity: number;
        }[]
      >();

    if (allProductions) {
      const sumPieces = allProductions
        .map((production) => production.quantity)
        .reduce((a, b) => a + b, 0);

      if (quantity > totalPieces.quantity - sumPieces)
        throw new Error('La cantidad no puede ser mayor a la disponible');
    }

    const production = await MoTwillLaserJobProduction.query().insert({
      mo_twill_laser_job_id: jobID,
      quantity,
      machine_id: machineID,
      employee_id: employeeID,
      comment,
    });

    if (!production.id) throw new Error('No se pudo registrar la producción');

    return res.status(201).json({
      ok: true,
      mesage: 'Producción creada',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }
  }

  logger.error('Error interno del servidor');

  return res.status(500).json({
    ok: false,
    message: 'Error interno del servidor',
  });
}

export async function getAllProductionsByJob(req: Request, res: Response) {
  try {
    const { jobID } = req.params as unknown as { jobID: number };

    if (!jobID) throw new Error('Falta el id del trabajo');

    const productions = await MoTwillLaserJobProduction.query()
      .join(
        'machines',
        'machines.machine_id',
        'mo_twill_laser_job_productions.machine_id'
      )
      .join(
        'employees',
        'employees.employee_id',
        'mo_twill_laser_job_productions.employee_id'
      )
      .where('mo_twill_laser_job_id', jobID)
      .where('is_active', 1)
      .select([
        { id: 'mo_twill_laser_job_productions.id' },
        { quantity: 'mo_twill_laser_job_productions.quantity' },
        { machine_name: 'machines.machine' },
        { comment: 'mo_twill_laser_job_productions.comment' },
        { employee_name: 'employees.first_name' },
        { employee_id: 'employees.employee_id' },
        { created_at: 'mo_twill_laser_job_productions.created_at' },
      ])
      .orderBy('created_at', 'desc')
      .castTo<
        {
          id: number;
          quantity: number;
          machine_name: string;
          employee_name: string;
          comment: string;
          employee_id: number;
          created_at: string;
        }[]
      >();

    return res.status(200).json({
      ok: true,
      mesage: 'Producciones encontradas',
      data: productions,
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function deleteProduction(req: Request, res: Response) {
  try {
    const { productionID } = req.params as unknown as { productionID: number };

    const { employeeID } = req.body as unknown as {
      employeeID: number;
    };

    if (!productionID) throw new Error('Falta el id de la producción');

    if (!employeeID) throw new Error('Falta el encargado');

    const seachProduction = await MoTwillLaserJobProduction.query()
      .where('id', productionID)
      .where('is_active', 1)
      .select([
        { id: 'mo_twill_laser_job_productions.id' },
        {
          employee_id: 'mo_twill_laser_job_productions.employee_id',
        },
      ])
      .first()
      .castTo<{
        id: number;
        employee_id: number;
      }>();

    if (!seachProduction) throw new Error('Producción no encontrada');
    if (seachProduction.employee_id !== employeeID)
      throw new Error(
        'No eres el encargado de esta producción, no puedes borrarla'
      );

    await MoTwillLaserJobProduction.query()
      .where('id', productionID)
      .patch({ is_active: false });

    return res.status(200).json({
      ok: true,
      message: 'Producción eliminada',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function createNewConsumptionToJob(req: Request, res: Response) {
  try {
    const { jobID } = req.params as unknown as { jobID: number };
    const { height, width, quantity } = req.body as unknown as {
      height: number;
      width: number;
      quantity: number;
    };

    if (!jobID) throw new Error('Falta el id del trabajo');
    if (!height || !width || !quantity)
      throw new Error('Faltan datos para el consumo');

    const consumption = await newOperatorConsumption({
      moTwillLaserJobID: jobID,
      height,
      width,
      quantity,
    });

    if (!consumption) throw new Error('No se pudo crear el consumo');

    return res.status(201).json({
      ok: true,
      message: 'Consumo creado',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function getConsumptionsToJob(req: Request, res: Response) {
  try {
    const { jobID } = req.params as unknown as { jobID: number };

    if (!jobID) throw new Error('Falta el id del trabajo');

    const consumptions = await MoTwillLaserJobConsumption.query()
      .where('mo_twill_laser_job_id', jobID)
      .where('is_active', 1)
      .select([
        { id: 'mo_twill_laser_job_consumptions.id' },
        { height: 'mo_twill_laser_job_consumptions.height' },
        { width: 'mo_twill_laser_job_consumptions.width' },
        { quantity: 'mo_twill_laser_job_consumptions.quantity' },
      ])
      .castTo<
        {
          id: number;
          height: number;
          width: number;
          quantity: number;
        }[]
      >();

    return res.status(200).json({
      ok: true,
      message: 'Consumos encontrados',
      data: consumptions || [],
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function deleteConsumptionToJob(req: Request, res: Response) {
  try {
    const { consumptionID } = req.params as unknown as {
      consumptionID: number;
    };

    if (!consumptionID) throw new Error('Falta el id del consumo');

    await MoTwillLaserJobConsumption.query()
      .where('id', consumptionID)
      .patch({ is_active: false });

    return res.status(200).json({
      ok: true,
      message: 'Consumo eliminado',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function updateConsumptionToJob(req: Request, res: Response) {
  try {
    const { consumptionID } = req.params as unknown as {
      consumptionID: number;
    };

    const { height, width, quantity } = req.body as unknown as {
      height?: number;
      width?: number;
      quantity?: number;
    };

    if (!consumptionID) throw new Error('Falta el id del consumo');

    const actualConsumption = await MoTwillLaserJobConsumption.query()
      .where('id', consumptionID)
      .where('is_active', 1)
      .first();

    if (!actualConsumption)
      throw new Error('No se encontro el consumo a editar');

    await MoTwillLaserJobConsumption.query()
      .where('id', consumptionID)
      .patch({
        height: height || actualConsumption.height,
        width: width || actualConsumption.width,
        quantity: quantity || actualConsumption.quantity,
      });

    return res.status(200).json({
      ok: true,
      message: 'Consumo editado',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function getAllStatusesForJob(_req: Request, res: Response) {
  try {
    const statuses = await MoTwillLaserJobStatus.query()
      .where('is_active', 1)
      .select([
        { id: 'mo_twill_laser_job_status.id' },
        { name: 'mo_twill_laser_job_status.name' },
      ])
      .castTo<{ id: number; name: string }[]>();

    return res.status(200).json({
      ok: true,
      message: 'Estados encontrados',
      data: statuses || [],
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function addLayersToJob(req: Request, res: Response) {
  try {
    const { jobID } = req.params as unknown as { jobID: number };
    const { layers, specialLayers } = req.body as unknown as {
      layers: number;
      specialLayers: number;
    };

    if (!jobID) throw new Error('Falta el id del trabajo');
    if (!layers || !specialLayers)
      throw new Error('Faltan los datos de las capas');

    const job = await MoTwillLaserJob.query()
      .where('id', jobID)
      .where('is_active', 1)
      .first();

    if (!job) throw new Error('No se encontro el trabajo');

    await MoTwillLaserJob.query()
      .where('id', jobID)
      // TODO: layers and special_layers are strings in the database
      .patch({
        layers: layers ? layers.toString() : job.layers.toString(),
        special_layers: specialLayers
          ? specialLayers.toString()
          : job.special_layers.toString(),
      });

    return res.status(200).json({
      ok: true,
      message: 'Trabajo editado',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export async function productionsByEmployee(
  req: Request,
  res: Response
): Promise<Response> {
  try {
    const { id } = req.params as unknown as { id: number };

    if (!id) throw new Error('Falta el id del operador');

    const today = dayjs(new Date().toISOString().split('T')[0]).format(
      'YYYY-MM-DD'
    );

    const productionsByEmployee = await MoTwillLaserJobProduction.query()
      .join(
        'machines',
        'machines.machine_id',
        'mo_twill_laser_job_productions.machine_id'
      )
      .join(
        'mo_twill_laser_jobs',
        'mo_twill_laser_jobs.id',
        'mo_twill_laser_job_productions.mo_twill_laser_job_id'
      )
      .join('mo_numbers', 'mo_numbers.mo_id', 'mo_twill_laser_jobs.mo_id')
      .where('mo_twill_laser_job_productions.employee_id', id)
      .where(
        'mo_twill_laser_job_productions.created_at',
        '>',
        `${today} 00:00:00`
      )
      .where('machines.status', 1)
      .where('mo_twill_laser_job_productions.is_active', 1)
      .select([
        { productionID: 'mo_twill_laser_job_productions.id' },
        { jobID: 'mo_twill_laser_jobs.id' },
        { machineName: 'machines.machine' },
        { quantity: 'mo_twill_laser_job_productions.quantity' },
        { order: 'mo_numbers.num' },
        { style: 'mo_numbers.style' },
        { orderQuantity: 'mo_numbers.quantity' },
        { company: 'mo_numbers.company_code' },
        { isRepo: 'mo_twill_laser_jobs.is_repo' },
      ])
      .castTo<
        {
          productionID: MoTwillLaserJobProduction['id'];
          jobID: MoTwillLaserJob['id'];
          machineName: Machine['machine'];
          quantity: MoTwillLaserJobProduction['quantity'];
          order: string;
          style: string;
          orderQuantity: number;
          company: string;
          isRepo: number;
        }[]
      >();

    const jobsID = productionsByEmployee
      .map((production) => production.jobID)
      .filter(
        (value: number, index: number, self: number[]): boolean =>
          self.indexOf(value) === index
      );

    const decorationsByJob = await MoTwillLaserJobDecoration.query()
      .join(
        'mo_twill_laser_decorations',
        'mo_twill_laser_decorations.id',
        'mo_twill_laser_job_decorations.mo_twill_laser_decoration_id'
      )
      .join(
        'mo_twill_laser_job_types',
        'mo_twill_laser_job_types.id',
        'mo_twill_laser_decorations.mo_twill_laser_job_type_id'
      )
      .whereIn('mo_twill_laser_job_decorations.mo_twill_laser_job_id', jobsID)
      .select([
        { decorationID: 'mo_twill_laser_decorations.id' },
        {
          jobID: 'mo_twill_laser_job_decorations.mo_twill_laser_job_id',
        },
        { typeName: 'mo_twill_laser_job_types.name' },
        { decorationName: 'mo_twill_laser_decorations.comment' },
      ])
      .castTo<
        {
          decorationID: number;
          decorationName: string;
          jobID: number;
          typeName: string;
        }[]
      >();

    const dataFormated: {
      productionID: number;
      machineName: string;
      quantity: number;
      order: string;
      style: string;
      orderQuantity: number;
      decoration: string;
      isRepo: number;
    }[] = [];

    for (const production of productionsByEmployee) {
      if (Number(production.company) === 3) {
        const searchDecoration = decorationsByJob.find(
          (decoration) => decoration.jobID === production.jobID
        );

        const decoration = await MoTwillLaserDecoration.query()
          .where('id', searchDecoration.decorationID)
          .select('child_mo_id')
          .first()
          .castTo<{
            child_mo_id: number;
          }>();

        const childMO = await MoNumber.query()
          .where('mo_id', decoration.child_mo_id)
          .select('num', 'style')
          .first()
          .castTo<{
            num: string;
            style: string;
          }>();

        const decorationName = `${searchDecoration.typeName}-${searchDecoration.decorationName}`;

        dataFormated.push({
          productionID: production.productionID,
          machineName: production.machineName,
          quantity: production.quantity,
          order: childMO.num,
          style: childMO.style,
          orderQuantity: production.orderQuantity,
          decoration: decorationName,
          isRepo: production.isRepo,
        });
      } else {
        const searchDecoration = decorationsByJob.find(
          (decoration) => decoration.jobID === production.jobID
        );

        const decorationName = `${searchDecoration.typeName}-${searchDecoration.decorationName}`;

        dataFormated.push({
          productionID: production.productionID,
          machineName: production.machineName,
          quantity: production.quantity,
          order: production.order,
          style: production.style,
          orderQuantity: production.orderQuantity,
          decoration: decorationName,
          isRepo: production.isRepo,
        });
      }
    }

    const totalOrders = dataFormated
      .map((order) => order.order)
      .filter((value, index, self) => self.indexOf(value) === index);

    const totalOrdersQuantity = dataFormated
      .map((order) => order.quantity)
      .reduce((a, b) => a + b, 0);

    return res.status(200).json({
      ok: true,
      message: 'Producciones por operador',
      data: {
        productions: dataFormated,
        orders: totalOrders,
        pieces: totalOrdersQuantity,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);
      return res.status(400).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');
    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}
