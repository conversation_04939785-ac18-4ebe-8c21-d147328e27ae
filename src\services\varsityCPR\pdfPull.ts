import currency from 'currency.js';
import { readFileSync } from 'fs';
import moment from 'moment';
import { PdfReader } from 'pdfreader';

import type {
  ClassHeader,
  FilePart,
  LineItemExpected,
  MadeItem,
  PageType,
  ReportData,
  ReportFormatType,
  SummaryData,
} from './config';
import { getRowType, lineItemTypes } from './config';
import { combineTillNext, getRowString, minMaxExpected } from './tools';

const sumItemsByClass = (
  items: MadeItem[],
  options?: {
    classCodeFilter?: string[];
  }
) => {
  const sumItemsByClass = items.reduce(
    (acc, cur) => {
      const { class_code } = cur;
      if (
        options?.classCodeFilter &&
        !options.classCodeFilter.includes(class_code)
      ) {
        return acc;
      }
      if (!acc[class_code]) {
        acc[class_code] = {
          class_code,
          sum_item_count: 0,
          sum_unit_count: 0,
          sum_labor: 0,
          sum_sub_labor: 0,
          sum_prt: 0,
          sum_prs: 0,
          sum_art: 0,
          sum_cut: 0,
          sum_sew: 0,
          sum_total: 0,
          summed_total: 0,
          diff_positive: 0,
          diff_negative: 0,
          items: [],
        };
      }
      acc[class_code].sum_item_count += 1;
      acc[class_code].sum_unit_count += cur.qty ?? 0;
      acc[class_code].sum_labor = currency(acc[class_code].sum_labor)
        .add(cur.sub_labor ?? 0)
        .add(cur.prt ?? 0)
        .add(cur.prs ?? 0)
        .add(cur.art ?? 0).value;
      acc[class_code].sum_sub_labor = currency(
        acc[class_code].sum_sub_labor
      ).add(cur.sub_labor ?? 0).value;
      acc[class_code].sum_prt = currency(acc[class_code].sum_prt).add(
        cur.prt ?? 0
      ).value;
      acc[class_code].sum_prs = currency(acc[class_code].sum_prs).add(
        cur.prs ?? 0
      ).value;
      acc[class_code].sum_art = currency(acc[class_code].sum_art).add(
        cur.art ?? 0
      ).value;
      acc[class_code].sum_cut = currency(acc[class_code].sum_cut).add(
        cur.cut ?? 0
      ).value;
      acc[class_code].sum_sew = currency(acc[class_code].sum_sew).add(
        cur.sew ?? 0
      ).value;
      acc[class_code].sum_total = currency(acc[class_code].sum_total).add(
        cur.total ?? 0
      ).value;
      acc[class_code].summed_total = currency(acc[class_code].summed_total)
        .add(cur.sew ?? 0)
        .add(cur.cut ?? 0)
        .add(cur.sub_labor ?? 0)
        .add(cur.prs ?? 0)
        .add(cur.prt ?? 0)
        .add(cur.art ?? 0).value;
      acc[class_code].diff_negative = currency(
        acc[class_code].diff_negative
      ).add(cur.diff < 0 ? cur.diff : 0).value;
      acc[class_code].diff_positive = currency(
        acc[class_code].diff_positive
      ).add(cur.diff > 0 ? cur.diff : 0).value;
      acc[class_code].items.push(cur);

      return acc;
    },
    {} as {
      [key: string]: {
        class_code: string;
        sum_item_count: number;
        sum_unit_count: number;
        sum_labor: number;
        sum_sub_labor: number;
        sum_prt: number;
        sum_prs: number;
        sum_art: number;
        sum_cut: number;
        sum_sew: number;
        sum_total: number;
        summed_total: number;
        diff_positive: number;
        diff_negative: number;
        items: MadeItem[];
      };
    }
  );
  return sumItemsByClass;
};

const compareSummaryToItems = (
  items: MadeItem[],
  summariesMade: SummaryData[],
  options?: {
    classCodeFilter?: string[];
  }
) => {
  const summedItemsByClass = sumItemsByClass(items, options);

  // compare item summary with class summary
  for (const summaryLine of summariesMade) {
    const { class_code } = summaryLine;
    if (!class_code) {
      console.log('no class code', summaryLine);
      throw new Error('no class code');
    }
    if (
      options?.classCodeFilter &&
      !options.classCodeFilter.includes(class_code)
    ) {
      continue;
    }
    const summedItems = summedItemsByClass[class_code];
    if (!summedItems) {
      console.log('no sum class', class_code);
      throw new Error('no sum class');
    }
    summaryLine.summed_items_total = currency(summedItems.summed_total).value;
    if (
      summaryLine.sum_item_count !== summedItems.sum_item_count ||
      summaryLine.sum_unit_count !== summedItems.sum_unit_count ||
      (summaryLine.sum_labor ?? 0) !== summedItems.sum_labor ||
      summaryLine.sum_cut !== summedItems.sum_cut ||
      summaryLine.sum_sew !== summedItems.sum_sew ||
      (summaryLine.sum_total ?? 0) !== summedItems.sum_total
    ) {
      const errors = [];
      console.log(summedItems.items);
      console.log('sum item does not match sum class in compare');
      if (options?.classCodeFilter) {
        console.log('filter', options.classCodeFilter);
      }
      console.log('summary', summaryLine);
      console.log('summed Items', {
        ...summedItems,
        items: [],
      });

      if (summaryLine.sum_item_count !== summedItems.sum_item_count) {
        console.log(
          'item count does not match',
          summaryLine.sum_item_count,
          summedItems.sum_item_count
        );
        errors.push(
          `item count does not match, ${summaryLine.sum_item_count} - ${summedItems.sum_item_count}`
        );
      }
      if (summaryLine.sum_unit_count !== summedItems.sum_unit_count) {
        console.log(
          'unit count does not match',
          summaryLine.sum_unit_count,
          summedItems.sum_unit_count
        );
        errors.push(
          `unit count does not match, ${summaryLine.sum_unit_count} - ${summedItems.sum_unit_count}`
        );
      }
      if ((summaryLine.sum_labor ?? 0) !== summedItems.sum_labor) {
        console.log(
          'labor does not match',
          summaryLine.sum_labor,
          summedItems.sum_labor
        );
        errors.push(
          `labor does not match, ${summaryLine.sum_labor} - ${summedItems.sum_labor}`
        );
      }
      if (summaryLine.sum_cut !== summedItems.sum_cut) {
        console.log(
          'cut does not match',
          summaryLine.sum_cut,
          summedItems.sum_cut
        );
        errors.push(
          `cut does not match, ${summaryLine.sum_cut} - ${summedItems.sum_cut}`
        );
      }
      if (summaryLine.sum_sew !== summedItems.sum_sew) {
        console.log(
          'sew does not match',
          summaryLine.sum_sew,
          summedItems.sum_sew
        );
        errors.push(
          `sew does not match, ${summaryLine.sum_sew} - ${summedItems.sum_sew}`
        );
      }
      if ((summaryLine.sum_total ?? 0) !== summedItems.sum_total) {
        console.log(
          'total does not match',
          summaryLine.sum_total,
          summedItems.sum_total
        );
        errors.push(
          `total does not match, ${summaryLine.sum_total} - ${summedItems.sum_total}`
        );
      }
      // check if greater than 0.01 difference per unit
      if (
        currency(summaryLine.sum_labor).subtract(summedItems.sum_labor)
          .value === summedItems.diff_positive
      ) {
        console.log(
          'ignore labor diff',
          summaryLine.sum_labor,
          summedItems.sum_labor,
          summedItems.diff_positive
        );
      } else {
        throw new Error(
          `sum items does not match sum class with diffs ${
            summedItems.diff_positive
          } ${summedItems.diff_negative}, ${errors.join(' | ')}`
        );
      }
    }
  }
};

const checkAndUpdateItem = (
  item: Partial<MadeItem> | null,
  itemsMade: MadeItem[]
) => {
  if (!item) {
    throw new Error('no item to add');
  }
  if (!item.page) {
    console.log('no page', item);
    throw new Error('no page');
  }
  if (!item.row) {
    console.log('no row', item);
    throw new Error('no row');
  }
  if (!item.class_code) {
    throw new Error('no class code');
  }
  if (!item.class_name) {
    throw new Error('no class name');
  }
  if (!item.style) {
    throw new Error('no style');
  }
  if (!item.qty) {
    throw new Error('no qty');
  }
  if (!item.order) {
    throw new Error('no order');
  }
  if (!item.vch) {
    throw new Error('no vch');
  }

  let total = item.total;

  if (!total) {
    // can happen if costs are zero for cut and sew
    // unit cost would be 0 also
    if (
      item.cut != undefined &&
      item.cut === 0 &&
      item.sew != undefined &&
      item.sew === 0 &&
      item.unit != undefined &&
      item.unit === 0
    ) {
      total = 0;
    } else {
      console.log('no total', item);
      throw new Error('no total');
    }
  }

  // check item totals here
  const summedValues = currency(item.cut)
    .add(item.sew)
    .add(item.sub_labor ?? 0)
    .add(item.prt ?? 0)
    .add(item.prs ?? 0)
    .add(item.art ?? 0).value;
  const diff = currency(item.total).subtract(summedValues).value;
  const diffPerUnit =
    item.qty === 0 ? 0 : currency(diff).divide(item.qty).value;
  if (
    diff !== 0 &&
    (item.qty === 0 || currency(diff).divide(item.qty).value < 0) // currency only does 2 decimals
  ) {
    console.log('new item total diff', diff, diffPerUnit, item);
    // throw new Error('new item total diff not zero');
  }

  const newMadeItem: MadeItem = {
    ...item,
    page: item.page,
    row: item.row,
    class_code: item.class_code,
    class_name: item.class_name,
    cut: item.cut ?? 0,
    sew: item.sew ?? 0,
    unit: item.unit ?? 0,
    total: total,
    style: item.style,
    order: item.order,
    qty: item.qty,
    vch: item.vch,
    diff: diff,
  };
  itemsMade.push(newMadeItem);
};

export const processCprFileParts = async (
  fileParts: FilePart[],
  options?: {
    showPages?: number[];
  }
) => {
  const showPages = options.showPages ?? [];

  if (!fileParts || !fileParts.length) {
    console.log('no file parts');
    return;
  }

  try {
    console.log('first part', fileParts[0]);
    const groupByPageAndRow = fileParts.reduce((acc, cur) => {
      const { page, y } = cur;
      if (!acc[page]) {
        acc[page] = {};
      }
      if (!acc[page][y]) {
        acc[page][y] = [];
      }
      acc[page][y].push(cur);
      return acc;
    }, {} as { [key: number]: { [key: number]: FilePart[] } });

    const pageNumbers = Object.keys(groupByPageAndRow).map((p) => Number(p));
    pageNumbers.sort((a, b) => a - b);
    console.log('pages found', pageNumbers.length);
    console.log('pages', pageNumbers);

    // check for missing pages
    let lastPage = 0;
    for (const page of pageNumbers) {
      if (page != lastPage + 1) {
        console.log('missing page', page, lastPage);
        throw new Error('missing page');
      }
      lastPage = page;
    }

    // const groupByPage = fileParts.reduce((acc, cur) => {
    //   const { page } = cur;
    //   if (!acc[page]) {
    //     acc[page] = [];
    //   }
    //   acc[page].push(cur);
    //   return acc;
    // }, {} as { [key: number]: FilePart[] });

    const summariesMade: SummaryData[] = [];
    const itemsMade: MadeItem[] = [];
    const reportData: ReportData = {
      report_type: null,
      run_date: null,
      run_time: null,
      invoice_number: null,
      report_start: null,
      report_end: null,
      pay_date: null,
      accepted_format_type: null,
      summary_pages: [],
      accepted_pages: [],
      rejected_pages: [],
    };
    let curRow = 0;
    let curItem: Partial<MadeItem> | null = null;
    let lastRowType: string | null = null;
    const curHeader: ClassHeader = {
      class_code: null,
      class_name: null,
    };
    let lastPageType: PageType | null = null;
    let lastPageFormat: ReportFormatType | null = null;
    let pageCount = 0;
    for (const pageString of pageNumbers) {
      pageCount++;
      const page = Number(pageString);

      if (page != pageCount) {
        console.log('page out of order', page, pageCount);
        throw new Error('page out of order');
      }

      let pageType: PageType | null = null;
      let pageFormat = lastPageFormat;

      const rows = Object.keys(groupByPageAndRow[page]).sort(
        (a, b) => Number(a) - Number(b)
      );

      console.log('page', page, rows.length, pageFormat);

      for (const [rowIndex, rowKey] of rows.entries()) {
        const row = Number(rowKey);
        // used on fat report where date is split out
        let curSummaryRow: Partial<SummaryData> | null = null;
        // ignoring header on each page
        const parts = groupByPageAndRow[page][row];
        console.log('row', page, row, parts.length);
        if (!parts || !parts.length) {
          console.log('no parts', page, row);
          throw new Error('no parts');
        }
        // sort parts by x
        parts.sort((a, b) => a.x - b.x);
        const rowString = getRowString(parts);

        // get row type
        const rowTypeData = getRowType(
          reportData.report_type,
          parts,
          lastRowType,
          pageFormat,
          page,
          row
        );
        if (!rowTypeData) {
          console.log('no row type', rowString);
          console.log('page', page);
          console.log('row Index', rowIndex);
          console.log('part count', parts.length);
          console.log('report type', reportData.report_type);
          console.log('format', pageFormat);
          console.log('last row type', lastRowType);
          console.log('parts', parts);

          throw new Error(
            `no row type - PAGE: ${page} - ROW: ${rowIndex} - PARTS: ${parts.length} - ${reportData.report_type} - ${pageFormat} - ${rowString}`
          );
        }
        const rowType = rowTypeData[0];
        const reportTypePull = rowTypeData[1];
        lastRowType = rowType;

        // console.log(
        //   'row type',
        //   rowType,
        //   reportTypePull,
        //   reportData.report_type
        // );

        // ignored specific rows
        if (rowType === 'skiprow') {
          if (rowIndex === 0) {
            pageType = lastPageType;
            pageFormat = lastPageFormat;
          }
          // ignore summary and rejected lines
          if (pageType === 'summary' || pageType === 'rejected') {
            if (curItem) {
              checkAndUpdateItem(curItem, itemsMade);
            }
            curItem = null;
            break;
          }

          continue;
        }

        // check and update report type
        if (reportTypePull) {
          if (
            reportData.report_type &&
            reportData.report_type !== reportTypePull
          ) {
            console.log(
              'report type already set',
              reportData.report_type,
              reportTypePull,
              page,
              rowIndex,
              rowType,
              rowString
            );
            throw new Error(
              `report type already set - ${reportData.report_type} - ${reportTypePull} - ${page} - ${rowIndex} - ${rowType} - ${rowString}`
            );
          }
          reportData.report_type = reportTypePull;
        }

        // make sure we have a report type
        if (!reportData.report_type) {
          console.log('no report type', page, row, rowType, parts);
          throw new Error('no report type');
        }

        const lineTypeInfo = lineItemTypes[reportData.report_type]?.[rowType];
        if (!lineTypeInfo) {
          console.log('no line type info', rowType, parts);
          throw new Error('no line type info');
        }

        const expectedSorted = [...lineTypeInfo.expected].sort(
          (a, b) => a.x - b.x
        );

        if (lineTypeInfo.reportFormat) {
          if (
            pageFormat &&
            pageFormat !== lineTypeInfo.reportFormat &&
            rowIndex > 0
          ) {
            console.log(
              'report format already set',
              pageFormat,
              lineTypeInfo.reportFormat,
              rowType,
              page,
              row,
              parts
            );
            throw new Error(
              `report format already set - ${pageFormat} - ${lineTypeInfo.reportFormat} - ${reportData.report_type} - ${reportTypePull} - ${page} - ${rowIndex} - ${rowType} - ${rowString}`
            );
          }
          pageFormat = lineTypeInfo.reportFormat;
        }

        // deal with page type
        const pulledPageType = lineTypeInfo.pageType;
        // console.log(
        //   'row type',
        //   rowType,
        //   reportData.report_type,
        //   lineTypeInfo.type,
        //   pulledPageType,
        //   parts.length,
        //   pageType
        // );
        if (pulledPageType) {
          if (pageType && pageType !== pulledPageType) {
            if (pulledPageType === 'summary' && pageType === 'accepted') {
              // allow acceptd to change to summary
            } else {
              console.log('page type already set', pageType, pulledPageType);
              throw new Error('page type already set');
            }
          }
          pageType = pulledPageType;
        }
        if (lineTypeInfo.type != 'header' && rowIndex === 0) {
          // first row with no header so page type is same as previous
          pageType = lastPageType;
        }

        // ignore summary and rejected lines
        if (pageType === 'summary' || pageType === 'rejected') {
          if (curItem) {
            checkAndUpdateItem(curItem, itemsMade);
          }
          curItem = null;
          break;
        }

        // manage based on line type
        if (lineTypeInfo.type != 'header' && lineTypeInfo.type != 'subitem') {
          // finish current item
          if (curItem) {
            checkAndUpdateItem(curItem, itemsMade);
          }
          curItem = null;
        }
        if (lineTypeInfo.type === 'item') {
          curItem = {
            page: page,
            row: row,
            ...curHeader,
          };
        }
        if (lineTypeInfo.type === 'classTotal') {
          curSummaryRow = {
            page: page,
            row: row,
            class_code: curHeader.class_code,
            class_name: curHeader.class_name,
          };
        }
        if (lineTypeInfo.type === 'subitem') {
          if (!curItem) {
            console.log('no cur item', page, row, parts);
            throw new Error('no cur item for sub item');
          }
        }

        // ignore data for lines we dont care about
        if (lineTypeInfo.ignoreRowData) {
          console.log('ignore row data', rowType);
          continue;
        }

        if (
          lineTypeInfo.matchPartIndexToExpected &&
          lineTypeInfo.expected.length !== parts.length
        ) {
          console.log(
            'expected length does not match parts length',
            lineTypeInfo.expected.length,
            parts.length
          );
          console.log('page', page);
          console.log('row', row);
          console.log('part count', parts.length);
          console.log('row type', rowType);
          console.log('lineTypeInfo', lineTypeInfo);
          console.log('report type', reportData.report_type);
          console.log('format', pageFormat);
          console.log('parts', parts);
          throw new Error('expected length does not match parts length');
        }

        if (showPages.length && showPages.includes(page)) {
          console.log('page', page, rowIndex, rowType, parts.length, rowString);
        }

        let useParts = parts;
        // Special Row Parts Processing
        if (lineTypeInfo.specialPartProcess) {
          const [newParts, updateData, reportData] =
            lineTypeInfo.specialPartProcess(useParts);
          console.log(
            'special part process',
            useParts.length,
            newParts.length,
            updateData
          );
          useParts = newParts;
          if (updateData) {
            for (const [key, value] of Object.entries(updateData)) {
              curItem[key] = value;
            }
          }
          if (reportData) {
            for (const [key, value] of Object.entries(reportData)) {
              reportData[key] = value;
            }
          }
        }

        // PROCESS ROW PARTS
        for (let partIndex = 0; partIndex < useParts.length; partIndex++) {
          const part = useParts[partIndex];
          const { x, y, text } = part;
          if (y !== curRow) {
            curRow = y;
          }
          if (
            lineTypeInfo &&
            lineTypeInfo.ignorePartFunc &&
            lineTypeInfo.ignorePartFunc(part)
          ) {
            continue;
          }

          // get expected in 2 different ways
          let gettingExpected: LineItemExpected = null;
          if (lineTypeInfo.matchPartIndexToExpected) {
            // sort expected by x
            const sortedExpected = [...expectedSorted].sort((a, b) => {
              return a.x - b.x;
            });
            // get expected based on index
            gettingExpected = sortedExpected[partIndex];
          } else {
            const expectedByDistance = [...expectedSorted].sort(
              (a, b) => Math.abs(a.x - x) - Math.abs(b.x - x)
            );
            gettingExpected = expectedByDistance.find((e) => {
              const [min, max] = minMaxExpected(e);
              if (x > min && x < max) {
                return true;
              }
              return false;
            });
          }
          const expected = gettingExpected;
          if (!expected) {
            console.log(
              'no expected',
              rowType,
              reportData.report_type,
              x,
              part,
              useParts
            );
            throw new Error(
              `no expected - PAGE: ${page} - ${rowType} - PI: ${partIndex}/${
                parts.length
              } - ${
                reportData.report_type
              } - ${pageFormat} - ${rowType} - ${rowString} - ${JSON.stringify({
                x,
                text,
              })}`
            );
          }
          if (!expected.key) {
            continue;
          }
          // console.log('part proc', i, text, expected);
          let useValue = text.trim();
          if (expected.combineTillNext) {
            const { combineString, curPartIndex } = combineTillNext(
              useParts,
              expectedSorted,
              partIndex,
              expected,
              lineTypeInfo
            );
            partIndex = curPartIndex;
            useValue = combineString.trim();
          }

          if (expected.key === 'class_code' || expected.key === 'class_name') {
            // no extra spaces
            curHeader[expected.key] = useValue;
            continue;
          }
          if (
            expected.key === 'class' ||
            expected.key === 'CP' ||
            expected.key === 'sub_code' ||
            expected.key === 'cust_num' ||
            expected.key === 'siz' ||
            expected.key === 'rc' ||
            expected.key === 'dc' ||
            expected.key === 'factory'
          ) {
            curItem[expected.key] = useValue;
            continue;
          }
          if (expected.key === 'style') {
            // get right 8 characters from style and check if all numbers
            const orderCheck = useValue.slice(-8);
            const orderCheckNum = Number(orderCheck);
            const nextPart = useParts[partIndex + 1];
            if (
              !isNaN(orderCheckNum) &&
              nextPart &&
              nextPart.text.trim().length === 3 &&
              !isNaN(Number(nextPart.text.trim()))
            ) {
              // definitely an order number
              const styleNumber = useValue.slice(0, -8);
              curItem.order = orderCheckNum;
              curItem.style = styleNumber;
              curItem.style_text_overlap_order = true;
              // console.log(
              //   'style text overlap order',
              //   useValue,
              //   orderCheck,
              //   styleNumber,
              //   curItem
              // );
              continue;
            }
            curItem.style = useValue;
            continue;
          }

          if (
            expected.key === 'order' ||
            expected.key === 'vch' ||
            expected.key === 'size' ||
            expected.key === 'cut' ||
            expected.key === 'sew' ||
            expected.key === 'sub_labor' ||
            expected.key === 'ltr' ||
            expected.key === 'prt' ||
            expected.key === 'prs' ||
            expected.key === 'art' ||
            expected.key === 'let' ||
            expected.key === 'qty' ||
            expected.key === 'unit'
          ) {
            // number values
            const value = Number(useValue.replace(',', ''));
            if (isNaN(value)) {
              console.log(
                'not a number',
                useValue,
                rowType,
                expected,
                part,
                useParts
              );
              throw new Error(
                `not a number - ${useValue} - PAGE: ${page} - PI: ${
                  partIndex + 1
                }/${useParts.length} - ${
                  reportData.report_type
                } - ${rowType} - ${rowString}`
              );
            }
            curItem[expected.key] = value;
            continue;
          }

          if (expected.key === 'stars') {
            curItem[expected.key] = true;
            continue;
          }

          if (
            expected.key === 'sub_ext' ||
            expected.key === 'ext' ||
            expected.key === 'total'
          ) {
            // number values
            const value = Number(useValue.replace(',', ''));
            if (isNaN(value)) {
              console.log(
                'not a number',
                useValue,
                rowType,
                expected,
                part,
                useParts
              );
              throw new Error(
                `not a number - ${useValue} - PAGE: ${page} - PI: ${
                  partIndex + 1
                }/${useParts.length} - ${
                  reportData.report_type
                } - ${rowType} - ${rowString}`
              );
            }
            curItem.total = value;
            continue;
          }
          if (expected.key === 'qty_unit') {
            // need to split out numbers
            const [qty, unit] = useValue.split(/\s+/);
            const qtyValue = Number(qty.trim().replace(',', ''));
            if (isNaN(qtyValue)) {
              console.log(
                'qty is not a number',
                qty,
                rowType,
                expected,
                part,
                useParts
              );
              throw new Error(
                `qty is not a number - ${qty} - PAGE: ${page} - PI: ${
                  partIndex + 1
                }/${useParts.length} - ${
                  reportData.report_type
                } - ${rowType} - ${rowString}`
              );
            }

            const unitValue = Number(unit.trim().replace(',', ''));
            if (isNaN(unitValue)) {
              console.log(
                'units is not a number',
                unit,
                rowType,
                expected,
                part,
                useParts
              );
              throw new Error(
                `unit is not a number - ${unit} - PAGE: ${page} - PI: ${
                  partIndex + 1
                }/${useParts.length} - ${
                  reportData.report_type
                } - ${rowType} - ${rowString}`
              );
            }
            // console.log('qty unit', qtyValue, unitValue, text, part);
            curItem['qty'] = qtyValue;
            curItem['unit'] = unitValue;
            continue;
          }
          if (
            expected.key === 'sum_item_count' ||
            expected.key === 'sum_unit_count' ||
            expected.key === 'sum_labor' ||
            expected.key === 'sum_cut' ||
            expected.key === 'sum_sew' ||
            expected.key === 'sum_ext' ||
            expected.key === 'sum_total'
          ) {
            // console.log('use value', useValue, expected, text);
            // number values
            const value = Number(useValue.replace(',', ''));
            if (isNaN(value)) {
              console.log(
                'not a number',
                useValue,
                rowType,
                expected,
                part,
                useParts
              );
              throw new Error(
                `not a number - ${useValue} - PAGE: ${page} - PI: ${
                  partIndex + 1
                }/${useParts.length} - ${
                  reportData.report_type
                } - ${rowType} - ${rowString}`
              );
            }
            if (!curSummaryRow) {
              console.log(
                'no cur summary row',
                rowType,
                expected,
                part,
                useParts
              );
              throw new Error('no cur summary row');
            }
            if (expected.key === 'sum_ext' || expected.key === 'sum_total') {
              curSummaryRow.sum_total = value;
              continue;
            }
            curSummaryRow[expected.key] = value;
            continue;
          }
          if (expected.key === 'sum_cut_sew') {
            // need to split out numbers
            const [cut, sew] = useValue.split(/\s+/);
            const cutValue = Number(cut.trim().replace(',', ''));
            if (isNaN(cutValue)) {
              console.log(
                'cut is not a number',
                cut,
                rowType,
                expected,
                part,
                useParts
              );
              throw new Error(
                `cust is not a number - ${cut} - PAGE: ${page} - PI: ${
                  partIndex + 1
                }/${useParts.length} - ${
                  reportData.report_type
                } - ${rowType} - ${rowString}`
              );
            }
            const sewValue = Number(sew.trim().replace(',', ''));
            if (isNaN(sewValue)) {
              console.log(
                'sew is not a number',
                sew,
                rowType,
                expected,
                part,
                useParts
              );
              throw new Error(
                `sew is not a number - ${sew} - PAGE: ${page} - PI: ${
                  partIndex + 1
                }/${useParts.length} - ${
                  reportData.report_type
                } - ${rowType} - ${rowString}`
              );
            }
            if (!curSummaryRow) {
              console.log('no cur summary row');
              throw new Error('no cur summary row');
            }
            // console.log('cut sew', cutValue, sewValue, text, part);
            curSummaryRow['sum_cut'] = cutValue;
            curSummaryRow['sum_sew'] = sewValue;
            continue;
          }
          if (expected.key === 'page') {
            const value = Number(useValue);
            if (isNaN(value)) {
              console.log(
                'not a number',
                useValue,
                rowType,
                expected,
                part,
                useParts
              );
              throw new Error(
                `not a number - ${useValue} - PAGE: ${page} - PI: ${
                  partIndex + 1
                }/${useParts.length} - ${
                  reportData.report_type
                } - ${rowType} - ${rowString}`
              );
            }
            continue;
          }

          if (expected.key === 'run_date' || expected.key === 'pay_date') {
            const value = moment(useValue, 'M/D/YYYY');
            if (!value.isValid()) {
              throw new Error(
                `not a date - ${useValue} - PAGE: ${page} - PI: ${
                  partIndex + 1
                }/${useParts.length} - ${
                  reportData.report_type
                } - ${rowType} - ${rowString}`
              );
            }
            reportData[expected.key] = value.format('YYYY-MM-DD');
            continue;
          }

          if (
            expected.key === 'report_end' ||
            expected.key === 'report_start'
          ) {
            const value = moment(useValue, 'YYYY/MM/DD');
            if (!value.isValid()) {
              throw new Error(
                `not a date - ${useValue} - PAGE: ${page} - PI: ${
                  partIndex + 1
                }/${useParts.length} - ${
                  reportData.report_type
                } - ${rowType} - ${rowString}`
              );
            }
            if (
              reportData[expected.key] &&
              reportData[expected.key] !== value.format('YYYY-MM-DD')
            ) {
              console.log(
                'report start end already set',
                reportData[expected.key],
                value.format('YYYY-MM-DD')
              );
              throw new Error('report start end already set');
            }
            reportData[expected.key] = value.format('YYYY-MM-DD');
            continue;
          }
          if (
            expected.key === 'invoice_number' ||
            expected.key === 'run_time'
          ) {
            reportData[expected.key] = useValue;
            continue;
          }

          console.log('unexpected key', expected.key, part);
          throw new Error('unexpected key');
        }

        // check required information is there
        // if (rowType === 'mainThin') {
        //   console.log('here for check');
        //   if (!curItem || !curItem.order) {
        //     console.log('no order', curItem);
        //     console.log('info', lineTypeInfo)
        //     throw new Error('no order');
        //   }
        // }
        if (lineTypeInfo.infoCheckFunc !== undefined) {
          try {
            lineTypeInfo.infoCheckFunc(
              useParts,
              curItem,
              curHeader,
              reportData
            );
          } catch (e) {
            console.log(
              'info check error',
              page,
              row,
              pageType,
              pageFormat,
              useParts,
              curItem
            );
            throw e;
          }
        }

        if (curSummaryRow) {
          const page = curSummaryRow.page;
          const row = curSummaryRow.row;
          const class_code = curSummaryRow.class_code;
          const class_name = curSummaryRow.class_name;
          if (!page || !row || !class_code || !class_name) {
            console.log(
              'no page, row, class code, or class name on summary row',
              curSummaryRow
            );
            throw new Error('no page, row, class code, or class name');
          }

          const newSummary: SummaryData = {
            ...curSummaryRow,
            page,
            row,
            class_code,
            class_name,
            sum_item_count: curSummaryRow.sum_item_count ?? 0,
            sum_unit_count: curSummaryRow.sum_unit_count ?? 0,
            sum_labor: curSummaryRow.sum_labor ?? 0,
            sum_cut: curSummaryRow.sum_cut ?? 0,
            sum_sew: curSummaryRow.sum_sew ?? 0,
            sum_total: curSummaryRow.sum_total ?? 0,
          };
          summariesMade.push(newSummary);
          // run summary check to make sure values match
          try {
            compareSummaryToItems(itemsMade, summariesMade, {
              classCodeFilter: [curSummaryRow.class_code],
            });
          } catch (e) {
            console.log('current row type', rowTypeData);
            throw new Error(
              `${e.message} - ${reportData.report_type} - ${reportTypePull} - ${page} - ${rowIndex} - ${rowType} - ${rowString}`
            );
            // throw e;
          }
          // if (curSummaryRow && curSummaryRow.class_code === 'JVT') {
          //   console.log(
          //     'jvt',
          //     curSummaryRow,
          //     rowType,
          //     reportData.report_type,
          //     parts
          //   );
          // }
        }
      }
      lastPageFormat = pageFormat;

      switch (pageType) {
        case 'accepted':
          reportData.accepted_pages.push(page);

          // set accepted report format
          if (
            reportData.accepted_format_type &&
            reportData.accepted_format_type != pageFormat
          ) {
            throw new Error(
              `accepted report format already set - ${reportData.accepted_format_type} - ${pageFormat}`
            );
          }
          reportData.accepted_format_type = pageFormat;
          break;
        case 'rejected':
          reportData.rejected_pages.push(page);
          break;
        case 'summary':
          reportData.summary_pages.push(page);
          break;
        default:
          console.log('no page type', page);
          console.log('page type', pageType);
          console.log('report data', reportData);
          console.log('cur header', curHeader);
          throw new Error('no page type');
      }
      if (curHeader) lastPageType = pageType;
      if (showPages && showPages.includes(page)) {
        console.log('page', page);
        console.log('page type', pageType);
        console.log('report data', reportData);
        // console.log('items made', itemsMade);
        // console.log('summaries made', summariesMade);
        // console.log('cur header', curHeader);
      }
    }

    // if cprlettering, add unit price to items
    if (reportData.report_type === 'cprlettering') {
      for (const item of itemsMade) {
        const { qty, total } = item;
        if (!qty || !total) {
          console.log('no qty or total', item);
          throw new Error('no qty or total');
        }
        item.unit = currency(total).divide(qty).value;
      }
    }

    if (
      reportData.accepted_pages.length > 0 &&
      (!reportData.report_start || !reportData.report_end)
    ) {
      console.log('no report start or end', reportData);
      throw new Error('no report start or end');
    }

    if (!reportData.invoice_number) {
      console.log('no invoice number', reportData);
      throw new Error('no invoice number');
    }

    if (!reportData.accepted_format_type) {
      console.log('no accepted format type', reportData);
      throw new Error('no accepted format type');
    }

    // summaries check
    console.log('summaries', summariesMade.length);

    return {
      reportData,
      summariesMade,
      itemsMade,
    };
  } catch (e) {
    console.log('error', e);
    throw e;
  }
};

export const getVarsityCPRParts = (filePath: string) =>
  new Promise<{ fileParts: FilePart[]; unknownParts: unknown[] }>(
    (resolve, reject) => {
      try {
        const pdfBuffer = readFileSync(filePath);

        let page = 0;
        let curPage = 0;
        const fileParts = [];
        const filePages = [];
        const fileUnknown = [];
        let errorHappened = false;
        let pageParts = 0;

        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        const pdfParser = new PdfReader({
          debug: false,
        });

        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        pdfParser.parseBuffer(pdfBuffer, (err, item) => {
          if (err) {
            console.log('Parser error', err);
            console.log('error happened before', errorHappened);
            const parserError: string | null | undefined = err.parserError;
            if (parserError) {
              console.log('parser error', parserError);
              reject(new Error(parserError));
              return;
            }
            if (!errorHappened) {
              errorHappened = true;
              reject(new Error(JSON.stringify(err)));
            }
            return;
            // callback(err);
          }
          if (!item) {
            // console.log('no item');
            // console.log('pages', page);
            // console.log('count', count);
            // console.log('fileParts length', fileParts.length);
            // console.log('filePages', filePages);
            // console.log('fileFiles', fileFiles);
            // console.log('fileUnknown', fileUnknown);
            resolve({ fileParts: fileParts, unknownParts: fileUnknown });
            // processCprFileParts(fileParts, {
            //   response,
            // });
            // callback();
          } else if (item.file) {
            console.log('started new file', item);
            // fileFiles.push({
            //   ...item,
            //   page,
            // });
            // callback();
          } else if (item.page) {
            page++;
            pageParts++;
            // console.log('Starting parsed page', page, pageParts, item);
            pageParts = 0;
            filePages.push(item);
            // callback();
          } else if (item.text) {
            pageParts++;
            if (curPage !== page) {
              // console.log('new page adding', curPage, page, pageParts);
              curPage = page;
              pageParts = 0;
            }
            fileParts.push({
              page,
              ...item,
            });
          } else {
            console.log('unknown item', item);
            pageParts++;
            fileUnknown.push(item);
          }
        });
      } catch (e) {
        console.log('error reading file', e);
        reject(e);
      }
    }
  );
