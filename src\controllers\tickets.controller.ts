import dayjs from 'dayjs';
import type { Request, Response } from 'express';
import { raw, ref } from 'objection';
import { v4 as uuidv4 } from 'uuid';

import { Employee } from '@app/models/employee.schema';
import { WorkFragmentLog, WorkFragments } from '@app/models/fragment.schema';
import {
  Buildings,
  MoNumber,
  MoScans,
  WorkActivityLog,
  WorkAreaBatches,
  WorkAreaGroups,
  WorkAreaLines,
  WorkAreaTicketStatuses,
  WorkAreaTickets,
  WorkAreas,
  WorkInventoryBins,
  WorkNotes,
  WorkTypeVoucherTypes,
  WorkVoucherCompanyIgnoreUpdates,
  WorkVouchers,
} from '@app/models/tickets.schema';
import { createScan } from '@app/services/scanning';
import type { CustomField } from '@app/types';

export async function updateStatusBulk(req: Request, res: Response) {
  try {
    const {
      voucher,
      status,
      area,
      codeUser,
      nextArea,
      commentVoucher,
      updateCustomer = false,
      group,
    } = req.body;

    const useVoucherBarcode: string =
      voucher === undefined ? undefined : voucher === null ? null : voucher;
    if (useVoucherBarcode && typeof useVoucherBarcode !== 'string') {
      return res.status(400).json({
        ok: false,
        data: 'Codigo de voucher invalido',
      });
    }

    const useWorkAreaId =
      area === undefined ? undefined : area === null ? null : Number(area);
    if (useWorkAreaId && isNaN(useWorkAreaId)) {
      return res.status(400).json({
        ok: false,
        data: 'Codigo de area invalido',
      });
    }

    const useWorkAreaTicketStatusId =
      status === undefined
        ? undefined
        : status === null
        ? null
        : Number(status);
    if (useWorkAreaTicketStatusId && isNaN(useWorkAreaTicketStatusId)) {
      return res.status(400).json({
        ok: false,
        data: 'Codigo de status invalido',
      });
    }

    let updateWorkTickets;
    let ticketLog;
    let getWorkTicketStatuses;

    const dataError = [];
    const dataSuccess = [];

    if (useVoucherBarcode.substr(0, 4) === 'MEPB') {
      // consultamos si existe el voucher en el area
      const infoTicket = await WorkVouchers.query()
        .join(
          'work_area_tickets',
          'work_vouchers.id',
          'work_area_tickets.work_voucher_id'
        )
        .join(
          'work_areas',
          'work_area_tickets.work_area_id',
          'work_areas.work_area_id'
        )
        .join(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .join(
          'work_statuses',
          'work_area_ticket_statuses.work_status_id',
          'work_statuses.id'
        )
        .where('work_vouchers.barcode', useVoucherBarcode)
        .where('work_area_tickets.work_area_id', useWorkAreaId)
        .select(
          { statusId: 'work_statuses.id' },
          { id: 'work_vouchers.id' },
          {
            ticketId: 'work_area_tickets.id',
          },
          {
            mo_id: 'work_vouchers.mo_id',
          },
          MoNumber.query()
            .where('mo_numbers.mo_id', ref('work_vouchers.mo_id'))
            .select('mo_numbers.num')
            .as('mo'),
          {
            nameOld: 'work_area_ticket_statuses.name',
          },
          { colorHexOld: 'work_area_ticket_statuses.color_hex' },
          { group: 'work_vouchers.work_voucher_group_id' }
        )
        .castTo<
          {
            statusId: number;
            id: number;
            ticketId: number;
            mo_id: number;
            mo: string;
            nameOld: string;
            colorHexOld: string;
            group?: number;
          }[]
        >();

      if (infoTicket.length > 0) {
        // obtenemos el work_status_id del status del area
        getWorkTicketStatuses = await WorkAreaTicketStatuses.query()
          .where('work_area_id', useWorkAreaId)
          .where('id', useWorkAreaTicketStatusId)
          .select('name', 'color_hex', 'work_status_id')
          .castTo<
            {
              name: string;
              color_hex: string;
              work_status_id: number;
            }[]
          >();

        if (infoTicket[0]?.nameOld === getWorkTicketStatuses[0]?.name) {
          dataError.push({
            ok: false,
            uuidVoucher: uuidv4(),
            voucher: useVoucherBarcode,
            mo: 'No se puede actualizar la MO',
            error: `La Mo ya tiene el estado ${getWorkTicketStatuses[0]?.name}`,
          });
        } else {
          // logica para actualizar el status
          if ([10, 50].includes(+infoTicket[0].statusId)) {
            if (infoTicket[0]?.group) {
              dataError.push({
                ok: false,
                uuidVoucher: uuidv4(),
                voucher: useVoucherBarcode,
                mo: infoTicket[0].mo,
                error:
                  'El ticket fue asignado a un grupo, no se puede actualizar',
              });
            } else {
              if (nextArea) {
                if (getWorkTicketStatuses[0]?.work_status_id === 100) {
                  updateWorkTickets = await WorkAreaTickets.query()
                    .update({
                      work_area_ticket_status_id: useWorkAreaTicketStatusId,
                      next_work_area_id: nextArea || null,
                      is_company_notified: false,
                      finished_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                    })
                    .where('id', infoTicket[0].ticketId);
                } else {
                  updateWorkTickets = await WorkAreaTickets.query()
                    .update({
                      work_area_ticket_status_id: useWorkAreaTicketStatusId,
                      next_work_area_id: nextArea || null,
                      is_company_notified: false,
                    })
                    .where('id', infoTicket[0].ticketId);
                }
              } else {
                if (getWorkTicketStatuses[0].work_status_id === 100) {
                  updateWorkTickets = await WorkAreaTickets.query()
                    .update({
                      work_area_ticket_status_id: useWorkAreaTicketStatusId,
                      is_company_notified: false,
                      finished_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                    })
                    .where('id', infoTicket[0].ticketId);
                } else {
                  updateWorkTickets = await WorkAreaTickets.query()
                    .update({
                      work_area_ticket_status_id: useWorkAreaTicketStatusId,
                      is_company_notified: false,
                    })
                    .where('id', infoTicket[0].ticketId);
                }
              }

              if (commentVoucher) {
                await WorkNotes.query().insert({
                  mo_id: infoTicket[0].mo_id,
                  work_area_ticket_id: infoTicket[0].ticketId,
                  note: commentVoucher,
                  employee_id: codeUser,
                });
              }

              ticketLog = await WorkActivityLog.query().insert({
                work_voucher_id: infoTicket[0].id,
                employee_id: codeUser,
                work_area_id: useWorkAreaId,
                module_name: 'ticket',
                module_id: infoTicket[0].ticketId,
                activity: 'TicketStatusChanged',
                data: JSON.stringify({
                  old_status_name: infoTicket[0].nameOld,
                  old_color: infoTicket[0].colorHexOld,
                  new_status_name: getWorkTicketStatuses[0].name,
                  new_color: getWorkTicketStatuses[0].color_hex,
                }),
              });
              dataSuccess.push({
                ok: true,
                uuidVoucher: uuidv4(),
                voucher: useVoucherBarcode,
                mo: infoTicket[0].mo,
                error: `Se actualizo de ${infoTicket[0].nameOld} a ${getWorkTicketStatuses[0].name}`,
              });
            }
          } else {
            dataError.push({
              ok: false,
              uuidVoucher: uuidv4(),
              voucher: useVoucherBarcode,
              mo: infoTicket[0].mo,
              error: 'El ticket ya ha sido completado',
            });
          }
        }
      } else {
        dataError.push({
          ok: false,
          uuidVoucher: uuidv4(),
          voucher: useVoucherBarcode,
          mo: 'No existe MO en el area',
          error: 'No existe un voucher',
        });
      }
    } else if (useVoucherBarcode.substr(0, 4) === 'MEVB') {
      // obtenemos el id del voucher
      const voucherId = Number(useVoucherBarcode.substr(4));
      if (isNaN(voucherId)) {
        dataError.push({
          ok: false,
          uuidVoucher: uuidv4(),
          voucher: useVoucherBarcode,
          mo: 'No es un voucher o falta el prefijo',
          error: 'No es un voucher',
        });
      }

      // consultamos si existe el voucher en el area
      // TODO: should be using first since only one voucher should be passed
      const infoTicket = await WorkVouchers.query()
        .join(
          'work_area_tickets',
          'work_vouchers.id',
          '=',
          'work_area_tickets.work_voucher_id'
        )
        .join(
          'work_areas',
          'work_area_tickets.work_area_id',
          '=',
          'work_areas.work_area_id'
        )
        .join(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .join(
          'work_statuses',
          'work_area_ticket_statuses.work_status_id',
          'work_statuses.id'
        )
        .where('work_vouchers.id', voucherId)
        .where('work_area_tickets.work_area_id', useWorkAreaId)
        .select(
          { statusId: 'work_statuses.id' },
          {
            ticketId: 'work_area_tickets.id',
          },
          {
            mo_id: 'work_vouchers.mo_id',
          },
          MoNumber.query()
            .where('mo_numbers.mo_id', ref('work_vouchers.mo_id'))
            .select('mo_numbers.num')
            .as('mo'),
          {
            nameOld: 'work_area_ticket_statuses.name',
          },
          { colorHexOld: 'work_area_ticket_statuses.color_hex' },
          { group: 'work_vouchers.work_voucher_group_id' }
        )
        .castTo<
          {
            statusId: number;
            ticketId: number;
            mo_id: number;
            mo: string;
            nameOld: string;
            colorHexOld: string;
            group?: number;
          }[]
        >();

      if (infoTicket.length > 0) {
        // obtenemos el work_status_id del status del area
        getWorkTicketStatuses = await WorkAreaTicketStatuses.query()
          .where('work_area_id', useWorkAreaId)
          .where('id', useWorkAreaTicketStatusId)
          .select('name', 'color_hex', 'work_status_id')
          .castTo<
            {
              name: string;
              color_hex: string;
              work_status_id: number;
            }[]
          >();

        if (infoTicket[0]?.nameOld === getWorkTicketStatuses[0]?.name) {
          dataError.push({
            ok: false,
            uuidVoucher: uuidv4(),
            voucher: useVoucherBarcode,
            mo: 'No se puede actualizar el voucher',
            error: `El voucher ya tiene el estado ${getWorkTicketStatuses[0]?.name}`,
          });
        } else {
          // logica para actualizar el status
          if ([10, 50].includes(+infoTicket[0].statusId)) {
            if (infoTicket[0]?.group) {
              dataError.push({
                ok: false,
                uuidVoucher: uuidv4(),
                voucher: useVoucherBarcode,
                mo: infoTicket[0].mo,
                error:
                  'El ticket fue asignado a un grupo, no se puede actualizar',
              });
            } else {
              let scanMessage = '';
              if (nextArea) {
                if (getWorkTicketStatuses[0].work_status_id === 100) {
                  updateWorkTickets = await WorkAreaTickets.query()
                    .update({
                      work_area_ticket_status_id: useWorkAreaTicketStatusId,
                      next_work_area_id: nextArea || null,
                      is_company_notified: false,
                      finished_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                    })
                    .where('id', infoTicket[0].ticketId);

                  //add scan because is complete, if scanpoly is true
                  if (updateCustomer) {
                    //create scan
                    const scan = await createScan({
                      mo_id: null,
                      mo_barcode: `MEVBF${voucherId}`,
                      group_barcode: group,
                      quantity_reported: null,
                      employee_id: codeUser,
                      work_area_line_id: null,
                      type_action: 'FINISH',
                      affected_units: null,
                      work_area_group_id: null,
                      work_area_id: null,
                      partial_option: null,
                      update_customer: null,
                      work_voucher_id: voucherId,
                      work_ticket_id: infoTicket[0].ticketId,
                    });
                    if (scan.ok) {
                      scanMessage = ' se creo el scan';
                    } else {
                      scanMessage = 'Se creo ticket pero no pudo crear el scan';
                    }
                  }
                } else {
                  updateWorkTickets = await WorkAreaTickets.query()
                    .update({
                      work_area_ticket_status_id: useWorkAreaTicketStatusId,
                      next_work_area_id: nextArea || null,
                      is_company_notified: false,
                    })
                    .where('id', infoTicket[0].ticketId);
                }
              } else {
                if (getWorkTicketStatuses[0].work_status_id === 100) {
                  updateWorkTickets = await WorkAreaTickets.query()
                    .update({
                      work_area_ticket_status_id: useWorkAreaTicketStatusId,
                      is_company_notified: false,
                      finished_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                    })
                    .where('id', infoTicket[0].ticketId);
                  //add scan because is complete, if scanpoly is true
                  if (updateCustomer) {
                    //create scan
                    const scan = await createScan({
                      mo_id: null,
                      mo_barcode: `MEVBF${voucherId}`,
                      group_barcode: group,
                      employee_id: codeUser,
                      type_action: 'FINISH',
                      work_voucher_id: voucherId,
                      work_ticket_id: infoTicket[0].ticketId,
                      affected_units: null,
                      work_area_group_id: null,
                      work_area_id: null,
                      work_area_line_id: null,
                      partial_option: null,
                      quantity_reported: null,
                      update_customer: null,
                    });
                    if (scan.ok) {
                      scanMessage = ' se creo el scan';
                    } else {
                      scanMessage = 'Se creo ticket pero no pudo crear el scan';
                    }
                  }
                } else {
                  updateWorkTickets = await WorkAreaTickets.query()
                    .update({
                      work_area_ticket_status_id: useWorkAreaTicketStatusId,
                      is_company_notified: false,
                    })
                    .where('id', infoTicket[0].ticketId);
                }
              }
              if (commentVoucher) {
                await WorkNotes.query().insert({
                  mo_id: infoTicket[0].mo_id,
                  work_area_ticket_id: infoTicket[0].ticketId,
                  note: commentVoucher,
                  employee_id: codeUser,
                });
              }

              ticketLog = await WorkActivityLog.query().insert({
                work_voucher_id: voucherId,
                employee_id: codeUser,
                work_area_id: useWorkAreaId,
                module_name: 'ticket',
                module_id: infoTicket[0].ticketId,
                activity: 'TicketStatusChanged',
                data: JSON.stringify({
                  old_status_name: infoTicket[0].nameOld,
                  old_color: infoTicket[0].colorHexOld,
                  new_status_name: getWorkTicketStatuses[0].name,
                  new_color: getWorkTicketStatuses[0].color_hex,
                }),
              });
              dataSuccess.push({
                ok: true,
                uuidVoucher: uuidv4(),
                voucher: useVoucherBarcode,
                mo: infoTicket[0].mo,
                error: `Se actualizo de ${infoTicket[0].nameOld} a ${getWorkTicketStatuses[0].name} ${scanMessage}`,
              });
            }
          } else {
            dataError.push({
              ok: false,
              uuidVoucher: uuidv4(),
              voucher: useVoucherBarcode,
              mo: infoTicket[0].mo,
              error: 'El ticket ya ha sido completado',
            });
          }
        }
      } else {
        dataError.push({
          ok: false,
          uuidVoucher: uuidv4(),
          voucher: useVoucherBarcode,
          mo: 'No existe MO en el area',
          error: 'No existe un voucher',
        });
      }
    } else if (useVoucherBarcode.substr(0, 4) === 'MEGV') {
      const group_id = useVoucherBarcode.substr(4);

      const statuses = await WorkAreaTicketStatuses.query()
        .where('work_area_id', useWorkAreaId)
        .where('id', useWorkAreaTicketStatusId)
        .select('name', 'color_hex', 'work_status_id')
        .castTo<
          {
            name: string;
            color_hex: string;
            work_status_id: number;
          }[]
        >();

      const getAllVouchersToGroupVouchers = await WorkVouchers.query()
        .join(
          'work_area_tickets',
          'work_vouchers.id',
          'work_area_tickets.work_voucher_id'
        )
        .join(
          'work_areas',
          'work_area_tickets.work_area_id',
          'work_areas.work_area_id'
        )
        .join(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .join(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id ',
          'work_area_ticket_statuses.id'
        )
        .join(
          'work_statuses',
          'work_area_ticket_statuses.work_status_id',
          'work_statuses.id'
        )
        .join('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
        .join(
          'work_voucher_groups',
          'work_vouchers.work_voucher_group_id',
          'work_voucher_groups.id'
        )
        .select([
          { statusId: 'work_statuses.id' },
          { voucherId: 'work_vouchers.id' },
          { ticketId: 'work_area_tickets.id' },
          { areaName: 'work_areas.area_name' },
          { voucherType: 'work_voucher_types.name' },
          { nameGroup: 'work_voucher_groups.name' },
          { nameOld: 'work_area_ticket_statuses.name' },
          { colorHexOld: 'work_area_ticket_statuses.color_hex' },
          'mo_numbers.mo_id',
          'mo_numbers.mo_status',
          'mo_numbers.material_date',
          'mo_numbers.mo_order',
          'mo_numbers.required_date',
          'mo_numbers.ItemDescription8',
          'mo_numbers.num',
          'mo_numbers.po_numbers',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.customer',
        ])
        .where('work_vouchers.work_voucher_group_id', group_id)
        .where('work_area_tickets.work_area_id', useWorkAreaId)
        .orderBy('work_area_tickets.created_at', 'ASC')
        .castTo<
          {
            statusId: number;
            voucherId: number;
            ticketId: number;
            areaName: string;
            voucherType: string;
            nameGroup: string;
            nameOld: string;
            colorHexOld: string;
            mo_id: number;
            mo_status: string;
            material_date: string;
            mo_order: string;
            required_date: string;
            ItemDescription8: string;
            num: string;
            po_numbers: string;
            style: string;
            quantity: number;
            customer: string;
          }[]
        >();

      if (getAllVouchersToGroupVouchers[0]?.nameOld === statuses[0]?.name) {
        dataError.push({
          ok: false,
          uuidVoucher: uuidv4(),
          voucher: useVoucherBarcode,
          mo: 'No se puede actualizar la MO',
          error: `La Mo ya tiene el estado ${statuses[0]?.name}`,
        });
      } else {
        if ([10, 50].includes(getAllVouchersToGroupVouchers[0]?.statusId)) {
          const trx = await WorkAreaTickets.startTransaction();
          try {
            for (let i = 0; i < getAllVouchersToGroupVouchers.length; i++) {
              if (nextArea) {
                if (statuses[0].work_status_id === 100) {
                  updateWorkTickets = await WorkAreaTickets.query(trx)
                    .update({
                      work_area_ticket_status_id: useWorkAreaTicketStatusId,
                      next_work_area_id: nextArea,
                      is_company_notified: false,
                      finished_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                    })
                    .where('id', getAllVouchersToGroupVouchers[i].ticketId);
                } else {
                  updateWorkTickets = await WorkAreaTickets.query(trx)
                    .update({
                      work_area_ticket_status_id: useWorkAreaTicketStatusId,
                      next_work_area_id: nextArea || null,
                      is_company_notified: false,
                    })
                    .where('id', getAllVouchersToGroupVouchers[i].ticketId);
                }
              } else {
                updateWorkTickets = await WorkAreaTickets.query()
                  .update({
                    work_area_ticket_status_id: useWorkAreaTicketStatusId,
                    is_company_notified: false,
                  })
                  .where('id', getAllVouchersToGroupVouchers[i].ticketId);
              }

              if (commentVoucher) {
                await WorkNotes.query().insert({
                  mo_id: getAllVouchersToGroupVouchers[i].mo_id,
                  work_area_ticket_id:
                    getAllVouchersToGroupVouchers[i].ticketId,
                  note: commentVoucher,
                  employee_id: codeUser,
                });
              }

              await WorkActivityLog.query().insert({
                work_voucher_id: getAllVouchersToGroupVouchers[i].voucherId,
                employee_id: codeUser,
                work_area_id: useWorkAreaId,
                module_name: 'ticket',
                module_id: getAllVouchersToGroupVouchers[i].ticketId,
                activity: 'TicketStatusChanged',
                data: JSON.stringify({
                  old_status_name: getAllVouchersToGroupVouchers[i].nameOld,
                  old_color: getAllVouchersToGroupVouchers[i].colorHexOld,
                  new_status_name: statuses[0].name,
                  new_color: statuses[0].color_hex,
                }),
              });
            }

            trx.commit();
          } catch (error) {
            trx.rollback();
            console.log(error);
          }

          dataSuccess.push({
            ok: true,
            uuidVoucher: uuidv4(),
            voucher: useVoucherBarcode,
            mo: 'Se actualizaron todas las MOS del grupo',
            error: `Se actualizo de ${getAllVouchersToGroupVouchers[0].nameOld} a ${statuses[0].name}`,
          });
        } else {
          dataError.push({
            ok: false,
            uuidVoucher: uuidv4(),
            voucher: useVoucherBarcode,
            mo: 'No se pueden actualizar los Tickets',
            error: 'Los tickets han sido completados',
          });
        }
      }
    } else {
      dataError.push({
        ok: false,
        uuidVoucher: uuidv4(),
        voucher: useVoucherBarcode,
        mo: 'No es un voucher o falta el prefijo',
        error: 'No es un voucher',
      });
    }

    return res.status(200).json({
      ok: true,
      dataError,
      dataSuccess,
      updateWorkTickets,
      ticketLog,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function updateLocationBulk(req: Request, res: Response) {
  try {
    const { voucher, location, area, codeUser } = req.body;

    let updateLocation;
    let ticketLog;

    if (!voucher || typeof voucher !== 'string') {
      return res.status(400).json({
        ok: false,
        data: 'Codigo de voucher invalido',
      });
    }
    if (!location || typeof location !== 'number' || isNaN(location)) {
      return res.status(400).json({
        ok: false,
        data: 'Codigo de ubicacion invalido',
      });
    }
    if (!area || typeof area !== 'number' || isNaN(area)) {
      return res.status(400).json({
        ok: false,
        data: 'Codigo de area invalido',
      });
    }

    const dataError = [];
    const dataSuccess = [];

    if (voucher.startsWith('MEPB')) {
      // consultamos si existe el voucher en el area
      const infoTicket = await WorkVouchers.query()
        .join(
          'work_area_tickets',
          'work_vouchers.id',
          'work_area_tickets.work_voucher_id'
        )
        .join(
          'work_areas',
          'work_area_tickets.work_area_id',
          '=',
          'work_areas.work_area_id'
        )
        .leftJoin(
          'work_inventory_bins',
          'work_area_tickets.work_inventory_location_id',
          'work_inventory_bins.id'
        )
        .join(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .join(
          'work_statuses',
          'work_area_ticket_statuses.work_status_id',
          'work_statuses.id'
        )
        .where('work_vouchers.barcode', voucher)
        .where('work_areas.work_area_id', area)
        .select(
          { statusId: 'work_statuses.id' },
          MoNumber.query()
            .where('mo_numbers.mo_id', ref('work_vouchers.mo_id'))
            .select('mo_numbers.num')
            .as('mo'),
          {
            ticketId: 'work_area_tickets.id',
          },
          {
            group: 'work_vouchers.work_voucher_group_id',
          },
          { nameBinOld: 'work_inventory_bins.name' },
          { idBin: 'work_inventory_bins.id' }
        )
        .castTo<
          {
            statusId: number;
            ticketId: number;
            mo: string;
            group?: number;
            nameBinOld: string;
            idBin: number;
          }[]
        >();

      if (infoTicket.length > 0) {
        const infoBin = await WorkInventoryBins.query()
          .where('work_inventory_bins.id', location)
          .select({ nameNewBin: 'work_inventory_bins.name' })
          .castTo<
            {
              nameNewBin: string;
            }[]
          >();

        if (infoTicket[0].nameBinOld === infoBin[0].nameNewBin) {
          dataError.push({
            ok: false,
            uuidVoucher: uuidv4(),
            voucher,
            mo: 'No se puede actualizar la MO',
            error: `La MO ya esta en la ubicacion ${infoBin[0].nameNewBin}`,
          });
        } else {
          if ([10, 50].includes(+infoTicket[0].statusId)) {
            if (infoTicket[0]?.group) {
              dataError.push({
                ok: false,
                uuidVoucher: uuidv4(),
                voucher,
                mo: infoTicket[0].mo,
                error:
                  'El ticket fue asignado a un grupo, no se puede actualizar',
              });
            } else {
              updateLocation = await WorkAreaTickets.query()
                .update({
                  work_inventory_location_id: location,
                })
                .where('work_area_tickets.id', '=', infoTicket[0].ticketId);

              ticketLog = await WorkActivityLog.query().insert({
                employee_id: codeUser,
                work_area_id: area,
                module_name: 'ticket',
                module_id: infoTicket[0].ticketId,
                activity: 'TicketInventoryMoved',
                data: JSON.stringify({
                  old_bin: infoTicket[0].nameBinOld || 'Sin ubicacion',
                  new_bin: infoBin[0].nameNewBin,
                }),
              });

              dataSuccess.push({
                ok: true,
                uuidVoucher: uuidv4(),
                voucher,
                mo: infoTicket[0].mo,
                error: `Se actualizo de ${
                  infoTicket[0].nameBinOld || 'Sin ubicacion'
                } a ${infoBin[0].nameNewBin}`,
              });
            }
          } else {
            dataError.push({
              ok: false,
              uuidVoucher: uuidv4(),
              voucher,
              mo: infoTicket[0].mo,
              error: 'El ticket ya ha sido completado',
            });
          }
        }
      } else {
        dataError.push({
          ok: false,
          uuidVoucher: uuidv4(),
          voucher,
          mo: 'No existe mo en el area',
          error: 'No existe un voucher',
        });
      }
    } else if (voucher.startsWith('MEVB')) {
      const voucherId = voucher.substr(4);

      // consultamos si existe el voucher en el area
      const infoTicket = await WorkVouchers.query()
        .join(
          'work_area_tickets',
          'work_vouchers.id',
          '=',
          'work_area_tickets.work_voucher_id'
        )
        .join(
          'work_areas',
          'work_area_tickets.work_area_id',
          '=',
          'work_areas.work_area_id'
        )
        .leftJoin(
          'work_inventory_bins',
          'work_area_tickets.work_inventory_location_id',
          'work_inventory_bins.id'
        )
        .join(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .join(
          'work_statuses',
          'work_area_ticket_statuses.work_status_id',
          'work_statuses.id'
        )
        .where('work_vouchers.id', voucherId)
        .where('work_area_tickets.work_area_id', area)
        .select(
          { statusId: 'work_statuses.id' },
          MoNumber.query()
            .where('mo_numbers.mo_id', ref('work_vouchers.mo_id'))
            .select('mo_numbers.num')
            .as('mo'),
          {
            ticketId: 'work_area_tickets.id',
          },
          {
            group: 'work_vouchers.work_voucher_group_id',
          },
          { nameBinOld: 'work_inventory_bins.name' },
          { idBin: 'work_inventory_bins.id' }
        )
        .castTo<
          {
            statusId: number;
            ticketId: number;
            mo: string;
            group?: number;
            nameBinOld: string;
            idBin: number;
          }[]
        >();

      if (infoTicket.length > 0) {
        const infoBin = await WorkInventoryBins.query()
          .join(
            'work_zones',
            'work_inventory_bins.work_zone_id',
            'work_zones.id'
          )
          .join(
            'work_area_zones',
            'work_zones.id',
            'work_area_zones.work_zone_id'
          )
          .where('work_inventory_bins.id', location)
          .where('work_area_zones.work_area_id', area)
          .select({ nameNewBin: 'work_inventory_bins.name' })
          .castTo<
            {
              nameNewBin: string;
            }[]
          >();

        if (infoTicket[0].nameBinOld === infoBin[0].nameNewBin) {
          dataError.push({
            ok: false,
            uuidVoucher: uuidv4(),
            voucher,
            mo: 'No se puede actualizar la MO',
            error: `La MO ya esta en la ubicacion ${infoBin[0].nameNewBin}`,
          });
        } else {
          if ([10, 50].includes(+infoTicket[0].statusId)) {
            if (infoTicket[0]?.group) {
              dataError.push({
                ok: false,
                uuidVoucher: uuidv4(),
                voucher,
                mo: infoTicket[0].mo,
                error:
                  'El ticket fue asignado a un grupo, no se puede actualizar',
              });
            } else {
              updateLocation = await WorkAreaTickets.query()
                .update({
                  work_inventory_location_id: location,
                })
                .where('work_area_tickets.id', infoTicket[0].ticketId);

              ticketLog = await WorkActivityLog.query().insert({
                employee_id: codeUser,
                work_area_id: area,
                module_name: 'ticket',
                module_id: infoTicket[0].ticketId,
                activity: 'TicketInventoryMoved',
                data: JSON.stringify({
                  old_bin: infoTicket[0].nameBinOld || 'Sin ubicacion',
                  new_bin: infoBin[0].nameNewBin,
                }),
              });

              dataSuccess.push({
                ok: true,
                uuidVoucher: uuidv4(),
                voucher,
                mo: infoTicket[0].mo,
                error: `Se actualizo de ${
                  infoTicket[0].nameBinOld || 'Sin ubicacion'
                } a ${infoBin[0].nameNewBin}`,
              });
            }
          } else {
            dataError.push({
              ok: false,
              uuidVoucher: uuidv4(),
              voucher,
              mo: infoTicket[0].mo,
              error: 'El ticket ya ha sido completado',
            });
          }
        }
      } else {
        dataError.push({
          ok: false,
          uuidVoucher: uuidv4(),
          voucher,
          mo: 'No existe MO en el area',
          error: 'No existe un voucher en el area',
        });
      }
    } else if (voucher.substr(0, 4) === 'MEGV') {
      const group_id = voucher.substr(4);

      const infoBin = await WorkInventoryBins.query()
        .join('work_zones', 'work_inventory_bins.work_zone_id', 'work_zones.id')
        .join(
          'work_area_zones',
          'work_zones.id',
          'work_area_zones.work_zone_id'
        )
        .where('work_inventory_bins.id', location)
        .where('work_area_zones.work_area_id', area)
        .select({ nameNewBin: 'work_inventory_bins.name' })
        .castTo<
          {
            nameNewBin: string;
          }[]
        >();

      const getAllVouchersToGroupVouchers = await WorkVouchers.query()
        .join(
          'work_area_tickets',
          'work_vouchers.id',
          'work_area_tickets.work_voucher_id'
        )
        .join(
          'work_areas',
          'work_area_tickets.work_area_id',
          'work_areas.work_area_id'
        )
        .leftJoin(
          'work_inventory_bins',
          'work_area_tickets.work_inventory_location_id',
          'work_inventory_bins.id'
        )
        .join(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .join(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id ',
          'work_area_ticket_statuses.id'
        )
        .join(
          'work_statuses',
          'work_area_ticket_statuses.work_status_id',
          'work_statuses.id'
        )
        .join('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
        .join(
          'work_voucher_groups',
          'work_vouchers.work_voucher_group_id',
          'work_voucher_groups.id'
        )
        .select([
          { statusId: 'work_statuses.id' },
          { voucherId: 'work_vouchers.id' },
          { ticketId: 'work_area_tickets.id' },
          { areaName: 'work_areas.area_name' },
          { voucherType: 'work_voucher_types.name' },
          { nameGroup: 'work_voucher_groups.name' },
          { nameBinOld: 'work_inventory_bins.name' },
          'mo_numbers.mo_id',
          'mo_numbers.mo_status',
          'mo_numbers.material_date',
          'mo_numbers.mo_order',
          'mo_numbers.required_date',
          'mo_numbers.ItemDescription8',
          'mo_numbers.num',
          'mo_numbers.po_numbers',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.customer',
        ])
        .where('work_vouchers.work_voucher_group_id', group_id)
        .where('work_area_tickets.work_area_id', area)
        .orderBy('work_area_tickets.created_at', 'ASC')
        .castTo<
          {
            statusId: number;
            voucherId: number;
            ticketId: number;
            areaName: string;
            voucherType: string;
            nameGroup: string;
            nameBinOld: string;
            mo_id: number;
            mo_status: string;
            material_date: string;
            mo_order: string;
            required_date: string;
            ItemDescription8: string;
            num: string;
            po_numbers: string;
            style: string;
            quantity: number;
            customer: string;
          }[]
        >();

      if (
        getAllVouchersToGroupVouchers[0].nameBinOld === infoBin[0].nameNewBin
      ) {
        dataError.push({
          ok: false,
          uuidVoucher: uuidv4(),
          voucher,
          mo: 'No se pueden actualizar los Tickets',
          error: `Los tickets ya estan en la ubicacion ${infoBin[0].nameNewBin}`,
        });
      } else {
        if ([10, 50].includes(+getAllVouchersToGroupVouchers[0]?.statusId)) {
          const trx = await WorkAreaTickets.startTransaction();
          try {
            for (let i = 0; i < getAllVouchersToGroupVouchers.length; i++) {
              updateLocation = await WorkAreaTickets.query()
                .update({
                  work_inventory_location_id: location,
                  is_company_notified: false,
                })
                .where(
                  'work_area_tickets.id',
                  getAllVouchersToGroupVouchers[i].ticketId
                );

              ticketLog = await WorkActivityLog.query().insert({
                employee_id: codeUser,
                work_area_id: area,
                module_name: 'ticket',
                module_id: getAllVouchersToGroupVouchers[i].ticketId,
                activity: 'TicketInventoryMoved',
                data: JSON.stringify({
                  old_bin:
                    getAllVouchersToGroupVouchers[i].nameBinOld ||
                    'Sin ubicacion',
                  new_bin: infoBin[0].nameNewBin,
                }),
              });
            }
            trx.commit();
          } catch (error) {
            trx.rollback();
            console.log(error);
          }

          dataSuccess.push({
            ok: true,
            uuidVoucher: uuidv4(),
            voucher,
            mo: 'Se actualizaron todas las MOS del grupo',
            error: `Se actualizo de ${getAllVouchersToGroupVouchers[0].nameBinOld} a ${infoBin[0].nameNewBin}`,
          });
        } else {
          dataError.push({
            ok: false,
            uuidVoucher: uuidv4(),
            voucher,
            mo: 'No se pueden actualizar los Tickets',
            error: 'Los tickets han sido completados',
          });
        }
      }
    } else {
      dataError.push({
        ok: false,
        uuidVoucher: uuidv4(),
        voucher,
        error: 'No es un voucher',
      });
    }

    return res.status(200).json({
      ok: true,
      dataError,
      dataSuccess,
      updateLocation,
      ticketLog,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

// change logic
export async function closeTickets(req: Request, res: Response) {
  const format1 = 'YYYY-MM-DD HH:mm:ss';
  const actualDate = new Date();

  try {
    const trx = await WorkAreaTickets.startTransaction();
    const { workAreaId, tickets } = req.body;

    if (!workAreaId || typeof workAreaId !== 'number' || isNaN(workAreaId)) {
      return res.status(400).json({
        ok: false,
        data: 'Codigo de area invalido',
      });
    }
    if (!tickets || !Array.isArray(tickets)) {
      return res.status(400).json({
        ok: false,
        data: 'Tickets invalidos',
      });
    }

    // obtenemos el id del estado completo del area
    const getWorkTicketStatuses = await WorkAreaTicketStatuses.query(trx)
      .where('work_area_id', workAreaId)
      .where('name', 'Completo')
      .select('id')
      .castTo<{ id: number }[]>();

    const idWorkTicketStatus = getWorkTicketStatuses[0].id;

    if (idWorkTicketStatus) {
      // cerramos el ticket
      let updateWorkTickets;
      for (let i = 0; i < tickets.length; i++) {
        if (!tickets[i] || typeof tickets[i] !== 'object') {
          return res.status(400).json({
            ok: false,
            data: 'Ticket invalido',
          });
        }
        const useTicketId = Number(tickets[i].id);
        if (!useTicketId || isNaN(useTicketId)) {
          return res.status(400).json({
            ok: false,
            data: 'Ticket id invalido',
          });
        }
        updateWorkTickets = await WorkAreaTickets.query(trx)
          .update({
            work_area_ticket_status_id: idWorkTicketStatus,
            is_company_notified: false,
            finished_at: dayjs(actualDate).format(format1),
          })
          .where('id', useTicketId);
      }
      trx.commit();
      if (updateWorkTickets > 0) {
        return res.status(200).json({
          ok: true,
          message: `[CLOSE] - Se cerró el ticket de la MO`,
        });
      } else {
        return res.status(400).json({
          ok: true,
          message: `[CLOSE] - No se cerró el ticket de la MO`,
        });
      }
    } else {
      return res.status(400).json({
        ok: true,
        message: `[CLOSE] - No se cerró el ticket de la MO, no se encontró el estatus completo del area`,
      });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

interface TicketSelected {
  id: number;
  voucher_group?: string;
  work_voucher_id?: number;
  mo_id?: number;
  voucherId?: number;
}

// (se usa para actualizar los datos desde la tabla de tickets)
export async function updateInfoTicket(req: Request, res: Response) {
  try {
    const {
      workAreaId,
      dataInputs: {
        ignoreTicket,
        expDate,
        groupsArea,
        lotsArea,
        linesAreas,
        location,
        nextArea,
        statusArea,
        commentVoucher,
      },
      ticketsSelected,
      codeUser,
    } = req.body;

    const format1 = 'YYYY-MM-DD HH:mm:ss';
    const actualDate = new Date();

    let updateNextArea;
    let updateExpFinishData;
    let updateLocation;
    let getWorkTicketStatuses;
    let updateWorkTickets;
    let ticketLog;
    let addNewComment;
    let scanTickets = false;

    if (!ticketsSelected || !Array.isArray(ticketsSelected)) {
      return res.status(400).json({
        ok: false,
        data: 'Tickets invalidos',
      });
    }
    const useTicketsSelected: TicketSelected[] = ticketsSelected;

    const getGroupsArea = await WorkAreaGroups.query()
      .where('work_area_id', +workAreaId)
      .where('update_customer', 0)
      .whereNotNull('barcode')
      .select('id', 'barcode')
      .castTo<
        {
          id: number;
          barcode: string;
        }[]
      >();

    const getLinesArea = await WorkAreaLines.query()
      .where('work_area_id', +workAreaId)
      .where('description', 'like', `%${codeUser}%`)
      .select('id', 'barcode')
      .first()
      .castTo<{
        id: number;
        barcode: string;
      }>();

    for (let i = 0; i < useTicketsSelected.length; i++) {
      const ticketSelected = useTicketsSelected[i];
      if (!ticketSelected || typeof ticketSelected !== 'object') {
        return res.status(400).json({
          ok: false,
          data: 'Ticket invalido',
        });
      }
      if (!ticketSelected.voucher_group) {
        const ticketId = Number(ticketSelected.id);
        if (!ticketId || isNaN(ticketId)) {
          return res.status(400).json({
            ok: false,
            data: 'Ticket id invalido',
          });
        }

        const moInfo = await MoNumber.query()
          .where('mo_id', +ticketSelected.mo_id)
          .select(
            'num',
            'mo_status',
            'po_numbers',
            'style',
            'quantity',
            'customer',
            'mo_barcode',
            'company_code'
          )
          .first()
          .castTo<{
            num: string;
            mo_status: string;
            po_numbers: string;
            style: string;
            quantity: string;
            customer: string;
            mo_barcode: string;
            company_code: number;
          }>();

        const infoTicket = await WorkAreaTickets.query()
          .join(
            'work_area_ticket_statuses',
            'work_area_tickets.work_area_ticket_status_id',
            'work_area_ticket_statuses.id'
          )
          .join(
            'work_areas',
            'work_area_tickets.work_area_id',
            'work_areas.work_area_id'
          )
          .leftJoin(
            'work_area_batches',
            'work_area_tickets.work_batch_id',
            'work_area_batches.id'
          )
          .where('work_area_tickets.id', ticketId)
          .select(
            {
              nameOld: 'work_area_ticket_statuses.name',
            },
            {
              batchId: 'work_area_tickets.work_batch_id',
            },
            {
              nameBatchOld: 'work_area_batches.name',
            },
            {
              idBatchOld: 'work_area_batches.id',
            },
            WorkInventoryBins.query()
              .where(
                'work_inventory_bins.id',
                ref('work_area_tickets.work_inventory_location_id')
              )
              .select('work_inventory_bins.name')
              .as('nameBinOld'),
            { colorHexOld: 'work_area_ticket_statuses.color_hex' },
            { expFinish: 'work_area_tickets.exp_finish_date' },
            { expWorkAreaLineId: 'work_area_tickets.exp_work_area_line_id' },
            {
              moTicketCreateGroupId:
                'work_area_tickets.mo_ticket_create_group_id',
            },
            WorkAreas.query()
              .where(
                'work_areas.work_area_id',
                ref('work_area_tickets.next_work_area_id')
              )
              .select('work_areas.area_name')
              .as('oldNextArea')
          )
          .castTo<
            {
              nameOld: string;
              batchId: number;
              nameBatchOld: string;
              idBatchOld: number;
              nameBinOld: string;
              colorHexOld: string;
              expFinish: string;
              expWorkAreaLineId: number;
              moTicketCreateGroupId: number;
              oldNextArea: string;
            }[]
          >();

        if (ignoreTicket) {
          if (ignoreTicket === 'SI') {
            if (
              !ticketSelected.work_voucher_id ||
              isNaN(Number(ticketSelected.work_voucher_id))
            ) {
              throw new Error('Codigo de grupo invalido');
            }
            await WorkVouchers.query()
              .update({
                ignore_next_area: true,
              })
              .where(
                'work_vouchers.id',
                '=',
                Number(ticketSelected.work_voucher_id)
              );
          } else if (ignoreTicket === 'NO') {
            if (
              !ticketSelected.work_voucher_id ||
              isNaN(Number(ticketSelected.work_voucher_id))
            ) {
              throw new Error('Codigo de grupo invalido');
            }
            await WorkVouchers.query()
              .update({
                ignore_next_area: false,
              })
              .where(
                'work_vouchers.id',
                '=',
                Number(ticketSelected.work_voucher_id)
              );
          } else {
            continue;
          }
        }

        if (commentVoucher) {
          addNewComment = await WorkNotes.query().insert({
            mo_id: ticketSelected.mo_id,
            work_area_ticket_id: ticketSelected.id,
            note: commentVoucher,
            employee_id: codeUser,
          });
        }

        if (linesAreas) {
          await WorkAreaTickets.query()
            .update({
              exp_work_area_line_id: linesAreas,
            })
            .where('work_area_tickets.id', '=', ticketId);

          ticketLog = await WorkActivityLog.query().insert({
            work_voucher_id: ticketSelected.voucherId,
            employee_id: codeUser,
            work_area_id: workAreaId,
            module_name: 'ticket',
            module_id: ticketSelected.id,
            activity: 'TicketFieldChanged',
            data: JSON.stringify({
              field_name: 'linea esperada',
              old_value: infoTicket[0].expWorkAreaLineId
                ? infoTicket[0].expWorkAreaLineId
                : 'N/A',
              new_value: linesAreas,
            }),
          });
        }

        if (groupsArea) {
          if (!groupsArea || isNaN(Number(groupsArea))) {
            throw new Error('Codigo de grupo invalido');
          }
          await WorkAreaTickets.query()
            .update({
              exp_work_area_group_id: Number(groupsArea),
            })
            .where('work_area_tickets.id', '=', ticketId);

          ticketLog = await WorkActivityLog.query().insert({
            work_voucher_id: ticketSelected.voucherId,
            employee_id: codeUser,
            work_area_id: workAreaId,
            module_name: 'ticket',
            module_id: ticketSelected.id,
            activity: 'TicketFieldChanged',
            data: JSON.stringify({
              field_name: 'grupo esperado',
              old_value: infoTicket[0].moTicketCreateGroupId
                ? infoTicket[0].moTicketCreateGroupId
                : 'N/A',
              new_value: groupsArea,
            }),
          });
        }

        if (statusArea) {
          if (!workAreaId || isNaN(Number(workAreaId))) {
            throw new Error('Codigo de area invalido');
          }
          if (!statusArea || isNaN(Number(statusArea))) {
            throw new Error('Codigo de status invalido');
          }

          // obtenemos el work_status_id del status del area
          getWorkTicketStatuses = await WorkAreaTicketStatuses.query()
            .where('work_area_id', Number(workAreaId))
            .where('id', Number(statusArea))
            .select('name', 'color_hex', 'work_status_id');

          // if el work_status_id es igual void cerramos el ticket
          if (getWorkTicketStatuses[0].work_status_id === 110) {
            scanTickets = true;

            updateWorkTickets = await WorkAreaTickets.query()
              .update({
                work_area_ticket_status_id: statusArea,
                // is_company_notified: 0,
                finished_at: dayjs(actualDate).format(format1),
              })
              .where('id', ticketId);

            ticketLog = await WorkActivityLog.query().insert({
              work_voucher_id: ticketSelected.voucherId,
              employee_id: codeUser,
              work_area_id: workAreaId,
              module_name: 'ticket',
              module_id: ticketSelected.id,
              activity: 'TicketStatusChanged',
              data: JSON.stringify({
                old_status_name: infoTicket[0].nameOld,
                old_color: infoTicket[0].colorHexOld,
                new_status_name: getWorkTicketStatuses[0].name,
                new_color: getWorkTicketStatuses[0].color_hex,
              }),
            });
          } else if (getWorkTicketStatuses[0].work_status_id === 100) {
            scanTickets = true;

            updateWorkTickets = await WorkAreaTickets.query()
              .update({
                work_area_ticket_status_id: statusArea,
                is_company_notified: false,
                finished_at: dayjs(actualDate).format(format1),
              })
              .where('id', +ticketSelected.id);

            ticketLog = await WorkActivityLog.query().insert({
              work_voucher_id: ticketSelected.voucherId,
              employee_id: codeUser,
              work_area_id: workAreaId,
              module_name: 'ticket',
              module_id: ticketSelected.id,
              activity: 'TicketStatusChanged',
              data: JSON.stringify({
                old_status_name: infoTicket[0].nameOld,
                old_color: infoTicket[0].colorHexOld,
                new_status_name: getWorkTicketStatuses[0].name,
                new_color: getWorkTicketStatuses[0].color_hex,
              }),
            });

            if (+workAreaId !== 9 && getGroupsArea.length > 0) {
              await createScan({
                mo_id: null,
                mo_barcode:
                  moInfo.company_code === 2
                    ? `A${moInfo.mo_barcode}`
                    : moInfo.mo_barcode,
                group_barcode: getGroupsArea[getGroupsArea.length - 1].barcode,
                quantity_reported: +moInfo.quantity,
                employee_id: +codeUser,
                work_area_line_id: getLinesArea ? getLinesArea.id : null,
                type_action: 'FINISH',
                work_area_group_id: null,
                work_area_id: null,
                partial_option: null,
                affected_units: null,
                work_ticket_id: null,
                update_customer: null,
                work_voucher_id: null,
              });
            }
          } else {
            updateWorkTickets = await WorkAreaTickets.query()
              .update({
                work_area_ticket_status_id: statusArea,
                is_company_notified: false,
              })
              .where('id', ticketSelected.id);

            ticketLog = await WorkActivityLog.query().insert({
              work_voucher_id: ticketSelected.voucherId,
              employee_id: codeUser,
              work_area_id: workAreaId,
              module_name: 'ticket',
              module_id: ticketSelected.id,
              activity: 'TicketStatusChanged',
              data: JSON.stringify({
                old_status_name: infoTicket[0].nameOld,
                old_color: infoTicket[0].colorHexOld,
                new_status_name: getWorkTicketStatuses[0].name,
                new_color: getWorkTicketStatuses[0].color_hex,
              }),
            });
          }
        }

        if (nextArea) {
          if (nextArea === 'NA') {
            updateNextArea = await WorkAreaTickets.query()
              .update({
                next_work_area_id: null,
              })
              .where('work_area_tickets.id', '=', ticketSelected.id);

            ticketLog = await WorkActivityLog.query().insert({
              work_voucher_id: ticketSelected.voucherId,
              employee_id: codeUser,
              work_area_id: workAreaId,
              module_name: 'ticket',
              module_id: ticketSelected.id,
              activity: 'TicketFieldChanged',
              data: JSON.stringify({
                field_name: 'siguiente area',
                old_value: infoTicket[0].oldNextArea,
                new_value: null,
              }),
            });
          } else {
            if (!nextArea || isNaN(Number(nextArea))) {
              throw new Error('Codigo de area invalido');
            }
            const infoArea = await WorkAreas.query()
              .where('work_areas.work_area_id', Number(nextArea))
              .select({ newAreaName: 'area_name' })
              .castTo<
                {
                  newAreaName: string;
                }[]
              >();

            updateNextArea = await WorkAreaTickets.query()
              .update({
                next_work_area_id: nextArea,
              })
              .where('work_area_tickets.id', '=', ticketSelected.id);

            ticketLog = await WorkActivityLog.query().insert({
              work_voucher_id: ticketSelected.voucherId,
              employee_id: codeUser,
              work_area_id: workAreaId,
              module_name: 'ticket',
              module_id: ticketSelected.id,
              activity: 'TicketFieldChanged',
              data: JSON.stringify({
                field_name: 'siguiente area',
                old_value: infoTicket[0].oldNextArea,
                new_value: infoArea[0].newAreaName,
              }),
            });
          }
        }

        if (expDate) {
          if (typeof expDate !== 'string') {
            throw new Error('Fecha invalida');
          }
          const dateFormat = dayjs(expDate).format('DD-MM-YYYY');

          updateExpFinishData = await WorkAreaTickets.query()
            .update({
              exp_finish_date: expDate,
            })
            .where('work_area_tickets.id', '=', ticketSelected.id);

          ticketLog = await WorkActivityLog.query().insert({
            work_voucher_id: ticketSelected.voucherId,
            employee_id: codeUser,
            work_area_id: workAreaId,
            module_name: 'ticket',
            module_id: ticketSelected.id,
            activity: 'TicketFieldChanged',
            data: JSON.stringify({
              field_name: 'fecha esperada a terminar',
              old_value: infoTicket[0].expFinish
                ? dayjs(infoTicket[0].expFinish).format(format1)
                : 'N/A',
              new_value: dateFormat,
            }),
          });
        }

        if (location) {
          if (location === 'NA') {
            updateLocation = await WorkAreaTickets.query()
              .update({
                work_inventory_location_id: null,
              })
              .where('work_area_tickets.id', '=', ticketSelected.id);

            ticketLog = await WorkActivityLog.query().insert({
              work_voucher_id: ticketSelected.voucherId,
              employee_id: codeUser,
              work_area_id: workAreaId,
              module_name: 'ticket',
              module_id: ticketSelected.id,
              activity: 'TicketInventoryMoved',
              data: JSON.stringify({
                old_bin: infoTicket[0].nameBinOld,
                new_bin: null,
              }),
            });
          } else {
            if (!location || isNaN(Number(location))) {
              throw new Error('Codigo de ubicacion invalido');
            }
            const infoBin = await WorkInventoryBins.query()
              .where('work_inventory_bins.id', Number(location))
              .select({ nameNewBin: 'work_inventory_bins.name' })
              .castTo<
                {
                  nameNewBin: string;
                }[]
              >();

            updateLocation = await WorkAreaTickets.query()
              .update({
                work_inventory_location_id: location,
              })
              .where('work_area_tickets.id', '=', ticketSelected.id);

            ticketLog = await WorkActivityLog.query().insert({
              work_voucher_id: ticketSelected.voucherId,
              employee_id: codeUser,
              work_area_id: workAreaId,
              module_name: 'ticket',
              module_id: ticketSelected.id,
              activity: 'TicketInventoryMoved',
              data: JSON.stringify({
                old_bin: infoTicket[0].nameBinOld,
                new_bin: infoBin[0].nameNewBin,
              }),
            });
          }
        }

        if (lotsArea) {
          if (!lotsArea || isNaN(Number(lotsArea))) {
            throw new Error('Codigo de lote invalido');
          }
          const useLotAreaId = Number(lotsArea);
          if (infoTicket[0].batchId) {
            const infoBatch = await WorkAreaBatches.query()
              .where('work_area_batches.id', useLotAreaId)
              .select({ nameLot: 'work_area_batches.name' })
              .castTo<
                {
                  nameLot: string;
                }[]
              >();
            updateLocation = await WorkAreaTickets.query()
              .update({
                work_batch_id: useLotAreaId,
              })
              .where('work_area_tickets.id', ticketSelected.id);

            await WorkActivityLog.query().insert({
              work_voucher_id: ticketSelected.voucherId,
              employee_id: codeUser,
              work_area_id: workAreaId,
              module_name: 'Lot',
              module_id: useLotAreaId,
              activity: 'ChangedTicketOfLot',
              data: JSON.stringify({
                value: ticketSelected.voucherId,
                old_lot: infoTicket[0].nameBatchOld,
                new_lot: infoBatch[0].nameLot,
              }),
            });

            await WorkActivityLog.query().insert({
              work_voucher_id: ticketSelected.voucherId,
              employee_id: codeUser,
              work_area_id: workAreaId,
              module_name: 'Lot',
              module_id: infoTicket[0].idBatchOld,
              activity: 'ChangedTicketOfLot',
              data: JSON.stringify({
                value: ticketSelected.voucherId,
                old_lot: infoTicket[0].nameBatchOld,
                new_lot: infoBatch[0].nameLot,
              }),
            });
          } else {
            const infoBatch = await WorkAreaBatches.query()
              .where('work_area_batches.id', useLotAreaId)
              .select({ nameLot: 'work_area_batches.name' })
              .castTo<
                {
                  nameLot: string;
                }[]
              >();

            updateLocation = await WorkAreaTickets.query()
              .update({
                work_batch_id: useLotAreaId,
              })
              .where('work_area_tickets.id', ticketSelected.id);

            ticketLog = await WorkActivityLog.query().insert({
              work_voucher_id: ticketSelected.voucherId,
              employee_id: codeUser,
              work_area_id: workAreaId,
              module_name: 'Lot',
              module_id: useLotAreaId,
              activity: 'AddedTicketToLot',
              data: JSON.stringify({
                value: ticketSelected.voucherId,
                old_lot: infoTicket[0].nameBatchOld,
                new_lot: infoBatch[0].nameLot,
              }),
            });
          }
        }
      }
    }

    return res.status(200).json({
      ok: true,
      scanTickets,
      updateNextArea: updateNextArea || null,
      updateExpFinishData: updateExpFinishData || null,
      updateLocation: updateLocation || null,
      updateWorkTickets: updateWorkTickets || null,
      addNewComment: addNewComment || null,
      ticketLog: ticketLog || null,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getLogOfTicket(req: Request, res: Response) {
  try {
    const { ticket } = req.body;
    if (!ticket || isNaN(Number(ticket))) {
      return res.status(400).json({
        ok: false,
        data: 'Ticket invalido',
      });
    }
    const useTicketId = Number(ticket);

    const getLogs = await WorkActivityLog.query()
      .select(
        'work_activity_log.id',
        'work_activity_log.activity',
        'work_activity_log.data',
        'work_activity_log.created_at',
        Employee.query()
          .where('employees.employee_id', ref('work_activity_log.employee_id'))
          .select('employees.first_name')
          .as('userName')
      )
      .where('work_activity_log.module_name', 'ticket')
      .where('work_activity_log.module_id', useTicketId)
      .orderBy('created_at', 'desc');

    return res.status(200).json({
      ok: true,
      data: getLogs,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getInfoOfTicket(req: Request, res: Response) {
  try {
    const { ticket, workAreaId } = req.body;
    if (!ticket || isNaN(Number(ticket))) {
      return res.status(400).json({
        ok: false,
        data: 'Ticket invalido',
      });
    }
    const useTicketId = Number(ticket);

    if (!workAreaId || isNaN(Number(workAreaId))) {
      return res.status(400).json({
        ok: false,
        data: 'Area invalida',
      });
    }
    const useWorkAreaId = Number(workAreaId);

    const getInfo = await WorkAreaTickets.query()
      .join(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .join(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id ',
        'work_voucher_types.id'
      )
      .join(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id ',
        'work_area_ticket_statuses.id'
      )
      .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
      .leftJoin(
        'work_voucher_groups',
        'work_vouchers.work_voucher_group_id',
        'work_voucher_groups.id'
      )
      .leftJoin(
        'work_inventory_bins',
        'work_area_tickets.work_inventory_location_id',
        'work_inventory_bins.id'
      )
      .select(
        { inventory: 'work_inventory_bins.name' },
        { group: 'work_voucher_groups.name' },
        { group_id: 'work_vouchers.work_voucher_group_id' },
        { voucherId: 'work_vouchers.id' },
        { isPrimary: 'work_vouchers.is_primary' },
        { mo_id: 'mo_numbers.mo_id' },
        { mo: 'mo_numbers.num' },
        { moExport: 'mo_numbers.sched_start' },
        { quantity: 'mo_numbers.quantity' },
        { order: 'mo_numbers.mo_order' },
        { style: 'mo_numbers.style' },
        { created_at: 'work_area_tickets.created_at' },
        { exp_finish_date: 'work_area_tickets.exp_finish_date' },
        { idTicketInfo: 'work_area_tickets.id' },
        { line_id: 'work_area_tickets.exp_work_area_line_id' },
        { repo: 'work_vouchers.is_repo' },
        { statusTicket: 'work_area_ticket_statuses.name' },
        { statusIdTicket: 'work_area_ticket_statuses.work_status_id' },
        WorkAreaTickets.query()
          .where(
            'work_area_tickets.work_voucher_id',
            ref('work_vouchers.merge_voucher_id')
          )
          .select('work_area_tickets.id')
          .limit(1)
          .as('idTicket'),
        WorkAreaTickets.query()
          .where(
            'work_area_tickets.work_voucher_id',
            ref('work_vouchers.merge_voucher_id')
          )
          .select('work_vouchers.merge_voucher_id')
          .limit(1)
          .as('mainVoucher'),
        WorkAreaTickets.query()
          .where(
            'work_area_tickets.work_voucher_id',
            ref('work_vouchers.merge_voucher_id')
          )
          .select('work_vouchers.mo_id')
          .limit(1)
          .as('moMainVoucher'),
        { voucherType: 'work_voucher_types.name' }
      )
      .where('work_area_tickets.id', useTicketId)
      .castTo<
        {
          inventory: string;
          group: string;
          group_id: number;
          voucherId: number;
          isPrimary: boolean;
          mo_id: number;
          mo: string;
          moExport: string;
          quantity: string;
          order: string;
          style: string;
          created_at: Date;
          exp_finish_date: Date;
          idTicketInfo: number;
          line_id: number;
          repo: boolean;
          statusTicket: string;
          statusIdTicket: number;
          idTicket?: number;
          mainVoucher?: number;
          moMainVoucher?: number;
          voucherType?: string;
        }[]
      >();

    const getInfoVoucher = await WorkVouchers.query()
      .join(
        'work_area_tickets',
        'work_vouchers.id',
        'work_area_tickets.work_voucher_id'
      )
      .where('work_area_tickets.work_area_id', useWorkAreaId)
      .where('work_vouchers.merge_voucher_id', getInfo[0].voucherId)
      .select(
        'work_area_tickets.id',
        { mo: 'work_vouchers.mo_id' },
        { voucher: 'work_vouchers.id' }
      )
      .castTo<
        {
          id: number;
          mo: number;
          voucher: number;
        }[]
      >();

    const getAllScanOfTheMO = await MoScans.query()
      .leftJoin('mo_numbers', 'mo_scans.mo_id', 'mo_numbers.mo_id')
      .leftJoin(
        'work_areas',
        'mo_scans.work_area_id',
        'work_areas.work_area_id'
      )
      .select([
        { mo: 'mo_numbers.num' },
        { client: 'mo_numbers.customer' },
        { date_scan: 'mo_scans.sew' },
        { supervisor: 'mo_scans.supervisor' },
        { area: 'work_areas.area_name' },
        { scan_id: 'mo_scans.scan_id' },
      ])
      .orderBy('mo_scans.scan_id', 'desc')
      .where('mo_scans.mo_id', getInfo[0].mo_id)
      .whereNull('mo_scans.removed_at')
      .castTo<
        {
          mo: string;
          client: string;
          date_scan: Date;
          supervisor: string;
          area: string;
          scan_id: number;
        }[]
      >();

    return res.status(200).json({
      ok: true,
      data: { getInfo, getInfoVoucher, getAllScanOfTheMO },
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getCommentsOfTicket(req: Request, res: Response) {
  try {
    const { ticket, mo } = req.body;
    const dataResponse: {
      id: number;
      user: string;
      note: string;
      created_at: Date;
    }[] = [];

    if (!ticket || isNaN(Number(ticket))) {
      return res.status(400).json({
        ok: false,
        data: 'Ticket invalido',
      });
    }
    const useWorkAreaTicketId = Number(ticket);

    if (!mo || isNaN(Number(mo))) {
      return res.status(400).json({
        ok: false,
        data: 'Mo invalido',
      });
    }
    const useMoId = Number(mo);

    const getCommetsTicket = await WorkAreaTickets.query()
      .join(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .join(
        'work_notes',
        'work_area_tickets.id',
        'work_notes.work_area_ticket_id'
      )
      .join('employees', 'work_notes.employee_id ', 'employees.employee_id')
      .join('mo_numbers', 'work_notes.mo_id', 'mo_numbers.mo_id')
      .join(
        'work_areas',
        'work_area_tickets.work_area_id',
        'work_areas.work_area_id'
      )
      .select(
        { id: 'work_notes.id' },
        { user: 'employees.first_name' },
        { note: 'work_notes.note' },
        { voucher: 'work_vouchers.id' },
        { area: 'work_areas.area_name' },
        { created_at: 'work_notes.created_at' }
      )
      .where('work_area_tickets.id', useWorkAreaTicketId)
      .orderBy('work_notes.created_at', 'desc')
      .castTo<
        {
          id: number;
          user: string;
          note: string;
          voucher?: number;
          area?: string;
          created_at: Date;
        }[]
      >();

    const getCommetsMo = await MoNumber.query()
      .join('work_notes', 'mo_numbers.mo_id', 'work_notes.mo_id')
      .join('employees', 'work_notes.employee_id ', 'employees.employee_id')
      .select(
        { id: 'work_notes.id' },
        { user: 'employees.first_name' },
        { note: 'work_notes.note' },
        { created_at: 'work_notes.created_at' }
      )
      .where('mo_numbers.mo_id', useMoId)
      .orderBy('work_notes.created_at', 'desc')
      .castTo<
        {
          id: number;
          user: string;
          note: string;
          created_at: Date;
        }[]
      >();

    const notesId = getCommetsTicket.map((note) => note.id);

    getCommetsMo.forEach((noteMo) => {
      if (!notesId.includes(noteMo.id)) {
        dataResponse.push(noteMo);
      }
    });

    return res.status(200).json({
      ok: true,
      // // TODO: overridding fields, is this right
      data: [...getCommetsTicket, ...dataResponse],
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getAllWorkVoucherTypes(req: Request, res: Response) {
  try {
    const { workAreaId } = req.query;
    const parseVoucherTypes: {
      name: string;
      id: number;
      updateClient: boolean;
    }[] = [];
    if (!workAreaId || isNaN(Number(workAreaId))) {
      return res.status(400).json({
        ok: false,
        data: 'Area invalida',
      });
    }
    const useWorkAreaId = Number(workAreaId);

    const getTypes = await WorkTypeVoucherTypes.query()
      .join(
        'work_types',
        'work_type_voucher_types.work_type_id',
        'work_types.id'
      )
      .join(
        'work_voucher_types',
        'work_type_voucher_types.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .join('work_areas', 'work_types.id', 'work_areas.work_type_id')
      .where('work_areas.work_area_id', useWorkAreaId)
      .select('work_voucher_types.name', 'work_voucher_types.id')
      .castTo<
        {
          name: string;
          id: number;
        }[]
      >();

    if (getTypes.length > 0) {
      for (let i = 0; i < getTypes.length; i++) {
        const searchTypeMain = await WorkVoucherCompanyIgnoreUpdates.query()
          .join(
            'work_types',
            'work_voucher_company_ignore_updates.work_type_id',
            'work_types.id'
          )
          .join(
            'work_voucher_types',
            'work_voucher_company_ignore_updates.work_voucher_type_id',
            'work_voucher_types.id'
          )
          .join('work_areas', 'work_types.id', 'work_areas.work_type_id')
          .where(
            'work_voucher_company_ignore_updates.work_voucher_type_id',
            getTypes[i].id
          )
          .select('work_voucher_types.id')
          .as('updateClient')
          // TODO: should use .first()
          .castTo<
            {
              id: number;
            }[]
          >();

        if (searchTypeMain.length > 0) {
          parseVoucherTypes.push({
            name: getTypes[i].name,
            id: getTypes[i].id,
            updateClient: !!searchTypeMain[0].id,
          });
        } else {
          parseVoucherTypes.push({
            name: getTypes[i].name,
            id: getTypes[i].id,
            updateClient: false,
          });
        }
      }

      return res.status(200).json({
        ok: true,
        data: parseVoucherTypes,
      });
    }

    return res.status(200).json({
      ok: true,
      data: parseVoucherTypes,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getBuildings(req: Request, res: Response) {
  try {
    const getBuild = await Buildings.query();
    return res.status(200).json({
      ok: true,
      data: getBuild,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function addComment(req: Request, res: Response) {
  try {
    const {
      mo,
      ticket = null,
      comment,
      employee,
      commentId,
      actionType,
      area,
    } = req.body;

    if (actionType === 'edit') {
      if (!commentId || isNaN(Number(commentId))) {
        return res.status(400).json({
          ok: false,
          data: 'Comentario invalido',
        });
      }
      const useCommentId = Number(commentId);
      const getCommentTicket = await WorkNotes.query()
        .where('id', useCommentId)
        .select('work_notes.note')
        .castTo<
          {
            note: string;
          }[]
        >();

      const editComment = await WorkNotes.query()
        .update({
          note: comment,
        })
        .where('id', useCommentId);

      if (editComment) {
        const ticketLog = await WorkActivityLog.query().insert({
          employee_id: employee,
          work_area_id: area,
          module_name: 'ticket',
          module_id: ticket,
          activity: 'UpdateComment',
          data: JSON.stringify({
            oldComment: getCommentTicket[0].note,
            newComment: comment,
          }),
        });

        return res.status(200).json({ ok: true, data: ticketLog });
      }
    } else {
      const addNewComment = await WorkNotes.query().insert({
        mo_id: mo,
        work_area_ticket_id: ticket,
        note: comment,
        employee_id: employee,
      });

      if (addNewComment) {
        return res.status(200).json({ ok: true });
      }
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function updateStatusTicketLog(req: Request, res: Response) {
  const { valueInput, ticketId, codeUser, area } = req.body;

  const format = 'YYYY-MM-DD HH:mm:ss';
  const actualDate = new Date();

  if (!ticketId || isNaN(Number(ticketId))) {
    return res.status(400).json({
      ok: false,
      data: 'Ticket invalido',
    });
  }
  const useTicketId = Number(ticketId);

  if (!valueInput || !valueInput.value || isNaN(Number(valueInput.value))) {
    return res.status(400).json({
      ok: false,
      data: 'Status invalido',
    });
  }
  const useTicketStatusId = Number(valueInput.value);

  try {
    const getOldDataTicket = await WorkAreaTickets.query()
      .join(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id ',
        'work_area_ticket_statuses.id'
      )
      .where('work_area_tickets.id', useTicketId)
      .select(
        'work_area_tickets.work_voucher_id',
        'work_area_ticket_statuses.name',
        'work_area_ticket_statuses.color_hex'
      )
      .castTo<
        {
          work_voucher_id: number;
          name: string;
          color_hex: string;
        }[]
      >();

    const ticketOtherArea = await WorkAreaTickets.query()
      .join(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .where('work_area_tickets.id', '<>', useTicketId)
      .where('work_vouchers.id', getOldDataTicket[0].work_voucher_id)
      .select('work_area_tickets.id')
      .castTo<
        {
          id: number;
        }[]
      >();

    const getColorNewStatus = await WorkAreaTicketStatuses.query().where(
      'work_area_ticket_statuses.id',
      useTicketStatusId
    );

    if (ticketOtherArea.length > 0) {
      return res
        .status(304)
        .json({ ok: false, message: 'Ya existe un ticket en otra area' });
    }
    await WorkAreaTickets.query()
      .update({
        work_area_ticket_status_id: valueInput.value,
        is_company_notified: false,
        finished_at: [100, 105, 110].includes(
          getColorNewStatus[0].work_status_id
        )
          ? dayjs(actualDate).format(format)
          : null,
      })

      .where('id', useTicketId);

    await WorkActivityLog.query().insert({
      employee_id: codeUser,
      work_area_id: area,
      module_name: 'ticket',
      module_id: ticketId,
      activity: 'TicketStatusChanged',
      data: JSON.stringify({
        old_status_name: getOldDataTicket[0].name,
        old_color: getOldDataTicket[0].color_hex,
        new_status_name: valueInput.label,
        new_color: getColorNewStatus[0].color_hex,
      }),
    });

    return res
      .status(200)
      .json({ ok: true, message: 'Se actualizo correctamente' });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function getTicketCompleteArea(req: Request, res: Response) {
  const { area } = req.body;
  if (!area || isNaN(Number(area))) {
    return res.status(400).json({
      ok: false,
      data: 'Area invalida',
    });
  }
  const useWorkAreaId = Number(area);
  try {
    const ticketCompleteArea = await WorkAreaTickets.query()
      .leftJoin(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
      .leftJoin(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id',
        'work_area_ticket_statuses.id'
      )
      .where('work_vouchers.ignore_next_area', false)
      .where('work_area_tickets.work_area_id', useWorkAreaId)
      .where('work_area_ticket_statuses.work_status_id', '<>', 110)
      .whereNotNull('work_area_tickets.finished_at')
      .select(
        'mo_numbers.mo_id',
        'mo_numbers.order_type',
        'mo_numbers.mo_status',
        'mo_numbers.po_number',
        'mo_numbers.required_date',
        'mo_numbers.num',
        'mo_numbers.mo_order',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.style_category',
        'mo_numbers.ItemDescription8',
        'mo_numbers.customer',
        'mo_numbers.company_code',
        'work_area_tickets.work_area_id',
        { work_voucher_id: 'work_vouchers.id' },
        { work_area_ticket_id: 'work_area_tickets.id' },
        { status_name: 'work_area_ticket_statuses.name' }
      );
    return res.status(200).json({ ok: true, data: ticketCompleteArea });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function getTicketToReceive(req: Request, res: Response) {
  const { area } = req.body;
  if (!area || isNaN(Number(area))) {
    return res.status(400).json({
      ok: false,
      data: 'Area invalida',
    });
  }
  const useWorkAreaId = Number(area);

  try {
    const ticketToReceive = await WorkAreaTickets.query()
      .leftJoin(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .leftJoin(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
      .join(
        'work_areas',
        'work_area_tickets.work_area_id',
        'work_areas.work_area_id'
      )
      .leftJoin(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id',
        'work_area_ticket_statuses.id'
      )
      .join(
        'work_statuses',
        'work_area_ticket_statuses.work_status_id',
        'work_statuses.id'
      )
      .where('work_vouchers.ignore_next_area', false)
      .where('work_statuses.id', '<>', '110')
      .where('work_area_tickets.next_work_area_id', useWorkAreaId)
      .whereNotNull('work_area_tickets.finished_at')
      .select(
        WorkNotes.query()
          .where('work_notes.work_area_ticket_id', ref('work_area_tickets.id'))
          .orderBy('work_notes.id', 'desc')
          .limit(1)
          .select('work_notes.note')
          .as('lastNote'),
        'mo_numbers.mo_id',
        'mo_numbers.order_type',
        'mo_numbers.mo_status',
        'mo_numbers.po_number',
        'mo_numbers.required_date',
        'mo_numbers.num',
        'mo_numbers.mo_order',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.style_category',
        'mo_numbers.ItemDescription8',
        'mo_numbers.customer',
        'mo_numbers.company_code',
        'work_area_tickets.work_area_id',
        'work_area_tickets.finished_at',
        { work_voucher_id: 'work_vouchers.id' },
        { work_voucher_type_id: 'work_vouchers.work_voucher_type_id' },
        { work_voucher_type: 'work_voucher_types.name' },
        { work_area_ticket_id: 'work_area_tickets.id' },
        { work_area_name: 'work_areas.area_name' },
        { status_name: 'work_area_ticket_statuses.name' }
      );
    return res.status(200).json({ ok: true, data: ticketToReceive });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function createNewTicketArea(req: Request, res: Response) {
  try {
    const { voucher, workAreaId, codeUser } = req.body;
    const format1 = 'YYYY-MM-DD HH:mm:ss';
    const actualDate = new Date();

    if (!voucher || typeof voucher !== 'string') {
      return res.status(400).json({
        ok: false,
        data: 'Voucher invalido',
      });
    }
    if (!workAreaId || isNaN(Number(workAreaId))) {
      return res.status(400).json({
        ok: false,
        data: 'Area invalida',
      });
    }
    const useWorkAreaId = Number(workAreaId);

    const dataMiddlewares = {
      workTicketStatuses: req.body.workTicketStatuses,
    };

    if (voucher.startsWith('MEVB')) {
      // obtenermos el voucherId
      const voucherId = Number(voucher.substr(4));
      if (isNaN(voucherId)) {
        return res.status(400).json({
          ok: false,
          data: 'Voucher invalido',
        });
      }

      // verificamos que no exista un ticket en el area con el voucher
      const searchTicketInArea = await WorkAreaTickets.query()
        .where('work_area_tickets.work_voucher_id', voucherId)
        .where('work_area_tickets.work_area_id', useWorkAreaId);

      if (searchTicketInArea.length > 0) {
        return res
          .status(202)
          .json({ ok: false, data: 'Ya existe en tu area' });
      } else {
        // Obtenemos la mo del voucher
        const getMoNumber = await WorkVouchers.query()
          .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
          .where('work_vouchers.id', voucherId)
          .select('mo_numbers.num')
          .castTo<
            {
              num: string;
            }[]
          >();

        // obtenemos el ultimo ticket creado con el voucher
        const getLastTicketInfo = await WorkAreaTickets.query()
          .select('work_area_tickets.id', {
            prevArea: 'work_area_tickets.work_area_id',
          })
          .where('work_area_tickets.work_voucher_id', voucherId)
          .limit(1)
          .orderBy('work_area_tickets.id', 'desc')
          .castTo<
            {
              id: number;
              prevArea: number;
            }[]
          >();

        // obtenemos el status completo del area anterior
        const getStatusCompletoPrevArea = await WorkAreaTicketStatuses.query()
          .where('work_area_id', getLastTicketInfo[0].prevArea)
          .where('work_status_id', 100)
          .limit(1)
          .select('id');

        // creamos el nuevo ticket en el area
        const ticketArea = await WorkAreaTickets.query().insert({
          work_area_id: workAreaId,
          work_voucher_id: voucherId,
          made_by_mo_scan: false,
          notify_company: true,
          is_company_notified: false,
          work_inventory_location_id: null,
          prev_work_area_id: getLastTicketInfo[0].prevArea,
          work_area_ticket_status_id: dataMiddlewares.workTicketStatuses,
        });

        // log del nuevo ticket creado
        await WorkActivityLog.query().insert({
          employee_id: codeUser,
          work_area_id: workAreaId,
          module_name: 'ticket',
          module_id: ticketArea.id,
          activity: 'TicketCreated',
          data: JSON.stringify({}),
        });

        // cerramos el ticket del area anterior
        const ticketPrevArea = await WorkAreaTickets.query()
          .update({
            work_area_ticket_status_id: getStatusCompletoPrevArea[0].id,
            is_company_notified: false,
            finished_at: dayjs(actualDate).format(format1),
          })
          .where('work_area_tickets.id', getLastTicketInfo[0].id);

        if (ticketArea && ticketPrevArea) {
          return res.status(200).json({
            ok: true,
            dataSuccess: {
              voucher: voucherId,
              ticket: ticketArea.id,
              mo: getMoNumber[0]?.num,
            },
          });
        }
      }
    } else if (voucher.startsWith('MEPB')) {
      // obtenermos el voucherId
      const voucherId = await WorkVouchers.query()
        .select('id')
        .where('barcode', voucher)
        .castTo<
          {
            id: number;
          }[]
        >();

      // verificamos que no exista un ticket en el area con el voucher
      const searchTicketInArea = await WorkAreaTickets.query()
        .where('work_area_tickets.work_voucher_id', voucherId[0].id)
        .where('work_area_tickets.work_area_id', useWorkAreaId);

      if (searchTicketInArea.length > 0) {
        return res
          .status(202)
          .json({ ok: false, data: 'Ya existe en tu area' });
      } else {
        // Obtenemos la mo del voucher
        const getMoNumber = await WorkVouchers.query()
          .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
          .where('work_vouchers.id', voucherId[0].id)
          .select('mo_numbers.num')
          .castTo<
            {
              num: string;
            }[]
          >();

        // obtenemos el ultimo ticket creado con el voucher
        const getLastTicketInfo = await WorkAreaTickets.query()
          .select('work_area_tickets.id', {
            prevArea: 'work_area_tickets.work_area_id',
          })
          .where('work_area_tickets.work_voucher_id', voucherId[0].id)
          .limit(1)
          .orderBy('work_area_tickets.id', 'desc')
          .castTo<
            {
              id: number;
              prevArea: number;
            }[]
          >();

        // obtenemos el status completo del area anterior
        const getStatusCompletoPrevArea = await WorkAreaTicketStatuses.query()
          .where('work_area_id', getLastTicketInfo[0].prevArea)
          .where('work_status_id', 100)
          .limit(1)
          .select('id');

        // creamos el nuevo ticket en el area
        const ticketArea = await WorkAreaTickets.query().insert({
          work_area_id: workAreaId,
          work_voucher_id: voucherId[0].id,
          made_by_mo_scan: false,
          notify_company: true,
          is_company_notified: false,
          work_inventory_location_id: null,
          prev_work_area_id: getLastTicketInfo[0].prevArea,
          work_area_ticket_status_id: dataMiddlewares.workTicketStatuses,
        });

        // log del nuevo ticket creado
        await WorkActivityLog.query().insert({
          employee_id: codeUser,
          work_area_id: workAreaId,
          module_name: 'ticket',
          module_id: ticketArea.id,
          activity: 'TicketCreated',
          data: JSON.stringify({}),
        });

        // cerramos el ticket del area anterior
        const ticketPrevArea = await WorkAreaTickets.query()
          .update({
            work_area_ticket_status_id: getStatusCompletoPrevArea[0].id,
            is_company_notified: false,
            finished_at: dayjs(actualDate).format(format1),
          })
          .where('work_area_tickets.id', getLastTicketInfo[0].id);

        if (ticketArea && ticketPrevArea) {
          return res.status(200).json({
            ok: true,
            dataSuccess: {
              voucher: voucherId[0].id,
              ticket: ticketArea.id,
              mo: getMoNumber[0]?.num,
            },
          });
        }
      }
    } else {
      return res.status(500).json({
        ok: false,
        message: 'No es un voucher',
      });
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({ ok: false });
  }
}

export async function getTicketToReceiveNotComplete(
  req: Request,
  res: Response
) {
  const { area } = req.body;
  if (!area || isNaN(Number(area))) {
    return res.status(400).json({
      ok: false,
      data: 'Area invalida',
    });
  }
  const useWorkAreaId = Number(area);
  try {
    const ticketToReceive = await WorkAreaTickets.query()
      .leftJoin(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
      .join(
        'work_areas',
        'work_area_tickets.work_area_id',
        'work_areas.work_area_id'
      )
      .leftJoin(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id',
        'work_area_ticket_statuses.id'
      )
      .where('work_vouchers.ignore_next_area', false)
      .where('work_area_tickets.next_work_area_id', useWorkAreaId)
      .whereNull('work_area_tickets.finished_at')
      .select(
        WorkNotes.query()
          .where('work_notes.work_area_ticket_id', ref('work_area_tickets.id'))
          .orderBy('work_notes.id', 'desc')
          .limit(1)
          .select('work_notes.note')
          .as('lastNote'),
        'mo_numbers.mo_id',
        'mo_numbers.order_type',
        'mo_numbers.mo_status',
        'mo_numbers.po_number',
        'mo_numbers.required_date',
        'mo_numbers.num',
        'mo_numbers.mo_order',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.style_category',
        'mo_numbers.ItemDescription8',
        'mo_numbers.customer',
        'mo_numbers.company_code',
        'work_area_tickets.work_area_id',
        { work_voucher_id: 'work_vouchers.id' },
        { work_area_ticket_id: 'work_area_tickets.id' },
        { work_area_name: 'work_areas.area_name' },
        { status_name: 'work_area_ticket_statuses.name' }
      );

    return res.status(200).json({ ok: true, data: ticketToReceive });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

interface CreateNewTicketVouchersMerge {
  main: boolean;
  value: number; // work_voucher_id
}

export async function createNewTickets(req: Request, res: Response) {
  try {
    const { area, itemsSelected, dataInputs, codeUser, vouchersMerge } =
      req.body;
    const {
      ticketAccion,
      statusArea,
      groupsArea,
      linesAreas,
      nextArea,
      location,
      expDate,
      commentVoucher,
    } = dataInputs;
    let updateTicketArea;

    if (!area || isNaN(Number(area))) {
      return res.status(400).json({
        ok: false,
        data: 'Area invalida',
      });
    }
    const useWorkAreaId = Number(area);

    if (!itemsSelected || !Array.isArray(itemsSelected)) {
      return res.status(400).json({
        ok: false,
        data: 'Items selectado invalido',
      });
    }
    const useItemsSelected: {
      mo_id?: number | null;
      work_voucher_id?: number | null;
      work_voucher_type_id?: number | null;
      work_area_id?: number | null;
      work_area_ticket_id?: number | null;
    }[] = itemsSelected;

    if (vouchersMerge && !Array.isArray(vouchersMerge)) {
      return res.status(400).json({
        ok: false,
        data: 'Voucher invalido',
      });
    }
    const useVouchersMerge: CreateNewTicketVouchersMerge[] = vouchersMerge;

    const format = 'YYYY-MM-DD HH:mm:ss';
    const actualDate = new Date();

    // TODO: duplicate actions for each if.  should put into a function to reduce code
    if (+ticketAccion === 1) {
      for (let i = 0; i < useItemsSelected.length; i++) {
        // Buscamos el estado completo del area
        const completeStatusArea = await WorkAreaTicketStatuses.query()
          .where('work_area_ticket_statuses.work_area_id', useWorkAreaId)
          .where('work_area_ticket_statuses.work_status_id', 100)
          .orderBy('work_area_ticket_statuses.sequence')
          .limit(1)
          .select('work_area_ticket_statuses.id')
          .castTo<
            {
              id: number;
            }[]
          >();

        // voucher merge
        const voucherMerge = await WorkVouchers.query()
          .join(
            'work_area_tickets',
            'work_vouchers.id',
            'work_area_tickets.work_voucher_id'
          )
          .leftJoin(
            'work_area_ticket_statuses',
            'work_area_tickets.work_area_ticket_status_id',
            'work_area_ticket_statuses.id'
          )
          .leftJoin(
            'work_statuses',
            'work_area_ticket_statuses.work_status_id',
            'work_statuses.id'
          )
          .whereNotIn('work_statuses.id', ['100', '105', '110'])
          .where('work_vouchers.mo_id', useItemsSelected[i].mo_id)
          .where(
            'work_vouchers.work_voucher_type_id',
            useItemsSelected[i].work_voucher_type_id
          )
          .where('work_area_tickets.work_area_id', useWorkAreaId)
          .select('work_vouchers.id')
          .castTo<{ id: number }[]>();

        // Evaluamos si existe voucher en nuestra area con el mo y tipo de voucher entrante
        if (voucherMerge.length > 0) {
          // Actualizamos el voucher del area, colocando el voucher actual como principal
          await WorkVouchers.query()
            .update({
              merge_voucher_id: useItemsSelected[i].work_voucher_id,
            })
            .where('work_vouchers.id', voucherMerge[0].id);

          // Actualizamos el ticket del area, cerrando el ticket del voucher existente
          await WorkAreaTickets.query()
            .update({
              work_area_ticket_status_id: completeStatusArea[0].id,
              is_company_notified: false,
              finished_at: dayjs(actualDate).format(format),
            })
            .where('work_area_tickets.work_voucher_id', voucherMerge[0].id)
            .where('work_area_tickets.work_area_id', useWorkAreaId);

          // Creamos el nuevo ticket con la infoRmación enviada y el voucher actual
          const ticket = await WorkAreaTickets.query().insert({
            work_area_id: area,
            work_voucher_id: useItemsSelected[i].work_voucher_id,
            made_by_mo_scan: false,
            notify_company: true,
            is_company_notified: false,
            exp_finish_date: expDate ? expDate : null,
            exp_work_area_line_id: linesAreas ? linesAreas : null,
            exp_work_area_group_id: groupsArea ? groupsArea : null,
            work_inventory_location_id: location ? location : null,
            prev_work_area_id: useItemsSelected[i].work_area_id,
            work_area_ticket_status_id: statusArea,
            next_work_area_id: nextArea ? nextArea : null,
          });

          if (ticket) {
            updateTicketArea = await WorkAreaTickets.query()
              .update({
                next_work_area_id: null,
              })
              .where(
                'work_area_tickets.id',
                useItemsSelected[i].work_area_ticket_id
              );

            if (commentVoucher) {
              await WorkNotes.query().insert({
                mo_id: useItemsSelected[i].mo_id,
                work_area_ticket_id: ticket.id,
                note: commentVoucher,
                employee_id: codeUser,
              });
            }

            await WorkActivityLog.query().insert({
              work_voucher_id: useItemsSelected[i].work_voucher_id,
              work_area_id: area,
              employee_id: codeUser,
              module_name: 'ticket',
              module_id: ticket.id,
              activity: 'TicketCreated',
              data: JSON.stringify({}),
            });
            // merge
            if (useVouchersMerge?.length > 1) {
              const searchMainVoucher = useVouchersMerge.find(
                (voucher) => voucher?.main === true
              );

              const getVouchersMerge = useVouchersMerge.filter(
                (voucher) => voucher.main === false
              );

              for (let i = 0; i < getVouchersMerge.length; i++) {
                //vouchers
                await WorkVouchers.query()
                  .update({
                    merge_voucher_id: searchMainVoucher.value,
                  })
                  .where('work_vouchers.id', getVouchersMerge[i].value);

                // tickets
                await WorkAreaTickets.query()
                  .update({
                    work_area_ticket_status_id: completeStatusArea[0].id,
                    is_company_notified: false,
                    finished_at: dayjs(actualDate).format(format),
                  })
                  .where(
                    'work_area_tickets.work_voucher_id',
                    getVouchersMerge[i].value
                  )
                  .where('work_area_tickets.work_area_id', useWorkAreaId);
              }
            }
          }
        } else {
          const ticket = await WorkAreaTickets.query().insert({
            work_area_id: area,
            work_voucher_id: useItemsSelected[i].work_voucher_id,
            made_by_mo_scan: false,
            notify_company: true,
            is_company_notified: false,
            exp_finish_date: expDate ? expDate : null,
            exp_work_area_line_id: linesAreas ? linesAreas : null,
            exp_work_area_group_id: groupsArea ? groupsArea : null,
            work_inventory_location_id: location ? location : null,
            prev_work_area_id: useItemsSelected[i].work_area_id,
            work_area_ticket_status_id: statusArea,
            next_work_area_id: nextArea ? nextArea : null,
          });
          if (ticket) {
            updateTicketArea = await WorkAreaTickets.query()
              .update({
                next_work_area_id: null,
              })
              .where(
                'work_area_tickets.id',
                useItemsSelected[i].work_area_ticket_id
              );

            if (commentVoucher) {
              await WorkNotes.query().insert({
                mo_id: useItemsSelected[i].mo_id,
                work_area_ticket_id: ticket.id,
                note: commentVoucher,
                employee_id: codeUser,
              });
            }

            await WorkActivityLog.query().insert({
              work_voucher_id: useItemsSelected[i].work_voucher_id,
              work_area_id: area,
              employee_id: codeUser,
              module_name: 'ticket',
              module_id: ticket.id,
              activity: 'TicketCreated',
              data: JSON.stringify({}),
            });

            if (useVouchersMerge?.length > 1) {
              const searchMainVoucher = useVouchersMerge.find(
                (voucher) => voucher?.main === true
              );

              const getVouchersMerge = useVouchersMerge.filter(
                (voucher) => voucher?.main === false
              );

              for (let i = 0; i < getVouchersMerge.length; i++) {
                //vouchers
                await WorkVouchers.query()
                  .update({
                    merge_voucher_id: searchMainVoucher.value,
                  })
                  .where('work_vouchers.id', getVouchersMerge[i].value);

                // tickets
                await WorkAreaTickets.query()
                  .update({
                    work_area_ticket_status_id: completeStatusArea[0].id,
                    is_company_notified: false,
                    finished_at: dayjs(actualDate).format(format),
                  })
                  .where(
                    'work_area_tickets.work_voucher_id',
                    getVouchersMerge[i].value
                  )
                  .where('work_area_tickets.work_area_id', useWorkAreaId);
              }
            }
          }
        }
      }
    }

    if (+ticketAccion === 2) {
      for (let i = 0; i < useItemsSelected.length; i++) {
        // Buscamos el estado completo del area
        const completeStatusArea = await WorkAreaTicketStatuses.query()
          .where('work_area_ticket_statuses.work_area_id', useWorkAreaId)
          .where('work_area_ticket_statuses.work_status_id', 100)
          .orderBy('work_area_ticket_statuses.sequence')
          .limit(1)
          .select('work_area_ticket_statuses.id')
          .castTo<
            {
              id: number;
            }[]
          >();

        // voucher merge
        const voucherArea = await WorkVouchers.query()
          .join(
            'work_area_tickets',
            'work_vouchers.id',
            'work_area_tickets.work_voucher_id'
          )
          .leftJoin(
            'work_area_ticket_statuses',
            'work_area_tickets.work_area_ticket_status_id',
            'work_area_ticket_statuses.id'
          )
          .leftJoin(
            'work_statuses',
            'work_area_ticket_statuses.work_status_id',
            'work_statuses.id'
          )
          .whereNotIn('work_statuses.id', ['100', '105', '110'])
          .where('work_vouchers.mo_id', useItemsSelected[i].mo_id)
          .where(
            'work_vouchers.work_voucher_type_id',
            useItemsSelected[i].work_voucher_type_id
          )
          .where('work_area_tickets.work_area_id', useWorkAreaId)
          .select('work_vouchers.id')
          .castTo<{ id: number }[]>();

        // Evaluamos si existe voucher en nuestra area con el mo y tipo de voucher entrante
        if (voucherArea.length > 0) {
          // Actualizamos el voucher del area, colocando el voucher existente como principal
          await WorkVouchers.query()
            .update({
              merge_voucher_id: voucherArea[0].id,
            })
            .where('work_vouchers.id', useItemsSelected[i].work_voucher_id);

          // Actualizamos el ticket del area, cerrando el ticket del voucher actual
          await WorkAreaTickets.query()
            .update({
              work_area_ticket_status_id: completeStatusArea[0].id,
              is_company_notified: false,
              finished_at: dayjs(actualDate).format(format),
            })
            .where('work_area_tickets.work_voucher_id', voucherArea[0].id)
            .where('work_area_tickets.work_area_id', useWorkAreaId);

          // Creamos el nuevo ticket con la información enviada y el voucher existente
          const ticket = await WorkAreaTickets.query().insert({
            work_area_id: area,
            work_voucher_id: voucherArea[0].id,
            made_by_mo_scan: false,
            notify_company: true,
            is_company_notified: false,
            exp_finish_date: expDate ? expDate : null,
            exp_work_area_line_id: linesAreas ? linesAreas : null,
            exp_work_area_group_id: groupsArea ? groupsArea : null,
            work_inventory_location_id: location ? location : null,
            prev_work_area_id: useItemsSelected[i].work_area_id,
            work_area_ticket_status_id: statusArea,
            next_work_area_id: nextArea ? nextArea : null,
          });
          if (ticket) {
            updateTicketArea = await WorkAreaTickets.query()
              .update({
                next_work_area_id: null,
              })
              .where(
                'work_area_tickets.id',
                useItemsSelected[i].work_area_ticket_id
              );

            if (commentVoucher) {
              await WorkNotes.query().insert({
                mo_id: useItemsSelected[i].mo_id,
                work_area_ticket_id: ticket.id,
                note: commentVoucher,
                employee_id: codeUser,
              });
            }

            await WorkActivityLog.query().insert({
              work_voucher_id: voucherArea[0].id,
              work_area_id: area,
              employee_id: codeUser,
              module_name: 'ticket',
              module_id: ticket.id,
              activity: 'TicketCreated',
              data: JSON.stringify({}),
            });
            if (useVouchersMerge?.length > 1) {
              const searchMainVoucher = useVouchersMerge.find(
                (voucher) => voucher?.main === true
              );

              const getVouchersMerge = useVouchersMerge.filter(
                (voucher) => voucher?.main === false
              );

              for (let i = 0; i < getVouchersMerge.length; i++) {
                //vouchers
                await WorkVouchers.query()
                  .update({
                    merge_voucher_id: searchMainVoucher.value,
                  })
                  .where('work_vouchers.id', getVouchersMerge[i].value);

                // tickets
                await WorkAreaTickets.query()
                  .update({
                    work_area_ticket_status_id: completeStatusArea[0].id,
                    is_company_notified: false,
                    finished_at: dayjs(actualDate).format(format),
                  })
                  .where(
                    'work_area_tickets.work_voucher_id',
                    getVouchersMerge[i].value
                  )
                  .where('work_area_tickets.work_area_id', useWorkAreaId);
              }
            }
          }
        } else {
          const ticket = await WorkAreaTickets.query().insert({
            work_area_id: area,
            work_voucher_id: useItemsSelected[i].work_voucher_id,
            made_by_mo_scan: false,
            notify_company: true,
            is_company_notified: false,
            exp_finish_date: expDate ? expDate : null,
            exp_work_area_line_id: linesAreas ? linesAreas : null,
            exp_work_area_group_id: groupsArea ? groupsArea : null,
            work_inventory_location_id: location ? location : null,
            prev_work_area_id: useItemsSelected[i].work_area_id,
            work_area_ticket_status_id: statusArea,
            next_work_area_id: nextArea ? nextArea : null,
          });
          if (ticket) {
            updateTicketArea = await WorkAreaTickets.query()
              .update({
                next_work_area_id: null,
              })
              .where(
                'work_area_tickets.id',
                useItemsSelected[i].work_area_ticket_id
              );

            if (commentVoucher) {
              await WorkNotes.query().insert({
                mo_id: useItemsSelected[i].mo_id,
                work_area_ticket_id: ticket.id,
                note: commentVoucher,
                employee_id: codeUser,
              });
            }

            await WorkActivityLog.query().insert({
              work_voucher_id: useItemsSelected[i].work_voucher_id,
              work_area_id: area,
              employee_id: codeUser,
              module_name: 'ticket',
              module_id: ticket.id,
              activity: 'TicketCreated',
              data: JSON.stringify({}),
            });
            if (useVouchersMerge?.length > 1) {
              const searchMainVoucher = useVouchersMerge.find(
                (voucher) => voucher?.main === true
              );

              const getVouchersMerge = useVouchersMerge.filter(
                (voucher) => voucher?.main === false
              );

              for (let i = 0; i < getVouchersMerge.length; i++) {
                //vouchers
                await WorkVouchers.query()
                  .update({
                    merge_voucher_id: searchMainVoucher.value,
                  })
                  .where('work_vouchers.id', getVouchersMerge[i].value);

                // tickets
                await WorkAreaTickets.query()
                  .update({
                    work_area_ticket_status_id: completeStatusArea[0].id,
                    is_company_notified: false,
                    finished_at: dayjs(actualDate).format(format),
                  })
                  .where(
                    'work_area_tickets.work_voucher_id',
                    getVouchersMerge[i].value
                  )
                  .where('work_area_tickets.work_area_id', useWorkAreaId);
              }
            }
          }
        }
      }
    }

    if (+ticketAccion === 3) {
      const completeStatusArea = await WorkAreaTicketStatuses.query()
        .where('work_area_ticket_statuses.work_area_id', useWorkAreaId)
        .where('work_area_ticket_statuses.work_status_id', 100)
        .orderBy('work_area_ticket_statuses.sequence')
        .limit(1)
        .select('work_area_ticket_statuses.id');

      // Se duplicara el voucher
      for (let i = 0; i < useItemsSelected.length; i++) {
        const ticket = await WorkAreaTickets.query().insert({
          work_area_id: area,
          work_voucher_id: useItemsSelected[i].work_voucher_id,
          made_by_mo_scan: false,
          notify_company: true,
          is_company_notified: false,
          exp_finish_date: expDate ? expDate : null,
          exp_work_area_line_id: linesAreas ? linesAreas : null,
          exp_work_area_group_id: groupsArea ? groupsArea : null,
          work_inventory_location_id: location ? location : null,
          prev_work_area_id: useItemsSelected[i].work_area_id,
          work_area_ticket_status_id: statusArea,
          next_work_area_id: nextArea ? nextArea : null,
        });
        if (ticket) {
          updateTicketArea = await WorkAreaTickets.query()
            .update({
              next_work_area_id: null,
            })
            .where(
              'work_area_tickets.id',
              useItemsSelected[i].work_area_ticket_id
            );

          if (commentVoucher) {
            await WorkNotes.query().insert({
              mo_id: useItemsSelected[i].mo_id,
              work_area_ticket_id: ticket.id,
              note: commentVoucher,
              employee_id: codeUser,
            });
          }

          await WorkActivityLog.query().insert({
            work_voucher_id: useItemsSelected[i].work_voucher_id,
            work_area_id: area,
            employee_id: codeUser,
            module_name: 'ticket',
            module_id: ticket.id,
            activity: 'TicketCreated',
            data: JSON.stringify({}),
          });

          if (vouchersMerge.length > 1) {
            const searchMainVoucher = useVouchersMerge.find(
              (voucher) => voucher?.main === true
            );

            const getVouchersMerge = useVouchersMerge.filter(
              (voucher) => voucher?.main === false
            );

            for (let i = 0; i < getVouchersMerge.length; i++) {
              //vouchers
              await WorkVouchers.query()
                .update({
                  merge_voucher_id: searchMainVoucher.value,
                })
                .where('work_vouchers.id', getVouchersMerge[i].value);

              // tickets
              await WorkAreaTickets.query()
                .update({
                  work_area_ticket_status_id: completeStatusArea[0].id,
                  is_company_notified: false,
                  finished_at: dayjs(actualDate).format(format),
                })
                .where(
                  'work_area_tickets.work_voucher_id',
                  getVouchersMerge[i].value
                )
                .where('work_area_tickets.work_area_id', useWorkAreaId);
            }
          }
        }
      }
    }

    if (updateTicketArea) {
      return res.status(200).json({ ok: true, data: 'Se crearon los tickets' });
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({ ok: false });
  }
}

export async function getStatusAreaComplete(req: Request, res: Response) {
  try {
    const { workAreaId } = req.body;

    if (!workAreaId || isNaN(Number(workAreaId))) {
      return res.status(400).json({
        ok: false,
        data: 'Area invalida',
      });
    }
    const useWorkAreaId = Number(workAreaId);

    const getStatusArea = await WorkAreaTicketStatuses.query()
      .join(
        'work_statuses',
        'work_area_ticket_statuses.work_status_id',
        '=',
        'work_statuses.id'
      )
      .where('work_area_ticket_statuses.work_area_id', useWorkAreaId)
      .where('work_statuses.id', 100)

      .select(
        { generalStatus: 'work_statuses.id' },
        { workStatusName: 'work_statuses.name' },
        'work_area_ticket_statuses.name',
        { colorHex: 'work_area_ticket_statuses.color_hex' },
        'work_area_ticket_statuses.id'
      )
      .orderBy('work_area_ticket_statuses.sequence', 'asc');

    return res.status(200).json({
      ok: true,
      data: getStatusArea,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

// (se usa para actualizar los datos desde la tabla de tickets)
export async function completeToScanTickets(req: Request, res: Response) {
  try {
    const format = 'YYYY-MM-DD HH:mm:ss';
    const now = new Date();

    const {
      status,
      ticketId,
      voucherId,
      workAreaId,
      codeEmployee,
      fragment,
      custom_fields,
    } = req.body;

    if (!ticketId || isNaN(Number(ticketId))) {
      return res.status(400).json({
        ok: false,
        data: 'Ticket invalido',
      });
    }
    const useTicketId = Number(ticketId);

    const infoTicket = await WorkAreaTickets.query()
      .join(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id',
        'work_area_ticket_statuses.id'
      )
      .join(
        'work_areas',
        'work_area_tickets.work_area_id',
        '=',
        'work_areas.work_area_id'
      )
      .where('work_area_tickets.id', useTicketId)
      .select(
        {
          nameOld: 'work_area_ticket_statuses.name',
        },
        WorkInventoryBins.query()
          .where(
            'work_inventory_bins.id',
            ref('work_area_tickets.work_inventory_location_id')
          )
          .select('work_inventory_bins.name')
          .as('nameBinOld'),
        { colorHexOld: 'work_area_ticket_statuses.color_hex' },
        { expFinish: 'work_area_tickets.exp_finish_date' },
        { expWorkAreaLineId: 'work_area_tickets.exp_work_area_line_id' },
        {
          moTicketCreateGroupId: 'work_area_tickets.mo_ticket_create_group_id',
        },
        WorkAreas.query()
          .where(
            'work_areas.work_area_id',
            ref('work_area_tickets.next_work_area_id')
          )
          .select('work_areas.area_name')
          .as('oldNextArea')
      )
      .castTo<
        {
          nameOld: string;
          nameBinOld: string;
          colorHexOld: string;
          expFinish: Date;
          expWorkAreaLineId: number;
          moTicketCreateGroupId: number;
          oldNextArea: string;
        }[]
      >();

    if (status) {
      if (!workAreaId || isNaN(Number(workAreaId))) {
        return res.status(400).json({
          ok: false,
          data: 'Area invalida',
        });
      }
      const useWorkAreaId = Number(workAreaId);
      if (!status || isNaN(Number(status))) {
        return res.status(400).json({
          ok: false,
          data: 'Status invalido',
        });
      }
      const useTicketStatusId = Number(status);
      // obtenemos el work_status_id del status del area
      const getWorkTicketStatuses = await WorkAreaTicketStatuses.query()
        .where('work_area_id', useWorkAreaId)
        .where('id', useTicketStatusId)
        .select('name', 'color_hex', 'work_status_id')
        .castTo<
          {
            name: string;
            color_hex: string;
            work_status_id: number;
          }[]
        >();

      await WorkAreaTickets.query()
        .update({
          work_area_ticket_status_id: status,
          is_company_notified: false,
          finished_at: dayjs(now).format(format),
        })
        .where('id', useTicketId);

      await WorkActivityLog.query().insert({
        work_voucher_id: voucherId,
        employee_id: codeEmployee,
        work_area_id: workAreaId,
        module_name: 'ticket',
        module_id: ticketId,
        activity: 'TicketStatusChanged',
        data: JSON.stringify({
          old_status_name: infoTicket[0].nameOld,
          old_color: infoTicket[0].colorHexOld,
          new_status_name: getWorkTicketStatuses[0].name,
          new_color: getWorkTicketStatuses[0].color_hex,
        }),
      });
    }

    if (fragment) {
      if (!custom_fields || !Array.isArray(custom_fields)) {
        return res.status(400).json({
          ok: false,
          data: 'Campos personalizados invalidos',
        });
      }
      const useCustomFields: CustomField[] = custom_fields;
      //  Get values the custom fields
      const custom_fields_values = useCustomFields.reduce((a, field) => {
        return {
          ...a,
          ['custom_' + field.name]: field.value || null,
        };
      }, {});

      // Update the fragment
      await WorkFragments.query()
        .update({
          ...custom_fields_values,
        })
        .where({
          id: fragment,
        });

      // Insert the log
      await WorkFragmentLog.query().insert({
        work_fragment_id: fragment,
        employee_id: codeEmployee,
        work_area_id: useTicketId,
        data: JSON.stringify({
          ...custom_fields_values,
          action: 'updateFragment',
        }),
      });
    }

    return res.status(200).json({
      ok: true,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getAllTicketsFromAMo(req: Request, res: Response) {
  try {
    const { mo, workAreaId } = req.body;
    let companyCode = 0;
    let barcode = '';
    const closedTicketStatusIds = [100, 105, 110];

    if (!mo || typeof mo !== 'string') {
      return res.status(400).json({
        ok: false,
        data: 'Mo invalida',
      });
    }

    if (!workAreaId || isNaN(Number(workAreaId))) {
      return res.status(400).json({
        ok: false,
        data: 'Area invalida',
      });
    }
    const useWorkAreaId = Number(workAreaId);

    if (
      mo.startsWith('P') ||
      mo.startsWith('A') ||
      mo.includes('-') ||
      mo.includes('/')
    ) {
      if (mo.startsWith('P')) {
        companyCode = 1;
        barcode = mo;
      } else if (mo.startsWith('A')) {
        companyCode = 2;
        barcode = mo.slice(1);
      } else {
        companyCode = 3;
        barcode = mo.replace('-', '/');
      }

      const getMoInfo = await WorkAreaTickets.query()
        .select([
          { ticket_id: 'work_area_tickets.id' },
          { voucher_id: 'work_vouchers.id' },
          { voucher_type: 'work_voucher_types.name' },
          'work_area_tickets.created_at',
          'mo_numbers.mo_status',
          'mo_numbers.mo_order',
          'mo_numbers.num',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.customer',
          raw("CASE WHEN work_vouchers.is_repo = 1 THEN 'Si' ELSE 'No' END").as(
            'is_repo'
          ),
          raw(
            "CASE WHEN work_vouchers.is_primary = 1 THEN 'Si' ELSE 'No' END"
          ).as('is_primary'),
          { ticket_status: 'work_area_ticket_statuses.name' },
          { bin_location: 'work_inventory_bins.name' },
        ])
        .leftJoin(
          'work_areas',
          'work_area_tickets.work_area_id',
          'work_areas.work_area_id'
        )
        .leftJoin(
          'work_vouchers',
          'work_area_tickets.work_voucher_id',
          'work_vouchers.id'
        )
        .leftJoin('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
        .leftJoin(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .leftJoin(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          '=',
          'work_area_ticket_statuses.id'
        )
        .leftJoin(
          'work_statuses',
          'work_area_ticket_statuses.work_status_id',
          '=',
          'work_statuses.id'
        )
        .leftJoin(
          'work_inventory_bins',
          'work_area_tickets.work_inventory_location_id',
          'work_inventory_bins.id'
        )
        .where('mo_numbers.mo_barcode', barcode)
        .where('mo_numbers.company_code', companyCode)
        .where('work_area_tickets.work_area_id', useWorkAreaId)
        .whereNotIn('work_statuses.id', closedTicketStatusIds);

      if (getMoInfo.length > 0) {
        return res.status(200).json({
          ok: true,
          data: getMoInfo,
          totalItems: getMoInfo.length,
        });
      } else {
        return res.status(200).json({
          ok: false,
          data: 'No existen ticket para la mo',
        });
      }
    } else {
      const getMoInfo = await WorkAreaTickets.query()
        .select([
          { ticket_id: 'work_area_tickets.id' },
          { voucher_id: 'work_vouchers.id' },
          { voucher_type: 'work_voucher_types.name' },
          'work_area_tickets.created_at',
          'mo_numbers.mo_status',
          'mo_numbers.mo_order',
          'mo_numbers.num',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.customer',
          raw("CASE WHEN work_vouchers.is_repo = 1 THEN 'Si' ELSE 'No' END").as(
            'is_repo'
          ),
          raw(
            "CASE WHEN work_vouchers.is_primary = 1 THEN 'Si' ELSE 'No' END"
          ).as('is_primary'),
          { ticket_status: 'work_area_ticket_statuses.name' },
          { bin_location: 'work_inventory_bins.name' },
        ])
        .leftJoin(
          'work_areas',
          'work_area_tickets.work_area_id',
          'work_areas.work_area_id'
        )
        .leftJoin(
          'work_vouchers',
          'work_area_tickets.work_voucher_id',
          'work_vouchers.id'
        )
        .leftJoin('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
        .leftJoin(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .leftJoin(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          '=',
          'work_area_ticket_statuses.id'
        )
        .leftJoin(
          'work_statuses',
          'work_area_ticket_statuses.work_status_id',
          '=',
          'work_statuses.id'
        )
        .leftJoin(
          'work_inventory_bins',
          'work_area_tickets.work_inventory_location_id',
          'work_inventory_bins.id'
        )
        .where('mo_numbers.mo_id', mo)
        .where('work_area_tickets.work_area_id', useWorkAreaId)
        .whereNotIn('work_statuses.id', closedTicketStatusIds);

      if (getMoInfo.length > 0) {
        return res.status(200).json({
          ok: true,
          data: getMoInfo,
          totalItems: getMoInfo.length,
        });
      } else {
        return res.status(200).json({
          ok: false,
          data: 'No existen ticket para la mo',
        });
      }
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function receivingAndMergeVouchers(req: Request, res: Response) {
  try {
    const { area, itemsSelected, dataInputs, codeUser, vouchersMerge } =
      req.body;

    const { statusArea, groupsArea, linesAreas, nextArea, location, expDate } =
      dataInputs;

    if (!itemsSelected || !Array.isArray(itemsSelected)) {
      return res.status(400).json({
        ok: false,
        data: 'Items seleccionados invalidos',
      });
    }
    const useItemsSelected: {
      work_area_ticket_id: number;
      work_voucher_id: number;
      work_area_id: number;
    }[] = itemsSelected;

    const useItemSelected = useItemsSelected[0];

    const format = 'YYYY-MM-DD HH:mm:ss';
    const actualDate = new Date();

    if (!area || isNaN(Number(area))) {
      return res.status(400).json({
        ok: false,
        data: 'Area invalida',
      });
    }
    const useWorkAreaId = Number(area);

    if (vouchersMerge && !Array.isArray(vouchersMerge)) {
      return res.status(400).json({
        ok: false,
        data: 'Voucher invalido',
      });
    }
    const useVouchersMerge: CreateNewTicketVouchersMerge[] = vouchersMerge;

    await WorkAreaTickets.query()
      .update({
        next_work_area_id: null,
      })
      .where('work_area_tickets.id', useItemSelected.work_area_ticket_id);

    const searchStatusArea = await WorkAreaTicketStatuses.query()
      .where('work_area_ticket_statuses.work_area_id', useWorkAreaId)
      .where('work_area_ticket_statuses.work_status_id', 50)
      .orderBy('work_area_ticket_statuses.sequence')
      .limit(1)
      .select('work_area_ticket_statuses.id');

    if (useVouchersMerge?.length > 1) {
      const getMainVoucher = useVouchersMerge.find(
        (voucher) => voucher?.main === true
      );

      const getVouchersMerge = useVouchersMerge.filter(
        (voucher) => voucher?.main === false
      );

      for (let i = 0; i < getVouchersMerge.length; i++) {
        // TODO: Similar or exact same actions as endpoint above.  should be a function
        await WorkVouchers.query()
          .update({
            merge_voucher_id: getMainVoucher.value,
          })
          .where('work_vouchers.id', getVouchersMerge[i].value);

        if (statusArea) {
          if (!statusArea || isNaN(Number(statusArea))) {
            return res.status(400).json({
              ok: false,
              data: 'Status invalido',
            });
          }
          const useTicketStatusId = Number(statusArea);
          const searchInfoStatusArea = await WorkAreaTicketStatuses.query()
            .where('work_area_ticket_statuses.work_area_id', useWorkAreaId)
            .where('work_area_ticket_statuses.id', useTicketStatusId)
            .select('id', 'work_status_id');

          if (searchInfoStatusArea[0].work_status_id === 100) {
            // create ticket
            const ticket = await WorkAreaTickets.query().insert({
              work_area_id: area,
              work_voucher_id: useItemSelected.work_voucher_id,
              made_by_mo_scan: false,
              notify_company: true,
              is_company_notified: false,
              exp_finish_date: expDate ? expDate : null,
              exp_work_area_line_id: linesAreas ? linesAreas : null,
              exp_work_area_group_id: groupsArea ? groupsArea : null,
              work_inventory_location_id: location ? location : null,
              prev_work_area_id: useItemSelected.work_area_id,
              work_area_ticket_status_id: statusArea,
              next_work_area_id: nextArea ? nextArea : null,
              finished_at: dayjs(actualDate).format(format),
            });

            await WorkActivityLog.query().insert({
              work_voucher_id: useItemSelected.work_voucher_id,
              work_area_id: area,
              employee_id: codeUser,
              module_name: 'ticket',
              module_id: ticket.id,
              activity: 'TicketCreated',
              data: JSON.stringify({}),
            });

            // update
            await WorkAreaTickets.query()
              .update({
                exp_work_area_line_id: linesAreas ? linesAreas : null,
                exp_work_area_group_id: groupsArea ? groupsArea : null,
                work_inventory_location_id: location ? location : null,
                next_work_area_id: nextArea ? nextArea : null,
                work_area_ticket_status_id: statusArea ? statusArea : null,
                finished_at: dayjs(actualDate).format(format),
              })
              .where(
                'work_area_tickets.work_voucher_id',
                getVouchersMerge[i].value
              )
              .where('work_area_tickets.work_area_id', useWorkAreaId);
          } else {
            // create ticket
            const ticket = await WorkAreaTickets.query().insert({
              work_area_id: area,
              work_voucher_id: useItemSelected.work_voucher_id,
              made_by_mo_scan: false,
              notify_company: true,
              is_company_notified: false,
              exp_finish_date: expDate ? expDate : null,
              exp_work_area_line_id: linesAreas ? linesAreas : null,
              exp_work_area_group_id: groupsArea ? groupsArea : null,
              work_inventory_location_id: location ? location : null,
              prev_work_area_id: useItemSelected.work_area_id,
              work_area_ticket_status_id: statusArea,
              next_work_area_id: nextArea ? nextArea : null,
            });

            await WorkActivityLog.query().insert({
              work_voucher_id: useItemSelected.work_voucher_id,
              work_area_id: area,
              employee_id: codeUser,
              module_name: 'ticket',
              module_id: ticket.id,
              activity: 'TicketCreated',
              data: JSON.stringify({}),
            });

            // update tickets
            await WorkAreaTickets.query()
              .update({
                exp_work_area_line_id: linesAreas ? linesAreas : null,
                exp_work_area_group_id: groupsArea ? groupsArea : null,
                work_inventory_location_id: location ? location : null,
                next_work_area_id: nextArea ? nextArea : null,
                work_area_ticket_status_id: statusArea ? statusArea : null,
              })
              .where(
                'work_area_tickets.work_voucher_id',
                getVouchersMerge[i].value
              )
              .where('work_area_tickets.work_area_id', useWorkAreaId);
          }
        } else {
          // create ticket
          const ticket = await WorkAreaTickets.query().insert({
            work_area_id: area,
            work_voucher_id: useItemSelected.work_voucher_id,
            made_by_mo_scan: false,
            notify_company: true,
            is_company_notified: false,
            exp_finish_date: expDate ? expDate : null,
            exp_work_area_line_id: linesAreas ? linesAreas : null,
            exp_work_area_group_id: groupsArea ? groupsArea : null,
            work_inventory_location_id: location ? location : null,
            prev_work_area_id: useItemSelected.work_area_id,
            work_area_ticket_status_id: searchStatusArea[0].id,
            next_work_area_id: nextArea ? nextArea : null,
            finished_at: dayjs(actualDate).format(format),
          });

          await WorkActivityLog.query().insert({
            work_voucher_id: useItemSelected.work_voucher_id,
            work_area_id: area,
            employee_id: codeUser,
            module_name: 'ticket',
            module_id: ticket.id,
            activity: 'TicketCreated',
            data: JSON.stringify({}),
          });

          // tickets
          await WorkAreaTickets.query()
            .update({
              exp_work_area_line_id: linesAreas ? linesAreas : null,
              exp_work_area_group_id: groupsArea ? groupsArea : null,
              work_inventory_location_id: location ? location : null,
              next_work_area_id: nextArea ? nextArea : null,
              work_area_ticket_status_id: searchStatusArea[0].id,
            })
            .where(
              'work_area_tickets.work_voucher_id',
              getVouchersMerge[i].value
            )
            .where('work_area_tickets.work_area_id', useWorkAreaId);
        }
      }
    } else {
      const ticket = await WorkAreaTickets.query().insert({
        work_area_id: area,
        work_voucher_id: useItemSelected.work_voucher_id,
        made_by_mo_scan: false,
        notify_company: true,
        is_company_notified: false,
        exp_finish_date: expDate ? expDate : null,
        exp_work_area_line_id: linesAreas ? linesAreas : null,
        exp_work_area_group_id: groupsArea ? groupsArea : null,
        work_inventory_location_id: location ? location : null,
        prev_work_area_id: useItemSelected.work_area_id,
        work_area_ticket_status_id: statusArea || searchStatusArea[0].id,
        next_work_area_id: nextArea ? nextArea : null,
      });

      await WorkActivityLog.query().insert({
        work_voucher_id: useItemSelected.work_voucher_id,
        work_area_id: area,
        employee_id: codeUser,
        module_name: 'ticket',
        module_id: ticket.id,
        activity: 'TicketCreated',
        data: JSON.stringify({}),
      });
    }

    return res.status(200).json({ ok: true, data: 'Se crearon los tickets' });
  } catch (error) {
    console.log(error);

    return res.status(500).json({ ok: false });
  }
}
