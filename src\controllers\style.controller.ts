import type { Request, Response } from 'express';
import type multer from 'multer';
import path from 'path';

import type { StyleCombo } from '@app/models/style.schema';
import {
  getSingleStyleDocument,
  getStyleCombos,
  getSuggestedStyles,
  updateStyleCombos,
  uploadStyleDocuments,
} from '@app/services/styles';

export async function getSuggestedStylesController(
  req: Request,
  res: Response
) {
  try {
    const { style } = req.params;

    if (!style) {
      return res.status(400).json({
        ok: false,
        error: 'Agregue un estilo',
      });
    }

    const suggestedStyle = await getSuggestedStyles(style);

    if (!suggestedStyle) {
      return res.status(400).json({
        ok: false,
        error: 'No se encontró ningun style',
      });
    }

    return res.status(200).json({
      ok: true,
      data: suggestedStyle,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: `Error, ${error.message}`,
    });
  }
}

export async function getStyleCombosController(req: Request, res: Response) {
  try {
    const { styleId } = req.params;

    if (!styleId) {
      return res.status(400).json({
        ok: false,
        error: 'Agregue un estilo',
      });
    }

    if (isNaN(+styleId)) {
      return res.status(400).json({
        ok: false,
        error: 'Style no tiene formato correcto',
      });
    }

    const suggestedStyle = await getStyleCombos(+styleId);

    if (!suggestedStyle) {
      return res.status(400).json({
        ok: false,
        error: 'No se encontró ningun style',
      });
    }

    return res.status(200).json({
      ok: true,
      data: suggestedStyle,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: `Error, ${error.message}`,
    });
  }
}

export async function updateStyleCombosController(req: Request, res: Response) {
  try {
    const {
      styleId,
      input,
    }: { styleId: number; input: Partial<StyleCombo>[] } = req.body;

    if (!styleId) {
      return res.status(400).json({
        ok: false,
        error: 'Agregue un estilo',
      });
    }

    if (isNaN(+styleId)) {
      return res.status(400).json({
        ok: false,
        error: 'Style no tiene formato correcto',
      });
    }

    if (!input) {
      return res.status(400).json({
        ok: false,
        error: 'Input no tiene formato correcto',
      });
    }

    const suggestedStyle = await updateStyleCombos(+styleId, input);

    if (!suggestedStyle) {
      return res.status(400).json({
        ok: false,
        error: 'No se encontró ningun style',
      });
    }

    return res.status(200).json({
      ok: true,
      data: suggestedStyle,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: `Error, ${error.message}`,
    });
  }
}

export async function uploadStyleComboDocumentsController(
  req: Request & { file: multer.File },
  res: Response
) {
  try {
    const body = Object.assign({}, req.body);
    const {
      comment,
      type,
      styleNumber,
    }: { comment: string; type: string; styleNumber: string } = body;
    const file = req.file;

    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf'];

    if (!file) {
      return res.status(400).json({
        ok: false,
        error: 'No se recibio ningun archivo',
      });
    }

    //getting list of allowd extensions
    const ext = path.extname(file.originalname);

    if (!allowedExtensions.includes(ext.toLowerCase())) {
      return res.status(400).json({
        ok: false,
        error: 'Formato de archivo no permitido',
      });
    }

    const attachment = await uploadStyleDocuments(
      file,
      comment,
      type,
      styleNumber
    );

    if (!attachment) {
      return res.status(400).json({
        ok: false,
        error: 'No se encontró ningun style',
      });
    }

    return res.status(200).json({
      ok: true,
      data: attachment,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: `Error, ${error.message}`,
    });
  }
}

export async function getStyleDocument(req: Request, res: Response) {
  try {
    const { type, styleId } = req.params;

    if (!styleId) {
      return res.status(400).json({
        ok: false,
        error: 'Agregue un estilo',
      });
    }

    if (isNaN(+styleId)) {
      return res.status(400).json({
        ok: false,
        error: 'Style no tiene formato correcto',
      });
    }

    const singleStyleDocument = await getSingleStyleDocument(+styleId, type);

    if (!singleStyleDocument) {
      return res.status(400).json({
        ok: false,
        error: 'No se encontró ningun style',
      });
    }

    return res.status(200).json({
      ok: true,
      data: singleStyleDocument,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: `Error, ${error.message}`,
    });
  }
}
