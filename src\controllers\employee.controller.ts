import type { Request, Response } from 'express';
import { raw, ref } from 'objection';

import { Employee } from '@app/models/employee.schema';
import {
  WorkActivityLog,
  WorkAreaEmployees,
  WorkVoucherTypes,
} from '@app/models/tickets.schema';
import {
  getEmployeeByCode,
  searchEmployeesActiveById,
} from '@app/services/employee';
import { buildLogger } from '@app/settings';

const logger = buildLogger('controllers:employee');

export const simpleSignin = async (req: Request, res: Response) => {
  const { employeeCode } = req.body;
  const employeeNumber = Number(employeeCode);
  if (!employeeNumber || isNaN(employeeNumber)) {
    return res.status(400).json({
      ok: false,
      data: 'Codigo de empleado invalido',
    });
  }
  try {
    const employee = await getEmployeeByCode(employeeNumber);
    if (!employee) {
      return res.status(404).json({
        ok: false,
        data: 'No se encontro el empleado',
      });
    }
    return res.status(200).json({
      ok: true,
      data: employee,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
};

export async function getEmployeeArea(req: Request, res: Response) {
  const { codeEmployee } = req.body;
  if (!codeEmployee || isNaN(Number(codeEmployee))) {
    return res.status(400).json({
      ok: false,
      data: 'Codigo de empleado invalido',
    });
  }

  try {
    const infoEmployee = await WorkAreaEmployees.query()
      .join(
        'employees',
        'work_area_employees.employee_id',
        '=',
        'employees.employee_id'
      )
      .join(
        'work_areas',
        'work_area_employees.work_area_id',
        '=',
        'work_areas.work_area_id'
      )
      .where('work_area_employees.employee_id', Number(codeEmployee))
      .select(
        'employees.first_name',
        'work_area_employees.employee_id',
        'work_areas.area_name ',
        'work_areas.default_work_voucher_type_id',
        'work_area_employees.role',
        'work_areas.work_type_id',
        WorkVoucherTypes.query()
          .where(
            'work_voucher_types.id',
            ref('work_areas.default_work_voucher_type_id')
          )
          .select('work_voucher_types.name')
          .as('voucherType'),
        WorkVoucherTypes.query()
          .where(
            'work_voucher_types.id',
            ref('work_areas.default_work_voucher_type_id')
          )
          .select('work_voucher_types.id')
          .as('voucherTypeId'),
        'work_area_employees.work_area_id'
      );
    if (infoEmployee.length > 0) {
      return res.status(200).json({
        ok: true,
        data: infoEmployee,
      });
    } else {
      return res.status(403).json({
        ok: false,
        data: 'No hay informacion',
      });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function loginEmployee(req: Request, res: Response) {
  const codeEmployee: number = req.body.codeUser;
  try {
    const searchUser = await WorkAreaEmployees.query().where(
      'employee_id',
      codeEmployee
    );

    if (searchUser.length > 0) {
      const searchUserInfo = await WorkAreaEmployees.query()
        .join(
          'employees',
          'work_area_employees.employee_id',
          'employees.employee_id'
        )
        .where('work_area_employees.employee_id', codeEmployee)
        .select(
          raw(
            "CASE WHEN work_area_employees.role = 1 THEN 'ADMIN' ELSE 'USER' END"
          ).as('rol'),
          { name: 'employees.first_name' },
          { main_area: 'work_area_employees.main_work_area_id' },
          'employees.image'
        );

      const searchUserAreas = await WorkAreaEmployees.query()
        .join(
          'work_areas',
          'work_area_employees.work_area_id',
          'work_areas.work_area_id'
        )
        .join(
          'work_voucher_types',
          'work_areas.default_work_voucher_type_id ',
          'work_voucher_types.id'
        )
        .where('work_area_employees.employee_id', codeEmployee)
        .select('work_areas.work_area_id', 'work_areas.area_name', {
          voucher_type: 'work_areas.default_work_voucher_type_id',
          voucher_type_name: 'work_voucher_types.name',
        });

      return res.status(200).json({
        ok: true,
        data: {
          ...searchUserInfo[0],
          id: codeEmployee,
          areas: searchUserAreas,
        },
      });
    } else {
      return res.status(403).json({
        ok: false,
        data: 'Ocurrio un error con los datos',
      });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getLogOfEmployee(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const { employee_code }: { employee_code: number } = req.body;

    const getLogs = await WorkActivityLog.query()
      .select(
        'work_activity_log.id',
        'work_activity_log.activity',
        'work_activity_log.data',
        'work_activity_log.created_at'
      )
      .where('work_activity_log.module_name', 'ticket')
      .where('work_activity_log.employee_id', employee_code)
      .whereIn('work_activity_log.activity', [
        'TicketStatusChanged',
        'TicketInventoryMoved',
        'TicketCreated',
      ])
      .orderBy('created_at', 'desc');

    return res.status(200).json({
      ok: true,
      data: getLogs,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getEmployeeInformation(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const { employee_code }: { employee_code: number } = req.body;

    const getEmployee = await Employee.query()
      .select(
        'employee_id',
        'first_name',
        'last_name',
        'short_name',
        'emp_barcode',
        'barcode'
      )
      .where('employee_id', employee_code);

    if (getEmployee.length > 0) {
      return res.status(200).json({
        ok: true,
        data: getEmployee,
      });
    }
    return res.status(200).json({
      ok: false,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getEmployeesByEmployeeCode(req: Request, res: Response) {
  try {
    const { id } = req.query as unknown as {
      id: number;
    };

    if (!id) throw new Error('El codigo de empleado es requerido');

    const employees = await searchEmployeesActiveById(id);

    if (employees.length === 0) {
      return res.status(404).json({
        ok: false,
        message: 'No se encontraron empleados',
      });
    }

    return res.status(200).json({
      ok: true,
      data: employees,
      message: 'Empleados encontrados',
      total: employees.length,
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Error interno del servidor');

    return res.status(500).json({
      ok: false,
      message: 'Error interno del servidor',
    });
  }
}

export const getEmployeeByBarcode = async (req: Request, res: Response) => {
  try {
    const { barcode } = req.params;
    const employee = await Employee.query()
      .findOne({
        emp_barcode: barcode,
      })
      .select(
        'employee_id',
        'first_name',
        'last_name',
        'short_name',
        'emp_barcode',
        'barcode'
      );
    if (!employee) {
      return res.status(404).json({
        ok: false,
        data: 'No se encontro el empleado',
      });
    }
    return res.status(200).json({
      ok: true,
      data: employee,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const getEmployeeById = async (req: Request, res: Response) => {
  try {
    const { employee_id } = req.params;

    const getEmployee = await Employee.query()
      .select(
        'employee_id',
        'first_name',
        'last_name',
        'short_name',
        'emp_barcode',
        'barcode'
      )
      .where('employee_id', employee_id);

    if (getEmployee.length > 0) {
      return res.status(200).json({
        ok: true,
        data: getEmployee,
      });
    }
    return res.status(200).json({
      ok: false,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
};
