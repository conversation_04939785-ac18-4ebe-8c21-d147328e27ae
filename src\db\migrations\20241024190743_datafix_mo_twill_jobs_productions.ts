import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.raw(`insert into mo_twill_laser_job_productions (mo_twill_laser_job_id,quantity,machine_id,employee_id,is_active)
select
	mtlj.id as mo_twill_laser_job_id,
	mo.quantity,
	mtlj.machine_id,
	mtlj.employee_id,
	1 as is_active
from mo_twill_laser_jobs mtlj
left join mo_numbers mo on mo.mo_id = mtlj.mo_id 
left join (
select
	mtljp.mo_twill_laser_job_id
from mo_twill_laser_job_productions mtljp 
group by mtljp.mo_twill_laser_job_id
) ljp on ljp.mo_twill_laser_job_id = mtlj.id
where mtlj.finished_at is not null
and ljp.mo_twill_laser_job_id is null`);
}

export async function down(): Promise<void> {
  return Promise.resolve();
}
