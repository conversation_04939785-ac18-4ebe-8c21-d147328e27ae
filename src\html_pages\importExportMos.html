<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    <h1>Import/Export MOS</h1>
    <form>
      <!---Removed form submit url-->
      <label for="employeeId">Employee Id</label>
      <input type="text" id="employeeId" />
      <br />
      <label for="mos">Enter MOS</label>
      <textarea id="mos" rows="20" cols="100"></textarea>
      <!---Id attribute added to input field to be submitted--->
      <br />
      <input type="button" onclick="myFunction()" value="Descargar" />
      <!---Input type is button NOT submit--->
    </form>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script>
      function myFunction() {
        var dataValue = $('#mos')
          .val()
          .split(/[\n,]+/)
          .map((s) => s.trim());
        var employeeId = $('#employeeId').val();
        console.log('mos value', dataValue);

        $.ajax({
          type: 'POST',
          //remove the .php from results.php.php
          url: '/importexport/moExport',
          //Add the request header
          headers: {
            employeeId: employeeId,
          },
          contentType: 'application/x-www-form-urlencoded',
          //Add form data
          data: { mos: dataValue },
          //   success: function (response) {
          //     console.log(response);
          //     console.log('Success');
          //     if (response?.error) {
          //       alert(response.error);
          //       return;
          //     }
          //   },
          success: function (response, status, xhr) {
            var filename = '';
            var disposition = xhr.getResponseHeader('Content-Disposition');
            if (disposition && disposition.indexOf('attachment') !== -1) {
              var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
              var matches = filenameRegex.exec(disposition);
              if (matches != null && matches[1])
                filename = matches[1].replace(/['"]/g, '');
            }

            console.log('filename', filename);
            console.log('response', response);
            const contentType = xhr.getResponseHeader('Content-Type');
            console.log('contentType', contentType);

            // check if json response or file
            if (xhr.getResponseHeader('Content-Type') === 'application/json') {
              var responseJson = JSON.parse(response);
              if (responseJson.error) {
                alert(responseJson.error);
                return;
              } else {
                alert('JSON returned, not file');
              }
            }

            var type = xhr.getResponseHeader('Content-Type');
            var blob = new Blob([response], { type: type });

            if (typeof window.navigator.msSaveBlob !== 'undefined') {
              // IE workaround for "HTML7007: One or more blob URLs were revoked by closing the blob for which they were created. These URLs will no longer resolve as the data backing the URL has been freed."
              window.navigator.msSaveBlob(blob, filename);
            } else {
              var URL = window.URL || window.webkitURL;
              var downloadUrl = URL.createObjectURL(blob);

              if (filename) {
                // use HTML5 a[download] attribute to specify filename
                var a = document.createElement('a');
                // safari doesn't support this yet
                if (typeof a.download === 'undefined') {
                  window.location = downloadUrl;
                } else {
                  a.href = downloadUrl;
                  a.download = filename;
                  document.body.appendChild(a);
                  a.click();
                }
              } else {
                window.location = downloadUrl;
              }

              setTimeout(function () {
                URL.revokeObjectURL(downloadUrl);
              }, 100); // cleanup
            }
          }, // End of myFucntion
          error: function (error) {
            console.log('Error', error);
            const response = error.responseJSON;
            if (response?.error) {
              alert(response.error);
              return;
            } else {
              alert('Error occurred');
            }
          },
        });
      }
    </script>
  </body>
</html>
