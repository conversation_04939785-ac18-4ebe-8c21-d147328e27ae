import { App } from './app';
import { buildLogger } from './settings';

const logger: {
  log: (message: string) => void;
  error: (message: string) => void;
} = buildLogger('index.ts');

async function main(): Promise<void> {
  // Instanciamos la clase App
  const app = new App();

  // Ejecutamos el metodo listen para que nuestro servidor este escuchando en un puerto
  const listen: string = await app.listen();

  // Imprimimos el puerto en el que esta escuchando nuestro servidor
  logger.log(`Server on port ${listen}`);
}

// Ejecutamos nuestro servidor
main();
