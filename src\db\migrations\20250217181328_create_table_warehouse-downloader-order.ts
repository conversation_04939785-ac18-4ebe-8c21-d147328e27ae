import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(
    'warehouse_downloader_orders',
    (table): void => {
      table.increments('id').unsigned().primary();
      table.integer('employee_id').notNullable();
      table
        .foreign('employee_id', 'wdoei_employees_fk')
        .references('employee_id')
        .inTable('employees');
      table.integer('mo_id').notNullable();
      table
        .foreign('mo_id', 'wdomi_mo_numbers_fk')
        .references('mo_id')
        .inTable('mo_numbers');
      table.timestamp('finished_at').nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table
        .timestamp('updated_at')
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('warehouse_downloader_orders');
}
