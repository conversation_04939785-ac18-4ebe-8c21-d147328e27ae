import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.raw(`insert into mo_twill_laser_jobs (mo_id,sub_mo_id,mo_twill_laser_job_type_id,is_active,mo_twill_laser_varsity_art_job_id,mo_twill_laser_job_status_id)
select
	mo.parent_mo_id as mo_id,
	mo.mo_id as sub_mo_id,
	1 as mo_twill_laser_job_type_id,
	1 as is_active,
	mtlvaj.id as mo_twill_laser_varsity_art_job_id,
	1 as mo_twill_laser_job_status_id
from mo_twill_laser_varsity_art_jobs mtlvaj
left join mo_numbers mo on mo.mo_id = mtlvaj.child_mo_id
left join mo_twill_laser_jobs mtlj on mtlj.mo_twill_laser_varsity_art_job_id = mtlvaj.id
left join (
select
	twill_laser_artist_id,
	count(*) as consuption_count
from mo_twill_laser_artist_consumptions mtlac 
group by twill_laser_artist_id 
) ac on ac.twill_laser_artist_id = mtlvaj.id
where mo.parent_mo_id is not null
and mtlj.id is null
and ac.twill_laser_artist_id is not null`);
}

export async function down(): Promise<void> {
  return Promise.resolve();
}
