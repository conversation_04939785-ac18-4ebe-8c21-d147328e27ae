import dayjs from 'dayjs';
import xl from 'excel4node';
import type { Response } from 'express';

interface CellStyle {
  numberFormat?: string;
  font?: {
    bold?: boolean;
    charset?: number; // integer
    color?: string;
    condense?: boolean;
    extend?: boolean;
    family?: string;
    italics?: boolean;
    name?: string;
    outline?: boolean;
    scheme?: string; // §18.18.33 ST_FontScheme (Font scheme Styles)
    shadow?: boolean;
    strike?: boolean;
    size?: number; // integer
    underline?: boolean;
    vertAlign?: string;
  };
  fill?: {
    type?: string;
    patternType?: string;
    fgColor?: string;
  };
  alignment?: {
    horizontal?: string;
  };
}

export interface ExcelHeader {
  headerName?: string | null;
  width?: number | null;
  fieldType?: 'string' | 'number' | 'date' | 'dateonly' | null;
  headerCellStyle?: CellStyle | null;
  valueCellStyle?: CellStyle | null;
}

interface ExcelOptions<T> {
  response: Response;
  headerOrder?: (keyof T)[];
  headers?: Partial<Record<keyof T, ExcelHeader>>;
  baseHeaderCellStyle?: CellStyle;
  baseValueCellStyle?: CellStyle;
  freezeRow?: number;
  addFilter?: boolean;
  summaryHeaders?: (keyof T)[];
}
export const jsonToExcel = async <T>(
  fileName: string,
  sheetName: string,
  json: T[],
  options?: ExcelOptions<T>
) => {
  if (!options.response) {
    // can only write to response for now
    throw new Error('response is required');
  }

  const wb = new xl.Workbook();

  // Add Worksheets to the workbook
  const ws = wb.addWorksheet(sheetName);

  // add headers
  // get headers from options or json
  const jsonHeaders = Object.keys(json[0]) as (keyof T)[];
  if (options?.headerOrder) {
    // check all headers are in json headers
    for (const header of options.headerOrder) {
      if (!jsonHeaders.includes(header)) {
        throw new Error(`Header ${String(header)} not found in json`);
      }
    }
  }
  const headers = options?.headerOrder ?? jsonHeaders;

  // get cell styles for each header
  const baseHeaderStyle = options?.baseHeaderCellStyle
    ? wb.createStyle(options?.baseHeaderCellStyle)
    : null;
  const baseValueStyle = options?.baseValueCellStyle
    ? wb.createStyle(options?.baseValueCellStyle)
    : null;
  const headerCellStyleMap: Partial<Record<keyof T, any>> = {};
  const valueCellStyleMap: Partial<Record<keyof T, any>> = {};
  for (const header of headers) {
    const headerOptions = options?.headers?.[header];
    if (headerOptions?.headerCellStyle) {
      headerCellStyleMap[header] = wb.createStyle({
        ...options?.baseHeaderCellStyle,
        ...headerOptions.headerCellStyle,
      });
    } else if (baseHeaderStyle) {
      headerCellStyleMap[header] = baseHeaderStyle;
    }
    if (headerOptions?.valueCellStyle) {
      valueCellStyleMap[header] = wb.createStyle({
        ...options?.baseValueCellStyle,
        ...headerOptions.valueCellStyle,
      });
    } else if (baseValueStyle) {
      valueCellStyleMap[header] = baseValueStyle;
    }
  }

  const headerRow =
    options?.summaryHeaders && options.summaryHeaders.length > 0 ? 2 : 1;

  for (const [index, header] of headers.entries()) {
    const headerOptions = options?.headers?.[header];
    const headerCellStyle = headerCellStyleMap[header];
    const cell = ws
      .cell(headerRow, index + 1)
      .string(headerOptions?.headerName ?? header);
    if (headerCellStyle) {
      cell.style(headerCellStyle);
    }
    if (headerOptions?.width) {
      ws.column(index + 1).setWidth(headerOptions.width);
    }
  }

  // add data
  for (const [rowIndex, data] of json.entries()) {
    for (const [colIndex, header] of headers.entries()) {
      const headerOptions = options?.headers?.[header];
      const valueCellStyle = valueCellStyleMap[header];
      // if header is a date, add as date
      if (
        data[header] === null ||
        data[header] === undefined ||
        data[header].toString().trim() === ''
      ) {
        continue;
      }
      switch (headerOptions?.fieldType) {
        case 'dateonly': {
          const dateString = dayjs(data[header as string]).format('YYYY-MM-DD');
          const cell = ws
            .cell(rowIndex + 1 + headerRow, colIndex + 1)
            .string(dateString);

          break;
        }
        case 'date': {
          const cell = ws
            .cell(rowIndex + 1 + headerRow, colIndex + 1)
            .date(dayjs(data[header as string]).toDate());

          if (valueCellStyle) {
            cell.style(valueCellStyle);
          }
          break;
        }
        case 'number': {
          const value = Number(data[header]);
          if (isNaN(value)) {
            break;
          }
          const cell = ws
            .cell(rowIndex + 1 + headerRow, colIndex + 1)
            .number(value);
          if (valueCellStyle) {
            cell.style(valueCellStyle);
          }
          break;
        }
        default: {
          const cell = ws
            .cell(rowIndex + 1 + headerRow, colIndex + 1)
            .string(data[header].toString());
          if (valueCellStyle) {
            cell.style(valueCellStyle);
          }
        }
      }
    }
  }

  if (options?.freezeRow && !isNaN(Number(options.freezeRow))) {
    ws.row(Number(options.freezeRow)).freeze();
  }

  if (options?.summaryHeaders && options.summaryHeaders.length > 0) {
    // add sum function to first cell above associated column
    for (const [index, header] of headers.entries()) {
      if (options.summaryHeaders.includes(header)) {
        const cell = ws
          .cell(1, index + 1)
          .formula(
            `SUM(${xl.getExcelAlpha(index + 1)}${
              headerRow + 1
            }:${xl.getExcelAlpha(index + 1)}${json.length + headerRow})`
          );
        if (valueCellStyleMap[header]) {
          cell.style(valueCellStyleMap[header]);
        }
      }
    }
  }

  // add filter
  if (options?.addFilter) {
    ws.row(headerRow).filter();
  }

  if (options?.response) {
    wb.write(`${fileName}.xlsx`, options.response);
    return options.response.status(200);
  } else {
    return wb.writeToBuffer();
  }
};
