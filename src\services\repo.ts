import type { Knex } from 'knex';

import { WorkRepos } from '@app/models/repo.schema';

export const getRepoService = async (
  id?: string,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  const trx = options?.trx ?? undefined;
  const buildQuery = () => {
    return WorkRepos.query(trx)
      .join('mo_numbers', 'mo_numbers.mo_id', 'work_repos.mo_id')
      .join(
        'work_repo_statuses',
        'work_repo_statuses.id',
        'work_repos.work_repo_status_id'
      )
      .join(
        'work_areas',
        'work_repos.reported_work_area_id',
        'work_areas.work_area_id'
      )
      .join('work_issues', 'work_repos.work_issue_id', 'work_issues.id')
      .select([
        'mo_numbers.mo_order',
        'mo_numbers.num',
        'mo_numbers.customer',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'work_repos.id',
        'work_repos.creator_email',
        'work_repo_statuses.name as work_repo_status_name',
        'work_repos.customer_fault',
        'work_repos.is_customer_charged',
        'work_repos.customer_charge_invoice_number',
        'work_issues.issue',
        'work_repos.affected_units',
        'work_repos.work_reason_note',
        'work_areas.area_name as reported_work_area_name',
        'work_repos.charge_amount',
        'work_repos.estimated_material_cost',
        'work_repos.estimated_labor_cost',
        'work_repos.comments',
        'work_repos.item_comments',
        'work_repos.material_comments',
        'work_repos.approval_date',
        'work_repos.approval_user',
        'work_repos.materials_approver_email',
        'work_repos.materials_approve_date',
        'work_repos.in_production_date',
        'work_repos.finish_date',
        'work_repos.repo_type',
        'work_repos.last_status_changed',
        'work_repos.warehouse_materials_preparred',
        'work_repos.created_at',
      ])
      .where('work_repos.work_repo_status_id', '!=', 4)
      .where('work_repos.work_repo_status_id', '!=', 9)
      .orderBy('work_repos.created_at', 'desc');
  };

  if (+id > 0) {
    return await buildQuery().where('work_repos.id', id);
  }

  return await buildQuery().where('work_repos.customer_fault', true);
};

export const updateRepoInvoiceService = async (
  id: number,
  charge_amount: number,
  is_customer_charged: boolean,
  customer_charge_invoice_number: string,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  const trx = options?.trx ?? undefined;

  const currentRecord = await WorkRepos.query(trx).findById(id);

  if (!currentRecord) {
    throw new Error(`No se encontro repo con ID ${id}.`);
  }

  const updatedChargeAmount = currentRecord.charge_amount ?? charge_amount;

  return await WorkRepos.query(trx).updateAndFetchById(id, {
    charge_amount: updatedChargeAmount,
    is_customer_charged,
    customer_charge_invoice_number,
    customer_charge_invoice_date: is_customer_charged ? new Date() : null,
  });
};
