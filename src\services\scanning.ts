import { default as dayjs } from 'dayjs';
import type { PartialModelObject } from 'objection';

import { updateShift } from '@app/controllers/shift.controller';
import type { IFindMO } from '@app/interface/scanning.interfaces';
import { WorkFragments } from '@app/models/fragment.schema';
import {
  MoScans,
  Operators,
  StyleSams,
  WorkAreaGroupShifts,
  WorkAreaGroups,
  WorkAreaOperatorMap,
  WorkAreaTicketStatuses,
  WorkAreaTickets,
  WorkAreas,
} from '@app/models/tickets.schema';
import { sendScanLog } from '@app/services/discord';
import { getMoInfoForScan } from '@app/services/monumbers';
import { createVoucherAndTicket } from '@app/services/voucher';

export const SCAN_ACTION = {
  S: 'START',
  F: 'FINISH',
  C: 'CLOSE',
  R: 'RECEIVED',
};

async function createScanRow(data: {
  work_repo_id: number;
  sew_sam_value: string;
  style_sam_group_id: number;
  style_sam_id: number;
  poly_status: number;
  varsity_status: number;
  work_area_group_shift_id: number;
  quantity: number;
  mo_id: number;
  sew_ready: string;
  sew: string;
  supervisor: string;
  task_name: string;
  employee_id: number;
  work_area_id: number;
  work_voucher_id: number;
  work_area_group_id: number;
  work_area_ticket_id: number;
  work_area_line_id: number;
  work_fragment_id: number;
  is_repo: boolean;
}) {
  return await MoScans.query().insert({
    mo_id: data.mo_id,
    supervisor: data.supervisor,
    task_name: data.task_name,
    poly_status: data.poly_status,
    varsity_status: data.varsity_status,
    sew_ready:
      data.sew_ready !== undefined && data.sew_ready !== null
        ? data.sew_ready
        : null,
    sew: data.sew !== undefined && data.sew !== null ? data.sew : null,
    quantity:
      data.quantity !== undefined && data.quantity !== null
        ? data.quantity
        : null,
    work_area_id:
      data.work_area_id !== undefined && data.work_area_id !== null
        ? data.work_area_id
        : null,
    work_voucher_id:
      data.work_voucher_id !== undefined && data.work_voucher_id !== null
        ? data.work_voucher_id
        : null,
    work_area_group_id:
      data.work_area_group_id !== undefined && data.work_area_group_id !== null
        ? data.work_area_group_id
        : null,
    work_area_ticket_id:
      data.work_area_ticket_id !== undefined &&
      data.work_area_ticket_id !== null
        ? data.work_area_ticket_id
        : null,
    work_area_line_id:
      data.work_area_line_id !== undefined && data.work_area_line_id !== null
        ? data.work_area_line_id
        : null,
    is_repo:
      data.is_repo !== undefined && data.is_repo !== null
        ? data.is_repo
        : false,
    work_area_group_shift_id:
      data.work_area_group_shift_id !== undefined &&
      data.work_area_group_shift_id !== null
        ? data.work_area_group_shift_id
        : null,
    style_sam_id:
      data.style_sam_id !== undefined && data.style_sam_id !== null
        ? data.style_sam_id
        : null,
    style_sam_group_id:
      data.style_sam_group_id !== undefined && data.style_sam_group_id !== null
        ? data.style_sam_group_id
        : null,
    employee_id:
      data.employee_id !== undefined && data.employee_id !== null
        ? data.employee_id
        : null,
    sew_sam_value:
      data.sew_sam_value !== undefined && data.sew_sam_value !== null
        ? data.sew_sam_value
        : null,
    work_repo_id:
      data.work_repo_id !== undefined && data.work_repo_id !== null
        ? data.work_repo_id
        : null,
    work_fragment_id:
      data.work_fragment_id !== undefined && data.work_fragment_id !== null
        ? data.work_fragment_id
        : null,
  } as PartialModelObject<MoScans>);
}

export async function CheckJobRequest(
  moIDRequest: number,
  moBarcodeRequest: string,
  voucherIDRequest: number,
  typeActionRequest: string,
  badgeBarcode: string,
  lineName: string,
  quantityRequest: number
) {
  let voucherId: number;
  let lastWorkAreaTicketId: number;
  let lastWorkTicketAreaId: number;
  let lastWorkAreaTicketFinish: string;
  let voucherCode: string;
  let isRepo: boolean;
  let repoId: number;
  let companyCode: number;
  let jobBarcode: string;
  let fragmentId: number;
  let findMO: IFindMO;
  let action: string;
  let actionFound = false;
  let quantity: number;
  // *** revisar si moid ha sido seteado ***
  if (
    moIDRequest !== undefined &&
    moIDRequest !== null &&
    typeof moIDRequest === 'number'
  ) {
    const getMoInformation = await getMoInfoForScan('MO_ID', moIDRequest);

    if (!getMoInformation) {
      // enviar mensaje a discord
      await sendScanLog(
        'MOID SET IN REQUEST',
        `No se encontró la MO enviada en la peticion ${moIDRequest}, Line name : ${lineName}, por medio de MO ID`
      );

      // no se encontro MO enviar error
      return {
        ok: false,
        message:
          'No se encontró la MO enviada en la peticion, por medio del MO ID',
      };
    }
    //TODO: llenar informacion de la mo
  }

  if (moBarcodeRequest && typeof moBarcodeRequest === 'string') {
    if (moBarcodeRequest.startsWith('MEVB')) {
      actionFound = true;
      // obtener voucher id, revisar si lleva accion o no en el voucher
      const getAction = Number(moBarcodeRequest.charAt(4));
      const voucherID = isNaN(getAction)
        ? +moBarcodeRequest.slice(5)
        : +moBarcodeRequest.slice(4);

      // verificar si voucher id a sido seteado y es diferente del obtenido en el codigo de barra
      if (
        voucherIDRequest !== undefined &&
        voucherIDRequest !== null &&
        voucherID !== voucherIDRequest
      ) {
        // enviar mensaje a discord
        await sendScanLog(
          'VOUCHERID SET IN REQUEST',
          `No se encontró la MO enviada en la peticion ${voucherIDRequest}, Line name : ${lineName}`
        );

        // send to discord and return error
        return {
          ok: false,
          message: 'Voucher no es igual al enviado en la peticion',
        };
      }
      // set voucher id
      voucherId = voucherID;
      // obtener mo_id a partir del voucher
      findMO = await getMoInfoForScan('VOUCHER', voucherID);

      if (findMO) {
        // set last_work_area_ticket_id WorkAreaTickets
        const getWorkAreaTickets = await WorkAreaTickets.query()
          .where('work_voucher_id ', voucherID)
          .select('id', 'work_area_id', 'finished_at')
          .orderBy('id', 'desc')
          .first()
          .castTo<{
            id: number;
            work_area_id: number;
            finished_at: string;
          }>();

        if (getWorkAreaTickets) {
          lastWorkAreaTicketId = getWorkAreaTickets.id;
          lastWorkTicketAreaId = getWorkAreaTickets.work_area_id;
          lastWorkAreaTicketFinish = getWorkAreaTickets.finished_at;
        }
      } else {
        // enviar mensaje a discord
        await sendScanLog(
          'VOUCHER NOT FOUND',
          `Voucher no existe en la tabla ${voucherID}, Line name : ${lineName}`
        );

        // send to discord and return error
        return { ok: false, message: 'Voucher no existe en la tabla' };
      }
      const getScanAction = SCAN_ACTION[moBarcodeRequest.charAt(4)];
      if (getScanAction) {
        action = getScanAction;
      } else {
        if (typeActionRequest === undefined || typeActionRequest === null) {
          // enviar mensaje a discord
          await sendScanLog(
            'ACTION NOT FOUND',
            `Se necesita action cuando se envia voucher principal ${moBarcodeRequest}, Barcode : ${badgeBarcode}, Line name : ${lineName}`
          );
          return {
            ok: false,
            message:
              'Falta setear accion ya que se esta usando voucher principal',
          };
        }
        action = typeActionRequest;
      }
      voucherCode = moBarcodeRequest;
    } else if (moBarcodeRequest.startsWith('MMRP')) {
      actionFound = true;
      // obtener repo id
      const repoID = Number(moBarcodeRequest.slice(4));
      // obtener mo_id a partir del repo
      findMO = await getMoInfoForScan('REPO', repoID);

      if (findMO) {
        isRepo = true;
        repoId = repoID;
        // obtener accion si ha sido seteado
        if (typeActionRequest !== undefined && typeActionRequest !== null) {
          action = typeActionRequest;
        } else {
          action = SCAN_ACTION['F'];
        }
      } else {
        // enviar mensaje a discord
        await sendScanLog(
          'REPO NOT FOUND',
          `Repo no existe en la tabla ${repoID}, Line name : ${lineName}`
        );

        // send to discord and return error
        return { ok: false, message: 'Repo no existe en la tabla' };
      }
    } else {
      // *** Revisar mo barcode para todos los clientes ***
      // verificar si es varsity
      if (moBarcodeRequest.includes('/')) {
        // obtener accion si ha sido seteado
        if (typeActionRequest !== undefined && typeActionRequest !== null) {
          action = typeActionRequest;
        } else {
          action = SCAN_ACTION['F'];
        }
        companyCode = 3;
        jobBarcode = moBarcodeRequest;
      } else if (moBarcodeRequest.startsWith('AINPPMO')) {
        action = SCAN_ACTION['S'];
        jobBarcode = moBarcodeRequest.slice(3);
        companyCode = 2;
      } else if (moBarcodeRequest.startsWith('APPMO')) {
        action = SCAN_ACTION['F'];
        jobBarcode = moBarcodeRequest.slice(1);
        companyCode = 2;
      } else if (moBarcodeRequest.startsWith('INPPMO')) {
        action = SCAN_ACTION['S'];
        jobBarcode = moBarcodeRequest.slice(2);
        companyCode = 1;
      } else if (moBarcodeRequest.startsWith('PPMO')) {
        jobBarcode = moBarcodeRequest;
        action = SCAN_ACTION['F'];
        companyCode = 1;
      }

      // obtener mo id
      // obtenemos el job_barcode y el company_code enviado
      if (!isNaN(companyCode)) {
        findMO = await getMoInfoForScan('BARCODE', companyCode, jobBarcode);
        if (!findMO) {
          // buscar en mo numbers en caso que lo proporcionado sea un numero de MO
          findMO = await getMoInfoForScan('MO_NUMBER', companyCode, jobBarcode);
          if (findMO) {
            actionFound = true;
            // set action finish if is empty
            if (typeActionRequest === undefined || typeActionRequest === null) {
              action = SCAN_ACTION['F'];
            }
          }
        } else {
          actionFound = true;
        }
      }

      //search for fragments
      const findFragment = await WorkFragments.query()
        .where('barcode', jobBarcode)
        .first();

      if (findFragment) {
        findMO = await getMoInfoForScan('MO_ID', findFragment.mo_id);

        if (findMO) {
          actionFound = true;
          // get fragments id and send to scan
          fragmentId = findFragment.id;
          // set action finish if is empty
          if (typeActionRequest === undefined || typeActionRequest === null) {
            action = SCAN_ACTION['F'];
          }
        } else {
          // enviar mensaje a discord
          await sendScanLog(
            'MO NOT FOUND',
            `No se encontró la MO ${moBarcodeRequest},\nBadgeOperator : ${badgeBarcode},\nCompanyCode: ${companyCode}, \nLine name : ${lineName}`
          );

          // no existe mandar a discord y retornar error
          return { ok: false, message: 'No se encontró la MO' };
        }
      }
    }
  }

  // reviasr si es un barcode valido
  if (!actionFound) {
    // enviar mensaje a discord
    await sendScanLog(
      'BARCODE FORMAT ERROR',
      `Formato incorrecto: ${moBarcodeRequest}, \nOperador : ${badgeBarcode}, \nLine name : ${lineName}`
    );

    // no son iguales enviar discord y retornar error
    return {
      ok: false,
      message: `Formato incorrecto: ${moBarcodeRequest}, \nOperador : ${badgeBarcode}, \nLine name : ${lineName}`,
    };
  }

  // revisar si mo id ya ha sido seteado   y es distinto al resultado
  if (
    moIDRequest !== undefined &&
    moIDRequest !== null &&
    moIDRequest !== findMO.mo_id
  ) {
    // enviar mensaje a discord
    await sendScanLog(
      'MOID SET IN REQUEST',
      `MO no coincide con la MO enviada en la peticion ${moIDRequest}`
    );

    // no son iguales enviar discord y retornar error
    return {
      ok: false,
      message: 'MO no coincide con la MO enviada en la peticion',
    };
  }

  // verificar si type_action ya ha sido seteado y es distinto al resultado
  if (
    typeActionRequest !== undefined &&
    typeActionRequest !== null &&
    typeActionRequest !== action
  ) {
    // enviar mensaje a discord
    await sendScanLog(
      'ACTION SET IN REQUEST',
      `Action no coincide con la Action enviada en la peticion ${typeActionRequest}, \nLine name : ${lineName}`
    );
    // retornar error
    return {
      ok: false,
      message: 'Action no coincide con la Action enviada en la peticion',
    };
  }

  // revisar si action y mo_id no son nulos
  if (findMO.mo_id === null) {
    // enviar mensaje a discord
    await sendScanLog(
      'MOID REQUIRED',
      `MO ID NULO, \n Operator : ${badgeBarcode}\n `
    );

    // retornar el error
    return { ok: false, message: 'MO ID NULO' };
  }

  if (typeActionRequest === null) {
    // enviar mensaje a discord
    await sendScanLog('ACTION REQUIRED', `ACTION NULO`);
    // retornar el error
    return {
      ok: false,
      message: 'ACTION ENVIADA EN LA LLAMADA Y NO TIENE FORMATO CORRECTO',
    };
  }

  // revisar cantidad proporcionada mayor que la cantidad de MO
  if (quantityRequest !== undefined && quantityRequest !== null) {
    if (quantityRequest > findMO.quantity) {
      // enviar mensaje a discord
      await sendScanLog(
        'QUANTITY SET',
        `EN PARCIAL SE REPORTO MAS DE LO QUE TIENE LA MO, \n Operator : ${badgeBarcode}, \nLine name : ${lineName} `
      );

      return {
        ok: false,
        message: 'CANTIDAD PROPORCIONADA ES MAYOR QUE LA MO',
      };
    }
    quantity = quantityRequest;
  } else {
    quantity = findMO.quantity;
  }

  return {
    ok: true,
    message: 'ok',
    data: {
      findMO,
      voucherId,
      lastWorkAreaTicketId,
      lastWorkTicketAreaId,
      lastWorkAreaTicketFinish,
      voucherCode,
      isRepo,
      repoId,
      jobBarcode,
      fragmentId,
      action,
      quantity,
    },
  };
}

export async function checkBadgeBarcodeRequest(data: {
  areaGroupID: number;
  areaID: number;
  styleID: number;
  updateCustomer: boolean;
  action: string;
  badgeBarcode: string;
  lastTicketID: number;
  lastTicketAreaID: number;
  lastAreaTicketFinish: string;
  companyCode: number;
  fragment_id: number;
}) {
  let workType = 0;
  let sam_value: string;
  let sam_id: number;
  let work_area_id: number;
  let workAreaId: number;
  let update_customer: boolean;
  let group_name: string;
  let group_description: string;
  let work_area_name: string;
  let default_work_area_line_id: number;
  let operator_id: number;
  let operator_barcode: string;
  let task_name: string;
  let work_area_group_id: number;
  let update_system: number;
  let varsity_system: number;
  let work_area_ticket_id: number;

  // revisar si se ha proporcionado work_area_group_id y revisar si existe en la tabla
  if (
    data.areaGroupID !== undefined &&
    data.areaGroupID !== null &&
    typeof data.areaGroupID === 'number'
  ) {
    const getWorkAreaGroups = await WorkAreaGroups.query()
      .where('id', data.areaGroupID)
      .select('work_area_id')
      .first()
      .castTo<{
        work_area_id: number;
      }>();

    if (getWorkAreaGroups) {
      // no se encontro work group con el work_group_id establecido en la peticion
      // enviar mensaje a discord
      await sendScanLog(
        'ACTION NULO',
        `grupo no encontrado con el id de grupo enviado en la peticion. GROUPID: ${data.areaGroupID}`
      );

      return {
        ok: false,
        message:
          'grupo no encontrado con el id de grupo enviado en la peticion',
      };
    }
  }

  // obtener area group por medio del codigo de barra
  if (data.badgeBarcode && typeof data.badgeBarcode === 'string') {
    const getWorkAreaGroups = await WorkAreaGroups.query()
      .join(
        'work_areas',
        'work_area_groups.work_area_id',
        '=',
        'work_areas.work_area_id'
      )
      .where('barcode', data.badgeBarcode)
      .select(
        'work_area_groups.work_area_id',
        'work_area_groups.id',
        'work_area_groups.default_work_area_line_id',
        'work_area_groups.update_customer',
        'work_area_groups.name',
        'work_area_groups.description',
        'work_area_groups.style_sam_group_id',
        'work_areas.work_type_id',
        'work_areas.area_name'
      )
      .first()
      .castTo<{
        work_area_id: number;
        id: number;
        default_work_area_line_id: number;
        update_customer: boolean;
        name: string;
        description: string;
        style_sam_group_id: number;
        work_type_id: number;
        area_name: string;
      }>();
    if (getWorkAreaGroups) {
      // reivsar si cuadra con el enviado
      if (
        data.areaID !== undefined &&
        data.areaID !== null &&
        data.areaID !== getWorkAreaGroups.work_area_id
      ) {
        // enviar mensaje a discord
        await sendScanLog(
          'AREAID SET IN REQUEST',
          `Area enviada en la peticion no coincide con el area asignada a este grupo. ${data.areaID}`
        );

        return {
          ok: false,
          message:
            'Area enviada en la peticion no coincide con el area asignada a este grupo',
        };
      }

      if (
        getWorkAreaGroups.style_sam_group_id !== null &&
        data.styleID !== null &&
        data.styleID !== undefined &&
        typeof data.styleID === 'number'
      ) {
        // get sam value and sam id from style_sam_group_id in the group
        const getStyleSamValue = await StyleSams.query()
          .where('style_id', data.styleID)
          .where('style_sam_group_id', getWorkAreaGroups.style_sam_group_id)
          .select('sam', 'id ')
          .first()
          .castTo<{ sam: string; id: number }>();

        if (getStyleSamValue) {
          sam_value = getStyleSamValue.sam;
          sam_id = getStyleSamValue.id;
        }
      }

      work_area_id = getWorkAreaGroups.work_area_id;
      workAreaId = work_area_id;
      update_customer = getWorkAreaGroups.update_customer;
      group_name = getWorkAreaGroups.name;
      group_description = getWorkAreaGroups.description;

      workType = getWorkAreaGroups.work_type_id;
      work_area_name = getWorkAreaGroups.area_name;
      // obteniendo  default work arealine id en caso de que sea null
      if (getWorkAreaGroups.default_work_area_line_id !== null) {
        default_work_area_line_id = getWorkAreaGroups.default_work_area_line_id;
      }

      if (
        data.updateCustomer !== undefined &&
        data.updateCustomer !== null &&
        !data.updateCustomer
      ) {
        update_customer = !data.updateCustomer;
      }

      // reivsar si cuadra el grupo si ha sido establecido con el enviado
      if (
        data.areaGroupID !== undefined &&
        data.areaGroupID !== null &&
        data.areaGroupID !== getWorkAreaGroups.id
      ) {
        // enviar mensaje a discord
        await sendScanLog(
          'GROUPID SET IN REQUEST',
          `Group Area enviada en la peticion no coincide con grupo encontrado por medio de barcode. ${data.areaGroupID}`
        );

        return {
          ok: false,
          message:
            'Group Area enviada en la peticion no coincide con grupo encontrado por medio de barcode',
        };
      }

      work_area_group_id = getWorkAreaGroups.id;
      // obteniendo operator_id de work_area_operator_map
      if (
        data.companyCode !== undefined &&
        data.companyCode !== null &&
        typeof data.companyCode === 'number'
      ) {
        const getOperatorMap = await WorkAreaOperatorMap.query()
          .where('work_area_id ', getWorkAreaGroups.work_area_id)
          .where('company_code  ', data.companyCode)
          .select('operator_id')
          .first()
          .castTo<{ operator_id: number }>();
        if (getOperatorMap) {
          operator_id = getOperatorMap.operator_id;
        } else {
          await sendScanLog(
            'OPERATOR MAP ERROR',
            `Problemas con operator map no existe operador para area: ${getWorkAreaGroups.work_area_id}, company: ${data.companyCode}`
          );

          return {
            ok: false,
            message: `Problemas con operator map no existe operador para area: ${getWorkAreaGroups.work_area_id}, company: ${data.companyCode}`,
          };
        }
      }
    } else {
      if (
        data.companyCode !== undefined &&
        data.companyCode !== null &&
        typeof data.companyCode === 'number'
      ) {
        // search operator id  in operator table
        const getOperator = await Operators.query()
          .where('barcode', data.badgeBarcode)
          .where('company_code  ', data.companyCode)
          .where('operator_status', 1)
          .select('operator_id', 'task')
          .castTo<{ operator_id: number; task: string }[]>();

        // operator found
        if (getOperator.length > 0) {
          operator_id = getOperator[0].operator_id;
          operator_barcode = data.badgeBarcode;
          task_name = getOperator[0].task;
        }
      }
    }
  }

  // validad areas en last work area ticket
  if (
    data.lastTicketID !== null &&
    work_area_id !== null &&
    data.lastTicketAreaID === work_area_id &&
    data.lastAreaTicketFinish === null
  ) {
    work_area_ticket_id = data.lastTicketID;
  }

  update_system = 1;
  varsity_system = 1;

  // setting update_system
  if (operator_id === null) {
    update_system = 1;
    varsity_system = 1;
  }

  if (data.action === 'START' && (workType === 12 || task_name === 'Sew')) {
    update_system = 2;
    varsity_system = 1;
  }

  if (data.action === 'FINISH' || data.action === 'CLOSE') {
    // se evalua que no sea costura se actualiza y si es costura se evalua si tiene el campo update customer igual a 0 para actualizar, sirve para las secciones
    // workType !== 12 no necesario, pero se deja para evitar inconveniente
    if (!update_customer) {
      update_system = 0;
      varsity_system = 0;
    } else {
      update_system = 1;
      varsity_system = 1;
    }

    if (
      data.action === 'FINISH' &&
      work_area_ticket_id !== undefined &&
      work_area_ticket_id !== null
    ) {
      update_system = 1;
      varsity_system = 1;
    }

    if (data.fragment_id !== undefined && data.fragment_id !== null) {
      update_system = 1;
      varsity_system = 1;
    }
  }

  return {
    ok: true,
    message: 'ok',
    data: {
      sam_value,
      sam_id,
      work_area_id,
      workAreaId,
      update_customer,
      group_name,
      group_description,
      work_area_name,
      default_work_area_line_id,
      operator_id,
      operator_barcode,
      task_name,
      work_area_group_id,
      update_system,
      varsity_system,
      work_area_ticket_id,
    },
  };
}

export async function createScanRequest(data: {
  action: string;
  work_area_ticket_id: number;
  work_area_id: number;
  mo_id: number;
  num: string;
  customer: string;
  operator_barcode: string;
  work_area_group_id: number;
  work_area_line_id: number;
  employee_id: number;
  is_repo: boolean;
  actual_date: Date;
  update_system: number;
  varsity_system: number;
  work_voucher_id: number;
  work_repo_id: number;
  work_fragment_id: number;
  task_name: string;
  group_name: string;
  group_description: string;
  group_null_barcode: string;
  operator_id: number;
  quantity: number;
  mo_unit_quantity: number;
  affected_units: number;
  partial: string;
  style_sam_id: number;
  sew_sam_value: string;
  area_name: string;
}) {
  const format1 = 'YYYY-MM-DD HH:mm:ss';
  let ticketID;
  // creating scans
  // action RECEIVED
  if (SCAN_ACTION['R'] === data.action) {
    if (
      (data.work_area_ticket_id === undefined ||
        data.work_area_ticket_id === null) &&
      data.work_area_id !== undefined &&
      data.work_area_id !== null
    ) {
      const getDisableDate = await WorkAreas.query()
        .where('work_area_id ', data.work_area_id)
        .select('disabled_date')
        .first()
        .castTo<{ disabled_date: Date }>();
      if (getDisableDate) {
        // if disable es nulo, crear ticket en area
        if (getDisableDate.disabled_date === null) {
          const work_area = await WorkAreas.query()
            .where('work_areas.work_area_id', data.work_area_id)
            .select('work_areas.default_work_voucher_type_id')
            .first()
            .castTo<{
              default_work_voucher_type_id: number;
            }>();

          const get_mo_info = { mo_id: data.mo_id, num: data.num };

          const response = await createVoucherAndTicket({
            is_repo: data.is_repo,
            get_mo_info,
            work_area_id: data.work_area_id,
            voucher_type_id: work_area.default_work_voucher_type_id,
            is_primary: false,
            work_area_group_id: data.work_area_group_id,
            next_work_area_id: null,
            work_area_line_id: data.work_area_line_id,
            location_id: null,
            employee_id: null,
            comments: null,
            ticket_status_id: null,
          });

          if (response.ok) {
            // create mo scan
            const addMoScans = await MoScans.query().insert({
              mo_id: data.mo_id,
              received_at: dayjs(data.actual_date).format(format1),
              work_area_id: data.work_area_id,
              supervisor: data.operator_barcode,
              task_name: data.task_name,
              work_voucher_id: response.voucher,
              work_area_group_id: data.work_area_group_id,
              work_area_ticket_id: response.ticket_id,
              poly_status: data.update_system,
              varsity_status: data.varsity_system,
              employee_id: data.employee_id,
            } as PartialModelObject<MoScans>);

            return {
              ok: true,
              message: 'Se ha creado un registro de recibido',
              data: addMoScans,
            };
          } else {
            // enviar mensaje a discord
            await sendScanLog(
              'TICKET NO PUDO SER CREADO',
              `Ya existe un ticket en esta area, no se puede recibir (RECEIVED) este MO. \n TICKET: ${data.work_area_ticket_id}, \n AREA GROUP: ${data.work_area_group_id}, \n MESSAGE : ${response.message}`
            );

            return {
              ok: false,
              data: 'Ya existe un ticket en esta area, no se puede recibir (RECEIVED) este MO',
            };
          }
        } else {
          // enviar mensaje a discord
          await sendScanLog(
            'TICKET NO PUDO SER CREADO',
            `Intenta recibir un ticket cuando la columna DesableDate no esta nula. \n TICKET: ${data.work_area_ticket_id}, \n AREA GROUP: ${data.work_area_group_id}`
          );

          return {
            ok: false,
            data: `Intenta recibir un ticket cuando la columna DesableDate no esta nula. \n TICKET: ${data.work_area_ticket_id}, \n AREA GROUP: ${data.work_area_group_id}`,
          };
        }
      } else {
        // enviar mensaje a discord
        await sendScanLog(
          'TICKET NO PUDO SER CREADO',
          `Intenta recibir un ticket cuando la columna DesableDate no esta nula. \n TICKET: ${data.work_area_ticket_id}, \n AREA GROUP: ${data.work_area_group_id}`
        );

        return {
          ok: false,
          data: `Intenta recibir un ticket cuando la columna DesableDate no esta nula. \n TICKET: ${data.work_area_ticket_id}, \n AREA GROUP: ${data.work_area_group_id}`,
        };
      }
    } else {
      // enviar mensaje a discord
      await sendScanLog(
        'TICKET ALREADY STARTED IN THIS AREA',
        `Ya existe un ticket en esta area, no se puede recibir (RECEIVED) este MO. \n TICKET: ${data.work_area_ticket_id}, \n AREA GROUP: ${data.work_area_group_id}`
      );

      return {
        ok: false,
        data: 'Ya existe un ticket en esta area, no se puede recibir (RECEIVED) este MO',
      };
    }
  }

  // action START
  if (SCAN_ACTION['S'] === data.action) {
    // validar si esta nulo group area id
    if (
      data.work_area_group_id === undefined ||
      data.work_area_group_id === null
    ) {
      // enviar mensaje a discord
      await sendScanLog(
        'GROUPID NULL',
        `Area group vacio no se puede iniciar(START) el trabajo. \n MOID : ${data.mo_id}, \n MO NUM:${data.num}, \n CUSTOMER: ${data.customer}, \n BARCODE : ${data.operator_barcode}`
      );

      return {
        ok: false,
        data: 'Area group vacio no se puede iniciar(START) el trabajo',
      };
    }
    // validar escaneos anteriores
    const getMoScan = await MoScans.query()
      .where('mo_id', data.mo_id)
      .where(
        'work_area_group_id',
        data.work_area_group_id !== undefined &&
          data.work_area_group_id !== null
          ? data.work_area_group_id
          : null
      )
      .where(
        'work_voucher_id',
        data.work_voucher_id !== undefined && data.work_voucher_id !== null
          ? data.work_voucher_id
          : null
      )
      .where(
        'work_area_ticket_id',
        data.work_area_ticket_id !== undefined &&
          data.work_area_ticket_id !== null
          ? data.work_area_ticket_id
          : null
      )
      .where(
        'work_repo_id',
        data.work_repo_id !== undefined && data.work_repo_id !== null
          ? data.work_repo_id
          : null
      )
      .where(
        'work_fragment_id',
        data.work_fragment_id !== undefined && data.work_fragment_id !== null
          ? data.work_fragment_id
          : null
      )
      .whereNull('received_at')
      .whereNull('closed_at')
      .whereNull('removed_at')
      .select('sew_ready', 'sew')
      .orderBy('scan_id', 'desc')
      .first()
      .castTo<{ sew_ready: string; sew: string }>();

    if (getMoScan) {
      if (getMoScan.sew_ready !== null && getMoScan.sew === null) {
        await sendScanLog(
          'MO ALREADY STARTED IN THIS AREA',
          `ya existe una produccion iniciada. \n MOID : ${data.mo_id}, \n MONUM : ${data.num}, \n CUSTOMER: ${data.customer}, \n GROUPID : ${data.work_area_group_id}, \n GROUP NAME : ${data.group_name}, \n GROUP DESCRIPTION : ${data.group_description}`
        );
        return {
          ok: false,
          data: `ya existe una produccion iniciada, Para la MO : ${data.num}`,
        };
      }
    }

    // create mo scan
    const addMoScans = await createScanRow({
      work_repo_id: data.work_repo_id ?? null, //work_repo_id
      sew_sam_value: null, //sew_sam_value
      style_sam_group_id: null, //style_sam_group_id
      style_sam_id: null, //style_sam_id
      poly_status: data.update_system, //poly_status
      varsity_status: data.varsity_system, //varsity_status
      work_area_group_shift_id: null, //work_area_group_shift_id
      quantity: null, //quantity
      mo_id: data.mo_id, //mo_id
      sew_ready: dayjs(data.actual_date).format(format1), //sew_ready
      sew: null, //sew
      supervisor: data.operator_barcode, //supervisor
      task_name: data.task_name, //task_name
      employee_id: data.employee_id, //employee_id
      work_area_id: data.work_area_id, //work_area_id
      work_voucher_id: data.work_voucher_id ?? null, //work_voucher_id
      work_area_group_id: data.work_area_group_id ?? null, //work_area_group_id
      work_area_ticket_id: data.work_area_ticket_id ?? null, //work_area_ticket_id
      work_area_line_id: data.work_area_line_id ?? null, //work_area_line_id
      work_fragment_id: data.work_fragment_id ?? null, //work_fragment_id
      is_repo: data.is_repo ?? false, //is_repo
    });

    return {
      ok: true,
      message: 'Se ha creado un registro de inicio de trabajo',
      data: addMoScans,
    };
  }

  // action FINISH
  if (SCAN_ACTION['F'] === data.action) {
    try {
      // validar si esta nulo group area id
      if (
        data.work_area_group_id === undefined ||
        data.work_area_group_id === null
      ) {
        // enviar mensaje a discord
        await sendScanLog(
          'GROUPID NULL',
          `Area group vacio no se puede finalizar (FINISH) el trabajo. \n MOID : ${data.mo_id}, \n MO NUM:${data.num}, \n CUSTOMER: ${data.customer}, \n BARCODE : ${data.group_null_barcode}, \n OPERATORID : ${data.operator_id}`
        );

        return {
          ok: false,
          data: 'Area group vacio no se puede finalizar (FINISH) el trabajo',
        };
      }
      // actualizar shift si existe
      const getShift = await WorkAreaGroupShifts.query()
        .where(
          'start_datetime_sv',
          '<=',
          dayjs(data.actual_date).format(format1)
        )
        .where('end_datetime_sv', '>=', dayjs(data.actual_date).format(format1))
        .where('work_area_group_id', data.work_area_group_id)
        .whereNull('removed_at')
        .select('id', 'operator_count');

      // validar escaneos anteriores
      const getMoScan = await MoScans.query()
        .where('mo_id', data.mo_id)
        .where(
          'work_area_group_id',
          data.work_area_group_id !== undefined &&
            data.work_area_group_id !== null
            ? data.work_area_group_id
            : null
        )
        .where(
          'work_voucher_id',
          data.work_voucher_id !== undefined && data.work_voucher_id !== null
            ? data.work_voucher_id
            : null
        )
        .where(
          'work_area_ticket_id',
          data.work_area_ticket_id !== undefined &&
            data.work_area_ticket_id !== null
            ? data.work_area_ticket_id
            : null
        )
        .where(
          'work_repo_id',
          data.work_repo_id !== undefined && data.work_repo_id !== null
            ? data.work_repo_id
            : null
        )
        .where(
          'work_fragment_id',
          data.work_fragment_id !== undefined && data.work_fragment_id !== null
            ? data.work_fragment_id
            : null
        )
        .whereNull('received_at')
        .whereNull('closed_at')
        .whereNull('removed_at')
        .where('is_repo', false)
        .select([
          'sew_ready',
          'sew',
          'scan_id',
          MoScans.query()
            .where('mo_id', data.mo_id)
            .where('work_area_group_id', data.work_area_group_id)
            .where('is_repo', false)
            .whereNull('removed_at')
            .sum('quantity')
            .as('qty_reported'),
        ])
        .orderBy('scan_id', 'desc')
        .first()
        .castTo<{
          sew_ready: string;
          sew: string;
          scan_id: number;
          qty_reported: number;
        }>();

      if (getMoScan) {
        // validar cantidades en caso que sea nula la cantidad
        if (
          (data.quantity === null || data.quantity === undefined) &&
          !data.is_repo
        ) {
          data.quantity = data.mo_unit_quantity - getMoScan.qty_reported;

          if (data.quantity < 0) {
            data.quantity = 0;
          }
        }
        // encontro escaneos anteriores se validan cantidades
        if (data.mo_unit_quantity === data.quantity && !data.is_repo) {
          data.quantity = data.mo_unit_quantity - getMoScan.qty_reported;
          if (data.quantity < 0) {
            data.quantity = 0;
          }

          if (data.quantity < 0) {
            data.quantity = 0;
          }
        }
        // significa que hay un escaneo previo, activo
        if (getMoScan.sew_ready !== null && getMoScan.sew === null) {
          // validar si creamos otro record
          if (data.partial !== undefined) {
            // update last record
            const updateMOScan = await MoScans.query()
              .update({
                sew: dayjs(data.actual_date).format(format1),
                poly_status:
                  data.update_system === 1
                    ? 1
                    : data.mo_unit_quantity <=
                      Number(getMoScan.qty_reported) + Number(data.quantity)
                    ? 0
                    : data.partial === 'partial'
                    ? 1
                    : 0,
                varsity_status: data.partial === 'partial' ? 1 : 0,
                work_area_line_id:
                  data.work_area_line_id !== undefined &&
                  data.work_area_line_id !== null
                    ? data.work_area_line_id
                    : null,
                quantity: data.quantity,
                employee_id: data.employee_id,
                is_repo:
                  data.is_repo !== undefined && data.is_repo == true
                    ? data.is_repo
                    : false,
                work_area_group_shift_id:
                  getShift.length > 0 ? getShift[0].id : null,
                style_sam_id:
                  data.style_sam_id !== undefined && data.style_sam_id !== null
                    ? data.style_sam_id
                    : null,
                sew_sam_value:
                  data.sew_sam_value !== undefined &&
                  data.sew_sam_value !== null
                    ? data.sew_sam_value
                    : null,
                work_fragment_id:
                  data.work_fragment_id !== undefined &&
                  data.work_fragment_id !== null
                    ? data.work_fragment_id
                    : null,
              })
              .where('scan_id ', getMoScan.scan_id);

            if (getShift.length > 0) {
              await updateShift(+getShift[0].id, +data.work_area_group_id);
            }

            data.partial =
              data.mo_unit_quantity <=
              Number(getMoScan.qty_reported) + Number(data.quantity)
                ? null
                : data.partial;

            if (updateMOScan > 0) {
              if (data.partial === 'partial') {
                const createMO = await createScanRow({
                  work_repo_id: data.work_repo_id ?? null, //work_repo_id
                  sew_sam_value: null, //sew_sam_value
                  style_sam_group_id: null, //style_sam_group_id
                  style_sam_id: null, //style_sam_id
                  poly_status: 1, //poly_status
                  varsity_status: 1, //varsity_status
                  work_area_group_shift_id: null, //work_area_group_shift_id
                  quantity: null, //quantity
                  mo_id: data.mo_id, //mo_id
                  sew_ready: dayjs(data.actual_date).format(format1), //sew_ready
                  sew: null, //sew
                  supervisor: data.operator_barcode, //supervisor
                  task_name: data.task_name, //task_name
                  employee_id: data.employee_id, //employee_id
                  work_area_id: data.work_area_id ?? null, //work_area_id
                  work_voucher_id: data.work_voucher_id ?? null, //work_voucher_id
                  work_area_group_id: data.work_area_group_id ?? null, //work_area_group_id
                  work_area_ticket_id: data.work_area_ticket_id ?? null, //work_area_ticket_id
                  work_area_line_id: data.work_area_line_id ?? null, //work_area_line_id
                  work_fragment_id: data.work_fragment_id ?? null, //work_fragment_id
                  is_repo: null, //is_repo
                });

                if (createMO instanceof MoScans) {
                  return {
                    ok: true,
                    message:
                      'Se actualizo un escaneo anterior con su fecha de sew partial',
                    data: updateMOScan,
                  };
                } else {
                  // enviar mensaje a discord
                  await sendScanLog(
                    'CREATE NEW MOSCAN ERROR. PARTIAL',
                    `Error al querer crear nuevo registro despues de actualizar registro anterior. opcion parcial. \n MOID: ${data.mo_id}, \n MO NUM:${data.num}, \n CUSTOMER: ${data.customer}, \n Operador: ${data.operator_barcode}, \n GROUP NAME : ${data.group_name}, \n GROUP DESCRIPTION: ${data.group_description}`
                  );

                  return {
                    ok: false,
                    data: 'Error al querer crear nuevo registro despues de actualizar registro anterior. opcion parcial',
                  };
                }
              } else {
                return {
                  ok: true,
                  message:
                    'Se actualizo un registro anterior y se mandara a actualizar sistema. Complete',
                  data: updateMOScan,
                };
              }
            } else {
              // enviar mensaje a discord
              await sendScanLog(
                'UPDATE MOSCAN ERROR',
                `No se logro actualizar el ultimo registro. \n MOID: ${data.mo_id}, \n MO NUM:${data.num}, \n CUSTOMER: ${data.customer}, \n Operador: ${data.operator_barcode}, \n GROUP NAME : ${data.group_name}, \n GROUP DESCRIPTION: ${data.group_description}`
              );

              return {
                ok: false,
                data: 'No se logro actualizar el ultimo registro',
              };
            }
          } else {
            // update last record
            if (
              getMoScan.qty_reported !== null &&
              getMoScan.qty_reported !== undefined &&
              !data.is_repo
            ) {
              data.quantity = data.mo_unit_quantity - getMoScan.qty_reported;
              if (data.quantity < 0) {
                data.quantity = 0;
              }
            }
            const updateMOScan = await MoScans.query()
              .update({
                sew: dayjs(data.actual_date).format(format1),
                poly_status: data.update_system,
                varsity_status: data.varsity_system,
                work_area_line_id:
                  data.work_area_line_id !== undefined &&
                  data.work_area_line_id !== null
                    ? data.work_area_line_id
                    : null,
                quantity:
                  data.is_repo !== undefined && data.is_repo == true
                    ? data.affected_units
                    : data.quantity,
                employee_id: data.employee_id,
                is_repo:
                  data.is_repo !== undefined && data.is_repo == true
                    ? data.is_repo
                    : false,
                work_area_group_shift_id:
                  getShift.length > 0 ? getShift[0].id : null,
                style_sam_id:
                  data.style_sam_id !== undefined && data.style_sam_id !== null
                    ? data.style_sam_id
                    : null,
                sew_sam_value:
                  data.sew_sam_value !== undefined &&
                  data.sew_sam_value !== null
                    ? data.sew_sam_value
                    : null,
                work_repo_id:
                  data.work_repo_id !== undefined && data.work_repo_id !== null
                    ? data.work_repo_id
                    : null,
                work_fragment_id:
                  data.work_fragment_id !== undefined &&
                  data.work_fragment_id !== null
                    ? data.work_fragment_id
                    : null,
              })
              .where('scan_id', getMoScan.scan_id);

            if (getShift.length > 0) {
              await updateShift(+getShift[0].id, +data.work_area_group_id);
            }
            if (updateMOScan > 0) {
              return {
                ok: true,
                message: 'Se actualizo un escaneo anterior con su fecha de sew',
                data: updateMOScan,
              };
            } else {
              // enviar mensaje a discord
              await sendScanLog(
                'UPDATE MOSCAN ERROR',
                `No se logro actualizar el ultimo registro. \n MOID: ${data.mo_id}, \n MO NUM:${data.num}, \n CUSTOMER: ${data.customer}, \n Operador: ${data.operator_barcode}, \n GROUP NAME : ${data.group_name}, \n GROUP DESCRIPTION: ${data.group_description}`
              );

              return {
                ok: false,
                data: `No se logro actualizar el ultimo registro. MOID: ${data.mo_id}, Operador: ${data.operator_barcode}`,
              };
            }
          }
        } else if (getMoScan.sew !== null) {
          // calcular 5 minutos despues del ultimo escaneo
          const sew = new Date(getMoScan.sew);
          let totalMin = (sew.getTime() - data.actual_date.getTime()) / 1000;

          // let minutes = Math.floor(totalMin / 60) % 60;
          // totalMin -= minutes * 60;
          totalMin /= 60;
          const minutes = Math.abs(Math.round(totalMin));

          if (minutes < 5) {
            // enviar mensaje a discord
            await sendScanLog(
              'SCANNED TIME 5 MIN.',
              `Ya ha escaneado espere 5 minutos para volver a escanear. \n MOID: ${data.mo_id},\n Operador: ${data.operator_barcode},\n AreaName : ${data.area_name},\n GroupName : ${data.group_name}, \n Time : ${minutes}`
            );

            return {
              ok: false,
              data: `Ya ha escaneado la MO: ${data.num}, Por Favor 5 minutos para volver a escanear`,
            };
          }
        }
      }
      // validar si creamos otro record
      if (
        data.partial !== undefined &&
        data.partial !== null &&
        !data.is_repo
      ) {
        const addmo = await createScanRow({
          work_repo_id: data.work_repo_id ?? null, //work_repo_id
          sew_sam_value: data.sew_sam_value ?? null, //sew_sam_value
          style_sam_group_id: null, //style_sam_group_id
          style_sam_id: data.style_sam_id ?? null, //style_sam_id
          poly_status:
            data.update_system === 1
              ? 1
              : data.mo_unit_quantity <= data.quantity
              ? 0
              : data.partial === 'partial'
              ? 1
              : 0, //poly_status
          varsity_status: data.partial === 'partial' ? 1 : 0, //varsity_status
          work_area_group_shift_id: getShift.length > 0 ? getShift[0].id : null, //work_area_group_shift_id
          quantity: data.quantity, //quantity
          mo_id: data.mo_id, //mo_id
          sew_ready: null, //sew_ready
          sew: dayjs(data.actual_date).format(format1), //sew
          supervisor: data.operator_barcode, //supervisor
          task_name: data.task_name, //task_name
          employee_id: data.employee_id, //employee_id
          work_area_id: data.work_area_id ?? null, //work_area_id
          work_voucher_id: data.work_voucher_id ?? null, //work_voucher_id
          work_area_group_id: data.work_area_group_id ?? null, //work_area_group_id
          work_area_ticket_id: data.work_area_ticket_id ?? null, //work_area_ticket_id
          work_area_line_id: data.work_area_line_id ?? null, //work_area_line_id
          work_fragment_id: data.work_fragment_id ?? null, //work_fragment_id
          is_repo:
            data.is_repo !== undefined && data.is_repo == true
              ? data.is_repo
              : false, //is_repo
        });

        if (getShift.length > 0) {
          await updateShift(+getShift[0].id, +data.work_area_group_id);
        }

        data.partial =
          data.mo_unit_quantity <= data.quantity ? null : data.partial;

        if (data.partial === 'partial') {
          // no se encontro registro, se crea nuevo scan con toda la informacion
          const createMO = await createScanRow({
            work_repo_id: data.work_repo_id ?? null, //work_repo_id
            sew_sam_value: data.sew_sam_value ?? null, //sew_sam_value
            style_sam_group_id: null, //style_sam_group_id
            style_sam_id: data.style_sam_id ?? null, //style_sam_id
            poly_status: 1, //poly_status
            varsity_status: 1, //varsity_status
            work_area_group_shift_id:
              getShift.length > 0 ? getShift[0].id : null, //work_area_group_shift_id
            quantity: null, //quantity
            mo_id: data.mo_id, //mo_id
            sew_ready: dayjs(data.actual_date).format(format1), //sew_ready
            sew: null, //sew
            supervisor: data.operator_barcode, //supervisor
            task_name: data.task_name, //task_name
            employee_id: data.employee_id, //employee_id
            work_area_id: data.work_area_id ?? null, //work_area_id
            work_voucher_id: data.work_voucher_id ?? null, //work_voucher_id
            work_area_group_id: data.work_area_group_id ?? null, //work_area_group_id
            work_area_ticket_id: data.work_area_ticket_id ?? null, //work_area_ticket_id
            work_area_line_id: data.work_area_line_id ?? null, //work_area_line_id
            work_fragment_id: data.work_fragment_id ?? null, //work_fragment_id
            is_repo:
              data.is_repo !== undefined && data.is_repo == true
                ? data.is_repo
                : false, //is_repo
          });
          if (createMO instanceof MoScans) {
            return {
              ok: true,
              message: 'Se creo nuevo registro y se agrego parcial',
              data: addmo,
            };
          } else {
            // enviar mensaje a discord
            await sendScanLog(
              'UPDATE MOSCAN ERROR.',
              `Error al actualizar escaneo anterior. \n MOID: ${data.mo_id}, \n MO NUM:${data.num}, \n CUSTOMER: ${data.customer}, \n Operador: ${data.operator_barcode}, \n GROUP NAME : ${data.group_name}, \n GROUP DESCRIPTION: ${data.group_description}`
            );

            return {
              ok: false,
              data: 'Error al actualizar escaneo anterior',
            };
          }
        } else {
          return {
            ok: true,
            message:
              'Se actualizo un registro anterior y se mandara a actualizar sistema. Complete',
            data: addmo,
          };
        }
      } else {
        if (data.quantity < 0) {
          data.quantity = 0;
        }
        // no se encontro registro, se crea nuevo scan con toda la informacion

        const addmo = await createScanRow({
          work_repo_id: data.work_repo_id ?? null, //work_repo_id
          sew_sam_value: data.sew_sam_value ?? null, //sew_sam_value
          style_sam_group_id: null, //style_sam_group_id
          style_sam_id: data.style_sam_id ?? null, //style_sam_id
          poly_status: data.update_system, //poly_status
          varsity_status: data.varsity_system, //varsity_status
          work_area_group_shift_id: getShift.length > 0 ? getShift[0].id : null, //work_area_group_shift_id
          quantity:
            data.is_repo !== undefined && data.is_repo == true
              ? data.affected_units === null ||
                data.affected_units === undefined
                ? data.quantity
                : data.affected_units
              : data.quantity, //quantity
          mo_id: data.mo_id, //mo_id
          sew_ready: null, //sew_ready
          sew: dayjs(data.actual_date).format(format1), //sew
          supervisor: data.operator_barcode, //supervisor
          task_name: data.task_name, //task_name
          employee_id: data.employee_id, //employee_id
          work_area_id: data.work_area_id ?? null, //work_area_id
          work_voucher_id: data.work_voucher_id ?? null, //work_voucher_id
          work_area_group_id: data.work_area_group_id ?? null, //work_area_group_id
          work_area_ticket_id: data.work_area_ticket_id ?? null, //work_area_ticket_id
          work_area_line_id: data.work_area_line_id ?? null, //work_area_line_id
          work_fragment_id: data.work_fragment_id ?? null, //work_fragment_id
          is_repo:
            data.is_repo !== undefined && data.is_repo == true
              ? data.is_repo
              : false, //is_repo
        });
        if (getShift.length > 0) {
          await updateShift(+getShift[0].id, +data.work_area_group_id);
        }

        if (addmo instanceof MoScans) {
          return {
            ok: true,
            message: 'Se creo nuevo registro',
            data: addmo,
          };
        } else {
          // enviar mensaje a discord
          await sendScanLog(
            'CREATE NEW MOSCAN ERROR.',
            `Error al agregar nuevo registro. \n MOID: ${data.mo_id}, \n MO NUM:${data.num}, \n CUSTOMER: ${data.customer}, \n Operador: ${data.operator_barcode}, \n GROUP NAME : ${data.group_name}, \n GROUP DESCRIPTION: ${data.group_description}`
          );

          return {
            ok: false,
            data: 'Error al agregar nuevo registro',
          };
        }
      }
    } catch (error) {
      return {
        ok: false,
        data: error,
      };
    }
  }

  // action CLOSE
  if (SCAN_ACTION['C'] === data.action) {
    // validar si esta nulo  area id sino es nulo cerrar ticket
    if (
      data.work_area_ticket_id !== undefined &&
      data.work_area_ticket_id !== null
    ) {
      // obtener status de area para completo
      const getWorkTicketStatuses = await WorkAreaTicketStatuses.query()
        .where('work_area_id', data.work_area_id)
        .where('name', 'Completo')
        .select('id');

      if (getWorkTicketStatuses.length > 0) {
        // close work_area_ticket_id (finished_ar and status)
        const updateTicket = await WorkAreaTickets.query()
          .update({
            finished_at: dayjs(data.actual_date).format(format1),
            work_area_ticket_status_id: getWorkTicketStatuses[0].id,
          })
          .where('id', data.work_area_ticket_id);

        if (updateTicket === 0) {
          // enviar mensaje a discord
          await sendScanLog(
            'UPDATE TICKET ERROR.',
            `Problema al actualizar ticket (CLOSE). TICKET: ${data.work_area_ticket_id}, AREA: ${data.work_area_id}`
          );

          return {
            ok: false,
            data: 'Problema al actualizar ticket (CLOSE)',
          };
        }
      } else {
        // enviar mensaje a discord
        await sendScanLog(
          'UPDATE TICKET ERROR.',
          `status de area no encontrada para completar. TICKET: ${data.work_area_ticket_id}, AREA: ${data.work_area_id}`
        );

        return {
          ok: false,
          data: 'status de area no encontrada para completar',
        };
      }
    }

    if (data.work_area_id !== undefined && data.work_area_id !== null) {
      // obtener antiguo escaneo en caso que se creara anteriormente un receive
      const getMoScan = await MoScans.query()
        .where('mo_id', data.mo_id)
        .where('work_area_id', data.work_area_id)
        .whereNull('sew')
        .whereNull('sew_ready')
        .whereNotNull('received_at')
        .whereNull('closed_at')
        .select('scan_id')
        .first()
        .castTo<{ scan_id: number }>();

      // existe un escaneo anterior
      if (getMoScan) {
        const updateMOScan = await MoScans.query()
          .update({
            closed_at: dayjs(data.actual_date).format(format1),
            supervisor: data.operator_barcode,
            task_name: data.task_name,
            work_area_group_id: data.work_area_group_id,
            work_area_ticket_id: data.work_area_ticket_id,
            work_voucher_id: data.work_voucher_id,
            poly_status: data.update_system,
            varsity_status: data.varsity_system,
          })
          .where('scan_id ', getMoScan.scan_id);

        return {
          ok: true,
          message: 'Se actualizo un escaneo anterior con su fecha de recibido',
          data: updateMOScan,
        };
      } else {
        // insertar nuevo registro con close date
        const addMoScans = await MoScans.query().insert({
          mo_id: data.mo_id,
          closed_at: dayjs(data.actual_date).format(format1),
          supervisor: data.operator_barcode,
          task_name: data.task_name,
          work_area_id: data.work_area_id,
          work_voucher_id: data.work_voucher_id,
          work_area_group_id: data.work_area_group_id,
          work_area_ticket_id: ticketID,
          poly_status: data.update_system,
          varsity_status: data.varsity_system,
          employee_id: data.employee_id,
        } as PartialModelObject<MoScans>);

        return {
          ok: true,
          message: 'Se creo nuevo registro con fecha de cerrado',
          data: addMoScans,
        };
      }
    } else {
      // enviar mensaje a discord
      await sendScanLog(
        'UPDATE TICKET ERROR.',
        `No se puede completar ya que area es nula. \n MOID: ${data.mo_id}, \n MO NUM:${data.num}, \n CUSTOMER: ${data.customer}, \n AREA: ${data.work_area_id}`
      );

      return {
        ok: false,
        data: 'No se puede completar ya que area es nula',
      };
    }
  }

  // enviar mensaje a discord
  await sendScanLog(
    'SCAN ERROR',
    `No se realizo ningun escaneo. \n MOID: ${data.mo_id}, \n MO NUM:${data.num}, \n CUSTOMER: ${data.customer}, \n Operador: ${data.operator_barcode}, \n GROUP NAME : ${data.group_name}, \n GROUP DESCRIPTION: ${data.group_description}`
  );

  return {
    ok: false,
    data: `No se realizo ningun escaneo. \n MOID: ${data.mo_id}, \n MO NUM:${data.num}, \n CUSTOMER: ${data.customer}, \n Operador: ${data.operator_barcode}, \n GROUP NAME : ${data.group_name}, \n GROUP DESCRIPTION: ${data.group_description}`,
  };
}

interface CreateScanData {
  mo_id: number | null;
  mo_barcode: string;
  group_barcode: string;
  quantity_reported: number | null;
  employee_id: number;
  work_area_line_id: number | null;
  type_action: string;
  work_area_group_id: number | null;
  work_area_id: number | null;
  update_customer: boolean | null;
  partial_option: string | null;
  affected_units: number | null;
  work_voucher_id: number | null;
  work_ticket_id: number | null;
}

export const createScan = async (createScanData: CreateScanData) => {
  /*
  moid: number | null,
  moBarcode: string,
  groupBarcode: string,
  quantityReported: number | null,
  employeeId: number,
  lineId: number | null,
  typeAction: string,
  areaGroupId: number | null,
  areaId: number | null,
  updateCustomer: boolean | null,
  partial_option: string | null,
  affectedUnits: number | null,
  voucherId: number | null,
  ticketId: number | null
) {
  */
  if (!createScanData.mo_barcode) {
    throw new Error('Barcode is required');
  }
  if (!createScanData.group_barcode) {
    throw new Error('Group Barcode is required');
  }
  if (!createScanData.employee_id) {
    throw new Error('Employee Id is required');
  }
  if (!createScanData.type_action) {
    throw new Error('Type Action is required');
  }

  const checkJob = await CheckJobRequest(
    createScanData.mo_id,
    createScanData.mo_barcode,
    null,
    createScanData.type_action,
    createScanData.group_barcode,
    null,
    createScanData.quantity_reported
  );
  if (checkJob.ok) {
    const checkGroup = await checkBadgeBarcodeRequest({
      areaGroupID: createScanData.work_area_group_id,
      areaID: createScanData.work_area_id,
      styleID: checkJob.data.findMO.style_id,
      updateCustomer: createScanData.update_customer,
      action: createScanData.type_action,
      badgeBarcode: createScanData.group_barcode,
      lastTicketID: null,
      lastTicketAreaID: null,
      lastAreaTicketFinish: null,
      companyCode: checkJob.data.findMO.company_code,
      fragment_id: checkJob.data.fragmentId,
    });
    if (checkGroup.ok) {
      let operatorBarcode: string;
      let taskName: string;
      // obteniendo informacion del operador
      if (
        checkGroup.data.operator_id !== undefined &&
        checkGroup.data.operator_id !== null
      ) {
        // obtener status de area para completo
        const getOperators = await Operators.query()
          .where('operator_id', +checkGroup.data.operator_id)
          .select('barcode', 'task')
          .first()
          .castTo<{ barcode: string; task: string }>();

        if (getOperators) {
          operatorBarcode = getOperators.barcode;
          taskName = getOperators.task;
        }
      }

      const createScan = await createScanRequest({
        action: checkJob.data.action,
        work_area_ticket_id:
          checkGroup.data.work_area_ticket_id == null
            ? createScanData.work_ticket_id
            : checkGroup.data.work_area_ticket_id,
        work_area_id: checkGroup.data.work_area_id,
        mo_id: checkJob.data.findMO.mo_id,
        num: checkJob.data.findMO.num,
        customer: checkJob.data.findMO.customer,
        operator_barcode: operatorBarcode,
        work_area_group_id: checkGroup.data.work_area_group_id,
        work_area_line_id: createScanData.work_area_line_id,
        employee_id: createScanData.employee_id,
        is_repo: checkJob.data.isRepo,
        actual_date: new Date(),
        update_system: checkGroup.data.update_system,
        varsity_system: checkGroup.data.varsity_system,
        work_voucher_id:
          checkJob.data.voucherId == null
            ? createScanData.work_voucher_id
            : checkJob.data.voucherId,
        work_repo_id: checkJob.data.repoId,
        work_fragment_id: checkJob.data.fragmentId,
        task_name: taskName,
        group_name: checkGroup.data.group_name,
        group_description: checkGroup.data.group_description,
        group_null_barcode: createScanData.group_barcode,
        operator_id: checkGroup.data.operator_id,
        quantity: createScanData.quantity_reported,
        mo_unit_quantity: checkJob.data.findMO.quantity,
        partial: createScanData.partial_option,
        style_sam_id: checkGroup.data.sam_id,
        sew_sam_value: checkGroup.data.sam_value,
        affected_units: createScanData.affected_units,
        area_name: checkGroup.data.work_area_name,
      });
      return createScan;
    } else {
      return checkGroup;
    }
  } else {
    return checkJob;
  }
};
