import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('repair_parts_users', (table) => {
    table.increments('id').primary();
    table.integer('employee_id').notNullable();
    table
      .foreign('employee_id', 'rpuei_employees_fk')
      .references('employee_id')
      .inTable('employees');
    table.integer('repair_part_building_id').notNullable();
    table
      .foreign('repair_part_building_id', 'rpu_buildings_fk')
      .references('building_id')
      .inTable('buildings');
    table.string('password').notNullable();
    table.boolean('is_active').notNullable().defaultTo(true);
    table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    table
      .timestamp('updated_at')
      .notNullable()
      .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('repair_parts_users');
}
