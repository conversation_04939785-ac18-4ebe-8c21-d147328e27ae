import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'plotter_combos',
    (table: Knex.TableBuilder) => {
      table.timestamp('removed_at').nullable();
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'plotter_combos',
    (table: Knex.TableBuilder) => {
      table.dropColumn('removed_at');
    }
  );
}
