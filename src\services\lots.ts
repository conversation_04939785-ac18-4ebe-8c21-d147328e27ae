import { ref } from 'objection';

import { WorkAreaBatches, WorkAreaTickets } from '@app/models/tickets.schema';

export interface ILots {
  id: number;
  name: string;
  description: string;
  ticketsByLot: number;
}

export const getLotsByAreaWithTicketCount = async (area: number) => {
  const data: ILots[] = [];

  const lots = await WorkAreaBatches.query()
    .where('work_area_batches.work_area_id', area)
    .whereNull('work_area_batches.finished_at')
    .select([
      'work_area_batches.id',
      'work_area_batches.name',
      'work_area_batches.description',
      WorkAreaTickets.query()
        .where('work_area_tickets.work_batch_id', ref('work_area_batches.id'))
        .groupBy('work_area_tickets.work_batch_id')
        .count('work_area_tickets.id')
        .as('ticketsByLot'),
    ])
    .castTo<ILots[]>();

  for (let i = 0; i < lots.length; i++) {
    if (lots[i].ticketsByLot > 0) {
      data.push(lots[i]);
    } else {
      data.push({ ...lots[i], ticketsByLot: 0 });
    }
  }

  return lots;
};
