import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.raw(`insert into mo_twill_laser_job_consumptions (mo_twill_laser_job_id,height,width,quantity,is_active)
select 
mtlj.id as mo_twill_laser_job_id,
mtlac.height,
mtlac.width,
mtlac.quantity,
mtlac.is_active
from mo_twill_laser_artist_consumptions mtlac 
left join mo_twill_laser_varsity_art_jobs mtlvaj on mtlvaj.id = mtlac.twill_laser_artist_id 
left join mo_twill_laser_jobs mtlj on mtlj.mo_twill_laser_varsity_art_job_id = mtlvaj.id
left join (
select
	mtljc.mo_twill_laser_job_id,
	count(*) as consumption_count
from mo_twill_laser_job_consumptions mtljc 
group by mtljc.mo_twill_laser_job_id 
) as ljc on ljc.mo_twill_laser_job_id = mtlj.id
where mtlj.id is not null
and ljc.mo_twill_laser_job_id is null`);
}

export async function down(): Promise<void> {
  return Promise.resolve();
}
