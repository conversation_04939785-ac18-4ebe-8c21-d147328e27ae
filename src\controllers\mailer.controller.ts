import type { Request, Response } from 'express';
import * as Handlebars from 'handlebars';
import * as nodemailer from 'nodemailer';

import { WorkTemplateQuerys, WorkTemplates } from '@app/models/template.schema';

export async function postMail(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const to = req.body.to;
  const cc = req.body.cc;
  const subject = req.body.subject;
  const text = req.body.text;
  const html = req.body.html;

  try {
    // let solo_repuesta = await nodemailer.createTestAccount();

    // create reusable transporter object using the default SMTP transport
    const transporter = nodemailer.createTransport({
      host: 'smtp.office365.com',
      port: 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.EMAIL_USER, // generated ethereal user
        pass: process.env.EMAIL_PASS, // generated ethereal password
      },
    });

    // send mail with defined transport object
    const info = await transporter.sendMail({
      from: process.env.EMAIL_USER, // sender address
      to, // list of receivers
      cc,
      subject, // Subject line
      text, // plain text body
      html, // html body
    });

    // Message sent: <<EMAIL>>

    return res.status(200).json({
      message_id: info.messageId,
    });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function sendTemplate(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const to: string = req.body.to;
  const cc: string = req.body.cc;
  const subject: string = req.body.subject;
  const templateId: number = req.body.template_id;
  const moduleId: number = req.body.module_id;

  try {
    if (!to) {
      return res.status(500).send('to is required for email');
    }

    if (!subject) {
      return res.status(500).send('subject is required for email');
    }

    // get and check template
    const getTemplate = await WorkTemplates.query()
      .select(
        'id',
        'name',
        'work_template_query_id',
        'template'
        // { voucherTypeId: "work_voucher_types.id" }
      )
      .where('id', templateId)
      .castTo<{
        id: number;
        name: string;
        work_template_query_id: number;
        template: string;
      }>()
      .first();

    if (!getTemplate) {
      return res.status(500).send('no template found');
    }

    // console.log(getTemplate)
    // console.log('query id', getTemplate.work_template_query_id)

    // get template query
    const getTemplateQuery = await WorkTemplateQuerys.query()
      .select(
        'id',
        'module_name',
        'query'
        // { voucherTypeId: "work_voucher_types.id" }
      )
      .where('id', getTemplate.work_template_query_id)
      .first()
      .castTo<{
        id: number;
        module_name: string;
        query: string;
      }>();

    if (!getTemplateQuery) {
      return res.status(500).send('missing template query');
    }

    const knex = WorkTemplateQuerys.knex(); // get the knex connection
    const templateDataQuery = await knex.raw(getTemplateQuery.query, {
      id: moduleId,
    });
    const templateData = templateDataQuery[0][0]; // first array for data, second to get the first value

    if (!templateData) {
      return res.status(500).send('no template data found');
    }

    // get child data
    const getTemplateChildrenQuerys = await WorkTemplateQuerys.query()
      .select(
        'id',
        'module_name',
        'query',
        'parent_field_name'
        // { voucherTypeId: "work_voucher_types.id" }
      )
      .where('parent_query_id', getTemplate.work_template_query_id)
      .whereNotNull('parent_field_name')
      .castTo<
        {
          id: number;
          module_name: string;
          query: string;
          parent_field_name: string;
        }[]
      >();

    if (getTemplateChildrenQuerys && getTemplateChildrenQuerys.length > 0) {
      for (const childQuery of getTemplateChildrenQuerys) {
        const childTemplateDataQuery = await knex.raw(childQuery.query, {
          id: moduleId,
        });
        const childTemplateData = childTemplateDataQuery[0]; // first array for data, second to get the first value

        templateData[childQuery.parent_field_name] = Object.values(
          // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
          JSON.parse(JSON.stringify(childTemplateData))
        );
      }
    }

    // function
    // cleancomments
    Handlebars.registerHelper(
      'cleancomments',
      function (aString: string): string {
        return aString?.replace(/\r?\n/g, '<br />');
      }
    );

    // create template and compile
    const template = Handlebars.compile(getTemplate.template);
    const filledTemplate = template(templateData);

    // create reusable transporter object using the default SMTP transport
    const transporter = nodemailer.createTransport({
      host: 'smtp.office365.com',
      port: 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.EMAIL_USER, // generated ethereal user
        pass: process.env.EMAIL_PASS, // generated ethereal password
      },
    });

    // send mail with defined transport object
    const info = await transporter.sendMail({
      from: process.env.EMAIL_USER, // sender address
      to, // list of receivers
      cc,
      subject, // Subject line
      html: filledTemplate, // html body
    });

    // Message sent: <<EMAIL>>
    return res.status(200).json({
      messageId: info.messageId,
    });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}
