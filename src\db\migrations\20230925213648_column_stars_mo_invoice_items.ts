import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'mo_invoice_items',
    (table: Knex.TableBuilder) => {
      table.boolean('stars').defaultTo(false);
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'mo_invoice_items',
    (table: Knex.TableBuilder) => {
      table.dropColumn('stars');
    }
  );
}
