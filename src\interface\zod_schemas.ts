import { z } from 'zod';

export const RegisterSchema = z.object({
  employeeID: z.coerce.number().min(1).max(999999999),
  password: z.string().min(6).max(20),
  areaID: z.coerce.number().min(1).max(999999999),
});

export const WarehousePullLoginSchema = z.object({
  employeeID: z.coerce.number().min(1).max(999999999),
  password: z.string().min(6).max(20),
});

export const WarehouseDownloaderLoginSchema = z.object({
  employeeID: z.coerce.number().min(1).max(999999999),
  password: z.string().min(6).max(20),
});

export const SearchOrderByMoOrVoucherSchema = z.object({
  query: z.string().min(1).max(100),
  searchOption: z.enum(['mo', 'voucher']),
  customer: z.string().min(1).max(100).optional(),
  sessionID: z.coerce.number().min(1).max(999999999),
});

export const AddOrdersToSessionSchema = z.object({
  sessionID: z.coerce.number(),
  orders: z
    .array(
      z.object({
        moID: z.coerce.number().min(1).max(999999999),
        voucherID: z.coerce.number().optional(),
      })
    )
    .min(1)
    .max(100),
});

export const sesssionIDSchema = z.object({
  sessionID: z.coerce.number(),
});

export const CreatePullSessionOrderPartsSchema = z.object({
  partNumbers: z.array(
    z.object({
      name: z.string(),
      subCategory: z.string(),
      units: z.string(),
      nums: z.array(
        z.object({
          num: z.string(),
          required: z.number(),
        })
      ),
    })
  ),
  sessionID: z.coerce.number(),
});

export const RoundedQuantitySchema = z.object({
  sessionOrderPartID: z.coerce.number(),
  quantity: z.coerce.number(),
});

export const CreateNewSessionSchema = z.object({
  employeeID: z.coerce.number(),
  sessionTypeID: z.coerce.number(),
  reason: z.string().min(1).max(100),
  comment: z.string().min(1).max(100),
  customer: z.string().min(1).max(100),
  isReposition: z.boolean(),
});

export const CreateAllocationsSchema = z.object({
  sessionID: z.coerce.number(),
  allocations: z
    .array(
      z.object({
        sessionOrderPartID: z.coerce.number(),
        containerCode: z.string(),
        quantity: z.coerce.number(),
      })
    )
    .min(1)
    .max(100),
});

export const CreateAllocationSchema = z.object({
  sessionID: z.coerce.number(),
  allocation: z.object({
    sessionOrderPartID: z.coerce.number(),
    containerCode: z.string(),
    quantity: z.coerce.number(),
  }),
});

export const UpdateAllocationSchema = z.object({
  id: z.coerce.number(),
  quantity: z.coerce.number(),
});

export const GetOrdersWithoutAllocationSchema = z.object({
  customer: z.string().min(1).max(100).optional(),
});

export const DownloadsSchema = z.object({
  employeeID: z.coerce.number(),
  moID: z.coerce.number(),
});

export const FinishDownloadSchema = z.object({
  container: z.object({
    voucher_id: z.coerce.number().optional(),
    warehouse_pull_session_allocations_id: z.coerce.number(),
    warehouse_pull_session_order_parts_id: z.coerce.number(),
    pulling_quantity: z.coerce.number(),
  }),
  employee_id: z.coerce.number(),
});

export const RepairPartsLoginSchema = z.object({
  userID: z.coerce.number(),
  password: z.coerce.string(),
});
