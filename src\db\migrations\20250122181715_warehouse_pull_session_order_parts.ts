import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(
    'warehouse_pull_session_order_parts',
    (table): void => {
      table.increments('id').unsigned().primary();
      table
        .integer('warehouse_pull_session_order_id', 10)
        .notNullable()
        .unsigned();
      table
        .foreign('warehouse_pull_session_order_id', 'wpsoi_wpsi_orders_fk')
        .references('id')
        .inTable('warehouse_pull_session_orders');
      table.string('part_number').notNullable();
      table.float('quantity_bom', 10, 2).notNullable();
      table.float('quantity_pulling', 10, 2).notNullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table
        .timestamp('updated_at')
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('warehouse_pull_session_order_parts');
}
