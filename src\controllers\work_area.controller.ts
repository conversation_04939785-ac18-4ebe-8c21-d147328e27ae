import type { Request, Response } from 'express';

const { WorkAreas, WorkAreaLines } = require('../models/tickets.schema');

export async function getWorkAreas(req: Request, res: Response) {
  try {
    const getWorkAreasName = await WorkAreas.query()
      .select(
        { idArea: 'work_areas.work_area_id' },
        { nameArea: 'work_areas.area_name' }
      )
      .whereNotNull('work_areas.area_name')
      .whereNull('work_areas.disabled_date');

    return res.status(200).json({
      ok: true,
      data: getWorkAreasName,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getWorkLines(req: Request, res: Response) {
  try {
    const workAreaID = req.body.work_area_id;
    const workLineBarcode = req.body.badge_barcode;

    if (workAreaID !== undefined && workAreaID !== null) {
      const getWorkLinesName = await WorkAreaLines.query()
        .select('id', 'work_area_id', 'name', 'barcode')
        .where('work_area_id', workAreaID);

      return res.status(200).json({
        ok: true,
        data: getWorkLinesName,
      });
    } else if (workLineBarcode !== undefined && workLineBarcode !== null) {
      const getWorkLinesName = await WorkAreaLines.query()
        .select('id', 'work_area_id', 'name', 'barcode')
        .where('barcode', workLineBarcode);

      return res.status(200).json({
        ok: true,
        data: getWorkLinesName,
      });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}
