// Importamos modulos de express para las rutas
import type { Request, Response } from 'express';

import type { IWareHouse } from '@app/interface/warehouse';

// Importacion de interfaces, le decimos a typescript que vamos a recibir en las reqs

// Importación del schema de nuestra base de datos
const { BraidReception } = require('../models/warehouse.schema');
const { Employee } = require('../models/employee.schema');

// obtener la lista de la base de datos

export async function getBraidReception(req: Request, res: Response) {
  try {
    const company: IWareHouse['company_code'] = req.params.company;
    // Consulta a la base de datos por medio del schema
    const receptions = await BraidReception.query().whereNull('removed_at');

    if (receptions.length > 0) {
      return res.status(200).json({
        receptions,
        status: 'ok',
        ok: true,
      });
    }
  } catch (error) {
    // Mensaje de error si la consulta falla
    return res.status(400).json({
      ok: false,
      message: 'Error, datos no validos',
      error,
    });
  }
}

export async function updateDatabase(req: Request, res: Response) {
  try {
    // console.log(req.body.id);
    // const column_id : WareHouseInterface = req.body.id;
    // const column_name : WareHouseInterface = req.body.column_name;
    // const column_value : WareHouseInterface = req.body.column_value;
    // update braid table
    if (req.body.column_name === 'received_area') {
      const numUpdated = await BraidReception.query()
        .findById(req.body.id)
        .patch({
          received_area: req.body.column_value,
        });

      return res.status(200).json({
        numUpdated,
        status: 'ok',
        ok: true,
      });
    } else if (req.body.column_name === 'comments') {
      const numUpdated = await BraidReception.query()
        .findById(req.body.id)
        .patch({
          comments: req.body.column_value,
        });

      return res.status(200).json({
        numUpdated,
        status: 'ok',
        ok: true,
      });
    }
  } catch (error) {
    // Mensaje de error si la consulta falla
    return res.status(400).json({
      ok: false,
      message: 'Error, datos no validos' + error,
      error,
    });
  }
}

export async function getUserinformation(req: Request, res: Response) {
  try {
    const employee = await Employee.query().where(
      'employee_id',
      req.body.employee_id
    );

    if (employee != null) {
      return res.status(200).json({
        employee,
        status: 'ok',
        ok: true,
      });
    } else {
      return res.status(500).json({
        status: 'false',
        ok: false,
      });
    }
  } catch (error) {
    // Mensaje de error si la consulta falla
    return res.status(400).json({
      ok: false,
      message: 'Error, datos no validos' + error,
      error,
    });
  }
}
