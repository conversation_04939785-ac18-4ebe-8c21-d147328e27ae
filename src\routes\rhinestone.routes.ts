import { Router } from 'express';

import {
  AddOrdersToMachine,
  AddRhinestoneColor,
  AddRhinestoneColorsToProvider,
  AddRhinestoneInventoryToMachine,
  AddRhinestoneSize,
  AddRhinestonesRange,
  AddRhinestonesSizesToProvider,
  AddStonesToFile,
  ConvertOrders,
  CreateOrder,
  CreateProvider,
  CreateRhinestoneOrder,
  DeleteFileFromARange,
  DeleteRangeFromAOrder,
  DeleteRhinestoneOrder,
  DeleteRhinestonesColorProvider,
  DeleteRhinestonesSizeProvider,
  GetAllOrders,
  GetFilesByOrderRange,
  GetMoNumbersByMoOrder,
  GetMoRhinestonesInfo,
  GetOrderInfo,
  GetOrdersByMoOrder,
  GetPartNumberBySizeAndColor,
  GetRhinestoneColors,
  GetRhinestoneInventoryByMachine,
  GetRhinestoneMachine,
  GetRhinestoneOrderItems,
  GetRhinestoneOrdersByMachine,
  GetRhinestonePartNumbersByName,
  GetRhinestoneProviders,
  GetRhinestoneSizes,
  GetRhinestonesProviderByID,
  GetRhinestonesRangeByName,
  GetRhinestonesRanges,
  GetTotalOrdersByDay,
  GetTotalPiecesByDay,
  RhinestoneOrdersReport,
  UpdateFile,
  UpdatePartNumberToInventoryOfMachine,
  UpdateRange,
  UpdateRhinestoneColor,
  UpdateRhinestoneItem,
  UpdateRhinestoneSize,
  UpdateRhinestonesProvider,
  UpdateRhinestonesRange,
  getRhinestoneOrderDetails,
} from '@app/controllers/rhinestone.controller';
import { validateJWT } from '@app/middlewares';

const rhinestonesRouter: Router = Router();

rhinestonesRouter
  .route('/')
  .post(validateJWT, CreateOrder)
  .delete(validateJWT, DeleteRhinestoneOrder);
rhinestonesRouter
  .route('/create-order')
  .post(validateJWT, CreateRhinestoneOrder);
rhinestonesRouter.route('/orders').post(GetAllOrders);
rhinestonesRouter
  .route('/color')
  .post(AddRhinestoneColor)
  .patch(UpdateRhinestoneColor)
  .get(GetRhinestoneColors);
rhinestonesRouter
  .route('/size')
  .post(AddRhinestoneSize)
  .patch(UpdateRhinestoneSize)
  .get(GetRhinestoneSizes);
rhinestonesRouter
  .route('/range')
  .post(AddRhinestonesRange)
  .patch(UpdateRhinestonesRange)
  .get(GetRhinestonesRanges);
rhinestonesRouter.route('/range/:rangeName').get(GetRhinestonesRangeByName);
rhinestonesRouter
  .route('/provider')
  .post(CreateProvider)
  .patch(UpdateRhinestonesProvider)
  .get(GetRhinestoneProviders);
rhinestonesRouter
  .route('/provider/:providerID')
  .get(GetRhinestonesProviderByID);
rhinestonesRouter
  .route('/color/provider')
  .post(AddRhinestoneColorsToProvider)
  .patch(DeleteRhinestonesColorProvider);
rhinestonesRouter
  .route('/size/provider')
  .post(AddRhinestonesSizesToProvider)
  .patch(DeleteRhinestonesSizeProvider);
rhinestonesRouter.route('/order/file/stones/add').post(AddStonesToFile);
rhinestonesRouter.route('/order/:order_id/items').get(GetRhinestoneOrderItems);
rhinestonesRouter.route('/order/items/:itemID').patch(UpdateRhinestoneItem);
rhinestonesRouter.route('/orders/by-mo-order').get(GetOrdersByMoOrder);
rhinestonesRouter.route('/machines').get(GetRhinestoneMachine);
rhinestonesRouter.route('/machines/orders').get(GetRhinestoneOrdersByMachine);
rhinestonesRouter.route('/machines/add-orders').patch(AddOrdersToMachine);
rhinestonesRouter
  .route('/machines/part-numbers')
  .get(GetRhinestoneInventoryByMachine)
  .patch(AddRhinestoneInventoryToMachine);
rhinestonesRouter.route('/part-numbers').get(GetRhinestonePartNumbersByName);
rhinestonesRouter
  .route('/part-numbers/size-color')
  .get(GetPartNumberBySizeAndColor);
rhinestonesRouter
  .route('/part-numbers')
  .patch(UpdatePartNumberToInventoryOfMachine);
rhinestonesRouter.route('/mos').get(GetMoNumbersByMoOrder);
rhinestonesRouter.route('/mo').get(GetMoRhinestonesInfo);
rhinestonesRouter.route('/pieces').get(GetTotalPiecesByDay);
rhinestonesRouter.route('/orders/day').get(GetTotalOrdersByDay);
rhinestonesRouter.route('/order').get(GetOrderInfo);
rhinestonesRouter.route('/order/range/files').get(GetFilesByOrderRange);
rhinestonesRouter.route('/order/range/file/delete').patch(DeleteFileFromARange);
rhinestonesRouter.route('/order/range/file/update').patch(UpdateFile);
rhinestonesRouter.route('/order/range/delete').patch(DeleteRangeFromAOrder);
rhinestonesRouter.route('/order/range/update').patch(UpdateRange);
rhinestonesRouter.route('/orders/report').post(RhinestoneOrdersReport);
rhinestonesRouter.route('/orders/convert').get(ConvertOrders);
rhinestonesRouter.route('/order/details').get(getRhinestoneOrderDetails);

export { rhinestonesRouter };
