import type { NextFunction, Request, Response } from 'express';

import {
  MoNumber,
  Operators,
  WorkAreaGroups,
  WorkAreaOperatorMap,
  WorkAreaTicketStatuses,
  WorkAreaTickets,
  WorkAreas,
  WorkInventoryBins,
  WorkVouchers,
  WorkZones,
} from '@app/models/tickets.schema';
import { sendScanLog } from '@app/services/discord';

// nuevos endpoints
export async function checkJobBarcode(
  req: Request,
  res: Response,
  next: NextFunction
) {
  try {
    const moIDRequest = Number(req.body.mo_id);
    const moBarcodeRequest = req.body.job_barcode;
    const voucherIDRequest = Number(req.body.voucher_id);
    const typeActionRequest = req.body.type_action;
    const badgeBarcode = req.body.badge_barcode;

    // *** revisar si moid ha sido seteado ***
    if (
      moIDRequest !== undefined &&
      moIDRequest !== null &&
      isNaN(moIDRequest) === false &&
      (req.body.company_code !== undefined || req.body.company_code !== null)
    ) {
      const getMoInformation = await MoNumber.query()
        .where('mo_id', moIDRequest)
        .select('mo_id');

      if (getMoInformation.length === 0) {
        // enviar mensaje a discord
        await sendScanLog(
          'MOID SET IN REQUEST',
          `No se encontró la MO enviada en la peticion ${moIDRequest}`
        );

        // no se encontro MO enviar error
        return res.status(200).json({
          ok: false,
          data: 'No se encontró la MO enviada en la peticion',
        });
      }
    }
    // *** obtener voucher y accion si ha sido enviado ***
    let action;

    if (
      moBarcodeRequest &&
      typeof moBarcodeRequest === 'string' &&
      moBarcodeRequest.startsWith('MEVB')
    ) {
      // obtener voucher id
      const voucherID = Number(moBarcodeRequest.slice(5));

      // verificar si voucher id a sido seteado y es diferente del obtenido en el codigo de barra
      if (
        isNaN(voucherID) === false &&
        voucherIDRequest !== undefined &&
        voucherIDRequest !== null &&
        voucherID !== voucherIDRequest
      ) {
        // enviar mensaje a discord
        await sendScanLog(
          'VOUCHERID SET IN REQUEST',
          `No se encontró la MO enviada en la peticion ${voucherIDRequest}`
        );

        // send to discord and return error
        return res.status(200).json({
          ok: false,
          data: 'Voucher no es igual al enviado en la peticion',
        });
      }

      // set voucher id
      req.body.voucher_id = voucherID;
      // obtener mo_id a partir del voucher
      const moNumber: any = await WorkVouchers.query()
        .join('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
        .where('work_vouchers.id', voucherID)
        .select(
          'mo_numbers.mo_id',
          'mo_numbers.company_code',
          'mo_numbers.quantity'
        )
        .limit(1);

      if (moNumber.length > 0) {
        // set mo id
        req.body.mo_id = moNumber[0].mo_id;
        req.body.company_code = moNumber[0].company_code;
        req.body.mo_quantity = moNumber[0].quantity;

        // set last_work_area_ticket_id WorkAreaTickets
        const getWorkAreaTickets = await WorkAreaTickets.query()
          .where('work_voucher_id ', voucherID)
          .select('id', 'work_area_id', 'finished_at')
          .orderBy('id', 'desc')
          .limit(1)
          .castTo<
            {
              id: number;
              work_area_id: number;
              finished_at: string;
            }[]
          >();

        if (getWorkAreaTickets.length > 0) {
          req.body.last_work_area_ticket_id = getWorkAreaTickets[0].id;
          req.body.last_work_ticket_area_id =
            getWorkAreaTickets[0].work_area_id;
          req.body.last_work_area_ticket_finish =
            getWorkAreaTickets[0].finished_at;
        }
      } else {
        // enviar mensaje a discord
        await sendScanLog(
          'VOUCHER NOT FOUND',
          `Voucher no existe en la tabla ${voucherID}`
        );

        // send to discord and return error
        return res
          .status(200)
          .json({ ok: false, data: 'Voucher no existe en la tabla' });
      }

      // obtener accion a partir del voucher
      switch (moBarcodeRequest.charAt(4)) {
        case 'R':
          action = 'RECEIVED';
          break;

        case 'S':
          action = 'START';
          break;

        case 'F':
          action = 'FINISH';
          break;

        case 'C':
          action = 'CLOSE';
          break;

        default:
          if (
            req.body.type_action === undefined ||
            req.body.type_action === null
          ) {
            // enviar mensaje a discord
            await sendScanLog(
              'ACTION NOT FOUND',
              `Se necesita action cuando se envia voucher principal ${moBarcodeRequest}, Barcode : ${badgeBarcode}`
            );

            return res.status(200).json({
              ok: false,
              data: 'Falta setear accion ya que se esta usando voucher principal',
            });
          }
          // DEFAULT
          action = req.body.type_action;
          break;
      }
      req.body.voucher_code = moBarcodeRequest;
    } else {
      // *** Revisar mo barcode para todos los clientes ***
      // verificar si es varsity
      if (
        moBarcodeRequest &&
        typeof moBarcodeRequest === 'string' &&
        moBarcodeRequest.includes('/')
      ) {
        // obtener accion si ha sido seteado
        if (typeActionRequest !== undefined && typeActionRequest !== null) {
          action = typeActionRequest;
        } else {
          action = 'FINISH';
        }
        req.body.company_code = 3;
        req.body.job_barcode = moBarcodeRequest;
      }

      // verificamos si es adidas
      if (
        moBarcodeRequest &&
        typeof moBarcodeRequest === 'string' &&
        moBarcodeRequest.startsWith('AINPPMO')
      ) {
        action = 'START';
        req.body.job_barcode = moBarcodeRequest.slice(3);
        req.body.company_code = 2;
      } else if (
        moBarcodeRequest &&
        typeof moBarcodeRequest === 'string' &&
        moBarcodeRequest.startsWith('APPMO')
      ) {
        action = 'FINISH';
        req.body.job_barcode = moBarcodeRequest.slice(1);
        req.body.company_code = 2;
      }

      // verificamos si es varpro
      if (
        moBarcodeRequest &&
        typeof moBarcodeRequest === 'string' &&
        moBarcodeRequest.startsWith('INPPMO')
      ) {
        action = 'START';
        req.body.job_barcode = moBarcodeRequest.slice(2);
        req.body.company_code = 1;
      } else if (
        moBarcodeRequest &&
        typeof moBarcodeRequest === 'string' &&
        moBarcodeRequest.startsWith('PPMO')
      ) {
        action = 'FINISH';
        req.body.company_code = 1;
      }
    }

    // si no es voucher
    if (
      (req.body.mo_id === undefined || req.body.mo_id === null) &&
      req.body.company_code !== undefined &&
      req.body.company_code !== null
    ) {
      // obtener mo id
      // obtenemos el job_barcode y el company_code enviado
      const moBarcode = req.body.job_barcode;
      const companyCode = Number(req.body.company_code);

      if (
        moBarcode &&
        companyCode &&
        typeof moBarcode === 'string' &&
        typeof companyCode === 'number' &&
        isNaN(companyCode) === false
      ) {
        const findMO = await MoNumber.query()
          .where('mo_barcode', moBarcode)
          .where('company_code', companyCode)
          .select('mo_id', 'quantity', 'mo_status', 'num', 'customer')
          .castTo<
            {
              mo_id: number;
              quantity: number;
              mo_status: string;
              num: string;
              customer: string;
            }[]
          >();

        if (findMO.length > 0) {
          // set mo id
          req.body.mo_id = findMO[0].mo_id;
          req.body.mo_quantity = findMO[0].quantity;

          req.body.customer = findMO[0].customer;
          req.body.mo_status = findMO[0].mo_status;
          req.body.num = findMO[0].num;

          // enviar discord si MO no es ok to produce ni in production
          if (
            findMO[0].mo_status !== 'Ok to Produce' &&
            findMO[0].mo_status !== 'In Production' &&
            findMO[0].mo_status !== 'Materials OK'
          ) {
            await sendScanLog(
              'MO STATUS LOG',
              `La MO ${findMO[0].num},\n Cliente : ${findMO[0].customer}, \n Tiene un Status ${findMO[0].mo_status}, \n Barcode: ${badgeBarcode}`
            );
          }
        } else {
          // buscar en mo numbers en caso que lo proporcionado sea un numero de MO
          const findMO = await MoNumber.query()
            .where('num', moBarcode)
            .where('company_code', companyCode)
            .select('mo_id', 'quantity')
            .castTo<{ mo_id: number; quantity: number }[]>();

          if (findMO.length > 0) {
            // set mo id
            req.body.mo_id = findMO[0].mo_id;
            req.body.mo_quantity = findMO[0].quantity;
            // set action finish if is empty
            if (typeActionRequest === undefined || typeActionRequest === null) {
              req.body.type_action = 'FINISH';
            }
          } else {
            // enviar mensaje a discord
            await sendScanLog(
              'MO NOT FOUND',
              `No se encontró la MO ${moBarcode}, BadgeOperator : ${badgeBarcode}, CompanyCode: ${companyCode}`
            );

            // no existe mandar a discord y retornar error
            return res
              .status(200)
              .json({ ok: false, data: 'No se encontró la MO' });
          }
        }
      }
    }

    // revisar si mo id ya ha sido seteado   y es distinto al resultado
    if (
      moIDRequest !== undefined &&
      moIDRequest !== null &&
      isNaN(moIDRequest) === false &&
      moIDRequest !== req.body.mo_id
    ) {
      // enviar mensaje a discord
      await sendScanLog(
        'MOID SET IN REQUEST',
        `MO no coincide con la MO enviada en la peticion ${moIDRequest}`
      );

      // no son iguales enviar discord y retornar error
      return res.status(200).json({
        ok: false,
        data: 'MO no coincide con la MO enviada en la peticion',
      });
    }

    // verificar si type_action ya ha sido seteado y es distinto al resultado
    if (
      typeActionRequest !== undefined &&
      typeActionRequest !== null &&
      typeof typeActionRequest === 'string' &&
      typeActionRequest !== action
    ) {
      // enviar mensaje a discord
      await sendScanLog(
        'ACTION SET IN REQUEST',
        `Action no coincide con la Action enviada en la peticion ${typeActionRequest}`
      );
      // send to discord and return error

      return res.status(200).json({
        ok: false,
        data: 'Action no coincide con la Action enviada en la peticion',
      });
    } else {
      // set action
      req.body.type_action = action;
    }

    // revisar si action y mo_id no son nulos
    if (req.body.mo_id === null) {
      // enviar mensaje a discord
      await sendScanLog('MOID REQUIRED', `MO ID NULO`);

      // send to discord and return error
      return res.status(400).json({ ok: false, data: 'MO ID NULO' });
    }

    if (req.body.type_action === null) {
      // send to discord and return error
      // enviar mensaje a discord
      await sendScanLog('ACTION REQUIRED', `ACTION NULO`);

      return res.status(200).json({ ok: false, data: 'ACTION NULO' });
    }

    // revisar cantidad proporcionada mayor que la cantidad de MO
    if (req.body.quantity !== undefined && req.body.quantity !== null) {
      if (req.body.quantity > req.body.mo_quantity) {
        // enviar mensaje a discord
        await sendScanLog('QUANTITY SET', `ACTION NULO`);

        return res.status(200).json({
          ok: false,
          data: 'CANTIDAD PROPORCIONADA ES MAYOR QUE LA MO',
        });
      }
    } else {
      req.body.quantity = req.body.mo_quantity;
    }

    return next();
  } catch (error) {
    const moBarcodeRequest = req.body.job_barcode;
    const badgeBarcode = req.body.badge_barcode;

    // enviar mensaje a discord
    await sendScanLog(
      'MIDDLEWARE  CHECK JOBBARCODE ERROR',
      `checkjobbarcode : ${moBarcodeRequest}, badgebarcode: ${badgeBarcode}`
    );

    return next(error);
  }
}

export async function checkBadgeBarcode(
  req: Request,
  res: Response,
  next: NextFunction
) {
  try {
    const badgeBarcode = req.body.badge_barcode;
    const areaID = req.body.work_area_id;
    const areaGroupID = Number(req.body.work_area_group_id);
    const companyCode = Number(req.body.company_code);
    const lastTicketID = req.body.last_work_area_ticket_id;
    const lastTicketAreaID = req.body.last_work_ticket_area_id;
    const lastAreaTicketFinish = req.body.last_work_area_ticket_finish;
    const action = req.body.type_action;
    let workType = 0;

    // revisar si se ha proporcionado work_area_group_id y revisar si existe en la tabla
    if (
      areaGroupID !== undefined &&
      areaGroupID !== null &&
      isNaN(areaGroupID) === false
    ) {
      const getWorkAreaGroup = await WorkAreaGroups.query()
        .where('id', areaGroupID)
        .select('work_area_id');

      if (getWorkAreaGroup.length === 0) {
        // no se encontro work group con el work_group_id establecido en la peticion
        // enviar mensaje a discord
        await sendScanLog(
          'ACTION NULO',
          `grupo no encontrado con el id de grupo enviado en la peticion. GROUPID: ${areaGroupID}`
        );

        return res.status(200).json({
          ok: false,
          data: 'grupo no encontrado con el id de grupo enviado en la peticion',
        });
      }
    }

    // obtener area group por medio del codigo de barra
    if (
      badgeBarcode !== undefined &&
      badgeBarcode !== null &&
      typeof badgeBarcode === 'string'
    ) {
      const getWorkAreaGroups = await WorkAreaGroups.query()
        .where('barcode', badgeBarcode)
        .select(
          'work_area_id',
          'id',
          'default_work_area_line_id',
          'update_customer',
          'name',
          'description'
        )
        .castTo<
          {
            work_area_id: number;
            id: number;
            default_work_area_line_id: number;
            update_customer: number;
            name: string;
            description: string;
          }[]
        >();

      if (getWorkAreaGroups.length > 0) {
        // reivsar si cuadra con el enviado
        if (
          areaID !== undefined &&
          areaID !== null &&
          areaID !== getWorkAreaGroups[0].work_area_id
        ) {
          // enviar mensaje a discord
          await sendScanLog(
            'AREAID SET IN REQUEST',
            `Area enviada en la peticion no coincide con el area asignada a este grupo. ${areaID}`
          );

          return res.status(200).json({
            ok: false,
            data: 'Area enviada en la peticion no coincide con el area asignada a este grupo',
          });
        }
        req.body.work_area_id = getWorkAreaGroups[0].work_area_id;
        req.body.workAreaId = req.body.work_area_id;
        req.body.update_customer = getWorkAreaGroups[0].update_customer;
        req.body.group_name = getWorkAreaGroups[0].name;
        req.body.group_description = getWorkAreaGroups[0].description;
        // obteniendo  default work arealine id en caso de que sea null
        if (getWorkAreaGroups[0].default_work_area_line_id !== null) {
          req.body.default_work_area_line_id =
            getWorkAreaGroups[0].default_work_area_line_id;
        }

        // reivsar si cuadra el grupo si ha sido establecido con el enviado
        if (
          areaGroupID !== undefined &&
          areaGroupID !== null &&
          areaGroupID !== getWorkAreaGroups[0].id
        ) {
          // enviar mensaje a discord
          await sendScanLog(
            'GROUPID SET IN REQUEST',
            `Group Area enviada en la peticion no coincide con grupo encontrado por medio de barcode. ${areaGroupID}`
          );

          return res.status(200).json({
            ok: false,
            data: 'Group Area enviada en la peticion no coincide con grupo encontrado por medio de barcode',
          });
        }
        req.body.work_area_group_id = getWorkAreaGroups[0].id;

        // obtener typo en base al area
        const getWorkType = await WorkAreas.query()
          .join(
            'work_area_groups',
            'work_areas.work_area_id',
            '=',
            'work_area_groups.work_area_id'
          )
          .where('work_area_groups.barcode', badgeBarcode)
          .select('work_areas.work_type_id', 'work_areas.area_name')
          .limit(1)
          .castTo<
            {
              work_type_id: number;
              area_name: string;
            }[]
          >();

        if (getWorkType.length > 0) {
          workType = getWorkType[0].work_type_id;
          req.body.work_area_name = getWorkType[0].area_name;
        }

        // obteniendo operator_id de work_area_operator_map
        if (
          companyCode !== undefined &&
          companyCode !== null &&
          isNaN(companyCode) === false
        ) {
          const getOperatorMap = await WorkAreaOperatorMap.query()
            .where('work_area_id', getWorkAreaGroups[0].work_area_id)
            .where('company_code', companyCode)
            .select('operator_id')
            .limit(1)
            .castTo<
              {
                operator_id: number;
              }[]
            >();

          if (getOperatorMap.length > 0) {
            req.body.operator_id = getOperatorMap[0].operator_id;
          } else {
            await sendScanLog(
              'OPERATOR MAP ERROR',
              `Problemas con operator map no existe operador para area: ${getWorkAreaGroups[0].work_area_id}, company: ${companyCode}`
            );

            return res.status(200).json({
              ok: false,
              data: `Problemas con operator map no existe operador para area: ${getWorkAreaGroups[0].work_area_id}, company: ${companyCode}`,
            });
          }
        }
      } else {
        if (companyCode !== undefined && companyCode !== null) {
          // search operator id  in operator table
          const getOperator = await Operators.query()
            .where('barcode', badgeBarcode)
            .where('company_code  ', companyCode)
            .where('operator_status', 1)
            .select('operator_id', 'task')
            .castTo<
              {
                operator_id: number;
                task: string;
              }[]
            >();

          // operator found
          if (getOperator.length > 0) {
            req.body.operator_id = getOperator[0].operator_id;
            req.body.operator_barcode = badgeBarcode;
            req.body.task_name = getOperator[0].task;
          }
        }
      }
    }

    // validad areas en last work area ticket
    if (
      lastTicketID !== null &&
      req.body.work_area_id !== null &&
      lastTicketAreaID === req.body.work_area_id &&
      lastAreaTicketFinish === null
    ) {
      req.body.work_area_ticket_id = lastTicketID;
    }

    req.body.update_system = 1;
    req.body.varsity_system = 1;

    // setting update_system
    if (req.body.operator_id === null) {
      req.body.update_system = 1;
      req.body.varsity_system = 1;
    }

    if (
      action === 'START' &&
      (workType === 12 || req.body.task_name === 'Sew')
    ) {
      req.body.update_system = 2;
      req.body.varsity_system = 1;
    }

    if (action === 'FINISH' || action === 'CLOSE') {
      // se evalua que no sea costura se actualiza y si es costura se evalua si tiene el campo update customer igual a 0 para actualizar, sirve para las secciones
      if (workType !== 12 || req.body.update_customer === 0) {
        req.body.update_system = 0;
        req.body.varsity_system = 0;
      } else {
        req.body.update_system = 1;
        req.body.varsity_system = 1;
      }

      if (
        action === 'FINISH' &&
        req.body.work_area_ticket_id !== undefined &&
        req.body.work_area_ticket_id !== null
      ) {
        req.body.update_system = 1;
        req.body.varsity_system = 1;
      }
    }

    return next();
  } catch (error) {
    // enviar mensaje a discord
    const moBarcodeRequest = req.body.job_barcode;
    const badgeBarcode = req.body.badge_barcode;

    await sendScanLog(
      'MIDDLEWARE CHECK BADGEBARCODE ERROR',
      `checkjobbarcode : ${moBarcodeRequest}, badgebarcode: ${badgeBarcode}`
    );

    return next(error);
  }
}

// antiguos endpoints
// funcion para verificar el tipo de accion que se realizara (entrada, salida y cliente)
export function checkTypeAction(
  req: Request,
  res: Response,
  next: NextFunction
) {
  // obtenemos el barcode enviado
  const moBarcode = req.body.mo_barcode;
  const proceso = req.body.proceso;
  const typeActionRequest = req.body.typeAction;
  let action;

  try {
    // verificar si es un voucher o no
    if (moBarcode && typeof moBarcode === 'string') {
      if (moBarcode.startsWith('MEVB')) {
        if (moBarcode.length > 4) {
          // evaluar tipo de accion y tipo voucher
          req.body.voucher = true;

          if (moBarcode.charAt(4) === 'R') {
            // RECEIVED
            action = 'RECEIVED';
            req.body.voucherCode = moBarcode;

            return next();
          } else if (moBarcode.charAt(4) === 'S') {
            // START
            action = 'START';
            req.body.voucherCode = moBarcode;

            return next();
          } else if (moBarcode.charAt(4) === 'F') {
            // FINISH
            action = 'FINISH';
            req.body.voucherCode = moBarcode;

            return next();
          } else if (moBarcode.charAt(4) === 'C') {
            // CLOSE
            action = 'CLOSE';
            req.body.voucherCode = moBarcode;

            return next();
          }
        } else {
          return res
            .status(200)
            .json({ ok: false, data: 'NO SE ENCONTRO ACCION PARA EL VOUCHER' });
        }
      } else {
        // verificar si es varsity
        req.body.voucher = false;
        if (moBarcode.includes('/')) {
          const MOVarsity = moBarcode.split('/');

          action = proceso;
          req.body.companyCode = 3;
          req.body.moOrder = MOVarsity[0];
          req.body.num = moBarcode;
          req.body.moBarcode = moBarcode;
          req.body.client = 'VARSITY';

          return next();
        }

        // verificamos si es adidas
        if (moBarcode.startsWith('AINPPMO')) {
          action = 'START';
          req.body.moBarcode = moBarcode.slice(3);
          req.body.companyCode = 2;
          req.body.client = 'ADIDAS';

          return next();
        } else if (moBarcode.startsWith('APPMO')) {
          action = 'FINISH';
          req.body.moBarcode = moBarcode.slice(1);
          req.body.companyCode = 2;
          req.body.client = 'ADIDAS';

          return next();
        }

        // verificamos si es varpro
        if (moBarcode.startsWith('INPPMO')) {
          action = 'START';
          req.body.moBarcode = moBarcode.slice(2);
          req.body.companyCode = 1;
          req.body.client = 'VARPRO';

          return next();
        } else if (moBarcode.startsWith('PPMO')) {
          action = 'FINISH';
          req.body.moBarcode = moBarcode;
          req.body.companyCode = 1;
          req.body.client = 'VARPRO';

          return next();
        }
      }
    }

    // verificar si typeAction ya ha sido seteado y es distinto al resultado
    if (typeActionRequest !== null && typeActionRequest !== action) {
      // send to discord and return error
    } else {
      req.body.typeAction = action;
    }
  } catch (error) {
    return next(error);
  }
}

// funcion para buscar en mo_numbers el mo_id del mo_barcode enviado
export async function getMoId(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void | Response<unknown, Record<string, unknown>>> {
  const voucherCode = req.body.voucherCode;
  const voucher = req.body.voucher;

  // TODO: voucher is not used at all in fuction?  should it be here
  if (voucher && voucherCode && typeof voucherCode === 'string') {
    // obtener moid partiendo desde voucher, primero se toma el id del voucher
    try {
      const voucherId = voucherCode.slice(5);
      const moNumber = await WorkVouchers.query()
        .join('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
        .where('work_vouchers.id', voucherId)
        .select(
          'mo_numbers.mo_id',
          'mo_numbers.quantity',
          'mo_numbers.company_code',
          'mo_numbers.mo_barcode',
          'work_vouchers.work_voucher_type_id'
        )
        .limit(1)
        .castTo<{
          mo_id: number;
          quantity: number;
          company_code: number;
          mo_barcode: string;
          work_voucher_type_id: number;
        }>();

      req.body.moId = moNumber[0].mo_id;
      req.body.moQuantity = moNumber[0].quantity;
      req.body.companyCode = moNumber[0].company_code;
      req.body.moBarcode = moNumber[0].mo_barcode;
      req.body.work_voucher_type_id = moNumber[0].work_voucher_type_id;

      switch (req.body.companyCode) {
        case 1:
          req.body.client = 'VARPRO';
          break;

        case 2:
          req.body.client = 'ADIDAS';
          break;

        case 3:
          req.body.client = 'VARSITY';
          break;
      }

      return next();
    } catch (error) {
      next(error);
    }
  } else {
    // obtenemos el mo_barcode y el company_code enviado
    const moBarcode = req.body.moBarcode;
    // const client = req.body.client;
    const companyCode = req.body.companyCode;

    // const moOrder = req.body.moOrder;
    // const num = req.body.num;
    if (
      moBarcode &&
      companyCode &&
      typeof moBarcode === 'string' &&
      typeof companyCode === 'number'
    ) {
      try {
        // if (client === "VARSITY") {

        // debido a que es un campo unico utilizaremos barcode de la mo.
        const getMoIdAndQuantity = await MoNumber.query()
          .where('mo_barcode', moBarcode)
          .where('company_code', companyCode)
          .select('mo_id', 'quantity')
          .castTo<
            {
              mo_id: number;
              quantity: number;
            }[]
          >();

        if (getMoIdAndQuantity.length > 0) {
          req.body.moId = getMoIdAndQuantity[0].mo_id;
          req.body.moQuantity = getMoIdAndQuantity[0].quantity;
          next();
        } else {
          return res
            .status(200)
            .json({ ok: false, data: 'No se encontró la MO' });
        }
        /* } 
      else {

        const getMoIdAndQuantity : any = await MoNumbers.query()
          .where("mo_barcode", moBarcode)
          .where("company_code", companyCode)
          .select("mo_id", "quantity");

        if (getMoIdAndQuantity.length > 0) {
          req.body.moId = getMoIdAndQuantity[0].mo_id;
          req.body.moQuantity = getMoIdAndQuantity[0].quantity;
          next();
        } else {
          res.status(400).json({ ok: false, data: "No se encontró la MO" });
        }
      }*/
      } catch (error) {
        next(error);
      }
    }
  }
}

// funcion para obtener en work_areas el work_area_id
export async function getWorkArea(
  req: Request,
  _res: Response,
  next: NextFunction
) {
  // obtenemos el area del operador
  const barcodeOperator = req.body.barcode_operator;
  try {
    // TODO: company code is not used in query?
    if (
      req.body.company_code !== 0 &&
      barcodeOperator &&
      typeof barcodeOperator === 'string'
    ) {
      const getWorkArea = await WorkAreaGroups.query()
        .orWhere('barcode', barcodeOperator)
        .select('work_area_id')
        .castTo<
          {
            work_area_id: number;
          }[]
        >();

      if (getWorkArea.length > 0) {
        req.body.workAreaId = getWorkArea[0].work_area_id;

        return next();
      } else {
        // no existe area para el operador solo crear un registro en mo scan
        req.body.workAreaId = 0;

        return next();
      }
    } else {
      next();
    }
  } catch (error) {
    return next(error);
  }
}

// funcion para buscar en operators el operator_id y el task
export async function getOperatorIdAndTask(
  req: Request,
  _res: Response,
  next: NextFunction
) {
  // obtenemos el barcode del operador y el cliente
  const barcodeOperator = req.body.barcode_operator;
  const client = req.body.client;
  const company_code = Number(req.body.company_code);

  if (!barcodeOperator || typeof barcodeOperator !== 'string') {
    return next();
  }

  try {
    // evaluar si el cliente es cero ya que puede pertenecer a la tabla work area groups - JAMAS ENTRA
    if (isNaN(company_code) === false && company_code === 0) {
      // req.body.operatorId = barcodeOperator;
      // obtenemos la task en base al type en el area al cual esta asignado este group area
      const getOperatorTask = await WorkAreas.query()
        .join(
          'work_area_groups',
          'work_areas.work_area_id',
          '=',
          'work_area_groups.work_area_id'
        )
        .join('work_types', 'work_areas.work_type_id', '=', 'work_types.id')
        .where('work_area_groups.barcode', barcodeOperator)
        .select('work_types.name', 'work_area_groups.work_area_id')
        .limit(1)
        .castTo<
          {
            name: string;
            work_area_id: number;
          }[]
        >();

      req.body.task = getOperatorTask[0].name;
      req.body.workAreaId = getOperatorTask[0].work_area_id;
      req.body.operatorId = 0;

      return next();
    } else if (client && typeof client === 'string') {
      // se obtiene la informacion del operador en caso de que exista en la tabla
      const getOperator = await Operators.query()
        .where('barcode', barcodeOperator)
        .where('client', client)
        .where('operator_status', 1)
        .select('operator_id', 'task')
        .castTo<
          {
            operator_id: number;
            task: string;
          }[]
        >();

      if (getOperator.length > 0) {
        req.body.operatorId = getOperator[0].operator_id;
        req.body.task = getOperator[0].task;

        return next();
      } else {
        // Operador no concuerda con orden a escanear
        req.body.operatorId = 0;

        return next();
      }
    }
  } catch (error) {
    return next(error);
  }
}

// funcion para obtener el id de work_area_ticket_statuses
export async function getWorkTicketStatuses(
  req: Request,
  res: Response,
  next: NextFunction
) {
  // evaluar si existe area o no
  const workAreaId = Number(req.body.workAreaId);
  if (
    workAreaId !== 0 &&
    workAreaId !== undefined &&
    workAreaId !== null &&
    isNaN(workAreaId) === false
  ) {
    // obtenemos el workAreaId

    try {
      const getWorkTicketStatuses = await WorkAreaTicketStatuses.query()
        .where('work_area_id', workAreaId)
        .where('name', 'Nuevo')
        .select('id')
        .castTo<
          {
            id: number;
          }[]
        >();

      if (getWorkTicketStatuses.length > 0) {
        req.body.workTicketStatuses = getWorkTicketStatuses[0].id;
      } else {
        // la siguiente consulta es por si no existe el work_area_ticket_statuses Activo
        // consultamos si existe el work_area_ticket_statuses Completo en el area
        const getWorkTicketStatusesCompleto =
          await WorkAreaTicketStatuses.query()
            .where('work_area_id', workAreaId)
            .where('name', 'Completo')
            .select('id', 'work_status_id');

        // si no existe el work_area_ticket_statuses Completo en el area lo creamos junto al work_area_ticket_statuses Activo
        if (getWorkTicketStatusesCompleto.length === 0) {
          const createWorkTicketStatusesCompleto =
            await WorkAreaTicketStatuses.query().insert({
              work_area_id: workAreaId,
              name: 'Completo',
              work_status_id: 100,
              sequence: 2,
            });

          const createWorkTicketStatusesNuevo =
            await WorkAreaTicketStatuses.query().insert({
              work_area_id: workAreaId,
              name: 'Nuevo',
              work_status_id: 50,
              sequence: 1,
            });

          // verificamos que se ingreso correctamente en la base de datos
          if (
            createWorkTicketStatusesNuevo.name.length > 0 &&
            createWorkTicketStatusesCompleto.name.length > 0
          ) {
            // retornamos el id del estado completo
            req.body.workTicketStatuses = createWorkTicketStatusesNuevo.id;
            req.body.workStatuses =
              createWorkTicketStatusesNuevo.work_status_id;
          } else {
            return res.status(403).json({
              ok: false,
              data: 'Ocurrio un error al ingresar los estados del area',
            });
          }
        } else {
          // si existe el work_area_ticket_statuses Completo en el area solo crearemos el work_area_ticket_statuses Activo
          const createWorkTicketStatusesNuevo =
            await WorkAreaTicketStatuses.query().insert({
              work_area_id: workAreaId,
              name: 'Nuevo',
              work_status_id: 50,
              sequence: 1,
            });

          // verificamos que se ingreso correctamente en la base de datos
          if (createWorkTicketStatusesNuevo.name.length > 0) {
            req.body.workTicketStatuses = createWorkTicketStatusesNuevo.id;
            req.body.workStatuses =
              createWorkTicketStatusesNuevo.work_status_id;
          } else {
            return res.status(403).json({
              ok: false,
              data: 'Ocurrio un error al ingresar el estado activo del area',
            });
          }
        }
      }
    } catch (error) {
      // enviar mensaje a discord
      await sendScanLog('MIDDLEWARE ERROR', 'ticket statuses_   ');

      return next(error);
    }
  }

  return next();
}

// funcion para obtener en voucher plate el id (REFACTOR UPDATE - ticket table)
// export async function getVoucherPlate(
//   req: Request,
//   res: Response,
//   next: NextFunction
// ): Promise<Response | any> {
//   // obtenemos el barcode del voucher plate
//   const voucherPlate = req.body.value.voucherPlateName;
//   try {
//     const getVocuherPlateId = await WorkVoucherPlates.query()
//       .where("barcode", voucherPlate)
//       .orWhere("name", voucherPlate)
//       .select("id");

//     if (getVocuherPlateId.length > 0) {
//       req.body.idVoucherPlate = getVocuherPlateId[0].id;
//     } else {
//       req.body.idVoucherPlate = false;
//     }
//     next();
//   } catch (error) {
//     next(error);
//   }
// }

// funcion para obtener el id del area (REFACTOR UPDATE - ticket table)
// export async function getAreaName(
//   req: Request,
//   res: Response,
//   next: NextFunction
// ): Promise<Response | any> {
//   // obtenemos el id del area
//   const areaNameNext = req.body.value.areaNameNext;
//   try {
//     const getIdAreaNameNext = await WorkAreas.query()
//       .where("area_name", areaNameNext)
//       .select("work_area_id ");

//     if (getIdAreaNameNext.length > 0) {
//       req.body.getIdAreaNameNext = getIdAreaNameNext[0].work_area_id;
//     } else {
//       req.body.getIdAreaNameNext = false;
//     }
//     next();
//   } catch (error) {
//     next(error);
//   }
// }

export async function getSequenceStatusArea(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void | Response<unknown, Record<string, unknown>>> {
  try {
    const { workAreaId, globalStatus, statusName } = req.body;

    if (
      !workAreaId ||
      !globalStatus ||
      !statusName ||
      typeof workAreaId !== 'number' ||
      typeof globalStatus !== 'number' ||
      typeof statusName !== 'string'
    ) {
      return res.status(400).json({
        ok: false,
        message: 'Faltan datos para el estatus',
      });
    }

    // verificamos que el nombre del status no exista en la base de datos
    const searchNameStatusArea = await WorkAreaTicketStatuses.query()
      .where('work_area_ticket_statuses.name', statusName)
      .where('work_area_ticket_statuses.work_status_id', workAreaId);

    if (searchNameStatusArea.length > 0) {
      return res.status(409).json({
        ok: false,
        message: `Ya existe un estatus en el area con el nombre ${statusName}`,
      });
    }
    // obtenemos la secuencia mas alta del area y el estado general
    const getSequenceStatusArea = await WorkAreaTicketStatuses.query()
      .where('work_area_ticket_statuses.work_area_id', workAreaId)
      .where('work_area_ticket_statuses.work_status_id', globalStatus)
      .orderBy('work_area_ticket_statuses.sequence', 'desc')
      .limit(1);

    if (getSequenceStatusArea[0].sequence < 5000.0) {
      req.body.newSequence = getSequenceStatusArea[0].sequence + 100;

      return next();
    }
  } catch (error) {
    return next(error);
  }
}

// ! actualizar datos
export async function getNameZone(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void | Response<unknown, Record<string, unknown>>> {
  try {
    const { zoneName, buildingId } = req.body;

    if (
      !zoneName ||
      !buildingId ||
      typeof zoneName !== 'string' ||
      typeof buildingId !== 'number'
    ) {
      return res.status(400).json({
        ok: false,
        message: 'Faltan datos para la zona',
      });
    }

    const searchNameZone = await WorkZones.query()
      .where('work_zones.name', zoneName)
      .where('work_zones.building_id', buildingId)
      .select('work_zones.name');

    if (searchNameZone.length > 0) {
      return res.status(409).json({
        ok: false,
        message: `Ya existe una zona para el edificio con el nombre ${zoneName}`,
      });
    }

    return next();
  } catch (error) {
    return next();
  }
}

// funcion para validar el nombre de la ubicacion cuando se actualiza
export async function getNameLocationArea(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void | Response<unknown, Record<string, unknown>>> {
  try {
    const { name, workInventoryZoneId, workAreaId } = req.body;

    if (
      !name ||
      !workInventoryZoneId ||
      !workAreaId ||
      typeof name !== 'string' ||
      typeof workInventoryZoneId !== 'number' ||
      typeof workAreaId !== 'number'
    ) {
      return res.status(400).json({
        ok: false,
        message: 'Faltan datos para la ubicacion',
      });
    }

    if (name) {
      const searchNameLocationArea = await WorkInventoryBins.query()
        .join('work_zones', 'work_inventory_bins.work_zone_id', 'work_zones.id')
        .join(
          'work_area_zones',
          'work_zones.id',
          'work_area_zones.work_zone_id'
        )
        .where('work_inventory_bins.name', name)
        .where('work_inventory_bins.work_zone_id', workInventoryZoneId)
        .where('work_area_zones.work_area_id', workAreaId)
        .select('work_inventory_bins.name');

      if (searchNameLocationArea.length > 0) {
        return res.status(409).json({
          ok: false,
          message: `Ya existe una ubicacion para el area con el nombre ${name}`,
        });
      }
    }

    return next();
  } catch (error) {
    return next(error);
  }
}

// funcion para validar el nombre de la ubicacion cuando se ingresa nuevo registro
export async function getNewNameLocationArea(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void | Response<unknown, Record<string, unknown>>> {
  try {
    const { name, workInventoryZoneId, workAreaId } = req.body as unknown as {
      name: string;
      workInventoryZoneId: number;
      workAreaId: number;
    };

    if (!name || !workInventoryZoneId || !workAreaId) {
      return res.status(400).json({
        ok: false,
        message: 'Faltan datos para la ubicacion',
      });
    }

    const searchNameLocationArea = await WorkInventoryBins.query()
      .join('work_zones', 'work_inventory_bins.work_zone_id', 'work_zones.id')
      .join('work_area_zones', 'work_zones.id', 'work_area_zones.work_zone_id')
      .where('work_inventory_bins.name', name)
      .where('work_inventory_bins.work_zone_id', workInventoryZoneId)
      .where('work_area_zones.work_area_id', workAreaId)
      .select('work_inventory_bins.name');

    if (searchNameLocationArea.length > 0) {
      return res.status(409).json({
        ok: false,
        message: `Ya existe una ubicacion para el area con el nombre ${name}`,
      });
    }

    return next();
  } catch (error) {
    return next(error);
  }
}
