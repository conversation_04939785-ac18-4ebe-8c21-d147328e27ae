import { Router } from 'express';

import {
  generateSewingPlanController,
  getEligibleMOsController,
  getSewingLineEfficiencyController,
  getSewingLineStyleHistoryController,
  getSewingPlanningDashboard,
} from '@app/controllers/sewingPlanning.controller';

const sewingPlanningRouter: Router = Router();

// Get eligible MOs for sewing planning
sewingPlanningRouter
  .route('/eligible-mos')
  .get(getEligibleMOsController);

// Get sewing line efficiency data
sewingPlanningRouter
  .route('/sewing-lines/efficiency')
  .get(getSewingLineEfficiencyController);

// Get sewing line style history
sewingPlanningRouter
  .route('/sewing-lines/style-history')
  .post(getSewingLineStyleHistoryController);

// Generate complete sewing plan
sewingPlanningRouter
  .route('/generate-plan')
  .get(generateSewingPlanController);

// Get dashboard data
sewingPlanningRouter
  .route('/dashboard')
  .get(getSewingPlanningDashboard);

export { sewingPlanningRouter };
