import { config } from 'dotenv';
import type { Knex } from 'knex';

config({ path: __dirname + `/../../.env` });

const options: Knex.Config = {
  client: 'mysql',
  connection: {
    dateStrings: false,
    database: process.env.DB_NAME || 'mysql',
    host: process.env.DB_HOST || 'localhost',
    password: process.env.DB_PASSWORD || '',
    port: +process.env.DB_PORT || 5432,
    user: process.env.DB_USER || 'root',
  },
  migrations: {
    tableName: 'work_knex_migrations',
    directory: './migrations',
  },
};

module.exports = options;
