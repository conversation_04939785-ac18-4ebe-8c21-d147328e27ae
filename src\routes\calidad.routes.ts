import { Router } from 'express';

import {
  CreateNewRollRevision,
  GetAllFabricsIssues,
  GetPPRC,
  GetRollInfo,
  GetYardInfo,
} from '@app/controllers/calidad.controller';

const calidadRouter = Router();

calidadRouter.route('/revisionRoll').post(CreateNewRollRevision);
calidadRouter.route('/issues').get(GetAllFabricsIssues);
calidadRouter.route('/pprc').post(GetPPRC);
calidadRouter.route('/rollInfo').post(GetRollInfo);
calidadRouter.route('/yardInfo').post(GetYardInfo);

export { calidadRouter };
