import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.raw(`insert into mo_twill_laser_job_decorations (mo_twill_laser_job_id,mo_twill_laser_decoration_id,is_active)
select
	mtlj.id as mo_twill_laser_job_id,
	mtld.id as mo_twill_laser_decoration_id,
	1 as is_active
from mo_twill_laser_varsity_art_jobs mtlvaj 
left join mo_twill_laser_jobs mtlj on mtlj.mo_twill_laser_varsity_art_job_id = mtlvaj.id
left join mo_twill_laser_decorations mtld on mtld.child_mo_id = mtlvaj.child_mo_id 
left join mo_twill_laser_job_decorations mtljd on mtljd.mo_twill_laser_job_id = mtlj.id and mtljd.mo_twill_laser_decoration_id = mtld.id
where mtlj.id is not null
and mtld.id is not null
and mtljd.id is null`);
}

export async function down(): Promise<void> {
  return Promise.resolve();
}
