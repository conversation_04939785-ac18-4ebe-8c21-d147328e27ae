import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'warehouse_pull_session_order_parts',
    (table): void => {
      table.string('unit', 25).notNullable().after('sub_category');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'warehouse_pull_session_order_parts',
    (table): void => {
      table.dropColumn('unit');
    }
  );
}
