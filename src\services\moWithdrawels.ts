import currency from 'currency.js';

import { getPolyMosWithdrawels } from '@app/Librarys/vpPolyService';
import { Country } from '@app/models/countries';
import { MoNumber } from '@app/models/pedreria.schema';

export interface MoExportWithdrawel {
  ManufactureNumber: string;
  CategoryName: string;
  SubcategoryName: string;
  ComponentFabricContent_: string;
  DatabaseUnitSymbol: string;
  Quantity: string;
  ComponentName: string;
  PartNumber: string;
  ContainerCode: string;
  Bin: string;
  CountryOfOrigin: string;
  Cafta: boolean;
  InvoiceNumber: string;
}

export interface MoExportByPartNumbers {
  ManufactureNumber: string;
  PartNumber: string;
  ComponentName: string;
  CategoryName: string;
  SubcategoryName: string;
  ComponentFabricContent_: string;
  DatabaseUnitSymbol: string;
  Quantity: string;
  CountryOfOrigins: string[];
  Cafta: boolean;
  InvoiceNumber: string;
}

export interface MoExportData {
  ManufactureNumber: string;
  PartNumbers: string; // seperated by new line and includes partnumber (unit) (total quantity) (country of origins)
  FabricContents: string; // seperated by new line
  FabricContent: string; // seperated by comma
  Cafta: boolean; // if all country of origins are CAFTA then true
  InvoiceNumber: string; // original invoice number in poly
}

let countriesCache: {
  lastCacheTime: number;
  data: Country[];
} = {
  lastCacheTime: 0,
  data: [],
};

const getCountiesCache = async () => {
  if (
    !countriesCache ||
    Date.now() - countriesCache.lastCacheTime > 1000 * 60 * 1
  ) {
    // cache for 1 minutes
    await Country.query().then((countries) => {
      countriesCache = {
        lastCacheTime: Date.now(),
        data: countries,
      };
    });
  }
  return countriesCache;
};

const getCountryCaftaDetails = async (countryCode: string) => {
  const countriesCache = await getCountiesCache();
  if (!countriesCache || !countriesCache.data) {
    throw new Error('Countries not found');
  }
  const countries = countriesCache.data;
  const country = countries.find(
    (c) =>
      c.code2 === countryCode ||
      c.code3 === countryCode ||
      c.name === countryCode ||
      c.alt_names.includes(countryCode)
  );
  if (!country) {
    return null;
  }
  const isCafta = country.is_cafta;
  return { country, isCafta };
};

export const getExportMosWithdrawels = async (givenMos: string[]) => {
  // get distinct mos
  // ignore empty mos
  const mos = Array.from(new Set(givenMos)).reduce<string[]>((acc, mo) => {
    if (mo.trim()) {
      acc.push(mo.trim());
    }
    return acc;
  }, []);
  // get MOs from database
  const mosFromDb = await MoNumber.query()
    .whereIn('num', mos)
    .where('company_code', 1);
  // check all mos are available
  // list which mos are not available
  const mosNotFound = mos.filter(
    (mo) => !mosFromDb.find((moDb) => moDb.num === mo)
  );
  if (mosNotFound.length > 0) {
    throw new Error(`MOs not found: ${mosNotFound.join(', ')}`);
  }
  const mosWithdrawals = await getPolyMosWithdrawels(mos);

  // check all country of origins exist
  // group by MO
  const mosWithdrawalsGrouped: Record<string, MoExportWithdrawel[]> = {};
  const errors: string[] = [];
  for (const mo of mos) {
    const moWithdrawals = mosWithdrawals.filter(
      (moWithdrawal) => moWithdrawal.ManufactureNumber === mo
    );
    if (!mosWithdrawalsGrouped[mo]) {
      mosWithdrawalsGrouped[mo] = [];
    }
    for (const moWithdrawal of moWithdrawals) {
      try {
        if (!moWithdrawal.Comments3) {
          throw new Error(
            `Country of origin missing for MO ${moWithdrawal.ManufactureNumber}, part number ${moWithdrawal.PartNumber}`
          );
        }
        const countryCaftaDetail = await getCountryCaftaDetails(
          moWithdrawal.Comments3
        );
        if (!countryCaftaDetail) {
          throw new Error(
            `Country of origin: ${moWithdrawal.Comments3}, not configured in database`
          );
        }
        mosWithdrawalsGrouped[mo].push({
          ...moWithdrawal,
          CountryOfOrigin: countryCaftaDetail.country.code2,
          Cafta: countryCaftaDetail.isCafta,
          InvoiceNumber: moWithdrawal.OrigInvoiceNumber,
        });
      } catch (error) {
        if (error instanceof Error) {
          errors.push(error.message);
        } else {
          errors.push('Unknown error');
        }
      }
    }
  }
  if (errors.length > 0) {
    throw new Error(errors.join('\n'));
  }

  return mosWithdrawalsGrouped;
};

export const getExportMoPartNumbersData = async (mos: string[]) => {
  const mosWithdrawalsGrouped = await getExportMosWithdrawels(mos);

  // group each mo partnumber together
  // and sum the quantity, list distinct origins comma separated
  // and have array of origins
  const moExportGroups: {
    [manufactureNumber: string]: MoExportByPartNumbers[];
  } = {};
  for (const mo of Object.keys(mosWithdrawalsGrouped)) {
    const moWithdrawals = mosWithdrawalsGrouped[mo];
    const partNumberMap: { [partNumber: string]: MoExportByPartNumbers } = {};
    for (const moWithdrawal of moWithdrawals) {
      if (!partNumberMap[moWithdrawal.PartNumber]) {
        partNumberMap[moWithdrawal.PartNumber] = {
          ManufactureNumber: moWithdrawal.ManufactureNumber,
          PartNumber: moWithdrawal.PartNumber,
          ComponentName: moWithdrawal.ComponentName,
          CategoryName: moWithdrawal.CategoryName,
          SubcategoryName: moWithdrawal.SubcategoryName,
          ComponentFabricContent_: moWithdrawal.ComponentFabricContent_,
          DatabaseUnitSymbol: moWithdrawal.DatabaseUnitSymbol,
          Quantity: '0',
          CountryOfOrigins: [],
          Cafta: true,
          InvoiceNumber: moWithdrawal.InvoiceNumber,
        };
      }
      partNumberMap[moWithdrawal.PartNumber].Quantity = currency(
        partNumberMap[moWithdrawal.PartNumber].Quantity
      )
        .add(moWithdrawal.Quantity)
        .toString();
      if (
        !partNumberMap[moWithdrawal.PartNumber].CountryOfOrigins.includes(
          moWithdrawal.CountryOfOrigin
        )
      ) {
        partNumberMap[moWithdrawal.PartNumber].CountryOfOrigins.push(
          moWithdrawal.CountryOfOrigin
        );
      }

      if (!moWithdrawal.Cafta) {
        partNumberMap[moWithdrawal.PartNumber].Cafta = false;
      }
    }

    moExportGroups[mo] = Object.values(partNumberMap);
  }

  return moExportGroups;
};

export const exportMoData = async (mos: string[]) => {
  const moExportGroups = await getExportMoPartNumbersData(mos);

  // group data by MO
  const MosData: MoExportData[] = [];
  for (const mo of Object.keys(moExportGroups)) {
    const moExportParts = moExportGroups[mo];

    const partNumbers = [];
    const fabricContents = [];
    const invoiceNumber = [];
    let cafta = true;
    for (const moExportPart of moExportParts) {
      partNumbers.push(
        `${moExportPart.PartNumber} (${moExportPart.DatabaseUnitSymbol}) (${
          moExportPart.Quantity
        }) (${moExportPart.CountryOfOrigins.join(', ')})`
      );
      fabricContents.push(`${moExportPart.ComponentFabricContent_}`);
      if (!moExportPart.Cafta) {
        cafta = false;
      }
      invoiceNumber.push(`${moExportPart.InvoiceNumber}`);
    }
    // join fabric contents but ignore duplicates
    const moData: MoExportData = {
      ManufactureNumber: mo,
      PartNumbers: partNumbers.join('\n'),
      FabricContents: fabricContents.join('\n'),
      FabricContent: Array.from(new Set(fabricContents)).join(','),
      Cafta: partNumbers.length > 0 && cafta,
      InvoiceNumber: invoiceNumber.join('\n'),
    };

    MosData.push(moData);
  }

  return MosData;
};

const cleanForCsv = (str: string) => {
  // add quotes to beginning and end of string
  // replace a double quote with two double quotes
  return `"${str.replace(/"/g, '""')}"`;
};

export const importExportMoDataCsv = async (mos: string[]) => {
  const moData = await exportMoData(mos);

  // create csv
  const csv = `MO,PartNumbers,FabricContents,FabricContent,Cafta,InvoiceNumber\n${moData
    .map(
      (mo) =>
        `${cleanForCsv(mo.ManufactureNumber)},${cleanForCsv(
          mo.PartNumbers
        )},${cleanForCsv(mo.FabricContents)},${cleanForCsv(
          mo.FabricContent
        )},${cleanForCsv(mo.Cafta.toString())},${cleanForCsv(mo.InvoiceNumber)}`
    )
    .join('\n')}`;

  return csv;
};
