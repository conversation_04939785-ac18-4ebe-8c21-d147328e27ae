// Importamos modulos de express para las rutas
import axios from 'axios';
import type { Request, Response } from 'express';

import { getRepoService, updateRepoInvoiceService } from '@app/services/repo';

export async function productionPage(req: Request, res: Response) {
  try {
    const repo_id = req.query.repo_id;
    const url = `http://restfulapi.varpro.org/PolyService.svc/get/repopage`;

    if (!repo_id) {
      return res.status(400).send('repo_id not given');
    }

    const body = { repo_id: repo_id };

    let filename = `RepoReport-${repo_id}.pdf`; // Be careful of special characters
    filename = encodeURIComponent(filename);

    const { data } = await axios.post(url, body, {
      responseType: 'stream',
    });

    res.setHeader('Content-disposition', 'inline; filename="' + filename + '"');
    res.setHeader('Content-type', 'application/pdf');

    data.pipe(res);
  } catch (e) {
    res.send(500).send('Unknown production error');
    console.log(e);
  }
}

export async function productionBulkPage(req: Request, res: Response) {
  try {
    const repo_ids = req.query.repo_ids as string;
    const url = `http://restfulapi.varpro.org/PolyService.svc/get/repospage`;

    if (!repo_ids) {
      return res.status(400).send('repo_id not given');
    }

    let repo_id_array: { repo_id: number | string }[] = [];

    if (Array.isArray(repo_ids)) {
      repo_id_array = repo_ids.map((repo_id) => {
        return { repo_id: repo_id.toString() };
      });
    }
    if (typeof repo_ids == 'string') {
      repo_id_array = repo_ids.split(',').map((repo_id) => {
        return { repo_id: repo_id };
      });
    }

    if (repo_id_array.length == 0) {
      return res.status(400).send('no repo_ids supplied');
    }

    const body = repo_id_array;

    let filename = `ReposReport-${repo_id_array
      .map((obj) => obj.repo_id)
      .join(',')}.pdf`; // Be careful of special characters
    filename = encodeURIComponent(filename);

    const { data } = await axios.post(url, body, {
      responseType: 'stream',
    });

    res.setHeader('Content-disposition', 'inline; filename="' + filename + '"');
    res.setHeader('Content-type', 'application/pdf');

    data.pipe(res);
  } catch (e) {
    res.send(500).send('Unknown production error');
    console.log(e);
  }
}

export async function getRepos(req: Request, res: Response) {
  try {
    const getIdParam = req.params.id;
    const getAllRepos = await getRepoService(getIdParam);

    return res.status(200).send({
      data: getAllRepos,
      ok: true,
    });
  } catch (e) {
    res.status(500).send('Unknown production error');
    console.log(e);
  }
}

export async function updateRepoInvoice(req: Request, res: Response) {
  try {
    const {
      id,
      charge_amount,
      is_customer_charged,
      customer_charge_invoice_number,
    }: {
      id: number;
      charge_amount: number;
      is_customer_charged: boolean;
      customer_charge_invoice_number: string;
    } = req.body;

    const updateRepoInvoice = await updateRepoInvoiceService(
      id,
      charge_amount,
      is_customer_charged,
      customer_charge_invoice_number
    );

    return res.status(200).send({
      data: updateRepoInvoice,
      ok: true,
    });
  } catch (e) {
    res.status(500).send('Unknown production error');
    console.log(e);
  }
}
