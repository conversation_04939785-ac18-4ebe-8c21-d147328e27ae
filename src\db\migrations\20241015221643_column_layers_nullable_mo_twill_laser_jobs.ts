import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('mo_twill_laser_jobs', (table) => {
    table.string('layers').nullable().alter();
    table.string('special_layers').nullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('mo_twill_laser_jobs', (table) => {
    table.string('layers').notNullable().alter();
    table.string('special_layers').notNullable().alter();
  });
}
