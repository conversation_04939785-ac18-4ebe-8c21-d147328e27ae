import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'mo_invoice_items',
    (table: Knex.TableBuilder) => {
      table.string('factory_code', 128).nullable().defaultTo(null);
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'mo_invoice_items',
    (table: Knex.TableBuilder) => {
      table.dropColumn('factory_code');
    }
  );
}
