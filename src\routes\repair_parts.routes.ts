import { Router } from 'express';

import {
  addInvoiceToDeliveryNote,
  cancelOrder,
  categories,
  createCategory,
  createLocation,
  createOrder,
  createRequest,
  createSupplier,
  deleteCategory,
  deleteLocation,
  deleteRepairPart,
  deleteSupplier,
  deliveryNoteWithoutInvoice,
  getPendingOrders,
  itemsRequest,
  locations,
  machines,
  mechanics,
  newRepairPart,
  part,
  receiveDeliveryNote,
  receiveInvoice,
  repairParts,
  repairPartsWithLowStock,
  requests,
  returnPart,
  suppliers,
  updateCategory,
  updateLocation,
  updateRepairPart,
  updateSupplier,
} from '@app/controllers/repair_parts.controller';
import { validateJWTRepairParts } from '@app/middlewares';

const repairPartsRouter = Router();

repairPartsRouter
  .route('/')
  .get(validateJWTRepairParts, repairParts)
  .post(validateJWTRepairParts, newRepairPart)
  .delete(validateJWTRepairParts, deleteRepairPart)
  .patch(validateJWTRepairParts, updateRepairPart);
repairPartsRouter
  .route('/requests')
  .get(validateJWTRepairParts, requests)
  .post(validateJWTRepairParts, createRequest);
repairPartsRouter
  .route('/request/items')
  .get(validateJWTRepairParts, itemsRequest);
repairPartsRouter.route('/returns').post(validateJWTRepairParts, returnPart);
repairPartsRouter.route('/part').get(validateJWTRepairParts, part);
repairPartsRouter
  .route('/low-stock')
  .get(validateJWTRepairParts, repairPartsWithLowStock);
repairPartsRouter
  .route('/suppliers')
  .get(suppliers)
  .delete(deleteSupplier)
  .post(createSupplier)
  .patch(validateJWTRepairParts, updateSupplier);
repairPartsRouter
  .route('/categories')
  .get(categories)
  .delete(deleteCategory)
  .post(createCategory)
  .patch(validateJWTRepairParts, updateCategory);
repairPartsRouter
  .route('/locations')
  .get(validateJWTRepairParts, locations)
  .post(validateJWTRepairParts, createLocation)
  .patch(validateJWTRepairParts, updateLocation)
  .delete(validateJWTRepairParts, deleteLocation);
repairPartsRouter
  .route('/orders')
  .post(validateJWTRepairParts, createOrder)
  .get(validateJWTRepairParts, getPendingOrders)
  .delete(cancelOrder);
repairPartsRouter
  .route('/orders/receive/delivery-note')
  .post(receiveDeliveryNote);
repairPartsRouter.route('/orders/receive/invoice').post(receiveInvoice);
repairPartsRouter
  .route('/delivery-notes')
  .get(deliveryNoteWithoutInvoice)
  .patch(validateJWTRepairParts, addInvoiceToDeliveryNote);
repairPartsRouter.route('/mechanics').get(mechanics);
repairPartsRouter.route('/machines').get(machines);

export { repairPartsRouter };
