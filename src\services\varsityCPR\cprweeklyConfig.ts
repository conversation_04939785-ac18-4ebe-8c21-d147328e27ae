import type {
  ClassHeader,
  FilePart,
  LineItemInfo,
  MadeItem,
  ReportData,
} from './config';

const mainItemCheck = (parts: FilePart[], curItem: Partial<MadeItem>) => {
  if (!curItem.vch) {
    throw new Error('vch is missing');
  }
  if (!curItem.order) {
    throw new Error('order is missing');
  }
  if (!curItem.style) {
    throw new Error('style is missing');
  }
  if (!curItem.qty) {
    throw new Error('qty is missing');
  }
};

const classHeaderCheck = (
  parts: FilePart[],
  curItem: Partial<MadeItem>,
  curHeader: Partial<ClassHeader>
) => {
  console.log('classHeaderCheck', curHeader);
  if (!curHeader.class_code) {
    throw new Error('class_code is missing');
  }
  if (!curHeader.class_name) {
    throw new Error('class_name is missing');
  }
};

const reportDatesCheck = (
  parts: FilePart[],
  curItem: Partial<MadeItem>,
  curHeader: Partial<ClassHeader>,
  curReport: ReportData
) => {
  // check if each value is a date and greater than 2000 and less than 2100
  if (!curReport.report_start) {
    // 2023/08/06
    throw new Error('report_start is missing');
  }
  if (!curReport.report_end) {
    // 2023/08/06
    throw new Error('report_end is missing');
  }
  if (!curReport.pay_date) {
    // 11/20/2023
    throw new Error('pay_date is missing');
  }
};

export const cprweekly: { [lineName: string]: LineItemInfo } = {
  main: {
    type: 'item',
    reportFormat: 'normal',
    expected: [
      { key: 'factory', x: 0.65 },
      { key: null, x: 1.944 },
      { key: null, x: 2.942 },
      { key: 'class', x: 3.95 },
      { key: 'style', x: 5.244 },
      { key: 'order', x: 9.404 },
      { key: 'vch', x: 12.127 },
      { key: 'ltr', x: 13.421 },
      { key: 'size', x: 15.427, buffer: 0.5 },
      { key: 'CP', x: 18.722, buffer: 0.4 },
      { key: 'qty_unit', x: 19.73, buffer: 0.3 },
      { key: 'cut', x: 27.902, buffer: 0.3 },
      { key: 'sew', x: 31.765, buffer: 0.3 },
      { key: 'ext', x: 36.212, buffer: 0.3 },
      { key: 'stars', x: 39.507, buffer: 0.3 },
    ],
    infoCheckFunc: mainItemCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length >= 11 &&
        parts.length <= 15 &&
        parts[0].text === 'PX' &&
        parts[0].x === 0.65
      ) {
        return true;
      }
    },
  },
  mainThin: {
    type: 'item',
    reportFormat: 'thin',
    infoCheckFunc: mainItemCheck,
    specialPartProcess: (parts) => {
      // pull out CP if it overlaps qty_unit
      const useParts = [];
      const updateData: Partial<MadeItem> = {};
      for (const part of parts) {
        if (part.x > 17 && part.x < 20) {
          const value = part.text.trim();
          const splitCheck = value.split(' ');
          // check if CP value by checking if it contains an alpha character
          if (value.match(/[a-z]/i) || splitCheck.length === 1) {
            updateData.CP = value.trim();
            continue;
          }
        }
        if (part.text === '***') {
          updateData.stars = true;
          continue;
        }
        useParts.push(part);
      }
      return [useParts, updateData, null];
    },
    expected: [
      { key: 'factory', x: 0.65 }, // 'PX  ',
      { key: null, x: 1.685 }, // 'PC ',
      { key: null, x: 2.375 }, // 'PX ',
      { key: 'class', x: 3.065, buffer: 0.3 }, // 'JVT ', x: 2.984,
      { key: 'style', x: 4.099, buffer: 0.3 }, // 'JVTGT-8L      ', x: 3.984, // can merge into order
      { key: 'order', x: 8.584, buffer: 0.5 }, // '12749045 ', x: 8.319,
      { key: 'vch', x: 11.343, min: 10, max: 11.8 }, // '002 ', x: 11.688,  x: 10.987,
      { key: 'ltr', x: 12.378, min: 11.9, max: 13.5 }, // '   7   ', x: 11.987,
      { key: 'size', x: 14.448, min: 13.8, max: 15.3 }, // ' 3.00      ', x: 15.138 x: 13.988,
      // { key: 'CP', x: 17.897, min: 17, max: 18.3 }, // 'T  ', x: 17.897, x: 18.242, x: 18.587, x: 17.322,
      { key: 'qty_unit', x: 18.932, min: 17.9, max: 20 }, // '     25        6.51         ', x: 18.587, x: 18.932 x: 17.989,
      { key: 'cut', x: 27.901, min: 26, max: 28.7 }, // '         .00 ', x: 27.901, x: 28.246, 28.591, x: 26.992,
      { key: 'sew', x: 32.04, min: 30.8, max: 33 }, // '      162.75   ', x: 32.385 x: 30.993,
      { key: 'ext', x: 36.869, min: 35, max: 37.5 }, // '    162.75', x: 37.214 x: 35.661,
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length >= 11 &&
        parts.length <= 15 &&
        parts[0].text === 'PX  ' &&
        parts[0].x === 0.65
      ) {
        return true;
      }
    },
  },
  mainFat: {
    type: 'item',
    reportFormat: 'fat',
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      const last3Parts = parts.slice(-3);
      const stars = last3Parts.every((part) => part.text === '*');
      if (stars) {
        console.log('has stars');
        const newParts = parts.slice(0, -3);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    expected: [
      { key: 'factory', x: 0.65 }, // text: 'PX',
      { key: null, x: 2.052 }, // text: 'PC',
      { key: 'class', x: 3.087 }, // text: 'PX',
      { key: 'cust_num', x: 4.131, buffer: 0.5 }, // text: 'JVT',
      { key: 'style', x: 5.412, min: 5.0, max: 6, combineTillNext: true }, // text: 'JVTGT2',
      { key: 'order', x: 10.184, min: 9.8, max: 11.5 }, // text: '14805558',
      { key: 'vch', x: 12.79, buffer: 0.5, min: 12.0, max: 13.3 }, // text: '002',
      { key: 'ltr', x: 14.9, buffer: 1 }, // text: '6',
      {
        key: 'size',
        x: 17.217,
        combineTillNext: true,
        buffer: 1.5,
        trim: true,
      }, // text: '3.',
      // { key: null, x: 17.545}, // text: '00',
      {
        key: 'CP',
        x: 20.328,
        min: 19.4,
        max: 20.8,
        combineTillNext: true,
        buffer: 0.5,
      }, // text: 'T',
      { key: 'qty', x: 23.563, buffer: 1.3 }, // text: '1 ',
      {
        key: 'unit',
        x: 26.713,
        combineTillNext: true,
        buffer: 1.5,
        trim: true,
      }, // text: '3.',
      // { key: null, x: 27.341}, // text: '00',
      {
        key: 'cut',
        x: 34.043,
        min: 31,
        max: 35,
        combineTillNext: true,
        buffer: 1.5,
        trim: true,
      }, // text: '.',
      // { key: null, x: 34.802}, // text: '00',
      {
        key: 'sew',
        x: 38.064,
        min: 36,
        max: 39.7, // 39.2 is max seen
        combineTillNext: true,
        buffer: 1.8,
        trim: true,
      }, // text: '3.',
      // { key: null, x: 39.292}, // text: '00',
      {
        key: 'ext',
        x: 42.959,
        min: 40,
        max: 44.2,
        combineTillNext: true,
        buffer: 2,
        trim: true,
      }, // text: '3.',
      // { key: null, x: 43.782}, // text: '00',
      // stars are remove from special check
      // { key: 'stars', x: 44.429, min: 44.426, max: 45, combineTillNext: true }, // text: '*',
      // { key: 'stars', x: 44.788, merged: true }, // text: '*',
      // { key: 'stars', x: 45.146, merged: true }, // text: '*',
    ],
    infoCheckFunc: mainItemCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts[0].x === 0.65 &&
        parts[0].text === 'PX' &&
        parts.length >= 16
      ) {
        return true;
      }
    },
  },
  mainFatBorder: {
    type: 'item',
    reportFormat: 'fatborder',
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      const last3Parts = parts.slice(-3);
      const stars = last3Parts.every((part) => part.text === '*');
      if (stars) {
        console.log('has stars');
        const newParts = parts.slice(0, -3);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    expected: [
      { key: 'factory', x: 2 }, // 'PX',
      { key: null, x: 3.326 }, // 'PC',
      { key: 'class', x: 4.317, buffer: 0.4 }, // 'PX',
      { key: 'cust_num', x: 5.309, buffer: 0.5 }, // 'L02',
      { key: 'style', x: 6.51, min: 6.2, max: 10, combineTillNext: true }, // 'HT97UO2', x: 6.484,
      { key: 'order', x: 11.065, min: 10.5, max: 11.5 }, // '31706761', x: 10.786,
      { key: 'vch', x: 13.57, buffer: 0.5 }, // '002', x: 13.291, x: 13.837,
      { key: 'ltr', x: 15.78, min: 14.8, max: 16.2 }, // '3', x: 15.161, x: 16.051,
      { key: 'size', x: 17.465, min: 16.6, max: 18.5, combineTillNext: true }, // '4.', x: 17.448,
      // { key: null, x: 18.066 }, // '00',
      { key: 'CP', x: 20.691, min: 20, max: 21.5, combineTillNext: true }, // 'GT', x: 20.412, x: 21.036,
      { key: 'qty', x: 23.755, min: 22.3, max: 24.5 }, // '2 ', x: 23.45,
      { key: 'unit', x: 26.719, min: 25, max: 28, combineTillNext: true }, // '1.', x: 26.748,
      // { key: null, x: 27.32 }, // '30',
      { key: 'cut', x: 33.994, min: 32, max: 35, combineTillNext: true }, // '.', x: 33.762,
      // { key: null, x: 34.329 }, // '00',
      { key: 'sew', x: 37.966, min: 36, max: 39, combineTillNext: true }, // '2.', x: 38.072,
      // { key: null, x: 38.567 }, // '60',
      { key: 'ext', x: 42.204, min: 40, max: 44, combineTillNext: true }, // '2.', x: 42.383,
      // { key: null, x: 42.805 }, // '60'
    ],
    infoCheckFunc: mainItemCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts[0].x === 2 &&
        parts[0].text === 'PX' &&
        parts.length >= 16
      ) {
        return true;
      }
    },
  },
  mainSuyapa: {
    type: 'item',
    reportFormat: 'suyapa',
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      const last3Parts = parts.slice(-3);
      const stars = last3Parts.every((part) => part.text === '*');
      if (stars) {
        console.log('has stars');
        const newParts = parts.slice(0, -3);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    expected: [
      { key: 'factory', x: 2 }, // text: 'PX'
      { key: null, x: 3.35 }, // text: 'PC'
      { key: 'class', x: 4.362, buffer: 0.4 }, // text: 'PX'
      { key: 'cust_num', x: 5.374, buffer: 0.5 }, // text: 'JVT'
      { key: 'style', x: 6.726, min: 6.3, max: 10.2, combineTillNext: true }, // text: 'JVTDAR2'
      { key: 'order', x: 11.448, min: 10.7, max: 11.7 }, // text: '12698383'
      { key: 'vch', x: 14.489, buffer: 0.5 }, // text: '002'
      { key: 'ltr', x: 16.848, min: 15.2, max: 17.2 }, // text: '6'
      { key: 'size', x: 18.538, min: 17.6, max: 19.6, combineTillNext: true }, // text: '3.'
      // { key: null, x: 19.139 }, // text: '00'
      { key: 'CP', x: 21.914, min: 21.2, max: 22.8, combineTillNext: true }, // text: 'T'
      { key: 'qty', x: 24.612, min: 23.3, max: 25.6 }, // text: '11'
      { key: 'unit', x: 27.988, min: 26.2, max: 29.3, combineTillNext: true }, // text: '3.'
      // { key: null, x: 28.584 }, // text: '00'
      { key: 'cut', x: 35.413, min: 33.2, max: 36.3, combineTillNext: true }, // text: '.'
      // { key: null, x: 35.752 }, // text: '00'
      { key: 'sew', x: 39.123, min: 37.2, max: 40.3, combineTillNext: true }, // text: '33.'
      // { key: null, x: 39.985 }, // text: '00'
      { key: 'ext', x: 43.511, min: 41, max: 45, combineTillNext: true }, // text: '33.'
      // { key: null, x: 44.369 }, // text: '00'
      // { key: null, x: 45.535 }, // text: '*'
      // { key: null, x: 45.874 }, // text: '*'
      // { key: null, x: 46.213 }, // text: '*'
    ],
    infoCheckFunc: mainItemCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapa' &&
        parts[0].x === 2 &&
        parts[0].text === 'PX' &&
        parts.length >= 16
      ) {
        return true;
      }
    },
  },
  mainSuyapaNew: {
    type: 'item',
    reportFormat: 'suyapaNew',
    expected: [
      { key: 'factory', x: 2 }, //  'PX'
      { key: null, x: 3.147 }, //  'PC'
      { key: null, x: 4.058 }, //  'PX'
      { key: 'class', x: 4.969 }, //  'JVT'
      { key: 'style', x: 6.117 }, //  'JVTCBBL2'
      { key: 'order', x: 9.624 }, //  '14015066'
      { key: 'vch', x: 11.852, buffer: 0.5 }, //  '008' - 11.749, 11.952
      { key: 'ltr', x: 13.099 }, //  '   6'
      { key: 'size', x: 14.954, buffer: 0.5 }, //  ' 3.00' - 14.751
      { key: 'CP', x: 17.754, buffer: 0.5 }, //  'T' - 17.551
      { key: 'qty_unit', x: 18.665, buffer: 0.5 }, //  '      8        3.00' - 18.462
      { key: 'cut', x: 25.477, buffer: 0.5 }, //  '         .00' - 25.274
      { key: 'sew', x: 28.749, buffer: 0.5 }, //  '       24.00' - 28.546
      { key: 'ext', x: 32.393 }, //  '     24.00' - 32.493, 32.29
      { key: 'stars', x: 35.192, buffer: 0.5 }, //  '***' , 35.089, 35.292
    ],
    infoCheckFunc: mainItemCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNew' &&
        parts.length >= 11 &&
        parts.length <= 15 &&
        parts[0].text === 'PX' &&
        parts[0].x === 2
      ) {
        return true;
      }
    },
  },
  sublimation: {
    type: 'subitem',
    reportFormat: 'normal',
    expected: [
      { key: 'sub_code', x: 9.607 },
      { key: null, x: 11.761 },
      { key: 'sub_labor', x: 22.511 },
      { key: 'sub_ext', x: 34.972 },
    ],
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      if (parts.length === 5 && parts[4].text === '***') {
        console.log('has stars');
        const newParts = parts.slice(0, -1);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length >= 4 &&
        parts[1].text === 'Sublimation' &&
        parts[1].x === 11.761 &&
        (parts.length === 4 || (parts.length === 5 && parts[4].text === '***'))
      ) {
        return true;
      }
    },
  },
  sublimationThin: {
    type: 'subitem',
    reportFormat: 'thin',
    expected: [
      { key: 'sub_code', x: 11.268 }, // '07     ',
      { key: null, x: 13.338 }, // 'Sublimation                          ',
      { key: 'sub_labor', x: 25.756 }, // '          1.42                             ',
      { key: 'sub_ext', x: 40.244 }, // '     14.92'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 4 &&
        parts[1].text === 'Sublimation                          ' &&
        parts[1].x === 13.338
      ) {
        return true;
      }
    },
  },
  sublimationThinAlt: {
    type: 'subitem',
    reportFormat: 'thin',
    expected: [
      { key: 'sub_code', x: 10.923 }, // '07     ',
      { key: null, x: 12.924 }, // 'Sublimation                          ',
      { key: 'sub_labor', x: 24.928 }, // '         30.42                             ',
      { key: 'sub_ext', x: 38.932 }, // '    183.56'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 4 &&
        parts[1].text === 'Sublimation                          ' &&
        parts[1].x === 12.924
      ) {
        return true;
      }
    },
  },
  sublimationFat: {
    type: 'subitem',
    reportFormat: 'fat',
    expected: [
      { key: 'sub_code', x: 11.785 }, // text: '07',
      { key: null, x: 14.214 }, // text: 'Subl',
      { key: null, x: 15.429 }, // text: 'i',
      { key: null, x: 15.786999999999999 }, // text: 'mat',
      { key: null, x: 16.818 }, // text: 'i',
      { key: null, x: 17.177 }, // text: 'on',
      {
        key: 'sub_labor',
        x: 31.022,
        combineTillNext: true,
        buffer: 2,
        trim: true,
      }, // text: '239.',
      // { key: null, x: 31.189 }, // text: '73',
      {
        key: 'sub_ext',
        x: 42.313,
        combineTillNext: true,
        buffer: 3,
        trim: true,
      }, // text: '1789.',
      // { key: null, x: 44.743 }, // text: '74',
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length > 6 &&
        parts[1].text === 'Subl' &&
        parts[2].text === 'i' &&
        parts[3].text === 'mat' &&
        parts[4].text === 'i' &&
        parts[5].text === 'on'
      ) {
        return true;
      }
    },
  },
  sublimationFatBorder: {
    type: 'subitem',
    reportFormat: 'fatborder',
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      const last3Parts = parts.slice(-3);
      const stars = last3Parts.every((part) => part.text === '*');
      if (stars) {
        console.log('has stars');
        const newParts = parts.slice(0, -3);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    expected: [
      { key: 'sub_code', x: 12.46 }, // '07',
      { key: null, x: 14.746 }, // 'Subl',
      { key: null, x: 15.922999999999998 }, // 'i',
      { key: null, x: 16.262 }, // 'mat',
      { key: null, x: 17.255 }, // 'i',
      { key: null, x: 17.589 }, // 'on',
      { key: 'sub_labor', x: 30.337, combineTillNext: true, buffer: 2 }, // '1.',
      // { key: null, x: 30.938 }, // '70',
      { key: 'sub_ext', x: 43.351, combineTillNext: true, buffer: 2 }, // '5.',
      // { key: null, x: 43.947 }, // '90'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length > 6 &&
        parts[1].text === 'Subl' &&
        parts[2].text === 'i' &&
        parts[3].text === 'mat' &&
        parts[4].text === 'i' &&
        parts[5].text === 'on'
      ) {
        return true;
      }
    },
  },
  sublimationSuyapa: {
    type: 'subitem',
    reportFormat: 'suyapa',
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      const last3Parts = parts.slice(-3);
      const stars = last3Parts.every((part) => part.text === '*');
      if (stars) {
        console.log('has stars');
        const newParts = parts.slice(0, -3);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    expected: [
      { key: 'sub_code', x: 12.46 }, // text: '07'
      { key: null, x: 14.824 }, // text: 'Subl'
      { key: null, x: 16 }, // text: 'i'
      { key: null, x: 16.339 }, // text: 'mat'
      { key: null, x: 17.332 }, // text: 'i'
      { key: null, x: 17.666 }, // text: 'on'
      { key: 'sub_labor', x: 30.351, combineTillNext: true, buffer: 2 }, // text: '13.'
      // { key: null, x: 31.214 }, // text: '10'
      { key: 'sub_ext', x: 43.177, combineTillNext: true, buffer: 2 }, // text: '107.'
      // { key: null, x: 44.296 }, // text: '40'
      // { key: null, x: 45.535 }, // text: '*'
      // { key: null, x: 45.874 }, // text: '*'
      // { key: null, x: 46.213 }, // text: '*'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapa' &&
        parts.length > 6 &&
        parts[1].text === 'Subl' &&
        parts[2].text === 'i' &&
        parts[3].text === 'mat' &&
        parts[4].text === 'i' &&
        parts[5].text === 'on'
      ) {
        return true;
      }
    },
  },
  sublimationSuyapaNew: {
    type: 'subitem',
    reportFormat: 'suyapaNew',
    expected: [
      { key: 'sub_code', x: 9.419 }, // '07'
      { key: null, x: 11.274 }, // 'Sublimation'
      { key: 'sub_labor', x: 20.211 }, // '        194.62'
      { key: 'sub_ext', x: 30.565 }, // '    882.82'
    ],
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      if (parts.length === 5 && parts[4].text === '***') {
        console.log('has stars');
        const newParts = parts.slice(0, -1);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNew' &&
        parts.length >= 4 &&
        parts[1].text === 'Sublimation' &&
        parts[1].x === 11.274 &&
        (parts.length === 4 || (parts.length === 5 && parts[4].text === '***'))
      ) {
        return true;
      }
    },
  },
  classHeader: {
    type: 'classHeader',
    reportFormat: 'normal',
    infoCheckFunc: classHeaderCheck,
    expected: [
      { key: null, x: 1.01 },
      { key: null, x: 1.048 },
      { key: 'class_code', x: 4.348 },
      { key: null, x: 4.962 },
      { key: null, x: 5 },
      { key: 'class_name', x: 6.867 },
      { key: null, x: 7.115 },
      { key: null, x: 7.152 },
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length === 8 &&
        parts[0].text === 'ITEM CLASS' &&
        parts[0].x === 1.01
      ) {
        return true;
      }
    },
  },
  classHeaderThin: {
    type: 'classHeader',
    reportFormat: 'thin',
    specialPartProcess: (parts) => {
      const newParts = parts.filter((part) => !part.text.startsWith('__'));
      return [newParts, null, null];
    },
    infoCheckFunc: classHeaderCheck,
    expected: [
      { key: null, x: 0.9199999999999999 }, // 'ITEM CLASS',
      // { key: null, x: 0.958 }, // '__________',
      { key: null, x: 0.958 }, // 'ITEM CLASS ',
      { key: null, x: 3.9130000000000003 }, // '___',
      { key: null, x: 3.95 }, // 'JVT   ',
      { key: 'class_code', x: 4.407 }, // 'JVT',
      { key: null, x: 5.54 }, // '____________________',
      { key: 'class_name', x: 5.577 }, // 'JV LEAGUE TWILL     ',
      // { key: null, x: 5.675 }, // 'JV LEAGUE TWILL'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 9 &&
        parts[0].text === 'ITEM CLASS' &&
        parts[0].x === 0.9199999999999999
      ) {
        return true;
      }
    },
  },
  classHeaderAlt: {
    type: 'classHeader',
    reportFormat: 'normal',
    infoCheckFunc: classHeaderCheck,
    expected: [
      { key: null, x: 1.01 },
      { key: null, x: 1.048 },
      // { key: null, x: 1.048 },
      { key: 'class_code', x: 4.348 },
      { key: null, x: 4.962 },
      { key: null, x: 5 },
      { key: 'class_name', x: 6.867 },
      { key: null, x: 7.115 },
      { key: null, x: 7.152 },
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length >= 8 &&
        parts.length <= 9 &&
        parts[0].text === 'ITEM CLASS' &&
        parts[0].x === 1.01
      ) {
        return true;
      }
    },
  },
  classHeaderFat: {
    type: 'classHeader',
    reportFormat: 'fat',
    infoCheckFunc: classHeaderCheck,
    ignorePartFunc: (part) => {
      if (part.text.includes('___')) {
        return true;
      }
      return false;
    },
    expected: [
      { key: null, x: 1.01 }, // text: 'I',
      { key: null, x: 1.048 }, // text: '__________',
      { key: null, x: 1.048 }, // text: 'I',
      { key: null, x: 1.368 }, // text: 'TEM ',
      { key: null, x: 1.406 }, // text: 'TEM ',
      { key: null, x: 2.743 }, // text: 'CLASS',
      { key: null, x: 2.781 }, // text: 'CLASS',
      {
        key: 'class_code',
        x: 4.766,
        buffer: 0.6,
        combineTillNext: true,
        ignoreRepeat: true,
      }, // text: 'JVT',
      // { key: null, x: 4.962}, // text: '___',
      // { key: null, x: 5}, // text: 'JVT',
      {
        key: 'class_name',
        x: 6.993,
        buffer: 1,
        combineTillNext: true,
        ignoreRepeat: true,
      }, // text: 'JV ',
      // { key: null, x: 7.115}, // text: '____________________',
      // { key: null, x: 7.152}, // text: 'JV ',
      // { key: null, x: 7.916}, // text: 'LEAGUE ',
      // { key: null, x: 8.076}, // text: 'LEAGUE ',
      // { key: null, x: 10.237}, // text: 'TWI',
      // { key: null, x: 10.397}, // text: 'TWI',
      // { key: null, x: 11.348}, // text: 'LL',
      // { key: null, x: 11.507}, // text: 'LL ',
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length > 7 &&
        parts[0].text === 'I' &&
        parts[3].text === 'TEM ' &&
        parts[6].text === 'CLASS'
      ) {
        return true;
      }
    },
  },
  classHeaderFatBorder: {
    type: 'classHeader',
    reportFormat: 'fatborder',
    infoCheckFunc: classHeaderCheck,
    ignorePartFunc: (part) => {
      if (part.text.includes('___')) {
        return true;
      }
      return false;
    },
    expected: [
      { key: null, x: 2.338 }, // 'I',
      { key: null, x: 2.375 }, // '__________',
      { key: null, x: 2.375 }, // 'I',
      { key: null, x: 2.676 }, // 'TEM ',
      { key: null, x: 2.714 }, // 'TEM ',
      { key: null, x: 4.004 }, // 'CLASS',
      { key: null, x: 4.041 }, // 'CLASS',
      {
        key: 'class_code',
        x: 5.943,
        buffer: 0.6,
        combineTillNext: true,
        ignoreRepeat: true,
      }, // 'L02',
      // { key: null, x: 6.05 }, // '___',
      // { key: null, x: 6.087 }, // 'L02',
      {
        key: 'class_name',
        x: 7.962,
        buffer: 1,
        combineTillNext: true,
        ignoreRepeat: true,
      }, // 'VI',
      // { key: null, x: 8.075 }, // '____________________',
      // { key: null, x: 8.112 }, // 'VI',
      // { key: null, x: 8.61 }, // 'NYL ',
      // { key: null, x: 8.76 }, // 'NYL ',
      // { key: null, x: 9.864 }, // 'TRANSFER/',
      // { key: null, x: 10.015 }, // 'TRANSFER/',
      // { key: null, x: 12.734 }, // 'OUTER',
      // { key: null, x: 12.889 }, // 'OUTER'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length > 7 &&
        parts[0].text === 'I' &&
        parts[3].text === 'TEM ' &&
        parts[6].text === 'CLASS'
      ) {
        return true;
      }
    },
  },
  classHeaderSuyapa: {
    type: 'classHeader',
    reportFormat: 'suyapa',
    infoCheckFunc: classHeaderCheck,
    ignorePartFunc: (part) => {
      if (part.text.includes('___')) {
        return true;
      }
      return false;
    },
    expected: [
      { key: null, x: 2.338 }, // text: 'I'
      { key: null, x: 2.375 }, // text: '__________'
      { key: null, x: 2.375 }, // text: 'I'
      { key: null, x: 2.676 }, // text: 'TEM '
      { key: null, x: 2.714 }, // text: 'TEM '
      { key: null, x: 4.004 }, // text: 'CLASS'
      { key: null, x: 4.041 }, // text: 'CLASS'
      {
        key: 'class_code',
        x: 6.052,
        buffer: 0.6,
        combineTillNext: true,
        ignoreRepeat: true,
      }, // text: 'JVT'
      // { key:null, x: 6.09 }, // text: '___'
      // { key:null, x: 6.09 }, // text: 'JVT'
      {
        key: 'class_name',
        x: 8.077,
        buffer: 1,
        combineTillNext: true,
        ignoreRepeat: true,
      }, // text: 'JV '
      // { key:null, x: 8.114 }, // text: '____________________'
      // { key:null, x: 8.114 }, // text: 'JV '
      // { key:null, x: 8.965 }, // text: 'LEAGUE '
      // { key:null, x: 9.002 }, // text: 'LEAGUE '
      // { key:null, x: 11.207 }, // text: 'TWI'
      // { key:null, x: 11.245 }, // text: 'TWI'
      // { key:null, x: 12.273 }, // text: 'LL'
      // { key:null, x: 12.31 }, // text: 'LL '
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapa' &&
        parts.length > 7 &&
        parts[0].text === 'I' &&
        parts[3].text === 'TEM ' &&
        parts[6].text === 'CLASS'
      ) {
        return true;
      }
    },
  },
  classHeaderSuyapaNew: {
    type: 'classHeader',
    reportFormat: 'suyapaNew',
    infoCheckFunc: classHeaderCheck,
    expected: [
      { key: null, x: 2.338 }, // 'ITEM CLASS'
      { key: null, x: 2.375 }, // '__________'
      // { key: null, x: 2.375 }, // 'ITEM CLASS'
      { key: 'class_code', x: 5.175 }, // 'JVT'
      { key: null, x: 6.05 }, // '___'
      { key: null, x: 6.087 }, // 'JVT'
      { key: 'class_name', x: 7.707 }, // 'JV LEAGUE TWILL'
      { key: null, x: 8.075 }, // '____________________'
      { key: null, x: 8.112 }, // 'JV LEAGUE TWILL     '
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNew' &&
        parts.length >= 8 &&
        parts.length <= 9 &&
        parts[0].text === 'ITEM CLASS' &&
        parts[0].x === 2.338
      ) {
        return true;
      }
    },
  },
  classTotal: {
    type: 'classTotal',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 1.01 }, // "text":"ITEM CLASS TOTALS >>>>>>>>",
      { key: null, x: 1.048 }, // "text":"ITEM CLASS TOTALS >>>>>>>>",
      { key: 'sum_item_count', x: 9.219 }, // "text":"     4",
      { key: null, x: 11.075 }, // "text":"     4 ",
      { key: null, x: 13.797 }, // "text":"ACCEPTED:",
      { key: null, x: 14.345 }, // "text":"ACCEPTED:",
      { key: 'sum_unit_count', x: 21.657 }, // "text":"   318",
      { key: null, x: 23.292 }, // "text":"   318",
      { key: 'sum_labor', x: 26.02 }, // "text":"           631.38",
      { key: null, x: 26.563 }, // "text":"           631.38       489.72",
      { key: 'sum_cut_sew', x: 32.99 }, // "text":"      489.72     1,924.20",
      { key: null, x: 37.7 }, // "text":"    1,924.20     3,045.30",
      { key: 'sum_ext', x: 42.335 }, // "text":"    3,045.30 ",
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length === 13 &&
        parts[0].x === 1.01 &&
        parts[0].text.includes('ITEM CLASS TOTALS')
      ) {
        return true;
      }
    },
  },
  classTotalThin: {
    type: 'classTotal',
    reportFormat: 'thin',
    specialPartProcess: (parts) => {
      const newParts = parts.filter((part) => {
        // remove double valued part
        if (part.x === 20.263) {
          return false;
        }
        return true;
      });
      return [newParts, null, null];
    },
    expected: [
      { key: null, x: 0.9199999999999999 }, // 'ITEM CLASS TOTALS >>>>>>>>',
      { key: null, x: 0.958 }, // 'ITEM CLASS TOTALS >>>>>>>>  ',
      { key: 'sum_item_count', x: 8.533 }, // '     8   ',
      { key: null, x: 10.1 }, // '     8', x: 9.96, x: 10.271
      { key: null, x: 11.015 }, // 'ACCEPTED:                ',
      { key: null, x: 11.292 }, // 'ACCEPTED:',
      { key: 'sum_unit_count', x: 17.78 }, // '    90   ',
      { key: null, x: 19.194 }, // '    90', x: 19.018, x: 19.294
      { key: null, x: 20.263 }, // '              .00          .00',
      { key: 'sum_labor', x: 20.54 }, // '              .00',
      { key: 'sum_cut_sew', x: 25.115 }, // '         .00       314.25',
      { key: null, x: 28.693 }, // '      314.25       314.25',
      { key: 'sum_ext', x: 32.188 }, // '      314.25 '
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 13 &&
        parts[0].x === 0.9199999999999999 &&
        parts[0].text.includes('ITEM CLASS TOTALS')
      ) {
        return true;
      }
    },
  },
  classTotalFat: {
    type: 'classTotal',
    reportFormat: 'fat',
    expected: [
      { key: null, x: 1.01 }, // text: 'I',
      { key: null, x: 1.048 }, // text: 'I',
      { key: null, x: 1.368 }, // text: 'TEM ',
      { key: null, x: 1.406 }, // text: 'TEM ',
      { key: null, x: 2.743 }, // text: 'CLASS ',
      { key: null, x: 2.781 }, // text: 'CLASS ',
      { key: null, x: 4.688 }, // text: 'TOTALS ',
      { key: null, x: 4.726 }, // text: 'TOTALS ',
      { key: null, x: 6.928 }, // text: '>>>>>>>>',
      { key: null, x: 6.965 }, // text: '>>>>>>>>',
      {
        key: 'sum_item_count',
        x: 11.018,
        buffer: 3,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // text: '6',
      // { key: null, x: 12.867}, // text: '6 ',
      { key: null, x: 14.216 }, // text: 'ACCEPTED:',
      { key: null, x: 14.345 }, // text: 'ACCEPTED:',
      {
        key: 'sum_unit_count',
        x: 24.324,
        buffer: 2,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // text: '29',
      // { key: null, x: 24.726}, // text: '29',
      {
        key: 'sum_labor',
        x: 30.47,
        buffer: 2,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
        min: 29.0,
        max: 31.8,
      }, // text: '.',
      // { key: null, x: 31.594}, // text: '.',
      // { key: null, x: 31.828000000000003}, // text: '00',
      // { key: null, x: 31.957}, // text: '00 ',
      {
        key: 'sum_cut',
        x: 34.9,
        buffer: 1.7,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
        min: 33.3,
        max: 36.6,
      }, // text: '.',
      // { key: null, x: 36.23}, // text: '.',
      // { key: null, x: 36.448}, // text: '00',
      // { key: null, x: 36.588}, // text: '00 ',
      {
        key: 'sum_sew',
        x: 39.603,
        buffer: 1.5,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
        min: 37.0,
        max: 41.5,
      }, // text: '81.',
      // { key: null, x: 40.218}, // text: '81.',
      // { key: null, x: 40.9}, // text: '50',
      // { key: null, x: 41.115}, // text: '50 ',
      {
        key: 'sum_ext',
        x: 44.53,
        buffer: 2,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
        min: 42,
        max: 46,
      }, // text: '81.',
      // { key: null, x: 44.853}, // text: '81.',
      // { key: null, x: 45.422}, // text: '50',
      // { key: null, x: 45.745}, // text: '50 ',
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts[0].text === 'I' &&
        parts[3].text === 'TEM ' &&
        parts[4].text === 'CLASS ' &&
        parts[6].text === 'TOTALS ' &&
        parts[8].text === '>>>>>>>>'
      ) {
        return true;
      }
    },
  },
  classTotalFatBorder: {
    type: 'classTotal',
    reportFormat: 'fatborder',
    expected: [
      { key: null, x: 2.338 }, // 'I',
      { key: null, x: 2.375 }, // 'I',
      { key: null, x: 2.676 }, // 'TEM ',
      { key: null, x: 2.714 }, // 'TEM ',
      { key: null, x: 4.004 }, // 'CLASS ',
      { key: null, x: 4.041 }, // 'CLASS ',
      { key: null, x: 5.88 }, // 'TOTALS ',
      { key: null, x: 5.918 }, // 'TOTALS ',
      { key: null, x: 8.044 }, // '>>>>>>>>',
      { key: null, x: 8.081 }, // '>>>>>>>>',
      {
        key: 'sum_item_count',
        x: 12.701,
        buffer: 1.8,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '2',
      // { key: null, x: 13.473 }, // '2 ',
      { key: null, x: 14.747 }, // 'ACCEPTED:',
      { key: null, x: 14.862 }, // 'ACCEPTED:',
      {
        key: 'sum_unit_count',
        x: 24.514,
        min: 23,
        max: 25.5,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '13', x: 23.836, 24.948,
      // { key: null, x: 24.614 }, // '13',
      {
        key: 'sum_labor',
        x: 30.954,
        buffer: 2,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '.', x: 30.799, x: 31.026,
      // { key: null, x: 31.065 }, // '.',
      // { key: null, x: 31.288 }, // '00',
      // { key: null, x: 31.399 }, // '00 ',
      {
        key: 'sum_cut',
        x: 35.293,
        min: 33.2,
        max: 36,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '.', x: 33.608,
      // { key: null, x: 35.412 }, // '.',
      // { key: null, x: 35.632 }, // '00',
      // { key: null, x: 35.751 }, // '00 ',
      {
        key: 'sum_sew',
        x: 38.977,
        min: 37,
        max: 40.5,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '16.', x: 37.326, x: 40.176,
      // { key: null, x: 39.164 }, // '16.',
      // { key: null, x: 39.839 }, // '90',
      // { key: null, x: 40.026 }, // '90 ',
      {
        key: 'sum_ext',
        x: 43.252,
        min: 41,
        max: 45,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '16.', x: 41.674, x: 44.526,
      // { key: null, x: 43.514 }, // '16.',
      // { key: null, x: 44.115 }, // '90',
      // { key: null, x: 44.376 }, // '90 '
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts[0].text === 'I' &&
        parts[3].text === 'TEM ' &&
        parts[4].text === 'CLASS ' &&
        parts[6].text === 'TOTALS ' &&
        parts[8].text === '>>>>>>>>'
      ) {
        return true;
      }
    },
  },
  classTotalSuyapa: {
    type: 'classTotal',
    reportFormat: 'suyapa',
    expected: [
      { key: null, x: 2.338 }, // text: 'I'
      { key: null, x: 2.375 }, // text: 'I'
      { key: null, x: 2.676 }, // text: 'TEM '
      { key: null, x: 2.714 }, // text: 'TEM '
      { key: null, x: 4.004 }, // text: 'CLASS '
      { key: null, x: 4.041 }, // text: 'CLASS '
      { key: null, x: 5.88 }, // text: 'TOTALS '
      { key: null, x: 5.918 }, // text: 'TOTALS '
      { key: null, x: 8.044 }, // text: '>>>>>>>>'
      { key: null, x: 8.081 }, // text: '>>>>>>>>'
      {
        key: 'sum_item_count',
        x: 12.9,
        buffer: 1.8,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // text: '4'
      // { key: null, x: 13.515 }, // text: '4 '
      { key: null, x: 14.824 }, // text: 'ACCEPTED:'
      { key: null, x: 14.861 }, // text: 'ACCEPTED:'
      {
        key: 'sum_unit_count',
        x: 24.612,
        min: 23,
        max: 25.5,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // text: '67'
      // { key: null, x: 24.65 }, // text: '67'
      {
        key: 'sum_labor',
        x: 31.025,
        buffer: 2,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // text: '.'
      // { key: null, x: 31.062 }, // text: '.'
      // { key: null, x: 31.364 }, // text: '00'
      // { key: null, x: 31.401 }, // text: '00 '
      {
        key: 'sum_cut',
        x: 35.413,
        min: 33.2,
        max: 36,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // text: '.'
      // { key: null, x: 35.45 }, // text: '.'
      // { key: null, x: 35.752 }, // text: '00'
      // { key: null, x: 35.789 }, // text: '00 '
      {
        key: 'sum_sew',
        x: 38.789,
        min: 37,
        max: 40.5,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // text: '144.'
      // { key: null, x: 38.826 }, // text: '144.'
      // { key: null, x: 39.913 }, // text: '50'
      // { key: null, x: 39.95 }, // text: '50 '
      {
        key: 'sum_ext',
        x: 43.177,
        min: 41,
        max: 45,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // text: '144.'
      // { key: null, x: 43.214 }, // text: '144.'
      // { key: null, x: 44.296 }, // text: '50'
      // { key: null, x: 44.334 }, // text: '50 '

      { key: null, x: 2.338 }, // 'I',
      { key: null, x: 2.375 }, // 'I',
      { key: null, x: 2.676 }, // 'TEM ',
      { key: null, x: 2.714 }, // 'TEM ',
      { key: null, x: 4.004 }, // 'CLASS ',
      { key: null, x: 4.041 }, // 'CLASS ',
      { key: null, x: 5.88 }, // 'TOTALS ',
      { key: null, x: 5.918 }, // 'TOTALS ',
      { key: null, x: 8.044 }, // '>>>>>>>>',
      { key: null, x: 8.081 }, // '>>>>>>>>',
      {
        key: 'sum_item_count',
        x: 12.701,
        buffer: 1.8,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '2',
      // { key: null, x: 13.473 }, // '2 ',
      { key: null, x: 14.747 }, // 'ACCEPTED:',
      { key: null, x: 14.862 }, // 'ACCEPTED:',
      {
        key: 'sum_unit_count',
        x: 24.514,
        min: 23,
        max: 25.5,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '13', x: 23.836, 24.948,
      // { key: null, x: 24.614 }, // '13',
      {
        key: 'sum_labor',
        x: 30.954,
        buffer: 2,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '.', x: 30.799, x: 31.026,
      // { key: null, x: 31.065 }, // '.',
      // { key: null, x: 31.288 }, // '00',
      // { key: null, x: 31.399 }, // '00 ',
      {
        key: 'sum_cut',
        x: 35.293,
        min: 33.2,
        max: 36,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '.', x: 33.608,
      // { key: null, x: 35.412 }, // '.',
      // { key: null, x: 35.632 }, // '00',
      // { key: null, x: 35.751 }, // '00 ',
      {
        key: 'sum_sew',
        x: 38.977,
        min: 37,
        max: 40.5,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '16.', x: 37.326, x: 40.176,
      // { key: null, x: 39.164 }, // '16.',
      // { key: null, x: 39.839 }, // '90',
      // { key: null, x: 40.026 }, // '90 ',
      {
        key: 'sum_ext',
        x: 43.252,
        min: 41,
        max: 45,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '16.', x: 41.674, x: 44.526,
      // { key: null, x: 43.514 }, // '16.',
      // { key: null, x: 44.115 }, // '90',
      // { key: null, x: 44.376 }, // '90 '
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapa' &&
        parts[0].text === 'I' &&
        parts[3].text === 'TEM ' &&
        parts[4].text === 'CLASS ' &&
        parts[6].text === 'TOTALS ' &&
        parts[8].text === '>>>>>>>>'
      ) {
        return true;
      }
    },
  },
  classTotalSuyapaNew: {
    type: 'classTotal',
    reportFormat: 'suyapaNew',
    expected: [
      { key: null, x: 2.338 }, // 'ITEM CLASS TOTALS >>>>>>>>'
      { key: null, x: 2.375 }, // 'ITEM CLASS TOTALS >>>>>>>>'
      { key: 'sum_item_count', x: 9.187 }, // '    11'
      { key: null, x: 11.787 }, // '    11 '
      { key: null, x: 14.115 }, // 'ACCEPTED:'
      { key: null, x: 14.862 }, // 'ACCEPTED:'
      { key: 'sum_unit_count', x: 20.967 }, // '   231'
      { key: null, x: 23.263 }, // '   231'
      { key: 'sum_labor', x: 25.59 }, // '              .00'
      { key: null, x: 26.338 }, // '              .00          .00'
      { key: 'sum_cut_sew', x: 32.375 }, // '         .00       597.50'
      { key: null, x: 36.8 }, // '      597.50       597.50'
      { key: 'sum_ext', x: 41.15 }, // '      597.50 '
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNew' &&
        parts.length === 13 &&
        parts[0].x === 2.338 &&
        parts[0].text.includes('ITEM CLASS TOTALS')
      ) {
        return true;
      }
    },
  },
  prt: {
    type: 'subitem',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 9.607 },
      { key: 'prt', x: 22.363 },
      { key: 'sub_ext', x: 34.972 }, // optional, if last of decorations
    ],
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      if (parts[parts.length - 1].text === '***') {
        console.log('has stars');
        const newParts = parts.slice(0, -1);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length >= 2 &&
        parts.length <= 4 &&
        parts[0].text === 'PRT' &&
        parts[0].x === 9.607
      ) {
        return true;
      }
    },
  },
  prtThin: {
    type: 'subitem',
    reportFormat: 'thin',
    expected: [
      { key: null, x: 11.268 }, // 'PRT                                         ',
      { key: 'prt', x: 26.101 }, // '          2.49'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length >= 2 &&
        parts.length <= 3 &&
        parts[0].text.startsWith('PRT') &&
        parts[0].x === 11.268
      ) {
        return true;
      }
    },
  },
  prtFat: {
    type: 'subitem',
    reportFormat: 'fat',
    expected: [
      { key: null, x: 11.785 }, // text: 'PRT',
      { key: 'prt', x: 30.787, combineTillNext: true, buffer: 3, trim: true }, // text: '44.',
      // { key: null, x: 31.684 }, // text: '40'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length === 3 &&
        parts[0].text === 'PRT' &&
        (parts[0].x === 11.785 || parts[0].x === 11.788)
      ) {
        return true;
      }
    },
  },
  prtSuyapa: {
    type: 'subitem',
    reportFormat: 'suyapa',
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      const last3Parts = parts.slice(-3);
      const stars = last3Parts.every((part) => part.text === '*');
      if (stars) {
        console.log('has stars');
        const newParts = parts.slice(0, -3);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    expected: [
      { key: null, x: 12.46 }, // text: 'PRT'
      { key: 'prt', x: 30.351, combineTillNext: true, buffer: 3, trim: true }, // text: '17.'
      // { key: null, x: 31.214 }, // text: '10'
      // { key: null, x: 45.535 }, // text: '*'
      // { key: null, x: 45.874 }, // text: '*'
      // { key: null, x: 46.213 }, // text: '*'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapa' &&
        (parts.length === 3 || parts.length === 6) &&
        parts[0].text === 'PRT' &&
        parts[0].x === 12.46
      ) {
        return true;
      }
    },
  },
  prtSuyapaNew: {
    type: 'subitem',
    reportFormat: 'suyapaNew',
    expected: [
      { key: null, x: 9.419 }, // 'PRT'
      { key: 'prt', x: 20.008 }, // '         74.82'
    ],
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      if (parts[parts.length - 1].text === '***') {
        console.log('has stars');
        const newParts = parts.slice(0, -1);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNew' &&
        parts.length >= 2 &&
        parts.length <= 4 &&
        parts[0].text === 'PRT' &&
        parts[0].x === 9.419
      ) {
        return true;
      }
    },
  },
  prs: {
    type: 'subitem',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 9.607 },
      { key: 'prs', x: 22.363 }, // text: '          4.01',
      { key: 'sub_ext', x: 34.972 }, // optional, if last of decorations
    ],
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      if (parts[parts.length - 1].text === '***') {
        console.log('has stars');
        const newParts = parts.slice(0, -1);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length >= 2 &&
        parts.length <= 4 &&
        parts[0].text === 'PRS' &&
        parts[0].x === 9.607
      ) {
        return true;
      }
    },
  },
  prsThin: {
    type: 'subitem',
    reportFormat: 'thin',
    expected: [
      { key: null, x: 11.268 }, // 'PRS                                         ',
      { key: 'prs', x: 26.101 }, // '          1.24'
      { key: 'sub_ext', x: 40.589 }, // "     11.52" // optional, if last of decorations
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length >= 2 &&
        parts.length <= 3 &&
        parts[0].text.startsWith('PRS') &&
        parts[0].x === 11.268
      ) {
        return true;
      }
    },
  },
  prsFat: {
    type: 'subitem',
    reportFormat: 'fat',
    expected: [
      { key: null, x: 11.785 }, // text: 'PRS',
      { key: 'prs', x: 30.787, combineTillNext: true, buffer: 3, trim: true }, // text: '22.',
      // { key: null, x: 31.684 }, // text: '17'
      {
        key: 'sub_ext',
        x: 44.167,
        combineTillNext: true,
        buffer: 3,
        trim: true,
      }, // text: '107.', // optional
      // { key: null, x: 45.328 }, // text: '23',  // optional
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length >= 3 &&
        parts[0].text === 'PRS' &&
        (parts[0].x === 11.785 || parts[0].x === 11.788)
      ) {
        return true;
      }
    },
  },
  prsSuyapa: {
    type: 'subitem',
    reportFormat: 'suyapa',
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      const last3Parts = parts.slice(-3);
      const stars = last3Parts.every((part) => part.text === '*');
      if (stars) {
        console.log('has stars');
        const newParts = parts.slice(0, -3);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    expected: [
      { key: null, x: 12.46 }, // text: 'PRS'
      { key: 'prs', x: 30.686, combineTillNext: true, buffer: 3, trim: true }, // text: '8.'
      // possible sub_ext to add if happens
      // { key: null, x: 31.286 }, // text: '57'
      // { key: null, x: 45.535 }, // text: '*'
      // { key: null, x: 45.874 }, // text: '*'
      // { key: null, x: 46.213 }, // text: '*'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapa' &&
        parts.length >= 3 &&
        parts[0].text === 'PRS' &&
        parts[0].x === 12.46
      ) {
        return true;
      }
    },
  },
  prsSuyapaNew: {
    type: 'subitem',
    reportFormat: 'suyapaNew',
    expected: [
      { key: null, x: 9.419 }, // 'PRS'
      { key: 'prs', x: 20.008 }, // '         37.45'
    ],
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      if (parts[parts.length - 1].text === '***') {
        console.log('has stars');
        const newParts = parts.slice(0, -1);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNew' &&
        parts.length >= 2 &&
        parts.length <= 4 &&
        parts[0].text === 'PRS' &&
        parts[0].x === 9.419
      ) {
        return true;
      }
    },
  },
  art: {
    type: 'subitem',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 9.607 },
      { key: 'art', x: 22.363 },
    ],
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      if (parts[parts.length - 1].text === '***') {
        console.log('has stars');
        const newParts = parts.slice(0, -1);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        (parts.length === 2 || parts.length === 3) &&
        parts[0].text === 'ART' &&
        parts[0].x === 9.607
      ) {
        return true;
      }
    },
  },
  artThin: {
    type: 'subitem',
    reportFormat: 'thin',
    expected: [
      { key: null, x: 11.268 }, //'ART                                         ',
      { key: 'art', x: 26.101 }, //'           .18'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 2 &&
        parts[0].text.startsWith('ART') &&
        parts[0].x === 11.268
      ) {
        return true;
      }
    },
  },
  artFat: {
    type: 'subitem',
    reportFormat: 'fat',
    expected: [
      { key: null, x: 11.785 }, // text: 'ART',
      { key: 'art', x: 31.509, combineTillNext: true, buffer: 3 }, // text: '.',
      // { key: null, x: 31.866999999999997 }, // text: '18',
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (format === 'fat' && parts[0].text === 'ART') {
        return true;
      }
    },
  },
  artSuyapa: {
    type: 'subitem',
    reportFormat: 'suyapa',
    specialPartProcess: (parts) => {
      // check if last 3 parts are stars
      const last3Parts = parts.slice(-3);
      const stars = last3Parts.every((part) => part.text === '*');
      if (stars) {
        console.log('has stars');
        const newParts = parts.slice(0, -3);
        const data = {
          stars: true,
        };
        return [newParts, data, null];
      }
      return [parts, null, null];
    },
    expected: [
      { key: null, x: 12.46 }, // text: 'ART'
      { key: 'art', x: 31.025, combineTillNext: true, buffer: 3 }, // text: '.'
      // { key: null, x: 31.364 }, // text: '18'
      // { key: null, x: 45.535 }, // text: '*'
      // { key: null, x: 45.874 }, // text: '*'
      // { key: null, x: 46.213 }, // text: '*'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (format === 'suyapa' && parts[0].text === 'ART') {
        return true;
      }
    },
  },
  artWithCode: {
    type: 'subitem',
    reportFormat: 'normal',
    expected: [
      { key: 'sub_code', x: 9.607 },
      { key: null, x: 11.761 },
      { key: 'art', x: 22.511 },
      { key: 'sub_ext', x: 34.972 },
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length === 4 &&
        parts[1].text === 'Art' &&
        parts[1].x === 11.761
      ) {
        return true;
      }
    },
  },
  artWithCodeThin: {
    type: 'subitem',
    reportFormat: 'thin',
    expected: [
      { key: 'sub_code', x: 11.268 }, // '06     ' ,
      { key: null, x: 13.338 }, // 'Art                                  ' ,
      { key: 'art', x: 25.756 }, // '           .00                             ' ,
      { key: 'sub_ext', x: 40.244 }, // '     31.26'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 4 &&
        parts[1].text.startsWith('Art') &&
        parts[1].x === 13.338
      ) {
        return true;
      }
    },
  },
  artWithCodeFat: {
    type: 'subitem',
    reportFormat: 'fat',
    expected: [
      { key: 'sub_code', x: 11.785 }, // text: '06',
      { key: null, x: 14.214 }, // text: 'Ar',
      { key: null, x: 14.9 }, // text: 't',
      { key: 'art', x: 31.428, combineTillNext: true, buffer: 3 }, // text: '.',
      // { key: 'art', x: 31.787, merged: true }, // text: '00',
      { key: 'sub_ext', x: 44.991, combineTillNext: true, buffer: 3 }, // text: '7.',
      // { key: 'sub_ext', x: 45.614, merged: true }, // text: '23',
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length === 7 &&
        parts[1].text === 'Ar' &&
        parts[2].text === 't'
      ) {
        return true;
      }
    },
  },
  topHeaderThin: {
    type: 'header',
    reportType: 'cprweekly',
    reportFormat: 'thin',
    expected: [
      { key: 'run_date', x: 0.65 }, // '11/20/23                                      ',
      { key: null, x: 16.173 }, // 'CONTRACTOR PAY REPORT  ',
      { key: null, x: 23.761 }, // 'FOR COMPANY#:  ',
      { key: null, x: 28.591 }, // '01                                 ',
      { key: null, x: 40.319 }, // 'PAGE ',
      { key: 'page', x: 41.698 }, // '   1'
    ],
    matchPartIndexToExpected: true,
    allowWithoutReportType: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 6 &&
        parts[0].x === 0.65 &&
        parts[5].x > 40 &&
        parts[5].x < 43 &&
        parts[1].text.trim() === 'CONTRACTOR PAY REPORT' &&
        parts[2].text.trim() === 'FOR COMPANY#:' &&
        parts[3].text.trim() === '01' &&
        parts[4].text.trim() === 'PAGE'
      ) {
        return true;
      }
    },
  },
  topHeaderThinAlt: {
    type: 'header',
    reportType: 'cprweekly',
    reportFormat: 'thin',
    specialPartProcess: (parts) => {
      const date = parts[0].text.trim();
      const pageMessy = parts[2].text.trim();
      // get page number from the end of the string
      const page = pageMessy.split(' ').pop();
      return [[], null, { run_date: date, page }];
    },
    expected: [
      { key: 'run_date', x: 0.65 }, // '11/20/23                                      ',
      { key: null, x: 16.173 }, // 'CONTRACTOR PAY REPORT  ',
      { key: null, x: 23.762 }, // 'FOR COMPANY#:  01                                 PAGE    8'
    ],
    matchPartIndexToExpected: true,
    allowWithoutReportType: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 3 &&
        parts[0].x === 0.65 &&
        parts[1].x === 16.173 &&
        parts[2].x === 23.762 &&
        parts[1].text.startsWith('CONTRACTOR PAY REPORT') &&
        parts[2].text.startsWith('FOR COMPANY#:')
      ) {
        return true;
      }
    },
  },
  topHeader: {
    type: 'header',
    reportType: 'cprweekly',
    reportFormat: 'normal',
    expected: [
      { key: 'run_date', x: 0.65 }, // "text":" 8/14/23",
      { key: null, x: 13.98 }, // "text":"CONTRACTOR PAY REPORT",
      { key: null, x: 20.714, buffer: 1.5 }, // "text":"FOR COMPANY#:",
      { key: null, x: 25.155, buffer: 1 }, // "text":"01",
      { key: null, x: 35.333, buffer: 0.5 }, // "text":"PAGE",
      { key: 'page', x: 36.914, buffer: 1 }, // "text":"  16",
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 6 &&
        parts[0].x === 0.65 &&
        parts[5].x > 35 &&
        parts[5].x < 38 &&
        parts[1].text.trim() === 'CONTRACTOR PAY REPORT' &&
        parts[2].text.trim() === 'FOR COMPANY#:' &&
        parts[3].text.trim() === '01' &&
        parts[4].text.trim() === 'PAGE'
      ) {
        return true;
      }
    },
    allowWithoutReportType: true,
  },
  topHeaderAlt: {
    type: 'header',
    reportType: 'cprweekly',
    reportFormat: 'normal',
    expected: [
      { key: 'run_date', x: 0.65 }, // '10/31/22',
      { key: null, x: 16.821 }, // '  CONTRACTOR PAY REPORT',
      { key: null, x: 24.129 }, // 'FOR COMPANY#:',
      { key: null, x: 28.284 }, // '  01',
      { key: null, x: 43.056 }, // '  PAGE',
      { key: null, x: 45.573 }, // ' 11'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 6 &&
        parts[0].x === 0.65 &&
        parts[5].x > 45 &&
        parts[5].x < 46 &&
        parts[1].text.trim() === 'CONTRACTOR PAY REPORT' &&
        parts[2].text.trim() === 'FOR COMPANY#:' &&
        parts[3].text.trim() === '01' &&
        parts[4].text.trim() === 'PAGE'
      ) {
        return true;
      }
    },
    allowWithoutReportType: true,
  },
  topHeaderIndent: {
    type: 'header',
    reportType: 'cprweekly',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 0.65 }, // ' 3/30/20',
      { key: null, x: 12.566 }, // 'CONTRACTOR PAY REPORT',
      { key: null, x: 18.572 }, // 'FOR COMPANY#:',
      { key: null, x: 22.515 }, // '01',
      { key: null, x: 31.602 }, // 'PAGE',
      { key: null, x: 32.973 }, // '   1'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 6 &&
        parts[0].x === 0.65 &&
        parts[5].x > 32 &&
        parts[5].x < 34 &&
        parts[1].text.trim() === 'CONTRACTOR PAY REPORT' &&
        parts[2].text.trim() === 'FOR COMPANY#:' &&
        parts[3].text.trim() === '01' &&
        parts[4].text.trim() === 'PAGE'
      ) {
        return true;
      }
    },
    allowWithoutReportType: true,
  },
  topHeaderFat: {
    type: 'header',
    reportFormat: 'fat',
    reportType: 'cprweekly',
    expected: [
      { key: 'run_date', x: 1.008, combineTillNext: true, buffer: 0.5 }, // text: '9/',
      { key: 'run_date', x: 1.636, merged: true }, // text: '07/',
      { key: 'run_date', x: 2.523, merged: true }, // text: '23',
      { key: null, x: 16.807 }, // text: 'CONTRACTOR ',
      { key: null, x: 20.585 }, // text: 'PAY ',
      { key: null, x: 21.911 }, // text: 'REPORT',
      { key: null, x: 24.711 }, // text: 'FOR ',
      { key: null, x: 26.091 }, // text: 'COMPANY#:',
      { key: null, x: 29.887 }, // text: '01',
      { key: null, x: 42.374 }, // text: 'PAGE',
      { key: 'page', x: 45.187, buffer: 1.5 }, // text: '1',
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 11 &&
        parts[0].x < 1.5 &&
        parts[3].text.trim() === 'CONTRACTOR' &&
        parts[4].text.trim() === 'PAY' &&
        parts[5].text.trim() === 'REPORT' &&
        parts[6].text.trim() === 'FOR' &&
        parts[7].text.trim() === 'COMPANY#:' &&
        parts[8].text.trim() === '01' &&
        parts[9].text.trim() === 'PAGE'
      ) {
        return true;
      }
      return false;
    },
    allowWithoutReportType: true,
  },
  topHeaderFatBorder: {
    type: 'header',
    reportFormat: 'fatborder',
    reportType: 'cprweekly',
    expected: [
      { key: 'run_date', x: 2.298, combineTillNext: true, buffer: 0.5 }, // '3/',
      { key: null, x: 2.876, merged: true }, // '09/',
      { key: null, x: 3.739, merged: true }, // '21',
      { key: null, x: 15.723 }, // 'CONTRACTOR ',
      { key: null, x: 19.582 }, // 'PAY ',
      { key: null, x: 20.893 }, // 'REPORT',
      { key: null, x: 23.583 }, // 'FOR ',
      { key: null, x: 24.944 }, // 'COMPANY#:',
      { key: null, x: 28.676 }, // '01',
      { key: null, x: 39.157 }, // 'PAGE',
      { key: 'page', x: 41.724, buffer: 1.5 }, // '1'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 11 &&
        parts[0].x > 1.5 &&
        parts[3].x === 15.723 &&
        parts[3].text.trim() === 'CONTRACTOR' &&
        parts[4].text.trim() === 'PAY' &&
        parts[5].text.trim() === 'REPORT' &&
        parts[6].text.trim() === 'FOR' &&
        parts[7].text.trim() === 'COMPANY#:' &&
        parts[8].text.trim() === '01' &&
        parts[9].text.trim() === 'PAGE'
      ) {
        console.log('topHeaderFat', parts);
        return true;
      }
      return false;
    },
    allowWithoutReportType: true,
  },
  topHeaderSuyapa: {
    type: 'header',
    reportFormat: 'suyapa',
    reportType: 'cprweekly',
    expected: [
      { key: 'run_date', x: 2.339, combineTillNext: true, buffer: 0.5 }, // text: '7/'
      { key: null, x: 2.94 }, // text: '15/'
      { key: null, x: 3.8019999999999996 }, // text: '24'
      { key: null, x: 17.526 }, // text: 'CONTRACTOR '
      { key: null, x: 21.184 }, // text: 'PAY '
      { key: null, x: 22.464 }, // text: 'REPORT'
      { key: null, x: 25.285 }, // text: 'FOR '
      { key: null, x: 26.613 }, // text: 'COMPANY#:'
      { key: null, x: 30.351 }, // text: '01'
      { key: null, x: 42.164 }, // text: 'PAGE'
      { key: 'page', x: 44.862 }, // text: '1'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 11 &&
        parts[0].x > 1.5 &&
        parts[3].x === 17.526 &&
        parts[3].text.trim() === 'CONTRACTOR' &&
        parts[4].text.trim() === 'PAY' &&
        parts[5].text.trim() === 'REPORT' &&
        parts[6].text.trim() === 'FOR' &&
        parts[7].text.trim() === 'COMPANY#:' &&
        parts[8].text.trim() === '01' &&
        parts[9].text.trim() === 'PAGE'
      ) {
        console.log('topHeaderSuyapa', parts);
        return true;
      }
      return false;
    },
    allowWithoutReportType: true,
  },
  topHeaderSuyapaNew: {
    type: 'header',
    reportType: 'cprweekly',
    reportFormat: 'suyapaNew',
    expected: [
      { key: null, x: 2 }, // ' 3/30/20',
      { key: null, x: 12.988 }, // 'CONTRACTOR PAY REPORT',
      { key: null, x: 18.543 }, // 'FOR COMPANY#:',
      { key: null, x: 22.21 }, // '01',
      { key: null, x: 30.597 }, // 'PAGE',
      { key: null, x: 31.906999999999996 }, // '   1'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 6 &&
        parts[0].x === 2 &&
        parts[5].x > 30 &&
        parts[5].x < 33 &&
        parts[1].text.trim() === 'CONTRACTOR PAY REPORT' &&
        parts[2].text.trim() === 'FOR COMPANY#:' &&
        parts[3].text.trim() === '01' &&
        parts[4].text.trim() === 'PAGE'
      ) {
        return true;
      }
    },
    allowWithoutReportType: true,
  },
  topHeaderSuyapaNewFat: {
    type: 'header',
    reportFormat: 'suyapaNewFat',
    reportType: 'cprweekly',
    expected: [
      { key: 'run_date', x: 2.298, combineTillNext: true, buffer: 0.5 }, // '7/'
      { key: null, x: 2.876, merged: true }, // '16/'
      { key: null, x: 3.739, merged: true }, // '24'
      { key: null, x: 15.799 }, // 'CONTRACTOR '
      { key: null, x: 19.653 }, // 'PAY '
      { key: null, x: 20.959 }, // 'REPORT'
      { key: null, x: 22.7 }, // 'FOR '
      { key: null, x: 24.067 }, // 'COMPANY#:'
      { key: null, x: 27.2 }, // '01'
      { key: null, x: 37.702 }, // 'PAGE'
      { key: 'page', x: 40.098, buffer: 1.5 }, // '1'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 11 &&
        parts[0].x > 1.5 &&
        parts[3].x === 15.799 &&
        parts[3].text.trim() === 'CONTRACTOR' &&
        parts[4].text.trim() === 'PAY' &&
        parts[5].text.trim() === 'REPORT' &&
        parts[6].text.trim() === 'FOR' &&
        parts[7].text.trim() === 'COMPANY#:' &&
        parts[8].text.trim() === '01' &&
        parts[9].text.trim() === 'PAGE'
      ) {
        return true;
      }
      return false;
    },
    allowWithoutReportType: true,
  },
  topRunData: {
    type: 'header',
    reportFormat: 'normal',
    expected: [
      { key: 'run_time', x: 0.65 }, // "text":"13:42:05",
      { key: 'invoice_number', x: 34.898 }, // "text":"ACR302PR",
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType) => {
      if (lastRowType === 'topHeader' && parts.length === 2) {
        return true;
      }
    },
  },
  topRunDataAlt: {
    type: 'header',
    reportFormat: 'normal',
    expected: [
      { key: 'run_time', x: 0.65 }, // '11:08:30',
      { key: 'invoice_number', x: 43.056 }, // '  ACR303PR'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType) => {
      if (lastRowType === 'topHeaderAlt' && parts.length === 2) {
        return true;
      }
    },
  },
  topRunDataIndent: {
    type: 'header',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 0.65 }, // ' 9:53:53',
      { key: null, x: 31.341 }, // 'ACR302PRS'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType) => {
      if (lastRowType === 'topHeaderIndent' && parts.length === 2) {
        return true;
      }
    },
  },
  topRunDataThin: {
    type: 'header',
    reportFormat: 'thin',
    expected: [
      { key: 'run_time', x: 0.65 }, // "text":"13:42:05",
      { key: 'invoice_number', x: 41.353 }, // "text":"ACR302PR",
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType) => {
      if (lastRowType === 'topHeaderThin' && parts.length === 2) {
        return true;
      }
    },
  },
  topRunDataThinAlt: {
    type: 'header',
    reportFormat: 'thin',
    specialPartProcess: (parts) => {
      const values = parts[0].text.split(' ');
      const run_time = values[0];
      const invoice_number = values[1];
      return [[], null, { run_time, invoice_number }];
    },
    expected: [
      { key: 'run_time', x: 0.65 }, // '10:55:49                                                                                                               ACR303PR'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType) => {
      if (
        lastRowType === 'topHeaderThinAlt' ||
        (lastRowType === 'topHeaderThin' && parts.length === 1)
      ) {
        return true;
      }
    },
  },
  topRunDataFatBorder: {
    type: 'header',
    reportFormat: 'fatborder',
    expected: [
      { key: 'run_time', x: 2, buffer: 2, combineTillNext: true }, // '12:',
      { key: 'run_time', x: 2.859, merged: true }, // '08:',
      { key: 'run_time', x: 3.722, merged: true }, // '17',
      { key: 'invoice_number', x: 37.604 }, // 'ACR302PRS'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType) => {
      if (lastRowType === 'topHeaderFatBorder' && parts.length === 4) {
        return true;
      }
      return false;
    },
  },
  topRunDataFat: {
    type: 'header',
    reportFormat: 'fat',
    expected: [
      { key: 'run_time', x: 0.65, buffer: 2, combineTillNext: true }, // text: '13:',
      { key: 'run_time', x: 1.496, merged: true }, // text: '53:',
      { key: 'run_time', x: 2.346, merged: true }, // text: '42',
      { key: 'invoice_number', x: 42.956, buffer: 2 }, // text: 'ACR302PRS'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType) => {
      if (lastRowType === 'topHeaderFat' && parts.length === 4) {
        return true;
      }
      return false;
    },
  },
  topRunDataFatAlt: {
    type: 'header',
    reportFormat: 'fat',
    expected: [
      { key: 'run_time', x: 0.65, buffer: 2, combineTillNext: true }, // text: '10',
      { key: 'run_time', x: 1.372, merged: true }, // text: ':',
      { key: 'run_time', x: 1.496, merged: true }, // text: '53:',
      { key: 'run_time', x: 2.346, merged: true }, // text: '42',
      { key: 'invoice_number', x: 42.956, buffer: 2 }, // text: 'ACR302PRS'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType) => {
      if (lastRowType === 'topHeaderFat' && parts.length === 5) {
        return true;
      }
      return false;
    },
  },
  topRunDataSuyapa: {
    type: 'header',
    reportFormat: 'suyapa',
    expected: [
      { key: 'run_time', x: 2, buffer: 2, combineTillNext: true }, // text: '14:'
      { key: 'run_time', x: 2.863, merged: true }, // text: '39:'
      { key: 'run_time', x: 3.725, merged: true }, // text: '50'
      { key: 'invoice_number', x: 42.164 }, // text: 'ACR302PR'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType) => {
      if (lastRowType === 'topHeaderSuyapa' && parts.length === 4) {
        return true;
      }
      return false;
    },
  },
  topRunDataSuyapaNew: {
    type: 'header',
    reportFormat: 'suyapaNew',
    expected: [
      { key: 'run_time', x: 2 }, // "text":"13:42:05",
      { key: 'invoice_number', x: 30.22 }, // "text":"ACR302PR",
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType) => {
      if (lastRowType === 'topHeaderSuyapaNew' && parts.length === 2) {
        return true;
      }
    },
  },
  topRunDataSuyapaNewFat: {
    type: 'header',
    reportFormat: 'suyapaNewFat',
    expected: [
      { key: 'run_time', x: 2, buffer: 2, combineTillNext: true }, // text: '13:',
      { key: 'run_time', x: 2.859, merged: true }, // text: '53:',
      { key: 'run_time', x: 3.722, merged: true }, // text: '42',
      { key: 'invoice_number', x: 37.702, buffer: 2 }, // text: 'ACR302PRS'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType) => {
      if (lastRowType === 'topHeaderSuyapaNewFat' && parts.length === 4) {
        return true;
      }
      return false;
    },
  },
  topReportNameAccepted: {
    type: 'header',
    reportFormat: 'normal',
    pageType: 'accepted',
    expected: [
      { key: null, x: 16.198 }, // "text":"ACCEPTED PAY TRANSACTIONS",
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length === 1 &&
        parts[0].text === 'ACCEPTED PAY TRANSACTIONS'
      ) {
        return true;
      }
    },
  },
  topReportNameAcceptedThin: {
    type: 'header',
    reportFormat: 'thin',
    pageType: 'accepted',
    expected: [
      { key: null, x: 19.202 }, // "text":"ACCEPTED PAY TRANSACTIONS",
    ],
    matchPartIndexToExpected: true,
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 1 &&
        parts[0].text === 'ACCEPTED PAY TRANSACTIONS'
      ) {
        return true;
      }
    },
  },
  topReportNameAcceptedFat: {
    type: 'header',
    reportFormat: 'fat',
    pageType: 'accepted',
    expected: [
      { key: null, x: 20.052 }, // text: 'ACCEPTED '
      { key: null, x: 23.045 }, // text: 'PAY '
      { key: null, x: 24.37 }, // text: 'TRANSACTI'
      { key: null, x: 27.336 }, // text: 'ONS'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length === 4 &&
        parts[0].text.trim() === 'ACCEPTED' &&
        parts[1].text.trim() === 'PAY' &&
        parts[2].text.trim() === 'TRANSACTI' &&
        parts[3].text.trim() === 'ONS'
      ) {
        return true;
      }
    },
  },
  topReportNameAcceptedFatBorder: {
    type: 'header',
    reportFormat: 'fatborder',
    pageType: 'accepted',
    expected: [
      { key: null, x: 18.2 }, // 'ACCEPTED ',
      { key: null, x: 21.242 }, // 'PAY ',
      { key: null, x: 22.548 }, // 'TRANSACTI',
      { key: null, x: 25.561 }, // 'ONS'
    ],
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length === 4 &&
        parts[0].text.trim() === 'ACCEPTED' &&
        parts[1].text.trim() === 'PAY' &&
        parts[2].text.trim() === 'TRANSACTI' &&
        parts[3].text.trim() === 'ONS'
      ) {
        return true;
      }
    },
  },
  topReportNameAcceptedSuyapa: {
    type: 'header',
    reportFormat: 'suyapa',
    pageType: 'accepted',
    expected: [
      { key: null, x: 20.224 }, // text: 'ACCEPTED '
      { key: null, x: 23.12 }, // text: 'PAY '
      { key: null, x: 24.399 }, // text: 'TRANSACTI'
      { key: null, x: 27.273 }, // text: 'ONS'
    ],
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapa' &&
        parts.length === 4 &&
        parts[0].text.trim() === 'ACCEPTED' &&
        parts[1].text.trim() === 'PAY' &&
        parts[2].text.trim() === 'TRANSACTI' &&
        parts[3].text.trim() === 'ONS'
      ) {
        return true;
      }
    },
  },
  topReportNameAcceptedSuyapaNew: {
    type: 'header',
    reportFormat: 'suyapaNew',
    pageType: 'accepted',
    expected: [
      { key: null, x: 14.811 }, // "text":"ACCEPTED PAY TRANSACTIONS",
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNew' &&
        parts.length === 1 &&
        parts[0].text === 'ACCEPTED PAY TRANSACTIONS'
      ) {
        return true;
      }
    },
  },
  topReportNameAcceptedSuyapaNewFat: {
    type: 'header',
    reportFormat: 'suyapaNewFat',
    pageType: 'accepted',
    expected: [
      { key: null, x: 18.2 }, // text: 'ACCEPTED '
      { key: null, x: 21.242 }, // text: 'PAY '
      { key: null, x: 22.548 }, // text: 'TRANSACTI'
      { key: null, x: 25.561 }, // text: 'ONS'
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNewFat' &&
        parts.length === 4 &&
        parts[0].text.trim() === 'ACCEPTED' &&
        parts[1].text.trim() === 'PAY' &&
        parts[2].text.trim() === 'TRANSACTI' &&
        parts[3].text.trim() === 'ONS'
      ) {
        return true;
      }
    },
  },
  topReportNameRejected: {
    type: 'header',
    reportFormat: 'normal',
    pageType: 'rejected',
    expected: [
      { key: null, x: 16.198 }, // "text":"REJECTED PAY TRANSACTION",
    ],
    matchPartIndexToExpected: true,
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length === 1 &&
        parts[0].text.trim() === 'REJECTED PAY TRANSACTIONS'
      ) {
        return true;
      }
    },
  },
  topReportNameRejectedThin: {
    type: 'header',
    reportFormat: 'thin',
    pageType: 'rejected',
    expected: [
      { key: null, x: 16.198 }, // "text":"REJECTED PAY TRANSACTION",
    ],
    matchPartIndexToExpected: true,
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 1 &&
        parts[0].text.trim() === 'REJECTED PAY TRANSACTIONS'
      ) {
        return true;
      }
    },
  },
  topReportNameRejectedFat: {
    type: 'header',
    reportFormat: 'fat',
    pageType: 'rejected',
    expected: [
      { key: null, x: 20.052 }, // text: 'REJECTED ',
      { key: null, x: 22.964 }, // text: 'PAY ',
      { key: null, x: 24.29 }, // text: 'TRANSACTI',
      { key: null, x: 27.256 }, // text: 'ONS',
    ],
    matchPartIndexToExpected: true,
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length === 4 &&
        parts[0].text.trim() === 'REJECTED' &&
        parts[1].text.trim() === 'PAY' &&
        parts[2].text.trim() === 'TRANSACTI' &&
        parts[3].text.trim() === 'ONS'
      ) {
        return true;
      }
    },
  },
  topReportNameRejectedFatBorder: {
    type: 'header',
    reportFormat: 'fatborder',
    pageType: 'rejected',
    expected: [
      { key: null, x: 20.052 }, // text: 'REJECTED ',
      { key: null, x: 22.964 }, // text: 'PAY ',
      { key: null, x: 24.29 }, // text: 'TRANSACTI',
      { key: null, x: 27.256 }, // text: 'ONS',
    ],
    matchPartIndexToExpected: true,
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length === 4 &&
        parts[0].text.trim() === 'REJECTED' &&
        parts[1].text.trim() === 'PAY' &&
        parts[2].text.trim() === 'TRANSACTI' &&
        parts[3].text.trim() === 'ONS'
      ) {
        return true;
      }
    },
  },
  topReportRange: {
    type: 'header',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 9.607 }, // "text":"SHIPPED BETWEEN:",
      { key: 'report_start', x: 14.908 }, // "text":"2023/08/06",
      { key: null, x: 18.49 }, // "text":"TO",
      { key: 'report_end', x: 19.784 }, // "text":"2023/08/12",
      { key: null, x: 23.371 }, // "text":"TO BE PAID ON:",
      { key: 'pay_date', x: 28.104 }, // "text":" 8/14/2023",
    ],
    infoCheckFunc: reportDatesCheck,
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length === 6 &&
        parts[0].text === 'SHIPPED BETWEEN:' &&
        parts[2].text === 'TO' &&
        parts[4].text === 'TO BE PAID ON:'
      ) {
        return true;
      }
    },
  },
  topReportRangeThin: {
    type: 'header',
    reportFormat: 'thin',
    expected: [
      { key: null, x: 11.268 }, // 'SHIPPED BETWEEN:  ',
      { key: null, x: 17.132 }, // '2023/11/12  ',
      { key: null, x: 20.927 }, // 'TO  ',
      { key: null, x: 21.962 }, // '2023/11/18  ',
      { key: null, x: 25.756 }, // 'TO BE PAID ON:  ',
      { key: null, x: 30.93 }, // '11/20/2023'
    ],
    infoCheckFunc: reportDatesCheck,
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 6 &&
        parts[0].text === 'SHIPPED BETWEEN:  ' &&
        parts[2].text === 'TO  ' &&
        parts[4].text === 'TO BE PAID ON:  '
      ) {
        return true;
      }
    },
  },
  topReportRangeFat: {
    type: 'header',
    reportFormat: 'fat',
    expected: [
      { key: null, x: 11.785 }, // text: 'SHI',
      { key: null, x: 12.82 }, // text: 'PPED ',
      { key: null, x: 14.496 }, // text: 'BETWEEN:',
      { key: 'report_start', x: 17.968, combineTillNext: true, buffer: 0.5 }, // text: '2023/',
      // { key:null, x: 19.398}, // text: '08/',
      // { key:null, x: 20.295}, // text: '27',
      { key: null, x: 21.644 }, // text: 'TO',
      { key: 'report_end', x: 23.02, combineTillNext: true, buffer: 0.5 }, // text: '2023/',
      // { key:null, x: 24.455}, // text: '09/',
      // { key:null, x: 25.352}, // text: '02',
      { key: null, x: 26.701 }, // text: 'TO ',
      { key: null, x: 27.731 }, // text: 'BE ',
      { key: null, x: 28.735 }, // text: 'PAI',
      { key: null, x: 29.738 }, // text: 'D ',
      { key: null, x: 30.451 }, // text: 'ON:',
      { key: 'pay_date', x: 32.615, combineTillNext: true, buffer: 0.5 }, // text: '9/',
      // { key:null, x: 33.248}, // text: '07/',
      // { key:null, x: 34.149}, // text: '2023'
    ],
    infoCheckFunc: reportDatesCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length === 18 &&
        parts[0].text === 'SHI' &&
        parts[1].text === 'PPED ' &&
        parts[2].text === 'BETWEEN:'
      ) {
        return true;
      }
    },
  },
  topReportRangeSummaryFat: {
    type: 'header',
    reportFormat: 'fat',
    expected: [
      { key: null, x: 9.95 }, // text: 'SHI',
      { key: null, x: 10.931 }, // text: 'PPED ',
      { key: null, x: 12.566 }, // text: 'BETW',
      { key: null, x: 13.819 }, // text: 'EEN:',
      { key: 'report_start', x: 15.726, combineTillNext: true, buffer: 0.5 }, // text: '2023/',
      // { key: null, x: 17.118 }, // text: '08/',
      // { key: null, x: 17.963 }, // text: '27',
      { key: null, x: 19.135 }, // text: 'TO',
      { key: 'report_end', x: 20.333, combineTillNext: true, buffer: 0.5 }, // text: '2023/',
      // { key: null, x: 21.724 }, // text: '09/',
      // { key: null, x: 22.57 }, // text: '02',
      { key: null, x: 23.746 }, // text: 'TO ',
      { key: null, x: 24.727 }, // text: 'BE ',
      { key: null, x: 25.68 }, // text: 'PAI',
      { key: null, x: 26.639 }, // text: 'D ',
      { key: null, x: 27.293 }, // text: 'ON:',
      { key: 'pay_date', x: 29.227, combineTillNext: true, buffer: 0.5 }, // text: '9/',
      // { key: null, x: 29.804 }, // text: '07/',
      // { key: null, x: 30.65 }, // text: '2023',
    ],
    infoCheckFunc: reportDatesCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length === 19 &&
        parts[0].x < 10.2 &&
        parts[0].text === 'SHI' &&
        parts[1].text === 'PPED ' &&
        parts[2].text === 'BETW' &&
        parts[3].text === 'EEN:'
      ) {
        return true;
      }
      return false;
    },
  },
  topReportRangeFatBordered: {
    type: 'header',
    reportFormat: 'fatborder',
    expected: [
      { key: null, x: 11.299 }, // 'SHI', x: 12.46,
      { key: null, x: 12.302 }, // 'PPED ',
      { key: null, x: 13.977 }, // 'BETW',
      { key: null, x: 15.259 }, // 'EEN:',
      { key: 'report_start', x: 17.198, combineTillNext: true, buffer: 0.5 }, // '2021/',
      // { key: null, x: 18.623 }, // '02/',
      // { key: null, x: 19.482 }, // '28',
      { key: null, x: 20.66 }, // 'TO',
      { key: 'report_end', x: 21.866, combineTillNext: true, buffer: 0.5 }, // '2021/',
      // { key: null, x: 23.285 }, // '03/',
      // { key: null, x: 24.149 }, // '06',
      { key: null, x: 25.332 }, // 'TO ',
      { key: null, x: 26.329 }, // 'BE ',
      { key: null, x: 27.304 }, // 'PAI',
      { key: null, x: 28.274 }, // 'D ',
      { key: null, x: 28.941 }, // 'ON:',
      { key: 'pay_date', x: 30.897, combineTillNext: true, buffer: 0.5 }, // '3/',
      // { key: null, x: 31.48 }, // '09/',
      // { key: null, x: 32.339 }, // '2021'
    ],
    infoCheckFunc: reportDatesCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length === 19 &&
        parts[0].x > 11 &&
        parts[0].text === 'SHI' &&
        parts[1].text === 'PPED ' &&
        parts[2].text === 'BETW' &&
        parts[3].text === 'EEN:'
      ) {
        return true;
      }
      return false;
    },
  },
  topReportRangeFatBorderedAlt: {
    type: 'header',
    reportFormat: 'fatborder',
    expected: [
      { key: null, x: 12.46 }, // 'SHI',
      { key: null, x: 13.448 }, // 'PPED ',
      { key: null, x: 15.068 }, // 'BETWEEN:',
      { key: 'report_start', x: 18.405, combineTillNext: true, buffer: 0.5 }, // '2021/',
      // { key: null, x: 19.791 }, // '02/',
      // { key: null, x: 20.654 }, // '28',
      { key: null, x: 21.928 }, // 'TO',
      { key: 'report_end', x: 23.227, combineTillNext: true, buffer: 0.5 }, // '2021/',
      // { key: null, x: 24.613 }, // '03/',
      // { key: null, x: 25.476 }, // '06',
      { key: null, x: 26.75 }, // 'TO ',
      { key: null, x: 27.742 }, // 'BE ',
      { key: null, x: 28.708 }, // 'PAI',
      { key: null, x: 29.674 }, // 'D ',
      { key: null, x: 30.349 }, // 'ON:',
      { key: 'pay_date', x: 32.401, combineTillNext: true, buffer: 0.5 }, // '3/',
      // { key: null, x: 33.002 }, // '09/',
      // { key: null, x: 33.864 }, // '2021'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length === 18 &&
        parts[0].x > 11 &&
        parts[0].text === 'SHI' &&
        parts[1].text === 'PPED ' &&
        parts[2].text === 'BETWEEN:' &&
        parts[6].x === 21.928
      ) {
        return true;
      }
      return false;
    },
  },
  topReportRangeFatBorderedBlank: {
    type: 'header',
    reportFormat: 'fatborder',
    expected: [
      { key: null, x: 12.46 }, // 'SHI',
      { key: null, x: 13.448 }, // 'PPED ',
      { key: null, x: 15.068 }, // 'BETWEEN:',
      { key: 'report_start', x: 18.405, combineTillNext: true, buffer: 0.5 }, // '2021/',
      // { key: null, x: 19.791 }, // '02/',
      // { key: null, x: 20.654 }, // '28',
      { key: null, x: 21.928 }, // 'TO',
      { key: 'report_end', x: 23.227, combineTillNext: true, buffer: 0.5 }, // '2021/',
      // { key: null, x: 24.613 }, // '03/',
      // { key: null, x: 25.476 }, // '06',
      { key: null, x: 26.75 }, // 'TO ',
      { key: null, x: 27.742 }, // 'BE ',
      { key: null, x: 28.708 }, // 'PAI',
      { key: null, x: 29.674 }, // 'D ',
      { key: null, x: 30.349 }, // 'ON:',
      { key: 'pay_date', x: 32.401, combineTillNext: true, buffer: 0.5 }, // '3/',
      // { key: null, x: 33.002 }, // '09/',
      // { key: null, x: 33.864 }, // '2021'
    ],
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length === 7 &&
        parts[0].x > 11 &&
        parts[0].text === 'SHI' &&
        parts[1].text === 'PPED ' &&
        parts[2].text === 'BETWEEN:'
      ) {
        return true;
      }
      return false;
    },
  },
  topReportRangeSuyapa: {
    type: 'header',
    reportFormat: 'suyapa',
    expected: [
      { key: null, x: 12.46 }, // 'SHI',
      { key: null, x: 13.448 }, // 'PPED ',
      { key: null, x: 15.068 }, // 'BETWEEN:',
      { key: 'report_start', x: 18.538, combineTillNext: true, buffer: 0.5 }, // '2021/',
      // { key: null, x: 19.791 }, // '02/',
      // { key: null, x: 20.654 }, // '28',
      { key: null, x: 22.588 }, // 'TO',
      { key: 'report_end', x: 23.939, combineTillNext: true, buffer: 0.5 }, // '2021/',
      // { key: null, x: 24.613 }, // '03/',
      // { key: null, x: 25.476 }, // '06',
      { key: null, x: 27.988 }, // 'TO ',
      { key: null, x: 28.98 }, // 'BE ',
      { key: null, x: 29.946 }, // 'PAI',
      { key: null, x: 30.912 }, // 'D ',
      { key: null, x: 31.586 }, // 'ON:',
      { key: 'pay_date', x: 33.727, combineTillNext: true, buffer: 0.5 }, // '3/',
      // { key: null, x: 33.002 }, // '09/',
      // { key: null, x: 33.864 }, // '2021'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapa' &&
        parts.length === 18 &&
        parts[0].x > 11 &&
        parts[0].text === 'SHI' &&
        parts[1].text === 'PPED ' &&
        parts[2].text === 'BETWEEN:' &&
        parts[6].x === 22.588
      ) {
        return true;
      }
      return false;
    },
  },
  topReportRangeSuyapaNew: {
    type: 'header',
    reportFormat: 'suyapaNew',
    expected: [
      { key: null, x: 9.382 }, // "text":"SHIPPED BETWEEN:",
      { key: 'report_start', x: 13.756 }, // "text":"2023/08/06",
      { key: null, x: 16.719 }, // "text":"TO",
      { key: 'report_end', x: 17.793 }, // "text":"2023/08/12",
      { key: null, x: 20.755 }, // "text":"TO BE PAID ON:",
      { key: 'pay_date', x: 24.658 }, // "text":" 8/14/2023",
    ],
    infoCheckFunc: reportDatesCheck,
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNew' &&
        parts.length === 6 &&
        parts[0].text === 'SHIPPED BETWEEN:' &&
        parts[2].text === 'TO' &&
        parts[4].text === 'TO BE PAID ON:'
      ) {
        return true;
      }
    },
  },
  topReportRangeSuyapaNewFat: {
    type: 'header',
    reportFormat: 'suyapaNewFat',
    expected: [
      { key: null, x: 11.299 }, // 'SHI'
      { key: null, x: 12.302 }, // 'PPED '
      { key: null, x: 13.977 }, // 'BETW'
      { key: null, x: 15.259 }, // 'EEN:'
      { key: 'report_start', x: 16.702, combineTillNext: true, buffer: 0.5 }, // '2024/'
      // { key: null, x: 18.127 }, // '07/'
      // { key: null, x: 18.985 }, // '07'
      { key: null, x: 20.299 }, // 'TO'
      { key: 'report_end', x: 21.499, combineTillNext: true, buffer: 0.5 }, // '2024/'
      // { key: null, x: 22.919 }, // '07/'
      // { key: null, x: 23.783 }, // '13'
      { key: null, x: 25.101 }, // 'TO '
      { key: null, x: 26.098 }, // 'BE '
      { key: null, x: 27.073 }, // 'PAI'
      { key: null, x: 28.043 }, // 'D '
      { key: null, x: 28.709 }, // 'ON:'
      { key: 'pay_date', x: 30.201, combineTillNext: true, buffer: 0.5 }, // '7/'
      // { key: null, x: 30.784 }, // '16/'
      // { key: null, x: 31.643 }, // '2024'
    ],
    infoCheckFunc: reportDatesCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNewFat' &&
        parts.length === 19 &&
        parts[0].text === 'SHI' &&
        parts[1].text === 'PPED ' &&
        parts[2].text === 'BETW' &&
        parts[3].text === 'EEN:'
      ) {
        return true;
      }
    },
  },
  topMfgEditSummary: {
    type: 'header',
    reportFormat: 'normal',
    pageType: 'summary',
    expected: [
      { key: null, x: 4.887 }, //'*** SUMMARY ***'
      // { key: null, x: 4.887 }, //'*** SUMMARY ***'
      { key: null, x: 18.092 }, // "text":"MFG EDIT",
      { key: null, x: 18.688 }, // "text":"MFG EDIT    ",
      { key: null, x: 22.774 }, // HOUSE
      { key: null, x: 24.828 }, // PX
      { key: null, x: 24.95 }, // PX
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length === 7 &&
        parts[0].text === '*** SUMMARY ***' &&
        parts[2].text.trim() === 'MFG EDIT' &&
        (parts[0].x === 4.887 || parts[0].x === 4.888)
      ) {
        return true;
      }
    },
  },
  topMfgEditSummaryFat: {
    type: 'header',
    reportFormat: 'fat',
    pageType: 'summary',
    expected: [
      { key: null, x: 4.887 }, // text: '*** ',
      { key: null, x: 4.887 }, // text: '*** ',
      { key: null, x: 5.76 }, // text: 'SUMMARY ',
      { key: null, x: 5.76 }, // text: 'SUMMARY ',
      { key: null, x: 8.568 }, // text: '***',
      { key: null, x: 8.568 }, // text: '***',
      { key: null, x: 18.651 }, // text: 'MFG ',
      { key: null, x: 18.688 }, // text: 'MFG ',
      { key: null, x: 20.04 }, // text: 'EDI',
      { key: null, x: 20.077 }, // text: 'EDI',
      { key: null, x: 21.021 }, // text: 'T',
      { key: null, x: 21.058 }, // text: 'T ',
      { key: null, x: 23.155 }, // text: 'HOUSE',
      { key: null, x: 24.95 }, // text: 'PX',
      { key: null, x: 25.471 }, // text: 'PX',
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length === 15 &&
        parts[0].text === '*** ' &&
        parts[1].text === '*** ' &&
        parts[2].text === 'SUMMARY ' &&
        parts[3].text === 'SUMMARY ' &&
        parts[6].text === 'MFG '
      ) {
        return true;
      }
      return false;
    },
  },
  topMfgEditSummaryFatBorder: {
    type: 'header',
    reportFormat: 'fatborder',
    pageType: 'summary',
    expected: [
      { key: null, x: 6.237 }, // '*** ',
      { key: null, x: 6.237 }, // '*** ',
      { key: null, x: 7.129 }, // 'SUMMARY ',
      { key: null, x: 7.129 }, // 'SUMMARY ',
      { key: null, x: 10.008 }, // '***',
      { key: null, x: 10.008 }, // '***',
      { key: null, x: 20.001 }, // 'MFG ',
      { key: null, x: 20.037 }, // 'MFG ',
      { key: null, x: 21.424 }, // 'EDI',
      { key: null, x: 21.46 }, // 'EDI',
      { key: null, x: 22.422 }, // 'T',
      { key: null, x: 22.458 }, // 'T ',
      { key: null, x: 24.566 }, // 'HOUSE',
      { key: null, x: 26.3 }, // 'PX',
      { key: null, x: 26.926 }, // 'PX'
    ],
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length === 15 &&
        parts[0].text === '*** ' &&
        parts[1].text === '*** ' &&
        parts[2].text === 'SUMMARY ' &&
        parts[3].text === 'SUMMARY ' &&
        parts[6].text === 'MFG '
      ) {
        return true;
      }
      return false;
    },
  },
  topMfgEditSummarySuyapaNew: {
    type: 'header',
    reportFormat: 'suyapaNew',
    pageType: 'summary',
    expected: [
      { key: null, x: 6.237 }, // '*** SUMMARY ***'
      // { key: null, x: 6.237 }, // '*** SUMMARY ***'
      { key: null, x: 16.227 }, // 'MFG EDIT'
      { key: null, x: 20.037 }, // 'MFG EDIT    '
      { key: null, x: 23.472 }, // 'HOUSE'
      { key: null, x: 25.25 }, // 'PX'
      { key: null, x: 26.3 }, // 'PX'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNew' &&
        parts.length === 7 &&
        parts[0].text === '*** SUMMARY ***' &&
        parts[2].text.trim() === 'MFG EDIT' &&
        parts[0].x === 6.237
      ) {
        return true;
      }
    },
  },
  topMfgEditSummarySuyapaNewFat: {
    type: 'header',
    reportFormat: 'suyapaNewFat',
    pageType: 'summary',
    expected: [
      { key: null, x: 6.199 }, // '*** '
      { key: null, x: 6.237 }, // '*** '
      { key: null, x: 7.086 }, // 'SUMMARY '
      { key: null, x: 7.123 }, // 'SUMMARY '
      { key: null, x: 9.965 }, // '***'
      { key: null, x: 10.003 }, // '***'
      { key: null, x: 20.001 }, // 'MFG '
      { key: null, x: 20.039 }, // 'MFG '
      { key: null, x: 21.424 }, // 'EDI'
      { key: null, x: 21.461 }, // 'EDI'
      { key: null, x: 22.422 }, // 'T'
      { key: null, x: 22.459 }, // 'T '
      { key: null, x: 24.198 }, // 'HOUSE'
      { key: null, x: 26.302 }, // 'PX'
      { key: null, x: 26.339 }, // 'PX'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNewFat' &&
        parts.length === 15 &&
        parts[0].text === '*** ' &&
        parts[1].text === '*** ' &&
        parts[2].text === 'SUMMARY ' &&
        parts[3].text === 'SUMMARY ' &&
        parts[6].text === 'MFG '
      ) {
        return true;
      }
      return false;
    },
  },
  topMfgEdit: {
    type: 'header',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 17.917 }, // "text":"MFG EDIT",
      { key: null, x: 22.25 }, // "text":"MFG EDIT    ",
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts[0].text === 'MFG EDIT' &&
        parts[0].x === 17.917 &&
        parts.length === 2
      ) {
        return true;
      }
      return false;
    },
  },
  topMfgEditThin: {
    type: 'header',
    reportFormat: 'thin',
    expected: [
      { key: null, x: 17 }, // 'MFG EDIT    ',
      { key: null, x: 21.272 }, // 'MFG EDIT'
    ],
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts[0].text === 'MFG EDIT    ' &&
        parts[0].x === 17 &&
        parts.length === 2
      ) {
        return true;
      }
      return false;
    },
  },
  topMfgEditFat: {
    type: 'header',
    reportFormat: 'fat',
    ignoreRowData: true,
    expected: [
      { key: null, x: 22.212 }, // text: 'MFG ',
      { key: null, x: 22.25 }, // text: 'MFG ',
      { key: null, x: 23.65 }, // text: 'EDI',
      { key: null, x: 23.683 }, // text: 'EDI',
      { key: null, x: 24.68 }, // text: 'T',
      { key: null, x: 24.719 }, // text: 'T ',
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts[0].text.trim() === 'MFG' &&
        parts[2].text.trim() === 'EDI' &&
        parts[4].text.trim() === 'T' &&
        (parts.length === 6 || parts.length === 9)
      ) {
        return true;
      }
      return false;
    },
  },
  topMfgEditFatBorder: {
    type: 'header',
    reportFormat: 'fatborder',
    ignoreRowData: true,
    expected: [
      { key: null, x: 22.249 }, // 'MFG ',
      { key: null, x: 22.287 }, // 'MFG ',
      { key: null, x: 23.633 }, // 'EDI',
      { key: null, x: 23.672 }, // 'EDI',
      { key: null, x: 24.625 }, // 'T',
      { key: null, x: 24.664 }, // 'T '
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts[0].text.trim() === 'MFG' &&
        parts[2].text.trim() === 'EDI' &&
        parts[4].text.trim() === 'T' &&
        parts.length === 6
      ) {
        return true;
      }
      return false;
    },
  },
  topMfgEditSuyapa: {
    type: 'header',
    reportFormat: 'suyapa',
    ignoreRowData: true,
    expected: [
      { key: null, x: 22.249 }, // text: 'MFG '
      { key: null, x: 22.286 }, // text: 'MFG '
      { key: null, x: 23.633 }, // text: 'EDI'
      { key: null, x: 23.67 }, // text: 'EDI'
      { key: null, x: 24.625 }, // text: 'T'
      { key: null, x: 24.663 }, // text: 'T '
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapa' &&
        parts[0].text.trim() === 'MFG' &&
        parts[2].text.trim() === 'EDI' &&
        parts[4].text.trim() === 'T' &&
        parts.length === 6
      ) {
        return true;
      }
      return false;
    },
  },
  topMfgEditSuyapaNew: {
    type: 'header',
    reportFormat: 'suyapaNew',
    expected: [
      { key: null, x: 16.265 }, // "text":"MFG EDIT",
      { key: null, x: 22.287 }, // "text":"MFG EDIT    ",
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNew' &&
        parts[0].text === 'MFG EDIT' &&
        parts[0].x === 16.265 &&
        parts.length === 2
      ) {
        return true;
      }
      return false;
    },
  },
  topColumnAdditional: {
    type: 'header',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 24.221 }, // "text":"ADDITIONAL",
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length === 1 &&
        parts[0].text === 'ADDITIONAL' &&
        parts[0].x === 24.221
      ) {
        return true;
      }
      return false;
    },
  },
  topColumnIndent: {
    type: 'header',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 24.221 }, // "text":"ADDITIONAL",
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length === 1 &&
        parts[0].text === 'ADDITIONAL' &&
        parts[0].x === 24.221
      ) {
        return true;
      }
      return false;
    },
  },
  topColumnAdditionalThin: {
    type: 'header',
    reportFormat: 'thin',
    expected: [
      { key: null, x: 28.861, buffer: 1 }, // "text":"ADDITIONAL",
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 1 &&
        parts[0].text === 'ADDITIONAL' &&
        parts[0].x > 27.8 &&
        parts[0].x < 29
      ) {
        return true;
      }
      return false;
    },
  },
  topColumnAdditionalFat: {
    type: 'header',
    reportFormat: 'fat',
    expected: [
      { key: null, x: 30.12 }, // text: 'ADDI',
      { key: null, x: 31.5 }, // text: 'TI',
      { key: null, x: 32.159 }, // text: 'ONAL'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length === 3 &&
        parts[0].text === 'ADDI' &&
        parts[1].text === 'TI' &&
        parts[2].text === 'ONAL' &&
        (parts[0].x === 30.12 || parts[0].x === 30.119)
      ) {
        return true;
      }
      return false;
    },
  },
  topColumnAdditionalSummaryFat: {
    type: 'header',
    reportFormat: 'fat',
    pageType: 'summary',
    ignoreRowData: true,
    expected: [
      { key: null, x: 18.651 }, // text: 'SUB',
      { key: null, x: 22.632 }, // text: 'SUB',
      { key: null, x: 26.613 }, // text: 'SUB',
      { key: null, x: 29.391 }, // text: 'ADDI',
      { key: null, x: 30.726 }, // text: 'TI',
      { key: null, x: 31.325 }, // text: 'ONAL',
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length === 6 &&
        parts[0].text === 'SUB' &&
        parts[1].text === 'SUB' &&
        parts[2].text === 'SUB' &&
        parts[3].text === 'ADDI' &&
        parts[4].text === 'TI' &&
        parts[5].text === 'ONAL'
      ) {
        return true;
      }
    },
  },
  topColumnAdditionalFatBorder: {
    type: 'header',
    reportFormat: 'fatborder',
    expected: [
      { key: null, x: 29.673 }, // 'ADDI',
      { key: null, x: 31.006 }, // 'TI',
      { key: null, x: 31.627 }, // 'ONAL'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length === 3 &&
        parts[0].text === 'ADDI' &&
        parts[1].text === 'TI' &&
        parts[2].text === 'ONAL' &&
        parts[0].x === 29.673
      ) {
        return true;
      }
      return false;
    },
  },
  topColumnAdditionalSuyapa: {
    type: 'header',
    reportFormat: 'suyapa',
    expected: [
      { key: null, x: 29.673 }, // 'ADDI',
      { key: null, x: 31.006 }, // 'TI',
      { key: null, x: 31.627 }, // 'ONAL'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapa' &&
        parts.length === 3 &&
        parts[0].text === 'ADDI' &&
        parts[1].text === 'TI' &&
        parts[2].text === 'ONAL' &&
        parts[0].x === 29.673
      ) {
        return true;
      }
      return false;
    },
  },
  topColumnAdditionalSuyapaNew: {
    type: 'header',
    reportFormat: 'suyapaNew',
    expected: [
      { key: null, x: 21.458 }, // "text":"ADDITIONAL",
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNew' &&
        parts.length === 1 &&
        parts[0].text === 'ADDITIONAL' &&
        parts[0].x === 21.458
      ) {
        return true;
      }
      return false;
    },
  },
  topColumnHeaders: {
    type: 'header',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 0.65 }, // "text":"VND",
      { key: null, x: 1.944 }, // "text":"CT",
      { key: null, x: 2.942 }, // "text":"PS",
      { key: null, x: 3.95 }, // "text":"CLS",
      { key: null, x: 5.244 }, // "text":"STYLE",
      { key: null, x: 9.973 }, // "text":"ORDER#",
      { key: null, x: 12.122 }, // "text":"VCH",
      { key: null, x: 13.416 }, // "text":"#LTR",
      { key: null, x: 15.857 }, // "text":"SIZE",
      { key: null, x: 17.719 }, // "text":"DC",
      { key: null, x: 19.014 }, // "text":"CP",
      { key: null, x: 20.594 }, // "text":"QTY",
      { key: null, x: 24.176 }, // "text":"UNIT",
      { key: null, x: 26.617 }, // "text":"LABOR",
      { key: null, x: 30.481 }, // "text":"CUT",
      { key: null, x: 34.354 }, // "text":"SEW",
      { key: null, x: 37.071 }, // "text":"EXT'D CMT",
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length === 17 &&
        parts[0].text === 'VND' &&
        parts[0].x === 0.65
      ) {
        return true;
      }
    },
  },
  topColumnHeadersThin: {
    type: 'header',
    reportFormat: 'thin',
    expected: [
      { key: null, x: 0.65 }, // 'VND ',
      { key: null, x: 1.685 }, // 'CT ',
      { key: null, x: 2.375 }, // 'PS ',
      { key: null, x: 3.065 }, // 'CLS ',
      { key: null, x: 4.099 }, // 'STYLE           ',
      { key: null, x: 9.274 }, // 'ORDER# ',
      { key: null, x: 11.343 }, // 'VCH ',
      { key: null, x: 12.378 }, // '#LTR    ',
      { key: null, x: 14.793 }, // 'SIZE  ',
      { key: null, x: 16.517 }, // 'DC  ',
      { key: null, x: 17.552 }, // 'CP   ',
      { key: null, x: 18.932 }, // 'QTY         ',
      { key: null, x: 22.726 }, // 'UNIT    ',
      { key: null, x: 25.141 }, // 'LABOR        ',
      { key: null, x: 29.28 }, // 'CUT          ',
      { key: null, x: 33.42 }, // 'SEW      ',
      { key: null, x: 36.179 }, // "EXT'D CMT"
    ],
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 17 &&
        parts[0].text === 'VND ' &&
        parts[0].x === 0.65
      ) {
        return true;
      }
    },
  },
  topColumnHeadersFat: {
    type: 'header',
    reportFormat: 'fat',
    expected: [
      { key: null, x: 0.65 }, // text: 'VND',
      { key: null, x: 2.039 }, // text: 'CT',
      { key: null, x: 3.1 }, // text: 'PS',
      { key: null, x: 4.145 }, // text: 'CLS',
      { key: null, x: 5.48 }, // text: 'STYLE',
      { key: null, x: 11 }, // text: 'ORDER#',
      { key: null, x: 13.469 }, // text: 'VCH',
      { key: null, x: 14.858 }, // text: '#LTR',
      { key: null, x: 17.489 }, // text: 'SI',
      { key: null, x: 18.17 }, // text: 'ZE',
      { key: null, x: 19.54 }, // text: 'DC',
      { key: null, x: 20.97 }, // text: 'CP',
      { key: null, x: 22.758 }, // text: 'QTY',
      { key: null, x: 27.027 }, // text: 'UNI',
      { key: null, x: 28.085 }, // text: 'T',
      { key: null, x: 29.887 }, // text: 'LABOR',
      { key: null, x: 34.407 }, // text: 'CUT',
      { key: null, x: 39.058 }, // text: 'SEW',
      { key: null, x: 42.217 }, // text: "EXT'",
      { key: null, x: 43.516 }, // text: 'D ',
      { key: null, x: 44.228 }, // text: 'CMT'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length === 21 &&
        parts[0].text === 'VND' &&
        parts[0].x === 0.65
      ) {
        return true;
      }
    },
  },
  topColumnHeadersFatBorder: {
    type: 'header',
    reportFormat: 'fatborder',
    expected: [
      { key: null, x: 2 }, // 'VND',
      { key: null, x: 3.331 }, // 'CT',
      { key: null, x: 4.349 }, // 'PS',
      { key: null, x: 5.341 }, // 'CLS',
      { key: null, x: 6.616 }, // 'STYLE',
      { key: null, x: 11.841 }, // 'ORDER#',
      { key: null, x: 14.214 }, // 'VCH',
      { key: null, x: 15.545 }, // '#LTR',
      { key: null, x: 18.041 }, // 'SI',
      { key: null, x: 18.694 }, // 'ZE',
      { key: null, x: 19.993 }, // 'DC',
      { key: null, x: 21.346 }, // 'CP',
      { key: null, x: 23.032 }, // 'QTY',
      { key: null, x: 27.061 }, // 'UNI',
      { key: null, x: 28.075 }, // 'T',
      { key: null, x: 29.765 }, // 'LABOR',
      { key: null, x: 34.057 }, // 'CUT',
      { key: null, x: 38.452 }, // 'SEW',
      { key: null, x: 41.442 }, // "EXT'",
      { key: null, x: 42.691 }, // 'D ',
      { key: null, x: 43.37 }, // 'CMT'
    ],
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length === 21 &&
        parts[0].text === 'VND' &&
        parts[0].x === 2
      ) {
        return true;
      }
    },
  },
  topColumnHeadersSuyapa: {
    type: 'header',
    reportFormat: 'suyapa',
    expected: [
      { key: null, x: 2 }, // text: 'VND'
      { key: null, x: 3.35 }, // text: 'CT'
      { key: null, x: 4.362 }, // text: 'PS'
      { key: null, x: 5.374 }, // text: 'CLS'
      { key: null, x: 6.726 }, // text: 'STYLE'
      { key: null, x: 12.126 }, // text: 'ORDER#'
      { key: null, x: 14.489 }, // text: 'VCH'
      { key: null, x: 15.835999999999999 }, // text: '#LTR'
      { key: null, x: 18.538 }, // text: 'SI'
      { key: null, x: 19.191 }, // text: 'ZE'
      { key: null, x: 20.563 }, // text: 'DC'
      { key: null, x: 21.914 }, // text: 'CP'
      { key: null, x: 23.6 }, // text: 'QTY'
      { key: null, x: 27.649 }, // text: 'UNI'
      { key: null, x: 28.663 }, // text: 'T'
      { key: null, x: 30.351 }, // text: 'LABOR'
      { key: null, x: 34.739 }, // text: 'CUT'
      { key: null, x: 39.123 }, // text: 'SEW'
      { key: null, x: 42.164 }, // text: "EXT'"
      { key: null, x: 43.413 }, // text: 'D '
      { key: null, x: 44.092 }, // text: 'CMT'
    ],
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapa' &&
        parts.length === 21 &&
        parts[0].text === 'VND' &&
        parts[0].x === 2
      ) {
        return true;
      }
    },
  },
  topColumnHeadersSuyapaNew: {
    type: 'header',
    reportFormat: 'suyapaNew',
    expected: [
      { key: null, x: 2 }, // 'VND'
      { key: null, x: 3.147 }, // 'CT'
      { key: null, x: 4.058 }, // 'PS'
      { key: null, x: 4.969 }, // 'CLS'
      { key: null, x: 6.117 }, // 'STYLE'
      { key: null, x: 10.096 }, // 'ORDER#'
      { key: null, x: 11.952 }, // 'VCH'
      { key: null, x: 13.099 }, // '#LTR'
      { key: null, x: 15.19 }, // 'SIZE'
      { key: null, x: 16.81 }, // 'DC'
      { key: null, x: 17.957 }, // 'CP'
      { key: null, x: 19.34 }, // 'QTY'
      { key: null, x: 22.376 }, // 'UNIT'
      { key: null, x: 24.467 }, // 'LABOR'
      { key: null, x: 27.739 }, // 'CUT'
      { key: null, x: 31.01 }, // 'SEW'
      { key: null, x: 33.338 }, // "EXT'D CMT"
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapaNew' &&
        parts.length === 17 &&
        parts[0].text === 'VND' &&
        parts[0].x === 2
      ) {
        return true;
      }
    },
  },
  finalTotal: {
    type: 'finalTotal',
    reportFormat: 'normal',
    ignoreRowData: true,
    expected: [
      { key: null, x: 1.048 }, // FINAL TOTALS >>>>>>>>>>>>>
      { key: null, x: 9.219 },
      { key: null, x: 11.112 },
      { key: null, x: 13.835 },
      { key: null, x: 14.345 },
      { key: null, x: 21.356 },
      { key: null, x: 21.657 },
      { key: null, x: 23.33 },
      { key: null, x: 26.563 },
      { key: null, x: 31.215 },
      { key: null, x: 33.027 },
      { key: null, x: 35.584 },
      { key: null, x: 37.662 },
      { key: null, x: 40.626 },
      { key: null, x: 42.335 },
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (format === 'normal' && parts[0].text.includes('FINAL TOTALS')) {
        return true;
      }
    },
  },
  finalTotalThin: {
    type: 'finalTotal',
    reportFormat: 'thin',
    ignoreRowData: true,
    expected: [
      { key: null, x: 0.958 }, // 'FINAL TOTALS >>>>>>>>>>>>>' ,
      { key: null, x: 0.958 }, // 'FINAL TOTALS >>>>>>>>>>>>>  ' ,
      { key: null, x: 8.57 }, // ' 1,941   ' ,
      { key: null, x: 10.271 }, // ' 1,941' ,
      { key: null, x: 11.015 }, // 'ACCEPTED:                ' ,
      { key: null, x: 11.33 }, // 'ACCEPTED:' ,
      { key: null, x: 17.818 }, // ' 14782                     ' ,
      { key: null, x: 19.294 }, // ' 14782' ,
      { key: null, x: 20.263 }, // '         7,713.94              ' ,
      { key: null, x: 25.152 }, // '    9,251.71              ' ,
      { key: null, x: 25.411 }, // '         7,713.94' ,
      { key: null, x: 26.786 }, // '    9,251.71' ,
      { key: null, x: 28.655 }, // '   67,170.14 ' ,
      { key: null, x: 30.611 }, // '   67,170.14' ,
      { key: null, x: 32.188 }, // '   84,106.23 ' ,
      { key: null, x: 33.776 }, // '   84,106.23'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (format === 'thin' && parts[0].text.includes('FINAL TOTALS')) {
        return true;
      }
    },
  },
  finalTotalFatBorder: {
    type: 'finalTotal',
    reportFormat: 'fatborder',
    ignoreRowData: true,
    expected: [
      { key: null, x: 2.375 }, // 'FI',
      { key: null, x: 2.375 }, // 'FI',
      { key: null, x: 2.996 }, // 'NAL ',
      { key: null, x: 2.996 }, // 'NAL ',
      { key: null, x: 4.251 }, // 'TOTALS ',
      { key: null, x: 4.251 }, // 'TOTALS ',
      { key: null, x: 6.419 }, // '>>>>>>>>>>>>>',
      { key: null, x: 6.419 }, // '>>>>>>>>>>>>>',
      // ...
      { key: null, x: 14.635 }, // 'ACCEPTED:',
      // ...
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length > 8 &&
        parts[0].text === 'FI' &&
        parts[2].text === 'NAL ' &&
        parts[4].text === 'TOTALS ' &&
        parts[6].text === '>>>>>>>>>>>>>' &&
        parts.some((part) => part.text.trim() === 'ACCEPTED:')
      ) {
        return true;
      }
    },
  },
  finalTotalSuyapa: {
    type: 'finalTotal',
    reportFormat: 'suyapa',
    ignoreRowData: true,
    expected: [
      { key: null, x: 2.375 }, // text: 'FI'
      { key: null, x: 2.375 }, // text: 'FI'
      { key: null, x: 2.996 }, // text: 'NAL '
      { key: null, x: 2.996 }, // text: 'NAL '
      { key: null, x: 4.251 }, // text: 'TOTALS '
      { key: null, x: 4.251 }, // text: 'TOTALS '
      { key: null, x: 6.419 }, // text: '>>>>>>>>>>>>>'
      { key: null, x: 6.419 }, // text: '>>>>>>>>>>>>>'
      // { key: null, x: 12.163 }, // text: '3,'
      // { key: null, x: 12.163 }, // text: '3,'
      // { key: null, x: 12.764 }, // text: '865'
      // { key: null, x: 12.764 }, // text: '865 '
      { key: null, x: 14.861 }, // text: 'ACCEPTED:'
      { key: null, x: 14.861 }, // text: 'ACCEPTED:'
      // { key: null, x: 23.637 }, // text: '39941'
      // { key: null, x: 23.637 }, // text: '39941'
      // { key: null, x: 29 }, // text: '38,'
      // { key: null, x: 29.038 }, // text: '38,'
      // { key: null, x: 29.863 }, // text: '489.'
      // { key: null, x: 29.9 }, // text: '489.'
      // { key: null, x: 30.987 }, // text: '63'
      // { key: null, x: 31.025 }, // text: '63 '
      // { key: null, x: 33.426 }, // text: '34,'
      // { key: null, x: 33.426 }, // text: '34,'
      // { key: null, x: 34.288 }, // text: '622.'
      // { key: null, x: 34.288 }, // text: '622.'
      // { key: null, x: 35.413 }, // text: '83'
      // { key: null, x: 35.413 }, // text: '83 '
      // { key: null, x: 37.437 }, // text: '247,'
      // { key: null, x: 37.475 }, // text: '247,'
      // { key: null, x: 38.562 }, // text: '686.'
      // { key: null, x: 38.599 }, // text: '686.'
      // { key: null, x: 39.686 }, // text: '66'
      // { key: null, x: 39.723 }, // text: '66 '
      // { key: null, x: 41.825 }, // text: '320,'
      // { key: null, x: 41.863 }, // text: '320,'
      // { key: null, x: 42.95 }, // text: '796.'
      // { key: null, x: 42.987 }, // text: '796.'
      // { key: null, x: 44.069 }, // text: '96'
      // { key: null, x: 44.107 }, // text: '96 '
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'suyapa' &&
        parts.length > 8 &&
        parts[0].text === 'FI' &&
        parts[2].text === 'NAL ' &&
        parts[4].text === 'TOTALS ' &&
        parts[6].text === '>>>>>>>>>>>>>' &&
        parts.some((part) => part.text.trim() === 'ACCEPTED:')
      ) {
        return true;
      }
    },
  },
  finalTotalSuyapaNew: {
    type: 'finalTotal',
    reportFormat: 'suyapaNew',
    ignoreRowData: true,
    expected: [
      { key: null, x: 2.375 }, // 'FINAL TOTALS >>>>>>>>>>>>>'
      { key: null, x: 2.375 }, // 'FINAL TOTALS >>>>>>>>>>>>>'
      { key: null, x: 9.187 }, // ' 3,547'
      { key: null, x: 11.825 }, // ' 3,547 '
      { key: null, x: 14.152 }, // 'ACCEPTED:'
      { key: null, x: 14.862 }, // 'ACCEPTED:'
      { key: null, x: 19.097 }, // '        26,709.99'
      { key: null, x: 20.967 }, // ' 41470'
      { key: null, x: 23.3 }, // ' 41470'
      { key: null, x: 26.338 }, // '        26,709.99 '
      { key: null, x: 29.876 }, // '   35,622.76'
      { key: null, x: 32.412 }, // '   35,622.76 '
      { key: null, x: 33.858 }, // '  244,340.48'
      { key: null, x: 36.762 }, // '  244,340.48 '
      { key: null, x: 38.753 }, // '  306,671.67'
      { key: null, x: 41.15 }, // '  306,671.67 '
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (format === 'suyapaNew' && parts[0].text.includes('FINAL TOTALS')) {
        return true;
      }
    },
  },
  finalTotalAcceptedFat: {
    type: 'finalTotal',
    reportFormat: 'fat',
    ignoreRowData: true,
    expected: [
      { key: null, x: 1.048 }, // text: 'FI',
      { key: null, x: 1.048 }, // text: 'FI',
      { key: null, x: 1.706 }, // text: 'NAL ',
      { key: null, x: 1.706 }, // text: 'NAL ',
      { key: null, x: 3.006 }, // text: 'TOTALS ',
      { key: null, x: 3.006 }, // text: 'TOTALS ',
      { key: null, x: 5.246 }, // text: '>>>>>>>>>>>>>',
      { key: null, x: 5.246 }, // text: '>>>>>>>>>>>>>',
      {
        key: null,
        x: 10.067,
        combineTillNext: true,
        min: 9.5,
        max: 12.5,
        ignoreRepeat: true,
      }, // text: '3,',
      // { key: null, x: 10.7 }, // text: '824',
      // { key: null, x: 11.466 }, // text: '3,',
      // { key: null, x: 12.094 }, // text: '824 ',
      { key: null, x: 13.981 }, // text: 'ACCEPTED:',
      { key: null, x: 14.345 }, // text: 'ACCEPTED:',
      {
        key: null,
        x: 23.454,
        combineTillNext: true,
        min: 23,
        max: 26,
        ignoreRepeat: true,
      }, // text: '41604',
      // { key: null, x: 23.688 }, // text: '41604',
      {
        key: null,
        x: 29.398,
        combineTillNext: true,
        min: 28,
        max: 31.8,
        ignoreRepeat: true,
      }, // text: '15,',
      // { key: null, x: 29.434 }, // text: '15,',
      // { key: null, x: 30.295 }, // text: '511.',
      // { key: null, x: 30.331 }, // text: '511.',
      // { key: null, x: 31.461 }, // text: '10',
      // { key: null, x: 31.502 }, // text: '10 ',
      {
        key: null,
        x: 33.749,
        combineTillNext: true,
        min: 33.0,
        max: 36.4,
        ignoreRepeat: true,
      }, // text: '22,',
      // { key: null, x: 34.107 }, // text: '22,',
      // { key: null, x: 34.65 }, // text: '866.',
      // { key: null, x: 35.004 }, // text: '866.',
      // { key: null, x: 35.817 }, // text: '55',
      // { key: null, x: 36.17 }, // text: '55 ',
      {
        key: null,
        x: 37.789,
        combineTillNext: true,
        min: 37.0,
        max: 41,
        ignoreRepeat: true,
      }, // text: '183,',
      // { key: null, x: 38.384 }, // text: '183,',
      // { key: null, x: 38.955 }, // text: '904.',
      // { key: null, x: 39.55 }, // text: '904.',
      // { key: null, x: 40.121 }, // text: '87',
      // { key: null, x: 40.716 }, // text: '87 ',
      {
        key: null,
        x: 42.457,
        combineTillNext: true,
        min: 42,
        max: 46,
        ignoreRepeat: true,
      }, // text: '222,',
      // { key: null, x: 43.057 }, // text: '222,',
      // { key: null, x: 43.628 }, // text: '202.',
      // { key: null, x: 44.223 }, // text: '202.',
      // { key: null, x: 44.79 }, // text: '80',
      // { key: null, x: 45.384 }, // text: '80 '
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length > 8 &&
        parts[0].text === 'FI' &&
        parts[2].text === 'NAL ' &&
        parts[4].text === 'TOTALS ' &&
        parts[6].text === '>>>>>>>>>>>>>' &&
        parts.some((part) => part.text.trim() === 'ACCEPTED:')
      ) {
        return true;
      }
    },
  },
  finalTotalRejected: {
    type: 'finalTotal',
    reportFormat: 'fat',
    pageType: 'rejected',
    ignoreRowData: true,
    expected: [
      { key: null, x: 1.01 }, // text: ' FI',
      { key: null, x: 1.048 }, // text: 'FI',
      { key: null, x: 1.706 }, // text: 'NAL ',
      { key: null, x: 1.803 }, // text: 'NAL ',
      { key: null, x: 3.006 }, // text: 'TOTALS>>>>>>>>>',
      { key: null, x: 3.103 }, // text: 'TOTALS>>>>>>>>>',
      { key: null, x: 12.508 }, // text: '62 ',
      { key: null, x: 12.544 }, // text: '62',
      { key: null, x: 14.127 }, // text: ' REJECTED:',
      { key: null, x: 14.345 }, // text: 'REJECTED:',
      { key: null, x: 23.294 }, // text: '1,',
      { key: null, x: 23.328 }, // text: '1,',
      { key: null, x: 23.922 }, // text: '165 ',
      { key: null, x: 23.956 }, // text: '165',
      { key: null, x: 31.556 }, // text: '.',
      { key: null, x: 31.594 }, // text: '.',
      { key: null, x: 31.918999999999997 }, // text: '00',
      { key: null, x: 31.957 }, // text: '00 ',
      { key: null, x: 36.23 }, // text: '.',
      { key: null, x: 36.267 }, // text: '.',
      { key: null, x: 36.588 }, // text: '00 ',
      { key: null, x: 36.626 }, // text: '00',
      { key: null, x: 40.903 }, // text: '.',
      { key: null, x: 40.94 }, // text: '.',
      { key: null, x: 41.261 }, // text: '00',
      { key: null, x: 41.298 }, // text: '00 ',
      { key: null, x: 45.575 }, // text: '.',
      { key: null, x: 45.613 }, // text: '.',
      { key: null, x: 45.928 }, // text: '00 ',
      { key: null, x: 45.967 }, // text: '00',
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length > 8 &&
        parts[0].text.trim() === 'FI' &&
        parts[2].text.trim() === 'NAL' &&
        parts[4].text.trim() === 'TOTALS>>>>>>>>>' &&
        parts.some((part) => part.text.trim() === 'REJECTED:')
      ) {
        return true;
      }
    },
  },
};
