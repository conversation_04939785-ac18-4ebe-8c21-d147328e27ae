import { Router } from 'express';

import {
  getActiveLaserMachinesByArea,
  getActiveMachineLinesByArea,
  getActiveMachinesByArea,
  getMachineByBarcode,
  getMachineStatusLog,
  getMachineStatuses,
  getMachinesByArea,
  updateMachineStatus,
} from '@app/controllers/machines.controller';

const machinesRouter = Router();

machinesRouter.route('/getMachinesByArea').post(getMachinesByArea);
machinesRouter.route('/getMachineStatusLog').post(getMachineStatusLog);
machinesRouter.route('/updateMachineStatus').post(updateMachineStatus);
machinesRouter.route('/getMachineStatus').get(getMachineStatuses);
machinesRouter.route('/area/:areaID').get(getActiveMachinesByArea);
machinesRouter.route('/area/lines/:areaID').get(getActiveMachineLinesByArea);
machinesRouter.route('/laser').get(getActiveLaserMachinesByArea);

machinesRouter.route('/barcode/:barcode').get(getMachineByBarcode);

export { machinesRouter };
