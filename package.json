{"name": "server-varpro", "version": "1.23.5", "private": "true", "description": "Servidor VARPRO, node.js, express, mysql2, knex.js, objection.js", "license": "ISC", "author": "VARPRO IT", "scripts": {"build": "npm run clean && tsc && tsc-alias && npm run copy-files", "clean": "rimraf ./dist", "cm": "git-cz", "copy-files": "copyfiles -u 1 src/**/*.html dist", "dev": "ts-node-dev -r tsconfig-paths/register src/index.ts", "format": "prettier --write \"**/*.+(ts)\"", "headtest": "ts-node-dev -r tsconfig-paths/register src/services/headtest.ts", "knex:down": "npx knex --knexfile src/db/knexfile.ts migrate:down", "knex:latest": "npx knex --knexfile src/db/knexfile.ts migrate:latest", "knex:make": "npx knex --knexfile src/db/knexfile.ts migrate:make -x ts", "knex:make:column": "npx knex --knexfile src/db/knexfile.ts migrate:make -x ts --stub stubs/add-column.stub.ts", "knex:make:table": "npx knex --knexfile src/db/knexfile.ts migrate:make -x ts --stub stubs/create-table.stub.ts", "knex:rollback": "npx knex --knexfile src/db/knexfile.ts migrate:rollback", "knex:unlock": "npx knex --knexfile src/db/knexfile.ts migrate:unlock ", "knex:up": "npx knex --knexfile src/db/knexfile.ts migrate:up", "lint": "eslint -c .eslintrc.js --ext .ts .", "lint:fix": "npm run lint -- --fix", "lint:staged": "lint-staged", "test": "jest --config ./jest.config.ts", "test:watch": "npm run test -- --watch", "version": "auto-changelog -p && git add CHANGELOG.md", "version-debug": "auto-changelog --template changelog-template.hbs -p --template json --output changelog-data.json"}, "keywords": ["Nodej<PERSON>", "TypeScript", "Express"], "dependencies": {"axios": "1.2.2", "bcryptjs": "2.4.3", "bwip-js": "^4.3.1", "cors": "2.8.5", "currency.js": "^2.0.4", "dayjs": "1.11.7", "dotenv": "16.0.3", "excel4node": "1.8.0", "express": "4.18.2", "formidable": "^2.1.2", "handlebars": "4.7.7", "helmet": "6.0.1", "http-status-codes": "2.3.0", "jsonwebtoken": "9.0.2", "knex": "2.4.0", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "mysql": "2.18.1", "nodemailer": "6.9.0", "objection": "3.0.1", "pdfreader": "^2.0.0", "qrcode": "^1.5.3", "serve-favicon": "2.5.0", "ssh2-sftp-client": "^10.0.3", "uuid": "9.0.0", "winston": "3.11.0", "xlsx": "^0.18.5", "zod": "3.24.1"}, "devDependencies": {"@commitlint/cli": "17.4.2", "@commitlint/config-conventional": "17.4.2", "@trivago/prettier-plugin-sort-imports": "4.0.0", "@types/bcryptjs": "2.4.6", "@types/cors": "2.8.13", "@types/express": "4.17.15", "@types/formidable": "^2.0.6", "@types/jest": "^29.5.2", "@types/jsonwebtoken": "9.0.5", "@types/morgan": "1.9.4", "@types/mysql": "2.15.21", "@types/node": "18.11.18", "@types/nodemailer": "6.4.7", "@types/qrcode": "^1.5.0", "@types/serve-favicon": "2.5.3", "@types/uuid": "9.0.0", "@typescript-eslint/eslint-plugin": "5.48.1", "@typescript-eslint/parser": "5.48.1", "auto-changelog": "2.4.0", "commitizen": "4.2.6", "copyfiles": "^2.4.1", "cz-conventional-changelog": "3.3.0", "eslint": "8.31.0", "eslint-config-prettier": "8.6.0", "eslint-import-resolver-typescript": "3.5.3", "eslint-plugin-import": "2.27.4", "eslint-plugin-no-relative-import-paths": "1.5.2", "jest": "^29.5.0", "lint-staged": "^15.2.2", "morgan": "1.10.0", "prettier": "2.8.2", "prettier-plugin-package": "1.3.0", "rimraf": "5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-node-dev": "2.0.0", "tsc-alias": "1.8.2", "tsconfig-paths": "4.1.2", "typescript": "4.9.4"}}