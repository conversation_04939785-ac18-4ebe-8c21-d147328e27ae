# Servidor Node.js, express, mysql2, knex.js typescript.

Servidor creado con **Node.js**.

## Comandos para inciar

- Comandos de desarrollo

```
- npm i(reconstruir node_modules)
- npm run build(compilar typescript a javascript ES6)
- npm run dev(ejecutar servidor typescript)
- npm run devjs(ejecutar servidor javascript)
```

## Configuraciones extra.

### Configuración typescript.

- Creando el archivo de configuración typescript:

```
tsc --init
```

test

- Modificando reglas de typescript:

```
target: es6,
outDir: dist
```

> **target** -> versión de javascript a compilar.

> **outDir** -> directorio donde se va a compilar el código javascript.

- Compilar typescript a javascript:

```
- tsc
```

> Solo va a funcionar si tenemos instalado typescript en su version 2.^ y tenemos configurado el tsconfig.ts.

## Dependencias.

### producción

- expressjs
- mysql2
- knex

### desarrollo

- typescript
- ts-node
- morgan
- nodemon
- @types/express
- @types/mysql2
- @types/morgan

## Husky

example
feat: some commit message

commitlint message types

- build
- chore
- ci
- docs
- feat
- fix
- perf
- refactor
- revert
- style
- test

# Knex

## Create Migration

### migration file naming examples

create_table-some_table
create_column-some_column-some_table
update_column-some_column-some_table
rename_column-old_column_name-new_column_name-some_table
rename_table-old_table_name-new_table_name
update-some_query_name

### Basic

```
npm run knex:make some_migration_name
```

### Table template

```
npm run knex:make:table some_table_migration_name
```

### Column template

```
npm run knex:make:column column-some_column-some_table
```

### run migrations

```
npm run knex:latest
```

# Varsity CPR invoices

Folder with old saved CPR and lettering files.

`\\*************\Shares\accounting\AR Customers Invoices 2024\VSF 2024`
