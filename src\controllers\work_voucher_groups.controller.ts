import dayjs from 'dayjs';
import type { Request, Response } from 'express';
import { fn, raw } from 'objection';
import { join } from 'path';
// import { WorkVouchers } from "@app/interface/tickets.interfaces";
import { v4 as uuidv4 } from 'uuid';

import { WorkAreaBatches } from '@app/models/tickets.schema';

const { ref } = require('objection');
const { Employee } = require('../models/employee.schema');

const {
  WorkAreaTickets,
  WorkActivityLog,
  WorkVouchers,
  WorkVoucherGroups,
  WorkAreaTicketStatuses,
} = require('../models/tickets.schema');

// crear un grupo de voucher
export async function createNewVoucherGroup(req: Request, res: Response) {
  const { name, codeEmployee, area } = req.body;

  try {
    const searchNameGroup = await WorkVoucherGroups.query()
      .where('name', name)
      .where('work_area_id_ref', area)
      .where('removed_at', null);

    if (searchNameGroup.length > 0) {
      return res
        .status(200)
        .json({ ok: false, data: 'Ya existe un grupo con ese nombre' });
    }

    const newVoucherGroup = await WorkVoucherGroups.query().insert({
      name,
      employee_id_ref: codeEmployee,
      work_area_id_ref: area,
    });

    if (newVoucherGroup) {
      const voucherGroupLog = await WorkActivityLog.query().insert({
        work_area_id: area,
        employee_id: codeEmployee,
        module_name: 'voucherGroup',
        module_id: newVoucherGroup.id,
        activity: 'VoucherGroupCreated',
        data: JSON.stringify({}),
      });
      if (voucherGroupLog) {
        const addBarcodeToVoucherGroup = await WorkVoucherGroups.query()
          .update({
            barcode: `MEGV${newVoucherGroup.id}`,
          })
          .where('id', newVoucherGroup.id);
        if (addBarcodeToVoucherGroup) {
          const getVoucherGroup = await WorkVoucherGroups.query().where(
            'work_voucher_groups.id',
            newVoucherGroup.id
          );
          if (getVoucherGroup) {
            return res.status(200).json({
              ok: true,
              data: getVoucherGroup,
            });
          } else {
            return res.status(204).json({
              ok: false,
              message: 'No se encontro el grupo de voucher',
            });
          }
        } else {
          return res.status(500).json({
            ok: false,
            message: 'No se agrego el codigo de barras al grupo de vocuher',
          });
        }
      } else {
        return res.status(500).json({
          ok: false,
          message: 'No se agrego el log de actividad',
        });
      }
    } else {
      return res
        .status(500)
        .json({ ok: false, message: 'No se creo el grupo de vocuher' });
    }
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

// obtener los grupos de voucher
export async function getVoucherGroups(req: Request, res: Response) {
  try {
    const { area } = req.body;

    const voucherGroups = await WorkVoucherGroups.query()
      .where('work_voucher_groups.work_area_id_ref', area)
      .whereNull('work_voucher_groups.removed_at')
      .select([
        'work_voucher_groups.id',
        'work_voucher_groups.name',
        'work_voucher_groups.barcode',
        fn
          .coalesce(
            WorkVouchers.query()
              .where(
                'work_vouchers.work_voucher_group_id',
                ref('work_voucher_groups.id')
              )
              .groupBy('work_vouchers.work_voucher_group_id')
              .count('work_vouchers.id'),
            0
          )
          .as('vouchersByGroup'),
      ]);

    return res.status(200).json({
      ok: true,
      data: voucherGroups,
    });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

// modificar el nombre del grupo de voucher
export async function updateVoucherGroup(req: Request, res: Response) {
  const { id, newName, oldName, codeEmployee } = req.body;
  try {
    const updateVoucherGroup = await WorkVoucherGroups.query()
      .update({
        name: newName,
      })
      .where('work_voucher_groups.id', id);

    if (updateVoucherGroup) {
      const voucherGroupLog = await WorkActivityLog.query().insert({
        employee_id: codeEmployee,
        module_name: 'voucherGroup',
        work_area_group_id: id,
        module_id: id,
        activity: 'VoucherGroupUpdated',
        data: JSON.stringify({
          old_name: oldName,
          new_name: newName,
        }),
      });
      if (voucherGroupLog) {
        return res.status(200).json({
          ok: true,
          message: 'Se actualizo el nombre del grupo de voucher',
        });
      } else {
        return res.status(500).json({
          ok: false,
          message: 'No se pudo registrar el log',
        });
      }
    } else {
      return res.status(500).json({
        ok: false,
        message: 'No se actualizo el grupo de voucher',
      });
    }
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

// agregar vouchers a un grupo de voucher
export async function addVouchersToGroup(req: Request, res: Response) {
  const { vouchers, groupId, codeEmployee, area } = req.body;

  try {
    const vouchersError = [];
    const voucherSuccess = [];

    const getNameGroup = await WorkVoucherGroups.query()
      .where('id', groupId)
      .select({ groupName: 'name' });

    for (let i = 0; i < vouchers.length; i++) {
      const searchVoucherInfo = await WorkVouchers.query()
        .where('work_vouchers.id', vouchers[i])
        .whereNull('work_vouchers.work_voucher_group_id');

      if (searchVoucherInfo.length > 0) {
        const searchLastOrderNumber = await WorkVouchers.query()
          .where('work_vouchers.work_voucher_group_id', groupId)
          .select('work_voucher_group_sort')
          .orderBy('work_voucher_group_sort', 'DESC')
          .limit(1);

        const updateVoucher = await WorkVouchers.query()
          .update({
            work_voucher_group_id: groupId,
            work_voucher_group_sort:
              +searchLastOrderNumber[0]?.work_voucher_group_sort + 1 || 1,
          })
          .where('work_vouchers.id', vouchers[i]);

        // obtener la informacion del voucher recien agregado
        const getVouchers = await WorkVouchers.query()
          .join(
            'work_area_tickets',
            'work_vouchers.id',
            'work_area_tickets.work_voucher_id'
          )
          .join(
            'work_areas',
            'work_area_tickets.work_area_id',
            'work_areas.work_area_id'
          )
          .join(
            'work_voucher_types',
            'work_vouchers.work_voucher_type_id',
            'work_voucher_types.id'
          )
          .join(
            'work_area_ticket_statuses',
            'work_area_tickets.work_area_ticket_status_id ',
            'work_area_ticket_statuses.id'
          )
          .join('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
          .join(
            'work_voucher_groups',
            'work_vouchers.work_voucher_group_id',
            'work_voucher_groups.id'
          )
          .select([
            { voucherId: 'work_vouchers.id' },
            { isPrimary: 'work_vouchers.is_primary' },
            { sort: 'work_vouchers.work_voucher_group_sort' },
            { ticketId: 'work_area_tickets.id' },
            { creado: 'work_area_tickets.created_at' },
            { areaName: 'work_areas.area_name' },
            { voucherType: 'work_voucher_types.name' },
            { nameGroup: 'work_voucher_groups.name' },
            { ticketStatus: 'work_area_ticket_statuses.name' },
            'mo_numbers.mo_id',
            'mo_numbers.mo_status',
            'mo_numbers.material_date',
            'mo_numbers.mo_order',
            'mo_numbers.required_date',
            'mo_numbers.ItemDescription8',
            'mo_numbers.num',
            'mo_numbers.po_numbers',
            'mo_numbers.style',
            'mo_numbers.quantity',
            'mo_numbers.customer',
          ])
          .where('work_vouchers.work_voucher_group_id', groupId)
          .where('work_vouchers.id', vouchers[i])
          .orderBy('work_vouchers.work_voucher_group_sort', 'ASC');

        if (updateVoucher) {
          const voucherLog = await WorkActivityLog.query().insert({
            work_voucher_id: vouchers[i],
            work_area_id: area,
            employee_id: codeEmployee,
            module_name: 'voucherGroup',
            module_id: groupId,
            activity: 'AddedVoucherToGroup',
            data: JSON.stringify({ value: vouchers[i] }),
          });
          if (voucherLog) {
            voucherSuccess.push(getVouchers[0]);
          }
        } else {
          vouchersError.push(vouchers[i]);
        }
      } else {
        const searchLastOrderNumber = await WorkVouchers.query()
          .where('work_vouchers.work_voucher_group_id', groupId)
          .select('work_voucher_group_sort')
          .orderBy('work_voucher_group_sort', 'DESC')
          .limit(1);

        // cambio de grupo
        const searchVoucherInfo = await WorkVouchers.query()
          .join(
            'work_voucher_groups',
            'work_vouchers.work_voucher_group_id',
            'work_voucher_groups.id'
          )
          .where('work_vouchers.id', vouchers[i])
          .whereNotNull('work_vouchers.work_voucher_group_id')
          .select([{ nameOldGroup: 'work_voucher_groups.name' }]);

        if (searchVoucherInfo.length > 0) {
          const updateVoucher = await WorkVouchers.query()
            .update({
              work_voucher_group_id: groupId,
              work_voucher_group_sort:
                +searchLastOrderNumber[0]?.work_voucher_group_sort + 1 || 1,
            })
            .where('work_vouchers.id', vouchers[i]);

          if (updateVoucher) {
            const voucherLog = await WorkActivityLog.query().insert({
              work_voucher_id: vouchers[i],
              work_area_id: area,
              employee_id: codeEmployee,
              module_name: 'voucherGroup',
              module_id: groupId,
              activity: 'MovedVoucherToGroup',
              data: JSON.stringify({
                oldGroup: searchVoucherInfo[0].nameOldGroup,
                newGroup: getNameGroup[0].groupName,
              }),
            });
            if (voucherLog) {
              voucherSuccess.push(vouchers[i]);
            }
          } else {
            vouchersError.push(vouchers[i]);
          }
        }
      }
    }
    return res.status(200).json({
      ok: true,
      vouchersError,
      voucherSuccess,
    });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function getLogOfVoucherGroups(req: Request, res: Response) {
  try {
    const { id } = req.body;

    const getLogs = await WorkActivityLog.query()
      .select(
        'work_activity_log.id',
        'work_activity_log.activity',
        { moduleId: 'work_activity_log.module_id' },
        'work_activity_log.data',
        'work_activity_log.created_at',
        Employee.query()
          .where('employees.employee_id', ref('work_activity_log.employee_id'))
          .select('employees.first_name')
          .as('userName')
      )
      .where('work_activity_log.module_name', 'voucherGroup')
      .where('work_activity_log.module_id', id)
      .distinct('work_activity_log.activity')
      .orderBy('created_at', 'desc');
    return res.status(200).json({
      ok: true,
      data: getLogs,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

// obtenemos todos los vouchers de un grupo de voucher
export async function getVouchersOfGroup(req: Request, res: Response) {
  try {
    const { id, area } = req.body;

    const getVouchers = await WorkVouchers.query()
      .join(
        'work_area_tickets',
        'work_vouchers.id',
        'work_area_tickets.work_voucher_id'
      )
      .join(
        'work_areas',
        'work_area_tickets.work_area_id',
        'work_areas.work_area_id'
      )
      .join(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .join(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id ',
        'work_area_ticket_statuses.id'
      )
      .join('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
      .join(
        'work_voucher_groups',
        'work_vouchers.work_voucher_group_id',
        'work_voucher_groups.id'
      )
      .select([
        { voucherId: 'work_vouchers.id' },
        { isPrimary: 'work_vouchers.is_primary' },
        { sort: 'work_vouchers.work_voucher_group_sort' },
        { ticketId: 'work_area_tickets.id' },
        { creado: 'work_area_tickets.created_at' },
        { areaName: 'work_areas.area_name' },
        { voucherType: 'work_voucher_types.name' },
        { nameGroup: 'work_voucher_groups.name' },
        { ticketStatus: 'work_area_ticket_statuses.name' },
        'mo_numbers.mo_id',
        'mo_numbers.mo_status',
        'mo_numbers.material_date',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        'mo_numbers.ItemDescription8',
        'mo_numbers.num',
        'mo_numbers.po_numbers',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.customer',
      ])
      .where('work_vouchers.work_voucher_group_id', id)
      .where('work_area_tickets.work_area_id', area)
      .orderBy('work_vouchers.work_voucher_group_sort', 'ASC');

    return res.status(200).json({
      ok: true,
      data: getVouchers,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

// eliminar vouchers de un grupo de vouchers
export async function deleteVouchersToGroup(req: Request, res: Response) {
  const { vouchers, groupId, codeEmployee, area } = req.body;
  try {
    const vouchersError = [];
    const voucherDeleted = [];

    for (let i = 0; i < vouchers.length; i++) {
      const searchVoucherInfo = await WorkVouchers.query()
        .where('work_vouchers.id', vouchers[i])
        .where('work_vouchers.work_voucher_group_id', groupId);
      if (searchVoucherInfo.length > 0) {
        const updateVoucher = await WorkVouchers.query()
          .update({
            work_voucher_group_id: null,
            work_voucher_group_sort: null,
          })
          .where('work_vouchers.id', vouchers[i]);
        if (updateVoucher) {
          const voucherLog = await WorkActivityLog.query().insert({
            work_voucher_id: vouchers[i],
            work_area_id: area,
            employee_id: codeEmployee,
            module_name: 'voucherGroup',
            module_id: groupId,
            activity: 'DeletedVoucherToGroup',
            data: JSON.stringify({ value: vouchers[i] }),
          });
          if (voucherLog) {
            voucherDeleted.push(vouchers[i]);
          }
        }
      } else {
        vouchersError.push(vouchers[i]);
      }
    }
    return res.status(200).json({
      ok: true,
      vouchersError,
      voucherDeleted,
    });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

// obtener todos los vouchers de un grupo de voucher
export async function getVouchersOfGroupById(req: Request, res: Response) {
  try {
    const { barcode } = req.body;

    const searchIdVoucherGroup = await WorkVoucherGroups.query().where(
      'barcode',
      barcode
    );

    if (searchIdVoucherGroup.length > 0) {
      const getVouchers = await WorkVouchers.query()
        .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
        .join(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .whereNotIn('mo_numbers.mo_status', [
          'Void',
          'Cancelled',
          'Materials',
          'Complete',
        ])
        .where('work_voucher_group_id', searchIdVoucherGroup[0].id)
        .select(
          'mo_numbers.mo_id',
          'mo_numbers.po_number',
          'mo_numbers.order_type',
          'mo_numbers.mo_order',
          'mo_numbers.num',
          'mo_numbers.mo_status',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.style_category',
          'mo_numbers.ItemDescription8',
          'mo_numbers.customer',
          'mo_numbers.company_code',
          'work_voucher_types.name',
          'work_vouchers.is_repo',
          { voucherId: 'work_vouchers.id' },
          { voucherTypeId: 'work_voucher_types.id' },
          { voucherGroupId: 'work_vouchers.work_voucher_group_id' }
        );

      const data = [];
      for (let i = 0; i < getVouchers.length; i++) {
        data.push({ ...getVouchers[i], uuid: uuidv4(), voucherGroup: true });
      }
      return res.status(200).json({
        ok: true,
        data: data,
      });
    } else {
      return res
        .status(200)
        .json({ ok: false, data: 'No existe el codigo de barras' });
    }
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function getNameVoucherGroup(req: Request, res: Response) {
  try {
    const { id } = req.body;

    const getNameGroup = await WorkVoucherGroups.query().where('id', id);
    if (getNameGroup.length > 0) {
      return res.status(200).json({
        ok: true,
        data: { name: getNameGroup[0].name },
      });
    }
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function addNewVoucherGroupsToTicket(req: Request, res: Response) {
  try {
    const { voucherId, voucherGroupName, area, codeUser } = req.body;

    // buscamos en los vouchers groups si existe el nombre
    const searchNameGroup = await WorkVoucherGroups.query()
      .where('name', voucherGroupName)
      .where('work_area_id_ref', area);

    if (searchNameGroup.length > 0) {
      return res
        .status(200)
        .json({ ok: false, data: 'Ya existe un grupo con ese nombre' });
    } else {
      const newVoucherGroup = await WorkVoucherGroups.query().insert({
        name: voucherGroupName,
        employee_id_ref: codeUser,
        work_area_id_ref: area,
      });

      if (newVoucherGroup) {
        // insertamos en log la creacion del voucher group
        const voucherGroupLog = await WorkActivityLog.query().insert({
          work_area_id: area,
          employee_id: codeUser,
          module_name: 'voucherGroup',
          module_id: newVoucherGroup.id,
          activity: 'VoucherGroupCreated',
          data: JSON.stringify({}),
        });

        // checamos si se inserto el log
        if (voucherGroupLog) {
          // actualizamos el barcode del voucher group
          const addBarcodeToVoucherGroup = await WorkVoucherGroups.query()
            .update({
              barcode: `MEGV${newVoucherGroup.id}`,
            })
            .where('id', newVoucherGroup.id);

          // checamos si se actualizo el barcode
          if (addBarcodeToVoucherGroup) {
            // agregamos el ticket al voucher group
            const searchVoucherInfo = await WorkVouchers.query()
              .where('work_vouchers.id', voucherId)
              .whereNull('work_vouchers.work_voucher_group_id');

            if (searchVoucherInfo.length > 0) {
              const searchLastOrderNumber = await WorkVouchers.query()
                .where(
                  'work_vouchers.work_voucher_group_id',
                  newVoucherGroup.id
                )
                .select('work_voucher_group_sort')
                .orderBy('work_voucher_group_sort', 'DESC')
                .limit(1);

              const updateVoucher = await WorkVouchers.query()
                .update({
                  work_voucher_group_id: newVoucherGroup.id,
                  work_voucher_group_sort:
                    +searchLastOrderNumber[0]?.work_voucher_group_sort + 1 || 1,
                })
                .where('work_vouchers.id', voucherId);
              if (updateVoucher) {
                const voucherLog = await WorkActivityLog.query().insert({
                  work_voucher_id: voucherId,
                  work_area_id: area,
                  employee_id: codeUser,
                  module_name: 'voucherGroup',
                  module_id: newVoucherGroup.id,
                  activity: 'AddedVoucherToGroup',
                  data: JSON.stringify({ value: voucherId }),
                });
                if (voucherLog) {
                  return res
                    .status(200)
                    .json({ ok: true, data: 'VoucherGroupCreated' });
                }
              } else {
                return res
                  .status(500)
                  .json({ ok: false, data: 'No se pudo actualizar el ticket' });
              }
            } else {
              // cambio de grupo
              const searchVoucherInfo = await WorkVouchers.query()
                .join(
                  'work_voucher_groups',
                  'work_vouchers.work_voucher_group_id ',
                  'work_voucher_groups.id'
                )
                .where('work_vouchers.id', voucherId)
                .whereNotNull('work_vouchers.work_voucher_group_id')
                .select([{ nameOldGroup: 'work_voucher_groups.name' }]);

              if (searchVoucherInfo.length > 0) {
                const searchLastOrderNumber = await WorkVouchers.query()
                  .where(
                    'work_vouchers.work_voucher_group_id',
                    newVoucherGroup.id
                  )
                  .select('work_voucher_group_sort')
                  .orderBy('work_voucher_group_sort', 'DESC')
                  .limit(1);

                const updateVoucher = await WorkVouchers.query()
                  .update({
                    work_voucher_group_id: newVoucherGroup.id,
                    work_voucher_group_sort:
                      +searchLastOrderNumber[0]?.work_voucher_group_sort + 1 ||
                      1,
                  })
                  .where('work_vouchers.id', voucherId);

                if (updateVoucher) {
                  const voucherLog = await WorkActivityLog.query().insert({
                    work_voucher_id: voucherId,
                    work_area_id: area,
                    employee_id: codeUser,
                    module_name: 'voucherGroup',
                    module_id: newVoucherGroup.id,
                    activity: 'MovedVoucherToGroup',
                    data: JSON.stringify({
                      oldGroup: searchVoucherInfo[0].nameOldGroup,
                      newGroup: voucherGroupName,
                    }),
                  });
                  if (voucherLog) {
                    return res
                      .status(200)
                      .json({ ok: true, data: 'VoucherGroupCreated' });
                  }
                } else {
                  return res.status(500).json({
                    ok: false,
                    data: 'No se pudo actualizar el ticket',
                  });
                }
              }
            }
          } else {
            return res.status(500).json({
              ok: false,
              message: 'No se agrego el codigo de barras al grupo de voucher',
            });
          }
        } else {
          return res.status(500).json({
            ok: false,
            message: 'No se agrego el log de actividad',
          });
        }
      } else {
        return res
          .status(500)
          .json({ ok: false, message: 'No se creo el grupo de vocuher' });
      }
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({ ok: false });
  }
}

export async function scanMosOfGroupVouchers(req: Request, res: Response) {
  try {
    const { group_vouchers_id, area, status_id, codeEmployee } = req.body;
    const format = 'YYYY-MM-DD HH:mm:ss';
    const now = new Date();

    // obtenemos la informacion del status a cambiar
    const getInfoOfStatus = await WorkAreaTicketStatuses.query()
      .where('work_area_id', area)
      .where('id', status_id)
      .select('name', 'color_hex', 'work_status_id');

    if (getInfoOfStatus.length > 0) {
      // obtenemos todos los tickets del grupo de vouchers
      const searchTickets = await WorkAreaTickets.query()
        .join(
          'work_vouchers',
          'work_area_tickets.work_voucher_id',
          'work_vouchers.id'
        )
        .join(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .where('work_vouchers.work_voucher_group_id', group_vouchers_id)
        .where('work_area_tickets.work_area_id', area)
        .whereNull('work_area_tickets.finished_at')
        .select([
          { voucher_id: 'work_vouchers.id' },
          { ticket_id: 'work_area_tickets.id' },
          { old_status_name: 'work_area_ticket_statuses.name' },
          { old_color_hex: 'work_area_ticket_statuses.color_hex' },
          { is_primary: 'work_vouchers.is_primary' },
        ]);

      if (searchTickets.length > 0) {
        for (let i = 0; i < searchTickets.length; i++) {
          // cambiamos el status del ticket
          const updateWorkTickets = await WorkAreaTickets.query()
            .update({
              work_area_ticket_status_id: status_id,
              is_company_notified: 0,
              finished_at: dayjs(now).format(format),
            })
            .where('id', searchTickets[i].ticket_id);
          if (updateWorkTickets) {
            await WorkActivityLog.query().insert({
              work_voucher_id: searchTickets[i].voucher_id,
              employee_id: codeEmployee,
              work_area_id: area,
              module_name: 'ticket',
              module_id: searchTickets[i].ticket_id,
              activity: 'TicketStatusChanged',
              data: JSON.stringify({
                old_status_name: searchTickets[i].old_status_name,
                old_color: searchTickets[i].old_color_hex,
                new_status_name: getInfoOfStatus[0].name,
                new_color: getInfoOfStatus[0].color_hex,
              }),
            });
          }
        }

        return res.status(200).json({
          ok: true,
          data: searchTickets,
          totalRows: searchTickets.length,
        });
      } else {
        return res.status(400).json({
          ok: true,
          message: 'No hay tickets en el grupo de vouchers',
        });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({ ok: false });
  }
}

export async function changeStatusToAllTicketsInGroupVouchers(
  req: Request,
  res: Response
) {
  try {
    const { group_vouchers_id, area, status_id, codeEmployee } = req.body;
    const format = 'YYYY-MM-DD HH:mm:ss';
    const now = new Date();

    // obtenemos la informacion del status a cambiar
    const getInfoOfStatus = await WorkAreaTicketStatuses.query()
      .where('work_area_id', area)
      .where('id', status_id)
      .select('name', 'color_hex', 'work_status_id');

    if (getInfoOfStatus.length > 0) {
      // obtenemos todos los tickets activos del grupo de vouchers
      const searchTickets = await WorkAreaTickets.query()
        .join(
          'work_vouchers',
          'work_area_tickets.work_voucher_id',
          'work_vouchers.id'
        )
        .join(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .where('work_vouchers.work_voucher_group_id', group_vouchers_id)
        .where('work_area_tickets.work_area_id', area)
        .whereNull('work_area_tickets.finished_at')
        .select([
          { voucher_id: 'work_vouchers.id' },
          { ticket_id: 'work_area_tickets.id' },
          { old_status_name: 'work_area_ticket_statuses.name' },
          { old_color_hex: 'work_area_ticket_statuses.color_hex' },
          { is_primary: 'work_vouchers.is_primary' },
        ]);

      if (searchTickets.length > 0) {
        for (let i = 0; i < searchTickets.length; i++) {
          // cambiamos el status del ticket
          const updateWorkTickets = await WorkAreaTickets.query()
            .update({
              work_area_ticket_status_id: status_id,
              is_company_notified: 0,
              finished_at: dayjs(now).format(format),
            })
            .where('id', searchTickets[i].ticket_id);
          if (updateWorkTickets) {
            // escribimos el log de actividad
            const ticketLog = await WorkActivityLog.query().insert({
              work_voucher_id: searchTickets[i].voucher_id,
              employee_id: codeEmployee,
              work_area_id: area,
              module_name: 'ticket',
              module_id: searchTickets[i].ticket_id,
              activity: 'TicketStatusChanged',
              data: JSON.stringify({
                old_status_name: searchTickets[i].old_status_name,
                old_color: searchTickets[i].old_color_hex,
                new_status_name: getInfoOfStatus[0].name,
                new_color: getInfoOfStatus[0].color_hex,
              }),
            });
          }
        }

        return res.status(200).json({
          ok: true,
        });
      } else {
        return res.status(400).json({
          ok: true,
          message: 'No hay tickets en el grupo de vouchers',
        });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({ ok: false });
  }
}

export async function addNewVoucherGroups(req: Request, res: Response) {
  try {
    const { voucherGroupName, area, codeUser } = req.body;

    // buscamos en los vouchers groups si existe el nombre
    const searchNameGroup = await WorkVoucherGroups.query()
      .where('name', voucherGroupName)
      .where('work_area_id_ref', area);

    if (searchNameGroup.length > 0) {
      return res
        .status(200)
        .json({ ok: false, data: 'Ya existe un grupo con ese nombre' });
    } else {
      const newVoucherGroup = await WorkVoucherGroups.query().insert({
        name: voucherGroupName,
        employee_id_ref: codeUser,
        work_area_id_ref: area,
      });

      if (newVoucherGroup) {
        // insertamos en log la creacion del voucher group
        const voucherGroupLog = await WorkActivityLog.query().insert({
          work_area_id: area,
          employee_id: codeUser,
          module_name: 'voucherGroup',
          module_id: newVoucherGroup.id,
          activity: 'VoucherGroupCreated',
          data: JSON.stringify({}),
        });

        // checamos si se inserto el log
        if (voucherGroupLog) {
          // actualizamos el barcode del voucher group
          const addBarcodeToVoucherGroup = await WorkVoucherGroups.query()
            .update({
              barcode: `MEGV${newVoucherGroup.id}`,
            })
            .where('id', newVoucherGroup.id);

          // checamos si se actualizo el barcode
          if (addBarcodeToVoucherGroup) {
            return res
              .status(200)
              .json({ ok: true, data: { group_id: newVoucherGroup.id } });
          } else {
            return res.status(500).json({
              ok: false,
              message: 'No se agrego el codigo de barras al grupo de voucher',
            });
          }
        } else {
          return res.status(500).json({
            ok: false,
            message: 'No se agrego el log de actividad',
          });
        }
      } else {
        return res
          .status(500)
          .json({ ok: false, message: 'No se creo el grupo de vocuher' });
      }
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({ ok: false });
  }
}

export async function getInfoVoucherGroup(req: Request, res: Response) {
  try {
    const { voucher_group, workAreaId }: any = req.query;

    const vouchers = await WorkVouchers.query()
      .join(
        'work_area_tickets',
        'work_vouchers.id',
        'work_area_tickets.work_voucher_id'
      )
      .join(
        'work_areas',
        'work_area_tickets.work_area_id',
        'work_areas.work_area_id'
      )
      .join(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .join(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id ',
        'work_area_ticket_statuses.id'
      )
      .join('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
      .join(
        'work_voucher_groups',
        'work_vouchers.work_voucher_group_id',
        'work_voucher_groups.id'
      )
      .select([
        { voucherId: 'work_vouchers.id' },
        { sort: 'work_vouchers.work_voucher_group_sort' },
        { ticketId: 'work_area_tickets.id' },
        { areaTicket: 'work_area_tickets.work_area_id' },
        { areaName: 'work_areas.area_name' },
        { voucherType: 'work_voucher_types.name' },
        { nameGroup: 'work_voucher_groups.name' },
        { ticketStatus: 'work_area_ticket_statuses.name' },
        'mo_numbers.mo_id',
        'mo_numbers.mo_status',
        'mo_numbers.material_date',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        'mo_numbers.ItemDescription8',
        'mo_numbers.num',
        'mo_numbers.po_numbers',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.customer',
      ])
      .where('work_vouchers.work_voucher_group_id', voucher_group.substr(4))
      .where('work_area_tickets.work_area_id', workAreaId)
      .orderBy('work_vouchers.work_voucher_group_sort', 'ASC');

    if (vouchers.length > 0) {
      return res.status(200).json({
        ok: true,
        data: vouchers,
      });
    } else {
      return res.status(200).json({
        ok: false,
        data: 'No se encontro el Grupo de vouchers',
      });
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function receiveVouchersGroup(req: Request, res: Response) {
  try {
    const {
      convertToLot,
      vouchersGroup,
      expectedDate,
      location_id,
      line_id,
      group_id,
      area_id,
      workAreaId,
      codeEmployee,
    } = req.body;

    // buscamos el grupo de vouchers
    const searchVoucherGroup = await WorkVoucherGroups.query()
      .where('barcode', vouchersGroup)
      .select('id', 'name', 'work_area_id_ref');

    // buscamos los vouchers del grupo
    const searchVouchers = await WorkVouchers.query()
      .where('work_voucher_group_id', searchVoucherGroup[0].id)
      .select('id');

    // buscamos los tickets de los vouchers del grupo por el area del grupo a recibir
    const searchTickets = await WorkAreaTickets.query()
      .join(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id',
        'work_area_ticket_statuses.id'
      )
      .whereIn(
        'work_voucher_id',
        searchVouchers.map((v: { id: number }) => v.id)
      )
      .where(
        'work_area_tickets.work_area_id',
        searchVoucherGroup[0].work_area_id_ref
      )
      .select([
        'work_area_tickets.id',
        'work_area_ticket_statuses.work_status_id',
      ]);

    // verificamos que todos los tickets esten completos para poder recibir el grupo
    // el estatus 100 es el estatus de completado
    // el estatus 110 es el estatus de void
    const checkTickets = searchTickets.every(
      (t: { work_status_id: number }) =>
        t.work_status_id === 100 || t.work_status_id === 110
    );

    if (!checkTickets) {
      // obtenemos el estatus completo del area anterior
      const searchStatus = await WorkAreaTicketStatuses.query()
        .where('work_area_id', searchVoucherGroup[0].work_area_id_ref)
        .where('work_status_id', 100)
        .select('id')
        .limit(1);

      // completamos los tickets del area anterior
      const updateTickets = await WorkAreaTickets.query()
        .whereIn(
          'id',
          searchTickets.map((t: { id: number }) => t.id)
        )
        .patch({
          work_area_ticket_status_id: searchStatus[0].id,
          finished_at: new Date(),
        });

      // insertamos en el log de tickets
      for (let i = 0; i < searchTickets.length; i++) {
        if (searchTickets[i].work_status_id === 100) {
          await WorkActivityLog.query().insert({
            work_area_id: searchVoucherGroup[0].work_area_id_ref,
            employee_id: codeEmployee,
            module_name: 'Reception',
            module_id: searchTickets[i].id,
            activity: 'TicketReception',
            data: JSON.stringify({
              message: 'Ticket completado',
              ticket_id: searchTickets[i].id,
              employee_id: codeEmployee,
            }),
          });
        }
      }

      if (!updateTickets) {
        return res.status(400).json({
          ok: false,
          message: 'No se pudo actualizar los tickets',
        });
      }
    }

    //? recibimos el grupo de vouchers
    // cambiamos el area del grupo de vouchers a la del area de recepcion
    const updateVoucherGroup = await WorkVoucherGroups.query()
      .findById(searchVoucherGroup[0].id)
      .patch({
        work_area_id_ref: workAreaId,
      });

    if (!updateVoucherGroup) {
      return res.status(400).json({
        ok: false,
        message: 'No se pudo actualizar el grupo de vouchers',
      });
    }

    // buscamos el estatus nuevo del area de recepcion
    const searchStatus = await WorkAreaTicketStatuses.query()
      .where('work_area_id', workAreaId)
      .where('work_status_id', 50)
      .where('name', 'Nuevo')
      .select('id');

    if (searchStatus.length === 0) {
      // regresamos el grupo de vouchers a su area original
      await WorkVoucherGroups.query().findById(searchVoucherGroup[0].id).patch({
        work_area_id_ref: searchVoucherGroup[0].work_area_id_ref,
      });

      return res.status(400).json({
        ok: false,
        message: 'No se encontro el estatus Nuevo',
      });
    }

    // creamos los tickets para el area de recepcion
    const trx = await WorkAreaTickets.startTransaction();
    const tickets = [];

    for (let i = 0; i < searchVouchers.length; i++) {
      const ticket = await WorkAreaTickets.query(trx).insert({
        work_area_id: workAreaId,
        work_voucher_id: searchVouchers[i].id,
        work_area_ticket_status_id: searchStatus[0].id,
        exp_finish_date: expectedDate.length > 0 ? expectedDate : null,
        work_inventory_location_id: location_id ? location_id : null,
        exp_work_area_line_id: line_id ? line_id : null,
        exp_work_area_group_id: group_id ? group_id : null,
        next_work_area_id: area_id ? area_id : null,
      });

      if (!ticket) {
        await trx.rollback();

        // regresamos el area de los vouchers al area anterior
        await WorkVoucherGroups.query()
          .findById(searchVoucherGroup[0].id)
          .patch({
            work_area_id_ref: searchVoucherGroup[0].work_area_id_ref,
          });

        return res.status(400).json({
          ok: false,
          message: 'No se pudo crear el ticket',
        });
      }

      tickets.push(ticket);
    }

    if (tickets.length === 0) {
      await trx.rollback();

      // regresamos el area de los vouchers al area anterior
      await WorkVoucherGroups.query().findById(searchVoucherGroup[0].id).patch({
        work_area_id_ref: searchVoucherGroup[0].work_area_id_ref,
      });

      return res.status(400).json({
        ok: false,
        message: 'No se pudieron crear los tickets',
      });
    }

    // verificamos si se requiere convertir a lote
    if (convertToLot) {
      // creamos el lote, usaremos el nombre del grupo de vouchers como nombre y descripcion del lote
      const lot: any = await WorkAreaBatches.query().insert({
        work_area_id: workAreaId,
        name: searchVoucherGroup[0].name,
        description: searchVoucherGroup[0].name,
      });

      if (!lot) {
        await trx.rollback();

        // regresamos el area de los vouchers al area anterior
        await WorkVoucherGroups.query()
          .findById(searchVoucherGroup[0].id)
          .patch({
            work_area_id_ref: searchVoucherGroup[0].work_area_id_ref,
          });

        return res.status(400).json({
          ok: false,
          message: 'No se pudo crear el lote',
        });
      }

      // agregamos los tickets al lote
      const ticketsToLot = await WorkAreaTickets.query(trx)
        .update({
          work_batch_id: lot.id,
        })
        .whereIn(
          'id',
          tickets.map((t: { id: number }) => t.id)
        );

      if (!ticketsToLot) {
        await trx.rollback();

        // regresamos el area de los vouchers al area anterior
        await WorkVoucherGroups.query()
          .findById(searchVoucherGroup[0].id)
          .patch({
            work_area_id_ref: searchVoucherGroup[0].work_area_id_ref,
          });

        return res.status(400).json({
          ok: false,
          message: 'No se pudieron agregar los tickets al lote',
        });
      }

      // eliminamos los vouchers del grupo de vouchers
      const deleteVouchers = await WorkVouchers.query(trx)
        .update({
          work_voucher_group_id: null,
          work_voucher_group_sort: null,
        })
        .whereIn(
          'id',
          searchVouchers.map((v: { id: number }) => v.id)
        );

      if (!deleteVouchers) {
        await trx.rollback();

        // regresamos el area de los vouchers al area anterior
        await WorkVoucherGroups.query()
          .findById(searchVoucherGroup[0].id)
          .patch({
            work_area_id_ref: searchVoucherGroup[0].work_area_id_ref,
          });

        return res.status(400).json({
          ok: false,
          message: 'No se pudieron eliminar los vouchers del grupo',
        });
      }

      // eliminamos el grupo de vouchers
      await WorkVoucherGroups.query()
        .update({
          removed_at: new Date(),
        })
        .where('id', searchVoucherGroup[0].id);

      // logs
      // log de creacion de lote
      await WorkActivityLog.query().insert({
        work_area_id: workAreaId,
        employee_id: codeEmployee,
        module_name: 'Lot',
        module_id: lot.id,
        activity: 'CreatedBatch',
        data: JSON.stringify(lot),
      });

      // log de eliminacion de grupo de vouchers
      await WorkActivityLog.query().insert({
        work_area_id: workAreaId,
        employee_id: codeEmployee,
        module_name: 'voucherGroup',
        module_id: searchVoucherGroup[0].id,
        activity: 'VoucherGroupDeleted',
        data: JSON.stringify(searchVoucherGroup[0]),
      });

      await trx.commit();

      // retornamos el lote
      return res.status(200).json({
        ok: true,
        data: lot,
      });
    }

    await trx.commit();

    return res.status(200).json({
      ok: true,
      data: searchVoucherGroup[0],
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ ok: false });
  }
}
