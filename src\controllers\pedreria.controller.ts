import type { Request, Response } from 'express';

import { RhinestoneOrder } from '@app/models/rhinestone.schema';
import { buildLogger } from '@app/settings';

const logger = buildLogger('pedreria.controller');

export async function getOrders(_: Request, res: Response) {
  try {
    const getOrders = await RhinestoneOrder.query()
      .join('mo_numbers', 'rhinestones_orders.mo_id', 'mo_numbers.mo_id')
      .join(
        'production_types',
        'rhinestones_orders.type_id',
        'production_types.id'
      )
      // .where('rhinestones_orders.is_active', 1)
      .where('rhinestones_orders.order_status', 'active')
      .orderBy('rhinestones_orders.art_date', 'DESC')
      .orderBy('art_date', 'desc')
      .select([
        { orderId: 'rhinestones_orders.id' },
        { schedule: 'mo_numbers.required_date' },
        { order: 'mo_numbers.mo_order' },
        { voucher: 'mo_numbers.mo_order_item_num' },
        { style: 'mo_numbers.style' },
        { quantity: 'mo_numbers.quantity' },
        { dateArt: 'rhinestones_orders.art_date' },
        { dateWarehouse: 'rhinestones_orders.warehouse_date' },
        { type: 'production_types.type_name' },
      ])
      .castTo<
        {
          orderId: number;
          schedule: string;
          order: string;
          voucher: string;
          style: string;
          quantity: number;
          dateArt: string;
          dateWarehouse: string;
          type: string;
        }[]
      >();

    if (getOrders.length === 0) {
      logger.error('No se encontraron pedidos');
      return res
        .status(404)
        .json({ ok: false, message: 'No se encontraron pedidos' });
    }

    return res.status(200).json({
      ok: true,
      data: getOrders,
    });
  } catch (error) {
    logger.error(`Error al obtener pedidos: ${error}`);

    if (error instanceof Error) {
      return res.status(500).json({ ok: false, message: error.message });
    }

    return res.status(500).json({ ok: false, message: 'Error interno' });
  }
}
