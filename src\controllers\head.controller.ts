import type { Request, Response } from 'express';
import fs from 'fs';

import { parseForm } from '@app/helpers/promisifys';
import { headUpdate } from '@app/services/head';

export const getHeadFile = async (req: Request, res: Response) => {
  try {
    const [, files] = await parseForm(req);
    // console.log('fields', fields);
    // console.log('files', files);
    const filenames = Object.keys(files);

    if (filenames.length === 0) {
      throw Error('No files uploaded');
    }
    if (filenames.length > 1) {
      throw Error('Only one file allowed');
    }
    const fileName = filenames[0];
    const file = files[fileName];
    // console.log('got file named ' + fileName);
    if (Array.isArray(file)) {
      throw Error('File in file is not allowed');
    }
    console.log(file.filepath, file.newFilename, file.originalFilename);

    const newHead = await headUpdate(file.filepath);
    // console.log('form cpr read done');

    // removes file
    fs.unlinkSync(file.filepath);
    // console.log('form cpr file removed');
    return res.status(200).send(newHead);
  } catch (e) {
    console.log('head file error', e);
    res.status(400).send(e.message);
    return;
  }
};
