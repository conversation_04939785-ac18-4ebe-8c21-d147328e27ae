import dayjs from 'dayjs';
import type { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';

import { incrementChar } from '@app/helpers/generateBarcode';
import { WorkPreBarcodes } from '@app/models/tickets.schema';

export async function getPreBarcodes(req: Request, res: Response) {
  const { quantityBarcode } = req.body;

  try {
    const key = uuidv4();
    const barcode_total: any[] = [];

    while (barcode_total.length < quantityBarcode) {
      const get_available_barcodes: any = await WorkPreBarcodes.query()
        .whereNull('work_pre_barcodes.use_key')
        .orderBy('work_pre_barcodes.barcode', 'ASC');

      if (get_available_barcodes.length > 0) {
        const update_available_barcode = await WorkPreBarcodes.query()
          .update({
            use_key: key,
            use_date: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
          })
          .whereNull('use_date')
          .where('barcode', get_available_barcodes[0].barcode);

        if (update_available_barcode) {
          barcode_total.push({ preBarcode: get_available_barcodes[0].barcode });
        }
      } else {
        const get_last_barcode: any[] = await WorkPreBarcodes.query()
          .orderBy('work_pre_barcodes.barcode', 'DESC')
          .limit(1)
          .select('barcode')
          .whereNotNull('use_date');

        const barcode_pattern = incrementChar(
          get_last_barcode[0].barcode.split('MEPB')[1]
        );

        if (barcode_pattern === 'AAAAAAA') {
          return res.status(500).json({
            ok: false,
            message: 'No se puede generar mas codigos de barras',
          });
        } else {
          const new_barcode = await newBarcode({
            barcode: `MEPB${barcode_pattern}`,
            use_key: key,
            use_date: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
          });

          if (new_barcode.message !== 'barcode duplicado') {
            barcode_total.push({ preBarcode: new_barcode.barcode });
          }
        }
      }
    }

    return res.status(201).json({
      ok: true,
      barcodes: barcode_total,
      total_items: barcode_total.length,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

async function newBarcode({ barcode, use_key, use_date }: string | any) {
  try {
    const new_barcode = await WorkPreBarcodes.query().insert({
      barcode,
      use_key,
      use_date,
    });

    return new_barcode;
  } catch (error) {
    const result = (error as Error).name;

    if (result === 'UniqueViolationError') {
      return { ok: false, message: 'barcode duplicado' };
    }

    return error;
  }
}

export async function returnBarcodesPending(req: Request, res: Response) {
  try {
    const { last_barcode } = req.body;

    const get_last_barcode: any[] = await WorkPreBarcodes.query()
      .where('barcode', last_barcode)
      .select('use_key');

    const get_all_barcodes_from_print_queue: any[] =
      await WorkPreBarcodes.query()
        .where('use_key', get_last_barcode[0].use_key)
        .select('barcode')
        .orderBy('work_pre_barcodes.barcode', 'ASC');

    const last_barcode_printed = get_all_barcodes_from_print_queue
      .map((barcode: { barcode: string }) => barcode.barcode)
      .indexOf(last_barcode);

    const remaining_barcodes = get_all_barcodes_from_print_queue
      .filter((barcode: { barcode: string }, i: number) => {
        if (i > last_barcode_printed) return barcode;
        return false;
      })
      .map((barcode: { barcode: string }) => barcode.barcode);

    return res.status(200).json({ ok: true, remaining_barcodes });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}
