import { Router } from 'express';

import {
  createScan,
  getCompleteProduction,
  getCompleteProductionByArea,
  getEmployeeInfo,
  getInfoOperator,
  getInlineProduction,
  getMoInfoByBarcode,
  getMoInformationByGroup,
  removeScan,
  restoreScan,
  updateQuantityScan,
} from '@app/controllers/scanning.controller';
import {
  checkBadgeBarcode,
  checkJobBarcode,
  getWorkTicketStatuses,
} from '@app/middlewares/scanning.middlewares';

const scansRouter = Router();

scansRouter
  .route('/created')
  .post(checkJobBarcode, checkBadgeBarcode, getWorkTicketStatuses, createScan);

scansRouter
  .route('/station/getMoInformationByBarcode')
  .post(checkJobBarcode, getMoInfoByBarcode);
scansRouter.route('/station/pending').post(getInlineProduction);
scansRouter.route('/station/complete').post(getCompleteProduction);
scansRouter.route('/station/completebyarea').post(getCompleteProductionByArea);
scansRouter.route('/station/operator').post(getInfoOperator);
scansRouter.route('/station/removeat').post(removeScan);
scansRouter.route('/station/restorescan').post(restoreScan);
scansRouter.route('/station/updatequantityscan').post(updateQuantityScan);
scansRouter.route('/station/moinformation').post(getMoInformationByGroup);
scansRouter.route('/station/getEmployeeInfo').post(getEmployeeInfo);

export { scansRouter };
