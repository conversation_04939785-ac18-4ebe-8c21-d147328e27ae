import type { AxiosResponse } from 'axios';
import axios from 'axios';
import dotenv from 'dotenv';

const scanlogChannelUrl = process.env.DISCORD_SCANLOG_CHANNEL;
const webVoucherChannelUrl = process.env.DISCORD_WEBVOUCHER_CHANNEL;

dotenv.config();

const sendDiscordMessage = async (
  url: string,
  title: string,
  description: string
): Promise<AxiosResponse> => {
  if (!url) {
    return;
  }

  const discordMessage = {
    embeds: [
      {
        title: title,
        description: description,
      },
    ],
  };

  return axios.post(`${url}`, JSON.stringify(discordMessage), {
    headers: { 'Content-Type': 'application/json' },
  });
};

export async function sendScanLog(
  title: string,
  description: string
): Promise<boolean> {
  if (!scanlogChannelUrl) {
    return false;
  }

  const discordMessage = {
    embeds: [
      {
        title: title,
        description: description,
      },
    ],
  };

  const response = await axios.post(
    `${scanlogChannelUrl}`,
    JSON.stringify(discordMessage),
    {
      headers: { 'Content-Type': 'application/json' },
    }
  );

  if (response.status === 200) {
    return true;
  } else {
    return false;
  }
}

export const sendWebVoucherLog = async (
  title: string,
  description: string
): Promise<boolean> => {
  if (!webVoucherChannelUrl) {
    return false;
  }

  const response = await sendDiscordMessage(
    webVoucherChannelUrl,
    title,
    description
  );

  if (response.status === 200) {
    return true;
  } else {
    return false;
  }
};
