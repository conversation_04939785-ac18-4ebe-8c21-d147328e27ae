import { Head, HeadItem, type HeadItemsShape, type HeadsShape } from "@app/models/head.schema";
import currency from "currency.js";
import { knex } from '@app/db';
import { readFile } from "xlsx";
import moment from "moment";

const numberCheck = (value: any, name: string) => {
  if (value === undefined || value === null || value === '' || value === 0) {
    return 0;
  }
  const numberValue = Number(value);
  if (isNaN(numberValue) || numberValue < 0) {
    console.log('value', value);
    throw new Error(`No ${name} found`);
  }
  return numberValue;
}

const getDateYearMonthDayString = (date: Date) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const useDay = day < 10 ? '0' + day : day.toString();
  const useMonth = month < 10 ? '0' + month : month.toString();
  return `${year}-${useMonth}-${useDay}`;
  return date.toISOString().split('T')[0];
}

export const headUpdate = async (excelFilePath: string) => {
  // read from a XLS file
  const workbook = readFile(excelFilePath, {
    // cellDates: true,
  });

  let useSheetName = null
  for (const sheetName of workbook.SheetNames) {
    console.log('sheetName', sheetName);
    if (sheetName.startsWith('#') || sheetName.startsWith('PYR #')) {
      useSheetName = sheetName;
      break;
    }
  }

  if (useSheetName === null) {
    throw new Error('No sheet found');
  }

  // check cell C5 starts with Nomina
  // pull name from cell
  // Nomina   231102

  // get c5 value
  const useSheet = workbook.Sheets[useSheetName]
  const fullName = useSheet['C5']?.v;
  console.log('fullname', fullName);
  if (!fullName || typeof fullName !== 'string' || !fullName.startsWith('Nomina')) {
    throw new Error('No Nomina found');
  }
  // split by multiple spaces
  const name = fullName.split(/\s+/)[1];

  if (!name || name === '') {
    throw new Error('No name found');
  }

  const fromDateCell = useSheet['G5'];
  const toDateCell = useSheet['J5'];

  console.log('fromDateCell', fromDateCell, typeof fromDateCell.v, isNaN(fromDateCell.v));
  console.log('toDateCell', toDateCell);

  if (typeof fromDateCell.v !== 'number' || isNaN(fromDateCell.v) || !fromDateCell.w) {
    throw new Error('No from date found');
  }
  if (typeof toDateCell.v !== 'number' || isNaN(toDateCell.v) || !toDateCell.w) {
    throw new Error('No to date found');
  }
  const from_date = moment(fromDateCell.w)
  const to_date = moment(toDateCell.w)

  if (!from_date.isValid()) {
    throw new Error('Invalid from date');
  }
  if (!to_date.isValid()) {
    throw new Error('Invalid to date');
  }

  const from_date_string = from_date.format('YYYY-MM-DD')
  const to_date_string = to_date.format('YYYY-MM-DD')

  console.log('fromDate string', from_date_string)
  console.log('toDate string', to_date_string)

  const headInput: Partial<HeadsShape> = {
    name,
    from_date: from_date_string,
    to_date: to_date_string,
  };

  console.log('headInput', headInput);


  // get c7 value
  const c7 = useSheet['C7'].v;
  if (typeof c7 != 'string' || !c7.startsWith('NOMBRE DEL EMPLEADO')) {
    throw new Error('No NOMBRE DEL EMPLEADO found');
  }
  const d7 = useSheet['D7'].v;
  if (typeof d7 != 'string' || !d7.startsWith('TIEMPO TRAB')) {
    throw new Error('No TIEMPO TRAB found');
  }
  const g7 = useSheet['G7'].v;
  if (typeof g7 != 'string' || !g7.startsWith('OTROS INGRESOS')) {
    throw new Error('No OTROS INGRESOS found');
  }
  const h7 = useSheet['H7'].v;
  if (typeof h7 != 'string' || !h7.startsWith('BONIFICACION')) {
    throw new Error('No BONIFICACION found');
  }
  const i7 = useSheet['I7'].v;
  if (typeof i7 != 'string' || !i7.startsWith('EXTRAS DIURNAS')) {
    throw new Error('No EXTRAS DIURNAS found');
  }
  const j7 = useSheet['J7'].v;
  if (typeof j7 != 'string' || !j7.startsWith('VALOR DIURNAS')) {
    throw new Error('No VALOR DIURNAS found');
  }
  const k7 = useSheet['K7'].v;
  if (typeof k7 != 'string' || !k7.startsWith('EXTRAS NOCTURNAS')) {
    throw new Error('No EXTRAS NOCTURNAS found');
  }
  const l7 = useSheet['L7'].v;
  if (typeof l7 != 'string' || !l7.startsWith('VALOR NOCTURNAS')) {
    throw new Error('No VALOR NOCTURNAS found');
  }
  const m7 = useSheet['M7'].v;
  if (typeof m7 != 'string' || !m7.startsWith('TOTAL DEVENGADO')) {
    throw new Error('No TOTAL DEVENGADO found');
  }
  const n7 = useSheet['N7'].v;
  if (typeof n7 != 'string' || !n7.startsWith('DESCUENTOS')) {
    throw new Error('No DESCUENTOS found');
  }
  const n8 = useSheet['N8'].v;
  if (typeof n8 != 'string' || !n8.startsWith('ISSS')) {
    throw new Error('No ISSS found');
  }
  const o8 = useSheet['O8'].v;
  if (typeof o8 != 'string' || !o8.startsWith('AFPS')) {
    throw new Error('No AFPS found');
  }
  const p8 = useSheet['P8'].v;
  if (typeof p8 != 'string' || !p8.startsWith('RENTA')) {
    throw new Error('No RENTA found');
  }
  const q8 = useSheet['Q8'].v;
  if (typeof q8 != 'string' || !q8.startsWith('OTROS')) {
    throw new Error('No OTROS found');
  }
  const r7 = useSheet['R7'].v;
  if (typeof r7 != 'string' || !r7.startsWith('TOTAL DESCUENTOS')) {
    throw new Error('No TOTAL DESCUENTOS found');
  }
  const s7 = useSheet['S7'].v;
  if (typeof s7 != 'string' || !s7.startsWith('LIQUIDO')) {
    throw new Error('No LIQUIDO found');
  }


  // Loop through rows to get groups
  // first group starts at row 10

  let curRow = 10;
  let curGroup = '';
  const seenGroups: string[] = [];
  let curGroupEmployees: Partial<HeadItemsShape>[] = [];
  const allEmployees: Partial<HeadItemsShape>[] = [];
  let state: 'groupStart' | 'groupRows' | 'groupFinish' = 'groupStart'
  let groupEmployeeSection = true;
  while (groupEmployeeSection) {
    const a1Value = useSheet[`A${curRow}`]?.v;
    // console.log('curRow', curRow, state, a1Value);
    if (typeof a1Value === 'string' && a1Value.startsWith('Total general de la planilla')) {
      groupEmployeeSection = false;
      break;
    }

    if (state === 'groupStart') {
      if (a1Value === undefined || typeof a1Value !== 'string' || a1Value === '') {
        throw new Error('No group name found');
      }
      curGroup = a1Value.trim();

      // name might begin with (##), example (*5)CASTELLI LAVADO or (1F)VARSITY STOCK
      // remove it
      const nameSplit = curGroup.split(')');
      if (nameSplit.length > 1) {
        if (nameSplit.length > 2) {
          throw new Error('Too many ) found');
        }
        console.log('nameSplit', nameSplit);
        curGroup = nameSplit[1];
      }
      if (seenGroups.includes(curGroup)) {
        // console.log('curGroup', curGroup);
        // console.log('seen group index', seenGroups.indexOf(curGroup));
        // console.log('seen group', seenGroups);
        // throw new Error(`Duplicate group found, ${curGroup}`);
      }
      seenGroups.push(curGroup);
      state = 'groupRows';
      curRow++;
      curRow++;
      continue;
    }

    if (state === 'groupRows') {
      if (a1Value === undefined || a1Value === null || a1Value === '') {
        state = 'groupFinish';
        curRow++;
        continue;
      }

      const newGroupEmployee: Partial<HeadItemsShape> = {
        accounting_department: curGroup,
        employee_id: numberCheck(useSheet[`B${curRow}`]?.v, 'employee_id'),
        employee_name: useSheet[`C${curRow}`]?.v,
        hours: numberCheck(useSheet[`D${curRow}`]?.v, 'hours_worked'),
        daily_rate: numberCheck(useSheet[`E${curRow}`]?.v, 'daily_rate'),
        amount: numberCheck(useSheet[`F${curRow}`]?.v, 'amount'),
        extra_amount: numberCheck(useSheet[`G${curRow}`]?.v, 'extra_amount'),
        bonus_amount: numberCheck(useSheet[`H${curRow}`]?.v, 'bonus_amount'),
        overtime_hours: numberCheck(useSheet[`I${curRow}`]?.v, 'overtime_hours'),
        overtime_amount: numberCheck(useSheet[`J${curRow}`]?.v, 'overtime_amount'),
        night_overtime_hours: numberCheck(useSheet[`K${curRow}`]?.v, 'night_overtime_hours'),
        night_overtime_amount: numberCheck(useSheet[`L${curRow}`]?.v, 'night_overtime_amount'),
        total_amount: numberCheck(useSheet[`M${curRow}`]?.v, 'total_amount'),
        deduction_isss_amount: numberCheck(useSheet[`N${curRow}`]?.v, 'deduction_isss_amount'),
        deduction_afp_amount: numberCheck(useSheet[`O${curRow}`]?.v, 'deduction_afp_amount'),
        deduction_isr_amount: numberCheck(useSheet[`P${curRow}`]?.v, 'deduction_isr_amount'),
        deduction_other_amount: numberCheck(useSheet[`Q${curRow}`]?.v, 'deduction_other_amount'),
        total_deduction_amount: numberCheck(useSheet[`R${curRow}`]?.v, 'total_deduction_amount'),
        net_amount: numberCheck(useSheet[`S${curRow}`]?.v, 'net_amount'),
      };

      curGroupEmployees.push(newGroupEmployee);
      curRow++;
      continue;
    }

    if (state === 'groupFinish') {
      const c1Value = useSheet[`C${curRow}`]?.v;
      // console.log('curRow', curRow, c1Value);
      if (typeof c1Value != 'string' || !c1Value.startsWith('Total personas por Depto')) {
        throw new Error('No Total personas por Depto found');
      }
      // get total
      const totalEmployees = Number(c1Value.split(/\s+/)[4]);
      // console.log('totalEmployees', totalEmployees);
      if (curGroupEmployees.length !== totalEmployees) {
        throw new Error('Total employees does not match');
      }
      state = 'groupStart';
      // console.log('curGroup', curGroup, curGroupEmployees.length);
      allEmployees.push(...curGroupEmployees);
      curGroupEmployees = [];
      curRow++;
      curRow++;
      continue;
    }

    throw new Error('Unknown state');
  }

  // get total
  const a1Value = useSheet[`A${curRow}`]?.v;
  const totalEmployees = Number(a1Value.split(/\s+/)[5]);

  if (allEmployees.length !== totalEmployees) {
    throw new Error('Total employees does not match');
  }

  const summary = {
    amount: numberCheck(useSheet[`F${curRow}`]?.v, 'amount'),
    extra_amount: numberCheck(useSheet[`G${curRow}`]?.v, 'extra_amount'),
    bonus_amount: numberCheck(useSheet[`H${curRow}`]?.v, 'bonus_amount'),
    overtime_hours: numberCheck(useSheet[`I${curRow}`]?.v, 'overtime_hours'),
    overtime_amount: numberCheck(useSheet[`J${curRow}`]?.v, 'overtime_amount'),
    night_overtime_hours: numberCheck(useSheet[`K${curRow}`]?.v, 'night_overtime_hours'),
    night_overtime_amount: numberCheck(useSheet[`L${curRow}`]?.v, 'night_overtime_amount'),
    total_amount: numberCheck(useSheet[`M${curRow}`]?.v, 'total_amount'),
    deduction_isss_amount: numberCheck(useSheet[`N${curRow}`]?.v, 'deduction_isss_amount'),
    deduction_afp_amount: numberCheck(useSheet[`O${curRow}`]?.v, 'deduction_afp_amount'),
    deduction_isr_amount: numberCheck(useSheet[`P${curRow}`]?.v, 'deduction_isr_amount'),
    deduction_other_amount: numberCheck(useSheet[`Q${curRow}`]?.v, 'deduction_other_amount'),
    total_deduction_amount: numberCheck(useSheet[`R${curRow}`]?.v, 'total_deduction_amount'),
    net_amount: numberCheck(useSheet[`S${curRow}`]?.v, 'net_amount'),
  };

  const summaryCheck = allEmployees.reduce((acc, cur) => {
    acc.amount = currency(acc.amount).add(cur.amount).value
    acc.extra_amount = currency(acc.extra_amount).add(cur.extra_amount).value
    acc.bonus_amount = currency(acc.bonus_amount).add(cur.bonus_amount).value
    acc.overtime_hours = currency(acc.overtime_hours).add(cur.overtime_hours).value
    acc.overtime_amount = currency(acc.overtime_amount).add(cur.overtime_amount).value
    acc.night_overtime_hours = currency(acc.night_overtime_hours).add(cur.night_overtime_hours).value
    acc.night_overtime_amount = currency(acc.night_overtime_amount).add(cur.night_overtime_amount).value
    acc.total_amount = currency(acc.total_amount).add(cur.total_amount).value
    acc.deduction_isss_amount = currency(acc.deduction_isss_amount).add(cur.deduction_isss_amount).value
    acc.deduction_afp_amount = currency(acc.deduction_afp_amount).add(cur.deduction_afp_amount).value
    acc.deduction_isr_amount = currency(acc.deduction_isr_amount).add(cur.deduction_isr_amount).value
    acc.deduction_other_amount = currency(acc.deduction_other_amount).add(cur.deduction_other_amount).value
    acc.total_deduction_amount = currency(acc.total_deduction_amount).add(cur.total_deduction_amount).value
    acc.net_amount = currency(acc.net_amount).add(cur.net_amount).value
    return acc;
  }, {
    amount: 0,
    extra_amount: 0,
    bonus_amount: 0,
    overtime_hours: 0,
    overtime_amount: 0,
    night_overtime_hours: 0,
    night_overtime_amount: 0,
    total_amount: 0,
    deduction_isss_amount: 0,
    deduction_afp_amount: 0,
    deduction_isr_amount: 0,
    deduction_other_amount: 0,
    total_deduction_amount: 0,
    net_amount: 0,
  });

  const finalCheck = {
    amount: summaryCheck.amount,
    extra_amount: summaryCheck.extra_amount,
    bonus_amount: summaryCheck.bonus_amount,
    overtime_hours: summaryCheck.overtime_hours,
    overtime_amount: summaryCheck.overtime_amount,
    night_overtime_hours: summaryCheck.night_overtime_hours,
    night_overtime_amount: summaryCheck.night_overtime_amount,
    total_amount: summaryCheck.total_amount,
    deduction_isss_amount: summaryCheck.deduction_isss_amount,
    deduction_afp_amount: summaryCheck.deduction_afp_amount,
    deduction_isr_amount: summaryCheck.deduction_isr_amount,
    deduction_other_amount: summaryCheck.deduction_other_amount,
    total_deduction_amount: summaryCheck.total_deduction_amount,
    net_amount: summaryCheck.net_amount,
  };

  console.log('summary', summary);
  console.log('finalCheck', finalCheck);


  if (summary.amount !== finalCheck.amount) {
    throw new Error('summary.amount does not match');
  }
  if (summary.extra_amount !== finalCheck.extra_amount) {
    throw new Error('summary.extra_amount does not match');
  }
  if (summary.bonus_amount !== finalCheck.bonus_amount) {
    throw new Error('summary.bonus_amount does not match');
  }
  if (summary.overtime_hours !== finalCheck.overtime_hours) {
    throw new Error('summary.overtime_hours does not match');
  }
  if (summary.overtime_amount !== finalCheck.overtime_amount) {
    throw new Error('summary.overtime_amount does not match');
  }
  if (summary.night_overtime_hours !== finalCheck.night_overtime_hours) {
    throw new Error('summary.night_overtime_hours does not match');
  }
  if (summary.night_overtime_amount !== finalCheck.night_overtime_amount) {
    throw new Error('summary.night_overtime_amount does not match');
  }
  if (summary.total_amount !== finalCheck.total_amount) {
    throw new Error('summary.total_amount does not match');
  }
  if (summary.deduction_isss_amount !== finalCheck.deduction_isss_amount) {
    throw new Error('summary.deduction_isss_amount does not match');
  }
  if (summary.deduction_afp_amount !== finalCheck.deduction_afp_amount) {
    throw new Error('summary.deduction_afp_amount does not match');
  }
  if (summary.deduction_isr_amount !== finalCheck.deduction_isr_amount) {
    throw new Error('summary.deduction_isr_amount does not match');
  }
  if (summary.deduction_other_amount !== finalCheck.deduction_other_amount) {
    throw new Error('summary.deduction_other_amount does not match');
  }
  if (summary.total_deduction_amount !== finalCheck.total_deduction_amount) {
    throw new Error('summary.total_deduction_amount does not match');
  }
  if (summary.net_amount !== finalCheck.net_amount) {
    throw new Error('summary.net_amount does not match');
  }

  return await knex.transaction(async (trx) => {

    // check if head exists, remove head and items
    const head = await Head.query(trx).findOne({
      name: headInput.name,
    });

    if (head && (getDateYearMonthDayString(head.from_date as Date) !== headInput.from_date || getDateYearMonthDayString(head.to_date as Date) !== headInput.to_date)) {
      throw new Error('Head already exists with different dates');
    }

    if (head) {
      await HeadItem.query(trx).delete().where({
        head_id: head.id,
      });
      await Head.query(trx).delete().where({
        id: head.id,
      });
      console.log('deleted head', head.id);
    }

    // create head
    const newHead = await Head.query(trx).insertAndFetch(headInput);

    // create head items
    const itemsToInsert = allEmployees.map((item) => {
      const { employee_name, ...rest } = item
      return {
        ...rest,
        head_id: newHead.id,
      };
    })
    await knex.batchInsert('head_items', itemsToInsert, 1000).transacting(trx);

    console.log('newHead finished', newHead);

    return newHead;
  });
}
