import { Router } from 'express';

import {
  CreateFragmentCustomField,
  CreateFragmentType,
  CreateNewFragment,
  GetAllFragmentsInRoll,
  GetAllFragmentsOfTheMO,
  GetAllRolls,
  GetFragment,
  GetFragmentCustomField,
  GetFragmentCustomFields,
  GetFragmentLog,
  GetFragmentNameAndTypeByMo,
  GetFragmentType,
  GetFragmentTypes,
  GetFragmentWithCustomFields,
  GetFragmentdById,
  GetFragments,
  GetFragmentsByNameAndMo,
  GetRollById,
  UpdateFragment,
} from '@app/controllers/fragment.controller';

const fragmentsRouter = Router();

fragmentsRouter.route('/types').get(GetFragmentTypes);
fragmentsRouter.route('/types/:id').get(GetFragmentType);
fragmentsRouter.route('/types/create').post(CreateFragmentType);
fragmentsRouter.route('/customfields/:id').get(GetFragmentCustomFields);
fragmentsRouter.route('/customfields/field/:id').get(GetFragmentCustomField);
fragmentsRouter.route('/customfields/create').post(CreateFragmentCustomField);
fragmentsRouter.route('/log/:id').get(GetFragmentLog);
fragmentsRouter.route('/name/:mo_id').get(GetFragmentNameAndTypeByMo);
fragmentsRouter.route('/rolls').get(GetRollById);
fragmentsRouter.route('/roll/fragments').get(GetAllFragmentsInRoll);
fragmentsRouter.route('/:id/customFields').get(GetFragmentWithCustomFields);
fragmentsRouter.route('/:id').get(GetFragment);
fragmentsRouter.route('/byId/:id').get(GetFragmentdById);
fragmentsRouter.route('/:id').patch(UpdateFragment);
fragmentsRouter.route('/mo/:mo_id/:name').get(GetFragmentsByNameAndMo);
fragmentsRouter.route('/').get(GetFragments);
fragmentsRouter.route('/create').post(CreateNewFragment);
fragmentsRouter.route('/rolls/all').get(GetAllRolls);
fragmentsRouter.route('/byMo/:mo').get(GetAllFragmentsOfTheMO);

export { fragmentsRouter };
