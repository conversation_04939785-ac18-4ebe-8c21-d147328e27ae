import { Employee } from '@app/models/employee.schema';
import { WorkAreaEmployees } from '@app/models/tickets.schema';
import { buildLogger } from '@app/settings';

const logger = buildLogger('service:employee');

export const getEmployeeByCode = (employeeCode: number) => {
  const employee = Employee.query().findById(employeeCode);
  if (!employee) {
    throw Error('Employee not found');
  }

  return employee;
};

export const getEmployeeByBarcode = async (barcode: string) => {
  try {
    const employee = await Employee.query().findOne('barcode', barcode);
    if (!employee) {
      throw Error('Employee not found');
    }

    return employee;
  } catch (error) {
    logger.error(`Error: ${error}`);
    throw Error('Error getting employee by barcode');
  }
};

interface EmployeeBasicInfo {
  id: number;
  name: string;
}

export const getEmployeeByArea = async (
  areaID: number
): Promise<EmployeeBasicInfo[]> => {
  try {
    const employees = await WorkAreaEmployees.query()
      .join('employees', 'work_area_employees.employee_id', 'employees.id')
      .where('work_area_employees.work_area_id', areaID)
      .select([
        'employees.employee_id',
        'employees.firts_name',
        'employees.last_name',
      ])
      .castTo<
        {
          id: number;
          first_name: string;
          last_name: string;
        }[]
      >();

    const employeesWithFullName = employees.map(
      (employee): EmployeeBasicInfo => {
        return {
          id: employee.id,
          name: `${employee.first_name} ${employee.last_name}`,
        };
      }
    );

    return employeesWithFullName;
  } catch (error) {
    logger.error(`Error: ${error}`);
    throw Error('Error getting employees by area');
  }
};

export const searchEmployeesActiveById = async (id: number) => {
  try {
    const employees = await Employee.query()
      .where('employee_id', 'like', `${id}%`)
      .where('status', 1)
      .select([
        'employee_id',
        'first_name',
        'last_name',
        'department',
        'barcode',
        'created_at',
        'departments_id',
      ]);

    return employees;
  } catch (error) {
    logger.error(`Error: ${error}`);
    throw Error('Error getting employees by ids');
  }
};
