import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(
    'invoice_types',
    (table: Knex.TableBuilder) => {
      table.increments('id').unsigned().primary();
      table.string('invoice_type').notNullable();
      table
        .timestamp('created_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      table
        .timestamp('updated_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('invoice_types');
}
