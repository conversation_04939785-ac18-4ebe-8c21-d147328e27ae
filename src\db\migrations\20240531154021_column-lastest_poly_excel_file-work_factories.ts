import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'work_factories',
    (table: Knex.TableBuilder) => {
      table
        .string('latest_poly_excel_file')
        .defaultTo(null)
        .nullable()
        .comment('nombre de archivo de poly excel file');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'work_factories',
    (table: Knex.TableBuilder) => {
      table.dropColumn('latest_poly_excel_file');
    }
  );
}
