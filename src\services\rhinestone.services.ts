import type { Model } from 'objection';
import { transaction } from 'objection';

import type { IOrder, IToken } from '@app/controllers/rhinestone.controller';
import {
  RhinestoneInventory,
  RhinestoneLog,
  RhinestoneOrder,
  RhinestoneOrderItem,
} from '@app/models/rhinestone.schema';

type ActionsType = 'create' | 'update' | 'delete';
interface IDataLog {
  newValue: any | null;
  oldValue: any | null;
}

export interface ICreateOrder {
  mo: IOrder;
  token: IToken;
}

interface ILogs {
  rhinestones_area_id: number;
  employee_id: number;
  action: ActionsType;
  module_id: number;
  module_name: string;
  data: IDataLog;
}

export const addLogs = async (
  model: typeof Model,
  {
    rhinestones_area_id,
    employee_id,
    action,
    module_id,
    module_name,
    data,
  }: ILogs
): Promise<string | boolean> => {
  try {
    const log = await model.query().insert({
      rhinestones_area_id,
      employee_id,
      action,
      module_id,
      module_name,
      data,
    });

    if (!log) throw new Error('No se puede agregar el log');

    return true;
  } catch (error) {
    if (error instanceof Error) {
      return error.message;
    }

    return error;
  }
};

export const createOrder = async ({
  mo,
  token,
}: ICreateOrder): Promise<number> => {
  try {
    const { moID, isRepo } = mo;
    const { area_id, employee_id } = token;

    const orderExists = await RhinestoneOrder.query()
      .where('mo_id', moID)
      .where('is_active', 1)
      .where('is_repo', 0)
      .first();

    if (isRepo === false && orderExists)
      throw new Error(`Ya existe una orden activa y como PRODUCCION con la mo`);

    const order = await RhinestoneOrder.query().insert({
      mo_id: moID,
      is_repo: isRepo,
      is_active: true,
    });

    if (!order) throw new Error('No se puede crear la orden');

    await addLogs(RhinestoneLog, {
      rhinestones_area_id: area_id,
      employee_id: employee_id,
      action: 'create',
      module_id: order.id,
      module_name: 'order',
      data: {
        newValue: {
          mo_id: moID,
          is_repo: isRepo,
          is_active: 1,
          order_exists: !!orderExists,
        },
        oldValue: null,
      },
    });

    return order.id;
  } catch (error) {
    throw new Error(`Error al crear la orden: ${error}`);
  }
};

export interface IStones {
  sizeID: number;
  colorID: number;
  itemStatus?: string;
  quantity: number;
  typeID: number;
}

interface IAddStoneToOrder {
  orderID: number;
  stones: IStones[];
}

export const addStonesToOrder = async ({
  orderID,
  stones,
}: IAddStoneToOrder): Promise<string | number[]> => {
  try {
    const items = await transaction(
      RhinestoneOrderItem,
      RhinestoneInventory,
      async (RhinestoneOrderItems, RhinestoneInventory) => {
        const stonesIds: number[] = [];

        for (const stone of stones) {
          const {
            sizeID,
            colorID,
            // TODO: This should be a boolean??
            itemStatus = 'active',
            quantity,
            typeID,
          } = stone;

          try {
            const stone = await RhinestoneOrderItems.query().insert({
              order_id: orderID,
              size_id: sizeID,
              color_id: colorID,
              // TODO: This should be a boolean?? changed to check for active
              is_active: itemStatus === 'active' ? true : false,
              quantity,
              type_id: typeID,
            });

            if (!stone) throw new Error('No se puede agregar la piedra');

            stonesIds.push(+stone.id);

            const inventory = await RhinestoneInventory.query()
              .where({
                size_id: sizeID,
                color_id: colorID,
              })
              .first();

            if (!inventory)
              throw new Error('No existe el part_number del inventario');

            const updateItem = await RhinestoneOrderItems.query()
              .update({
                rhinestone_inventory_id: inventory.id,
              })
              .where({
                id: stone.id,
              });

            if (!updateItem) throw new Error('No se puede actualizar el item');
          } catch (error) {
            throw new Error(
              `No se puede agregar la piedra: size: ${stone.sizeID} con el color: ${stone.colorID}`
            );
          }
        }

        return stonesIds;
      }
    );

    return items;
  } catch (error) {
    if (error instanceof Error) {
      return error.message;
    }

    return error;
  }
};
