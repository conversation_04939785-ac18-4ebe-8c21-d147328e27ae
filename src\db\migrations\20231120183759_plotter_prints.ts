import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('plotter_prints', (table: Knex.TableBuilder) => {
    table.increments('id').unsigned().primary();
    table
      .timestamp('created_at')
      .notNullable()
      .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    table
      .timestamp('updated_at')
      .notNullable()
      .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

    table.string('part_number').notNullable();
    table.integer('created_by_employee_id').notNullable();
    table.string('name');
    table.jsonb('plys');
    table.integer('quantity');
    table.decimal('width', 10, 4);
    table.decimal('length', 10, 4);
    table.decimal('utilization', 10, 4);
    table.integer('total_sizes');
    table.timestamp('marked_at').nullable()
    table.timestamp('printed_at').nullable()
    table.timestamp('warehoused_at').nullable()
    table.timestamp('cut_at').nullable()
    table.integer('plotter_roll_id').nullable()
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('plotter_prints');
}
