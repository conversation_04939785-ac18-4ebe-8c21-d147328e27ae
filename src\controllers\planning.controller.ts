import type { Request, Response } from 'express';
import type { Multer } from 'multer';

import { savePolyFile } from '@app/services/planning';

export const saveExcelFileController = async (
  req: Request & { file: Multer.File },
  res: Response
): Promise<void> => {
  try {
    const polyExcelFile = req.file;
    const filePath = await savePolyFile(polyExcelFile);
    res.status(200).send({ filePath });
  } catch (error) {
    res.status(500).send({ error: error.message });
  }
};
