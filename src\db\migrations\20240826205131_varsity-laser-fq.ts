import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'mo_twill_laser_artists',
    (table: Knex.TableBuilder) => {
      table.boolean('fq').notNullable().defaultTo(false);
      table.integer('employee_id').nullable().alter();
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'mo_twill_laser_artists',
    (table: Knex.TableBuilder) => {
      table.dropColumn('fq');
      table.integer('employee_id').notNullable().alter();
    }
  );
}
