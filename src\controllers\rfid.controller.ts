// Importamos modulos de express para las rutas
import type { Request, Response } from 'express';

// Importación de interfaces, le decimos a typescript que vamos a recibir en las reqs
// import { comments, ideas } from '../interface/Post';
// Ayuda a typescript, le decimos que vamos a recibir de las REQS
interface RfidVoucher {
  type: 'voucher';
  voucher_id: number;
  ticket_id: number | null;
  mo_id: number;
  mo_number: string;
  customer: string | null;
}
interface RfidUser {
  type: 'user';
  employee_id: number;
  first_name: string;
  last_name: string;
}

interface RfidData {
  [rfid: string]: RfidUser | RfidVoucher;
}

// Importación del schema de nuestra base de datos
const rfidData: RfidData = {
  B7B5A2D9: {
    type: 'user',
    employee_id: 3842,
    first_name: 'peter',
    last_name: 'brink',
  },
  E7DC914B: {
    type: 'user',
    employee_id: 9876,
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON><PERSON>',
  },
  BC5B0B39: {
    type: 'voucher',
    voucher_id: 203948,
    ticket_id: null,
    mo_id: 293483,
    mo_number: '1239483/002',
    customer: 'Castelli',
  },
  '2A587680': {
    type: 'voucher',
    voucher_id: 382823,
    ticket_id: null,
    mo_id: 193843,
    mo_number: '239837',
    customer: 'Adidas',
  },
};

// Obtenemos todos los registros y exportamos la función
export async function rfidEntity(req: Request, res: Response) {
  try {
    const rfid = req.query.rfid || null;
    console.log('rfid grabbed: ', rfid);
    if (!rfid) {
      res.status(400).json({
        ok: false,
        message: 'RFID is missing',
      });
      return;
    }

    if (typeof rfid != 'string') {
      res.status(400).json({
        ok: false,
        message: 'RFID is not a string',
      });
      return;
    }

    const data = rfidData[rfid.toUpperCase()];
    if (!data) {
      res.status(404).send('rfid not found');
      return;
    }
    console.log('data', data);
    res.send(data);
  } catch (error) {
    // Mensaje de error si la consulta falla
    return res.status(400).json({
      ok: false,
      message: 'Error, Something happened',
      error,
    });
  }
}
