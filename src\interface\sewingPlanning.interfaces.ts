export interface EligibleMO {
  mo_id: number;
  mo_number: string;
  style: string;
  style_id: number;
  customer: string;
  quantity: number;
  required_date: string;
  mo_status: string;
  style_category: string;
  product_category: string;
  company_code: number;
  scan_count: number;
  last_scan_date: string;
}

export interface SewingLineStyleHistory {
  work_area_group_id: number;
  work_area_group_name: string;
  style: string;
  style_id: number;
  scan_count: number;
  last_scan_date: string;
  total_quantity_scanned: number;
  avg_efficiency: number;
}

export interface SewingLineEfficiency {
  work_area_group_id: number;
  work_area_group_name: string;
  latest_efficiency: number;
  shift_date: string;
  operator_count: number;
  usable_minutes: number;
  est_production_minutes: number;
  section: string;
  planning_name: string;
}

export interface MOSewingLinePlan {
  mo_id: number;
  mo_number: string;
  style: string;
  customer: string;
  quantity: number;
  required_date: string;
  recommended_lines: RecommendedLine[];
  priority_score: number;
}

export interface RecommendedLine {
  work_area_group_id: number;
  work_area_group_name: string;
  recommendation_score: number;
  style_experience_score: number;
  efficiency_score: number;
  capacity_score: number;
  style_similarity_score: number;
  last_worked_style: string;
  latest_efficiency: number;
  section: string;
  planning_name: string;
}

export interface SewingPlanningFilters {
  company_codes?: number[];
  style_categories?: string[];
  customers?: string[];
  required_date_from?: string;
  required_date_to?: string;
  min_quantity?: number;
  max_quantity?: number;
  sections?: string[];
}

export interface SewingPlanningResult {
  eligible_mos: EligibleMO[];
  sewing_lines: SewingLineEfficiency[];
  mo_plans: MOSewingLinePlan[];
  summary: {
    total_mos: number;
    total_lines: number;
    avg_efficiency: number;
    total_quantity: number;
  };
}
