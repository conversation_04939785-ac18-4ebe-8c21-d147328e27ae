import type { ModelObject } from 'objection';

import { Model } from '@app/db';

export class Machine extends Model {
  static get tableName(): string {
    return 'machines';
  }

  static get idColumn() {
    return 'machine_id';
  }

  machine_id!: number;
  machine_type_id!: number;
  machine!: string;
  model!: string;
  machine_model_id!: number;
  serial!: string;
  barcode!: string;
  date_manufactured!: string;
  date_purchased!: string;
  url!: string;
  status!: number;
  rpm!: number;
  heads!: number;
  ip_address!: string;
  MAC!: string;
  last_inspected!: string;
  work_area_line_id!: number;
  created_at!: string;
  updated_at!: string;
}
export type MachineShape = ModelObject<Machine>;

export class MachineType extends Model {
  static get tableName(): string {
    return 'machine_types';
  }

  static get idColumn() {
    return 'machine_type_id';
  }

  machine_type_id!: number;
  name!: string;
  work_type_id!: number;
  updated_at!: string;
}
export class MachineStatus extends Model {
  static get tableName(): string {
    return 'work_machine_statuses';
  }

  id!: number;
  name!: string;
  working_status!: number;
  created_at!: string;
  updated_at!: string;
}

export class MachineStateLog extends Model {
  cur_state: number;
  static get tableName(): string {
    return 'machine_state_logs';
  }
}
