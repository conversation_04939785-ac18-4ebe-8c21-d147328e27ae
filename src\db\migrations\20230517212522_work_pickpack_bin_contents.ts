import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(
    'work_pickpack_bin_contents',
    (table: Knex.TableBuilder) => {
      table.increments('id').unsigned().primary();
      table
        .timestamp('created_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      table
        .timestamp('updated_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

      table.integer('work_pickpack_bin_id').unsigned().notNullable();
      table.integer('work_pickpack_item_id').unsigned().notNullable();
      table.integer('quantity').unsigned().notNullable();

      table.string('order_number').nullable();
      table.integer('order_item_number').nullable();
      table.integer('mo_id').nullable();
      table.integer('good_allocation_id').nullable();

      table.jsonb('rosters').nullable();

      table.unique(
        [
          'work_pickpack_bin_id',
          'work_pickpack_item_id',
          'order_number',
          'order_item_number',
          'mo_id',
        ],
        {
          indexName: 'style_order_mo_size_unique',
        }
      );
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('work_pickpack_bin_contents');
}
