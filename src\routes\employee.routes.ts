import { Router } from 'express';

import {
  getEmployeeArea,
  getEmployeeByBarcode,
  getEmployeeById,
  getEmployeeInformation,
  getEmployeesByEmployeeCode,
  simpleSignin,
} from '@app/controllers/employee.controller';

export const employeesRouter = Router();

employeesRouter.route('/').get(getEmployeesByEmployeeCode);
employeesRouter.route('/getAreaEmployee').post(getEmployeeArea);
employeesRouter.route('/getEmployeeInformation').post(getEmployeeInformation);
employeesRouter.route('/simpleSignin').post(simpleSignin);

employeesRouter.route('/barcode/:barcode').get(getEmployeeByBarcode);

employeesRouter.route('/:employee_id').get(getEmployeeById);
