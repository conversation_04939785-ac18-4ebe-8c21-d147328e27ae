{"compilerOptions": {"baseUrl": ".", "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "module": "commonjs", "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false, "outDir": "./dist", "paths": {"@app/*": ["src/*"]}, "rootDir": "src", "skipLibCheck": true, "sourceMap": true, "strict": false, "strictPropertyInitialization": false, "target": "ES2022", "types": ["node", "jest"]}, "include": ["./src/**/*"], "exclude": ["./node_modules"]}