import type { Request, Response } from 'express';

import type { SewingPlanningFilters } from '@app/interface/sewingPlanning.interfaces';
import {
  generateSewingPlan,
  getEligibleMOs,
  getSewingLineEfficiency,
  getSewingLineStyleHistory,
} from '@app/services/sewingPlanning';

/**
 * Get eligible MOs for sewing planning
 */
export async function getEligibleMOsController(req: Request, res: Response) {
  try {
    const filters: SewingPlanningFilters = {
      company_codes: req.query.company_codes ? 
        (Array.isArray(req.query.company_codes) ? 
          req.query.company_codes.map(Number) : 
          [Number(req.query.company_codes)]) : undefined,
      style_categories: req.query.style_categories ? 
        (Array.isArray(req.query.style_categories) ? 
          req.query.style_categories as string[] : 
          [req.query.style_categories as string]) : undefined,
      customers: req.query.customers ? 
        (Array.isArray(req.query.customers) ? 
          req.query.customers as string[] : 
          [req.query.customers as string]) : undefined,
      required_date_from: req.query.required_date_from as string,
      required_date_to: req.query.required_date_to as string,
      min_quantity: req.query.min_quantity ? Number(req.query.min_quantity) : undefined,
      max_quantity: req.query.max_quantity ? Number(req.query.max_quantity) : undefined,
      sections: req.query.sections ? 
        (Array.isArray(req.query.sections) ? 
          req.query.sections as string[] : 
          [req.query.sections as string]) : undefined,
    };

    const eligibleMOs = await getEligibleMOs(filters);

    res.status(200).json({
      ok: true,
      data: eligibleMOs,
      count: eligibleMOs.length,
    });
  } catch (error) {
    console.error('Error getting eligible MOs:', error);
    res.status(500).json({
      ok: false,
      message: 'Error retrieving eligible manufacturing orders',
      error: error.message,
    });
  }
}

/**
 * Get sewing line efficiency data
 */
export async function getSewingLineEfficiencyController(req: Request, res: Response) {
  try {
    const filters: SewingPlanningFilters = {
      sections: req.query.sections ? 
        (Array.isArray(req.query.sections) ? 
          req.query.sections as string[] : 
          [req.query.sections as string]) : undefined,
    };

    const sewingLines = await getSewingLineEfficiency(filters);

    res.status(200).json({
      ok: true,
      data: sewingLines,
      count: sewingLines.length,
    });
  } catch (error) {
    console.error('Error getting sewing line efficiency:', error);
    res.status(500).json({
      ok: false,
      message: 'Error retrieving sewing line efficiency data',
      error: error.message,
    });
  }
}

/**
 * Get sewing line style history
 */
export async function getSewingLineStyleHistoryController(req: Request, res: Response) {
  try {
    const { styles } = req.body;

    if (!styles || !Array.isArray(styles) || styles.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Styles array is required in request body',
      });
    }

    const styleHistory = await getSewingLineStyleHistory(styles);

    res.status(200).json({
      ok: true,
      data: styleHistory,
      count: styleHistory.length,
    });
  } catch (error) {
    console.error('Error getting sewing line style history:', error);
    res.status(500).json({
      ok: false,
      message: 'Error retrieving sewing line style history',
      error: error.message,
    });
  }
}

/**
 * Generate complete sewing plan
 */
export async function generateSewingPlanController(req: Request, res: Response) {
  try {
    const filters: SewingPlanningFilters = {
      company_codes: req.query.company_codes ? 
        (Array.isArray(req.query.company_codes) ? 
          req.query.company_codes.map(Number) : 
          [Number(req.query.company_codes)]) : undefined,
      style_categories: req.query.style_categories ? 
        (Array.isArray(req.query.style_categories) ? 
          req.query.style_categories as string[] : 
          [req.query.style_categories as string]) : undefined,
      customers: req.query.customers ? 
        (Array.isArray(req.query.customers) ? 
          req.query.customers as string[] : 
          [req.query.customers as string]) : undefined,
      required_date_from: req.query.required_date_from as string,
      required_date_to: req.query.required_date_to as string,
      min_quantity: req.query.min_quantity ? Number(req.query.min_quantity) : undefined,
      max_quantity: req.query.max_quantity ? Number(req.query.max_quantity) : undefined,
      sections: req.query.sections ? 
        (Array.isArray(req.query.sections) ? 
          req.query.sections as string[] : 
          [req.query.sections as string]) : undefined,
    };

    const sewingPlan = await generateSewingPlan(filters);

    res.status(200).json({
      ok: true,
      data: sewingPlan,
    });
  } catch (error) {
    console.error('Error generating sewing plan:', error);
    res.status(500).json({
      ok: false,
      message: 'Error generating sewing plan',
      error: error.message,
    });
  }
}

/**
 * Get sewing planning dashboard data
 */
export async function getSewingPlanningDashboard(req: Request, res: Response) {
  try {
    const filters: SewingPlanningFilters = {
      company_codes: req.query.company_codes ? 
        (Array.isArray(req.query.company_codes) ? 
          req.query.company_codes.map(Number) : 
          [Number(req.query.company_codes)]) : undefined,
      sections: req.query.sections ? 
        (Array.isArray(req.query.sections) ? 
          req.query.sections as string[] : 
          [req.query.sections as string]) : undefined,
    };

    const [eligibleMOs, sewingLines] = await Promise.all([
      getEligibleMOs(filters),
      getSewingLineEfficiency(filters),
    ]);

    // Calculate dashboard metrics
    const totalQuantity = eligibleMOs.reduce((sum, mo) => sum + mo.quantity, 0);
    const avgEfficiency = sewingLines.length > 0 ? 
      sewingLines.reduce((sum, line) => sum + (line.latest_efficiency || 0), 0) / sewingLines.length : 0;

    const urgentMOs = eligibleMOs.filter(mo => {
      const daysUntilRequired = new Date(mo.required_date).getTime() - new Date().getTime();
      return daysUntilRequired <= 7 * 24 * 60 * 60 * 1000; // 7 days or less
    });

    const topPerformingLines = sewingLines
      .filter(line => line.latest_efficiency > 0)
      .sort((a, b) => (b.latest_efficiency || 0) - (a.latest_efficiency || 0))
      .slice(0, 10);

    res.status(200).json({
      ok: true,
      data: {
        summary: {
          total_mos: eligibleMOs.length,
          total_lines: sewingLines.length,
          total_quantity: totalQuantity,
          avg_efficiency: Math.round(avgEfficiency * 10000) / 100,
          urgent_mos: urgentMOs.length,
        },
        urgent_mos: urgentMOs.slice(0, 10),
        top_performing_lines: topPerformingLines,
        recent_mos: eligibleMOs.slice(0, 20),
      },
    });
  } catch (error) {
    console.error('Error getting sewing planning dashboard:', error);
    res.status(500).json({
      ok: false,
      message: 'Error retrieving dashboard data',
      error: error.message,
    });
  }
}
