import type formidable from 'formidable';
import moment from 'moment';

import { updateDatabaseWithCprData } from './database';
import { getCPRXlsx } from './excelFile';
import { getVarsityCPRParts, processCprFileParts } from './pdfPull';

export const readSaveReportCprFile = async (
  file: formidable.File,
  options?: { force: boolean; showPages?: number[] }
) => {
  const force = options?.force ?? false;
  const showPages = options?.showPages;

  console.log('read cpr file', file.filepath);

  // pdfBuffer contains the file content
  const partsData = await getVarsityCPRParts(file.filepath);

  console.log('parts finsihed', partsData.fileParts.length);
  console.log('unknown parts', partsData.unknownParts.length);
  console.log('first part example', partsData[0]);

  if (partsData.unknownParts.length > 0) {
    console.log('unknown parts', partsData.unknownParts);
    throw new Error('Unknown parts found');
  }

  const CPRInfo = await processCprFileParts(partsData.fileParts, {
    showPages,
  });
  console.log('CPRInfo', CPRInfo.reportData);

  // update database
  if (CPRInfo.reportData.accepted_pages.length > 0) {
    await updateDatabaseWithCprData(
      CPRInfo.reportData,
      CPRInfo.itemsMade,
      file.originalFilename,
      {
        force,
      }
    );
  }

  // create excel file
  const cprWorkbook = getCPRXlsx(
    CPRInfo.reportData,
    CPRInfo.summariesMade,
    CPRInfo.itemsMade
  );

  const filenameToSave = `cpr_pull_${CPRInfo.reportData.report_type}_${
    CPRInfo.reportData.report_start
  }_${CPRInfo.reportData.report_end}_${moment().format('YYYYMMDDHHmmss')}.xlsx`;

  return [cprWorkbook, filenameToSave];
};
