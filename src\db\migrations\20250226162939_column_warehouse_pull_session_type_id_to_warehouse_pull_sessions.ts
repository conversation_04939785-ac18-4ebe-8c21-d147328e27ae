import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('warehouse_pull_sessions', (table): void => {
    table
      .integer('warehouse_pull_session_type_id', 10)
      .nullable()
      .unsigned()
      .after('employee_id');
    table
      .foreign(
        'warehouse_pull_session_type_id',
        'wpsti_warehouse_pull_session_types_fk'
      )
      .references('id')
      .inTable('warehouse_pull_session_types');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('warehouse_pull_sessions', (table): void => {
    table.dropForeign(
      'warehouse_pull_session_type_id',
      'wpsti_warehouse_pull_session_types_fk'
    );
    table.dropColumn('warehouse_pull_session_type_id');
  });
}
