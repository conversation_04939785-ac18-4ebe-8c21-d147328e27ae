import type { <PERSON><PERSON> } from 'knex';

import { knex } from '@app/db';
import { <PERSON><PERSON><PERSON>ber, MoSize } from '@app/models/pedreria.schema';
import {
  GoodsAllocations,
  PickPackBin,
  PickPackBinContent,
  PickPackBinTypes,
  PickPackItem,
  PickPackMoPick,
  PickPackMoPickItem,
  PickPackStation,
  PickPackStationSort,
} from '@app/models/pickpack.schema';

// interface ContainerRoster {
//   name: string | null;
//   number: string | null;
//   contact: string | null;
// }

interface MoContainerItems {
  work_pickpack_item_id: number;
  quantity: number;
  order_number?: string;
  order_item_number?: number;
  mo_id?: number;
  mo_size_id?: number;
  good_allocation_id?: number;
  rosters?: string[];
}

export const getItem = async (id: string) => {
  return PickPackItem.query().findById(id);
};

export const getItemByDetails = async (
  customer: string,
  style: string,
  division: string | null,
  color: string | null,
  size: string,
  design: string | null,
  options?: {
    transaction?: Knex.Transaction<any, any>;
  }
) => {
  const transaction = options?.transaction ?? undefined;

  const items = await PickPackItem.query(transaction)
    .where('customer', customer)
    .where('style', style)
    .where('division', division)
    .where('color', color)
    .where('size', size)
    .where('design', design);

  if (items.length > 1) {
    throw Error('More than one item found');
  }

  if (items.length === 0) {
    return null;
  }

  return items[0];
};

export const createItem = async (
  customer: string,
  style: string,
  division: string | null,
  color: string | null,
  size: string,
  design: string | null,
  options?: {
    barcode?: string;
    transaction?: Knex.Transaction<any, any>;
  }
) => {
  if (!customer) {
    throw Error('Customer is required');
  }
  if (!style) {
    throw Error('Style is required');
  }
  if (!size) {
    throw Error('Size is required');
  }

  let barcode = options?.barcode;
  if (customer === 'JL' && !barcode) {
    // JL specific
    // Should figure out a way to be generic
    barcode = `${style}-${design}-${size.replace(',', '/')}`;
  }

  const transaction = options?.transaction ?? undefined;

  const item = await PickPackItem.query(transaction).findOne({
    customer: customer,
    style: style,
    division: division,
    size: size,
    color: color,
    design: design,
  });

  if (item) {
    return item;
  }

  return await PickPackItem.query(transaction).insert({
    customer: customer,
    style: style,
    division: division,
    size: size,
    color: color,
    design: design,
    barcode: barcode,
  });
};

export const createBin = async (
  name: string,
  options?: {
    bin_type_id?: number;
    is_container?: boolean;
    container_bin_location_id?: number;
    transaction?: Knex.Transaction<any, any>;
    allow_items?: boolean;
    is_pickable?: boolean;
    sort_station_id?: number;
  }
) => {
  if (!name) {
    throw Error('Name is required');
  }

  const transaction = options?.transaction ?? undefined;
  const container_bin_location_id = options?.container_bin_location_id ?? null;

  if (options?.bin_type_id) {
    const binType = await PickPackBinTypes.query(transaction).findById(
      options?.bin_type_id
    );

    if (!binType) {
      throw Error('Bin type not found');
    }
  }

  if (container_bin_location_id) {
    const binLocation = await PickPackBin.query(transaction).findById(
      container_bin_location_id
    );

    if (!binLocation) {
      throw Error('Bin location not found');
    }

    if (!options?.is_container) {
      throw Error(
        'Bin location is not a container and cannot be put into another location'
      );
    }
  }

  const binCheck = await PickPackBin.query(transaction).findOne({
    name: name,
  });

  if (binCheck) {
    throw Error('Bin already exists');
  }

  const newBin = await PickPackBin.query(transaction).insert({
    name: name,
    container_bin_location_id: container_bin_location_id,
    work_pickpack_bin_type_id: options?.bin_type_id,
    is_container: options?.is_container ?? false,
    allow_items: options?.allow_items ?? true,
    is_pickable: options?.is_pickable ?? true,
    sort_station_id: options?.sort_station_id ?? null,
  });

  return newBin;
};

export const getPickPackStations = async () => {
  return PickPackStation.query();
};

export const getPickPackStation = async (station_id: number) => {
  return PickPackStation.query().findById(station_id);
};

export const nextStationSortContainer = async (station_id: number) => {
  const station = await getPickPackStation(station_id);

  if (!station) {
    throw Error('Station not found');
  }

  const sortingBins = await PickPackStationSort.query().where({
    work_pickpack_station_id: station_id,
  });

  if (sortingBins.length === 0) {
    return null;
  }

  const nextBin = sortingBins[0];

  return nextBin;
};

export const getBinAndContents = async (
  bin_id: number,
  options?: {
    transaction?: Knex.Transaction<any, any>;
  }
) => {
  const transaction = options?.transaction ?? undefined;

  const bin = await PickPackBin.query(transaction).findById(bin_id);

  if (!bin) {
    throw Error('Bin not found');
  }

  const binContents = await PickPackBinContent.query(transaction).where(
    'work_pickpack_bin_id',
    bin_id
  );

  return {
    bin: bin,
    contents: binContents,
  };
};

export const createMoContainer = async (
  mo_id: number,
  bin_location_id?: number
) => {
  return await knex.transaction(async (trx) => {
    try {
      const mo = await MoNumber.query(trx).findById(mo_id);

      if (!mo) {
        throw Error('Mo not found');
      }

      if (bin_location_id) {
        const binLocationCheck = await PickPackBin.query(trx).findById(
          bin_location_id
        );

        if (!binLocationCheck) {
          throw Error('Bin location not found');
        }
      }

      const newBinName = mo.mo_barcode;

      // check if mo is already has a container made for it
      const existingContainerCheck = await PickPackBin.query(trx)
        .where('name', newBinName)
        .first();

      if (existingContainerCheck) {
        throw Error(
          `Container already exists with name ${existingContainerCheck.name}`
        );
      }

      const allocation = await GoodsAllocations.query(trx)
        .where('ManufactureNumber', mo.num)
        .where('CustomerNumber', mo.customer);

      const moSizes = await MoSize.query(trx).where('mo_id', mo_id);

      const allocationTotal = allocation.reduce((acc, curr) => {
        return acc + curr.QuantityAllocated;
      }, 0);

      const sizeTotal = moSizes.reduce((acc, curr) => {
        return acc + curr.size_quantity;
      }, 0);

      if (allocationTotal !== sizeTotal) {
        throw Error('Mo sizes and allocation total do not match');
      }

      if (sizeTotal !== mo.quantity) {
        throw Error('Mo sizes and mo total do not match');
      }

      const containerItems: MoContainerItems[] = [];
      for (const allocationItem of allocation) {
        const rosterNames =
          !allocationItem.DetailDescription_ ||
          allocationItem.DetailDescription_.trim() === ''
            ? null
            : allocationItem.DetailDescription_.split(',');
        const rosterNumbers =
          !allocationItem.DetailSpec2_ ||
          allocationItem.DetailSpec2_.trim() === ''
            ? null
            : allocationItem.DetailSpec2_.split(',');
        const rosterContacts =
          !allocationItem.DetailSpec3_ ||
          allocationItem.DetailSpec3_.trim() === ''
            ? null
            : allocationItem.DetailSpec3_.split(',');

        if (
          rosterNames &&
          rosterNames.length !== allocationItem.QuantityAllocated
        ) {
          throw Error(
            `Roster names length does not match for size ${allocationItem.GarmentSize} on allocation ${allocationItem.good_allocation_id}`
          );
        }

        if (
          rosterNumbers &&
          rosterNumbers.length !== allocationItem.QuantityAllocated
        ) {
          throw Error(
            `Roster numbers length does not match for size ${allocationItem.GarmentSize} on allocation ${allocationItem.good_allocation_id}`
          );
        }

        if (
          rosterContacts &&
          rosterContacts.length !== allocationItem.QuantityAllocated
        ) {
          throw Error(
            `Roster contacts length does not match for size ${allocationItem.GarmentSize} on allocation ${allocationItem.good_allocation_id}`
          );
        }

        const rosters: string[] = [];
        if (rosterNames || rosterNumbers || rosterContacts) {
          for (let i = 0; i < allocationItem.QuantityAllocated; i++) {
            const rosterName =
              rosterNames && rosterNames.length > 0 ? rosterNames[i] : '';
            const rosterNumber =
              rosterNumbers && rosterNumbers.length > 0 ? rosterNumbers[i] : '';
            const rosterContact =
              rosterContacts && rosterContacts.length > 0
                ? rosterContacts[i]
                : '';

            rosters.push(
              `${allocationItem.OrderNumber}-${allocationItem.ItemNumber}-${allocationItem.GarmentSize}-${i}^${rosterName}|${rosterNumber}|${rosterContact}`
            );
          }
        }

        let work_pickpack_item = null;

        work_pickpack_item = await getItemByDetails(
          allocationItem.CustomerNumber,
          allocationItem.StyleNumber,
          allocationItem.DivisionName_,
          allocationItem.StyleColor,
          allocationItem.GarmentSize,
          allocationItem.ItemDescription8_,
          {
            transaction: trx,
          }
        );

        if (!work_pickpack_item) {
          work_pickpack_item = await createItem(
            allocationItem.CustomerNumber,
            allocationItem.StyleNumber,
            allocationItem.DivisionName_,
            allocationItem.StyleColor,
            allocationItem.GarmentSize,
            allocationItem.ItemDescription8_,
            {
              transaction: trx,
            }
          );
        }

        if (!work_pickpack_item) {
          throw Error(
            `Could not find or create item for goods allocation ${allocationItem.good_allocation_id}`
          );
        }

        containerItems.push({
          work_pickpack_item_id: work_pickpack_item.id,
          quantity: allocationItem.QuantityAllocated,
          order_number: allocationItem.OrderNumber.toString(),
          order_item_number: allocationItem.ItemNumber,
          mo_id: mo.mo_id,
          good_allocation_id: allocationItem.good_allocation_id,
          rosters,
        });
      }

      console.log('containerItems', containerItems);

      const newBin = await createBin(newBinName, {
        is_container: true,
        is_pickable: false,
        allow_items: true,
        container_bin_location_id: bin_location_id,
        transaction: trx,
      });

      for (const item of containerItems) {
        const newBinContent = await PickPackBinContent.query(trx)
          .insert({
            work_pickpack_bin_id: newBin.id,
            ...item,
          })
          .transacting(trx);
      }

      const moPick = await createMoPick(mo_id, containerItems, {
        transaction: trx,
      });

      const binAndContents = await getBinAndContents(newBin.id, {
        transaction: trx,
      });

      return binAndContents;
    } catch (err) {
      console.log('err', err);
      throw err;
    }
  });
};

export const createMoPick = async (
  mo_id: number,
  containerItems: MoContainerItems[],
  options?: {
    transaction?: Knex.Transaction;
  }
) => {
  const trx = options?.transaction;

  const moPick = await PickPackMoPick.query(trx).insert({
    mo_id,
  });

  for (const item of containerItems) {
    const moPickItem = await PickPackMoPickItem.query(trx).insert({
      work_pickpack_mo_pick_id: moPick.id,
      work_pickpack_item_id: item.work_pickpack_item_id,
      quantity: item.quantity,
    });
  }

  return moPick;
};

export const getAllBinOrders = async (options?: {
  transaction?: Knex.Transaction;
}) => {
  const trx = options?.transaction;

  const binContents = await PickPackBinContent.query(trx);

  const binOrders = binContents.reduce((acc, curr) => {
    if (!acc[curr.order_number]) {
      acc[curr.order_number] = {
        order_number: curr.order_number,
        items: [],
      };
    }

    acc[curr.order_number].items.push(curr);

    return acc;
  }, {});

  // check if all order items are available
  const orderItems = await GoodsAllocations.query(trx).where(
    'OrderNumber',
    'in',
    Object.keys(binOrders)
  );

  // group by order number
  const orderItemsByOrderNumber = orderItems.reduce((acc, curr) => {
    if (!acc[curr.OrderNumber]) {
      acc[curr.OrderNumber] = [];
    }

    acc[curr.OrderNumber].push(curr);

    return acc;
  }, {});

  // check if all items are available
  for (const orderNumber of Object.keys(binOrders)) {
    const orderItems = orderItemsByOrderNumber[orderNumber];

    const orderItemsByItemNumber = orderItems.reduce((acc, curr) => {
      if (!acc[curr.ItemNumber]) {
        acc[curr.ItemNumber] = [];
      }

      acc[curr.ItemNumber].push(curr);

      return acc;
    }, {});

    for (const itemNumber of Object.keys(orderItemsByItemNumber)) {
      const orderItems = orderItemsByItemNumber[itemNumber];

      const orderItemsBySize = orderItems.reduce((acc, curr) => {
        if (!acc[curr.GarmentSize]) {
          acc[curr.GarmentSize] = [];
        }

        acc[curr.GarmentSize].push(curr);

        return acc;
      }, {});

      for (const size of Object.keys(orderItemsBySize)) {
        const orderItems = orderItemsBySize[size];

        const totalQuantity = orderItems.reduce((acc, curr) => {
          return acc + curr.QuantityAllocated;
        }, 0);

        const binOrder = binOrders[orderNumber];

        const binOrderItem = binOrder.items.find((item) => {
          return (
            item.order_item_number === itemNumber && item.garment_size === size
          );
        });

        if (binOrderItem.quantity !== totalQuantity) {
          binOrderItem.is_available = false;
        }

        binOrderItem.total_quantity = totalQuantity;
      }
    }
  }

  return Object.values(binOrders);
};
