import { createLogger, format, transports } from 'winston';

const { combine, timestamp, printf, simple } = format;

const logger = createLogger({
  level: 'info',
  format: combine(
    simple(),
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    printf((info) => `[${info.timestamp}] - ${info.level}: ${info.message}`)
  ),
  transports: [
    new transports.File({
      maxFiles: 5,
      maxsize: 5242880,
      filename: `${__dirname}/../../logs/all.log`,
    }),
    new transports.File({
      maxFiles: 5,
      maxsize: 5242880,
      filename: `${__dirname}/../../logs/errors.log`,
      level: 'error',
    }),
    new transports.Console({
      level: 'debug',
    }),
  ],
});

export const buildLogger = (
  service: string
): {
  log: (message: string) => void;
  error: (message: string) => void;
} => {
  return {
    log: (message: string) => {
      logger.log('info', `[${service}] - ${message}`);
    },
    error: (message: string) => {
      logger.error(`[${service}] - ${message}`);
    },
  };
};
