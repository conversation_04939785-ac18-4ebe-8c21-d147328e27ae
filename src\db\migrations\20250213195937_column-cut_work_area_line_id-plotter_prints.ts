import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'plotter_prints',
    (table: Knex.TableBuilder) => {
      table.integer('cut_work_area_line_id').nullable();
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'plotter_prints',
    (table: Knex.TableBuilder) => {
      table.dropColumn('cut_work_area_line_id');
    }
  );
}
