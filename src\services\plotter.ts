import axios from 'axios';
import { log } from 'console';
import currency from 'currency.js';
import * as xl from 'excel4node';
import type { Knex } from 'knex';
import type { ModelObject } from 'objection';

import { knex } from '@app/db';
import { Employee } from '@app/models/employee.schema';
import { MoNumber, MoSize } from '@app/models/pedreria.schema';
import type {
  PlotterCombosShape,
  PlotterPrintGroupShape,
  PlotterPrintShape,
  PlotterRollShape,
} from '@app/models/plotter.schema';
import {
  PlotterCombo,
  PlotterComboLog,
  PlotterPrint,
  PlotterPrintGroups,
  PlotterPrintLog,
  PlotterRoll,
  PlotterRollLog,
} from '@app/models/plotter.schema';
import { Style, StyleCombo } from '@app/models/style.schema';
import { INCHES_PER_YARD } from '@app/utility';

import { createScan } from './scanning';

export interface PrintWithCombos extends PlotterPrintShape {
  combos: PlotterCombosShape[];
}

export interface PrintGroupWithCombos extends PlotterPrintGroupShape {
  combos: PlotterCombosShape[];
}

interface PrintFull extends PrintWithCombos {
  roll: PlotterRollShape;
}

export type MONumberShape = ModelObject<MoNumber>;

export interface MoNumberFull extends MONumberShape {
  sizes: MoSize[];
  styleCombos: StyleCombo[];
  styleDetails: Style;
}

interface ComboFull extends PlotterCombosShape {
  mo: Partial<MoNumberFull>;
}

interface RollFull extends PlotterRollShape {
  prints: PrintWithCombos[];
  recipient_employee_name?: string;
}

interface IPlyLay {
  plys: number;
  end: number;
  length: number;
  totalSizes: number;
}

interface Material {
  CategoryName: string;
  ComponentName: string;
  CustomerNumber: string;
  DatabaseUnits: string;
  ManufactureNumber: string;
  NominalCost: number;
  PONumber: string;
  POScheduleDate: string;
  PartNumber: string;
  QuantityAdjust: number;
  QuantityAllocated: number;
  QuantityOnHand: number;
  QuantityOrdered: number;
  QuantityRequired: number;
  QuantityWithdrawn: number;
  Shortage: number;
  StockOnHand: number;
  StockWarehouse: string;
  SubcategoryName: string;
}

interface ManufacturingOrder {
  customer: string;
  materials: Material[];
  mo_id: number;
  mo_order: string;
  mo_status: string;
  num: string;
  quantity: number;
  required_date: string;
  style: string;
}

interface IExcelFile {
  customer: string;
  mo: string;
  moIdList: string;
  order: string;
  style: string;
  styleCategory: string;
  marker: number;
  partNumber: string;
  extraFabricYards: number;
  requiredYards: number;
  safeExtraFabricYards: number;
  bom: number;
  width: number;
  lengthInch: number;
  totalSizes: number;
  utilizationPercentage: number;
  cutFabricYards: number;
  lengthOfPlotsYards: number;
  plotPlies: number;
  plotRoll: number;
  plotOrder: string;
  extraFabricPlotterYards: number;
  requiredExtraFabricPlotterYards: number;
}

const extractMultiplesNumbers = (inputString: string) => {
  // Regular expression to match "C" followed by numbers (with optional hyphens between them)
  const regex = /(CMB|CC|T|FC|IC|C)(\d+(-\d+)*)/;

  // Search for the pattern in the input string
  const match = inputString.toUpperCase().match(regex);

  // If a match is found, split the matched numbers by hyphen and return as an array of numbers
  if (match) {
    if (inputString.includes('-')) {
      const numbers = match[2]
        .split('-') // Split by hyphen
        .map((num) => parseInt(num, 10)) // Convert to integers
        .filter((n) => !isNaN(n)); // Filter out invalid numbers
      return numbers; // Return the array of numbers
    } else {
      return [parseInt(match[2], 10)];
    }
  }

  // If no match is found or the string doesn't match the pattern, return null
  return null;
};
const createStyleCombos = async (
  combos: Partial<PlotterCombosShape>[],
  styleInformation: Partial<Style>
) => {
  for (const insert of combos) {
    const extractMultiplesNum = extractMultiplesNumbers(insert.combo_number);

    if (!extractMultiplesNum) {
      continue;
    }

    for (const extractNumber of extractMultiplesNum) {
      //create style combos
      await StyleCombo.query().insert({
        style_id: styleInformation.style_id,
        combo_number: extractNumber,
        piece_count: 0,
        is_laser: insert.is_laser,
        is_facing: false,
        default_part_number: insert.part_number,
      });
    }
  }
};
export const createMixMO = async (
  employee: Employee,
  combos: Partial<PlotterCombosShape[]>,
  mos: Partial<MoNumber>[],
  print_groups: boolean,
  options?: { trx: Knex.Transaction }
) => {
  if (!combos.length) {
    throw Error('Combos es requerido');
  }
  if (!mos.length) {
    throw Error('MOs es requerido');
  }

  const trx = options?.trx ?? undefined;
  const allMOs: PlotterCombo[] = [];
  const errors = [];

  //create PlotterPrintGroups if print_groups is true
  let printGroup = null;
  if (print_groups) {
    printGroup = await PlotterPrintGroups.query(trx).insert({
      created_by_employee_id: employee.employee_id,
    });

    await PlotterPrintLog.query(trx).insert({
      plotter_print_id: printGroup.id,
      created_by_employee_id: employee.employee_id,
      type: 'create print group',
      data: {
        name: `${printGroup.id}-MIX`,
      },
    });
  }

  //create prints for each part number inside the combos
  await Promise.all(
    combos.map(async (combo) => {
      let printId = 0;
      if (!combo.plotter_print_id) {
        const insert: Partial<PlotterPrint> = {
          part_number: combo.part_number,
          created_by_employee_id: employee.employee_id,
          print_group_id: printGroup ? printGroup.id : null,
        };
        const print = await PlotterPrint.query(trx).insert(insert);
        const printName = `${print.id}-${print.part_number}`;
        printId = print.id;
        await PlotterPrint.query(trx).updateAndFetchById(print.id, {
          name: printName,
        });

        await PlotterPrintLog.query(trx).insert({
          plotter_print_id: print.id,
          created_by_employee_id: employee.employee_id,
          type: 'create',
          data: {
            ...insert,
            name: printName,
          },
        });
      }

      //add print to combo
      for (const mo of mos) {
        const existingCombo = await PlotterCombo.query(trx)
          .where('mo_id', mo.mo_id)
          .where('combo_number', combo.combo_number)
          .whereNull('removed_at')
          .first();
        if (existingCombo) {
          errors.push({
            mo_id: mo.mo_id,
            combo_number: combo.combo_number,
            message: 'Nombre del combo ya existe',
          });
          continue;
        }
        const insert = {
          mo_id: mo.mo_id,
          combo_number: combo.combo_number,
          part_number: combo.part_number,
          name: combo.name,
          description: combo.description,
          created_by_employee_id: employee.employee_id,
          is_laser: combo.is_laser ?? false,
          plotter_print_id: combo.plotter_print_id
            ? combo.plotter_print_id
            : printId,
          style_combos: combo.style_combos,
        };
        const comboWithPrint = await PlotterCombo.query(trx).insert(insert);

        await PlotterComboLog.query(trx).insert({
          plotter_combo_id: comboWithPrint.id,
          created_by_employee_id: employee.employee_id,
          type: 'create',
          data: insert,
        });

        if (comboWithPrint) {
          allMOs.push(comboWithPrint);
        }
      }
    })
  );

  //check if its only one style
  const getStyles = mos.map((mo) => mo.style);
  if (new Set(getStyles).size === 1) {
    const style = getStyles[0];
    if (style) {
      const styleInformation = await Style.query()
        .where('style_number', style)
        .first();
      //if style combos is empty, add new combos
      const getStyleCombos = await StyleCombo.query()
        .join('styles', 'styles.style_id', 'style_combos.style_id')
        .where('styles.style_number', style);

      if (getStyleCombos.length === 0 && styleInformation) {
        await createStyleCombos(combos, styleInformation);
      }
    }
  }

  return { allMOs, errors };
};

export const getCombo = async (
  id: number,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  const trx = options?.trx ?? undefined;
  if (!id) {
    throw Error('ID de combo es requerido');
  }

  const combo = await PlotterCombo.query(trx).findById(id);

  return combo;
};

export const getCombosByMo = async (
  moId: number,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  const trx = options?.trx ?? undefined;
  if (!moId) {
    throw Error('MO ID es requerido');
  }

  const combos = await PlotterCombo.query(trx)
    .leftJoin(
      'plotter_prints',
      'plotter_prints.id',
      'plotter_combos.plotter_print_id'
    )
    .select(
      'plotter_combos.*',
      'plotter_prints.print_group_id as plotter_print_group_id'
    )
    .where('plotter_combos.mo_id', moId)
    .whereNull('plotter_combos.removed_at');

  return combos;
};

export const createCombo = async (
  employee: Employee,
  input: Partial<PlotterCombosShape>
) => {
  if (!input.mo_id) {
    throw Error('MO ID es requerido');
  }
  if (!input.combo_number) {
    throw Error('Numero de combo es requerido');
  }
  if (!input.part_number) {
    throw Error('Part Number es requerido');
  }
  if (!input.name) {
    throw Error('Name es requerido');
  }
  if (!input.description) {
    throw Error('Descripcion es requerida');
  }

  const insertData = {
    mo_id: input.mo_id,
    combo_number: input.combo_number,
    part_number: input.part_number,
    name: input.name,
    description: input.description,
    created_by_employee_id: employee.employee_id,
    is_laser: input.is_laser ?? false,
    style_combos: input.style_combos,
  };

  return await knex.transaction(async (trx) => {
    // check for existing combo name
    const existingCombo = await PlotterCombo.query(trx)
      .where('mo_id', input.mo_id)
      .where('name', input.name)
      .whereNull('removed_at')
      .first();
    if (existingCombo) {
      throw Error('Nombre del combo ya existe');
    }
    const combo = await PlotterCombo.query(trx).insert(insertData);

    await PlotterComboLog.query(trx).insert({
      plotter_combo_id: combo.id,
      created_by_employee_id: employee.employee_id,
      type: 'create',
      data: insertData,
    });

    return combo;
  });
};

export const updateCombo = async (
  employee: Employee,
  id: number,
  input: Partial<PlotterCombosShape>
) => {
  if (!id) {
    throw Error('ID es requerido');
  }
  return await knex.transaction(async (trx) => {
    const currentCombo = await PlotterCombo.query(trx).findById(id);
    if (!currentCombo) {
      throw Error('Combo no encontrado');
    }

    if (
      currentCombo.plotter_print_id &&
      input.part_number &&
      input.part_number !== currentCombo.part_number
    ) {
      //UPDATE PRINT PART NUMBER
      const print = await PlotterPrint.query(trx).findById(
        currentCombo.plotter_print_id
      );
      if (print) {
        await PlotterPrint.query(trx).updateAndFetchById(print.id, {
          part_number: input.part_number,
          name: `${print.id}-${input.part_number}`,
        });

        //log print name  and part number update
        await PlotterPrintLog.query(trx).insert({
          plotter_print_id: print.id,
          created_by_employee_id: employee.employee_id,
          type: 'update',
          data: {
            part_number: input.part_number,
            name: `${print.id}-${input.part_number}`,
          },
        });
      }
    }

    const newUpdate = {
      combo_number: input.combo_number ?? currentCombo.combo_number,
      name: input.name ?? currentCombo.name,
      part_number: input.part_number ?? currentCombo.part_number,
      description: input.description ?? currentCombo.description,
      is_laser: input.is_laser ?? currentCombo.is_laser,
    };

    // check for existing combo name
    const existingCombo = await PlotterCombo.query(trx)
      .where('mo_id', currentCombo.mo_id)
      .where('name', newUpdate.name)
      .whereNot('id', currentCombo.id)
      .whereNull('removed_at')
      .first();
    if (existingCombo) {
      throw Error('Nombre del combo ya existe');
    }

    //get all prints joining with combos
    const allCombos = await PlotterCombo.query(trx).where(
      'plotter_print_id',
      currentCombo.plotter_print_id
    );

    if (allCombos && allCombos.length > 1) {
      //foreach allCombos
      let returnItem: PlotterCombo;
      for (const combo of allCombos) {
        returnItem = await PlotterCombo.query(trx).updateAndFetchById(
          combo.id,
          newUpdate
        );
        await PlotterComboLog.query(trx).insert({
          plotter_combo_id: combo.id,
          created_by_employee_id: employee.employee_id,
          type: 'update',
          data: newUpdate,
        });
      }
      return returnItem;
    } else {
      const combo = await PlotterCombo.query(trx).updateAndFetchById(
        id,
        newUpdate
      );

      await PlotterComboLog.query(trx).insert({
        plotter_combo_id: combo.id,
        created_by_employee_id: employee.employee_id,
        type: 'update',
        data: newUpdate,
      });

      return combo;
    }
  });
};

export const getPrint = async (
  id: number,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  const trx = options?.trx ?? undefined;
  if (!id) {
    throw Error('ID del print es requerido');
  }

  const print = await PlotterPrint.query(trx).findById(id);
  if (!print) {
    throw Error('Print no encontrado');
  }

  const combos = await PlotterCombo.query(trx)
    .where('plotter_print_id', id)
    .whereNull('removed_at');

  const moIds = combos.map((combo) => combo.mo_id);
  const mos = await MoNumber.query(trx).whereIn('mo_id', moIds);
  const mosSizes = await MoSize.query(trx).whereIn('mo_id', moIds);

  const mosFull = await Promise.all(
    mos.map(async (mo): Promise<MoNumberFull> => {
      const styleDetails = await Style.query(trx)
        .where('style_number', mo.style)
        .first();
      const styleCombos = await StyleCombo.query(trx)
        .where('style_id', styleDetails.style_id)
        .whereNull('style_combos.deleted_at');
      const sizes = mosSizes.filter((moSize) => moSize.mo_id === mo.mo_id);
      return {
        ...mo,
        sizes,
        styleDetails,
        styleCombos,
      };
    })
  );

  const combosFull = combos.map((combo): ComboFull => {
    const mo = mosFull.find((searchMO) => searchMO.mo_id === combo.mo_id);
    return {
      ...combo,
      mo,
    };
  });

  const roll = print.plotter_roll_id
    ? await PlotterRoll.query(trx).findById(print.plotter_roll_id)
    : null;

  const printFull: PrintFull = {
    ...print,
    roll,
    combos: combosFull,
  };

  return printFull;
};

export const createPrintFromCombo = async (
  employee: Employee,
  comboId: number,
  trx?: Knex.Transaction
) => {
  if (!comboId) {
    throw Error('Combo ID es requerido');
  }
  const combo = await PlotterCombo.query(trx).findById(comboId);
  if (!combo) {
    throw Error('Combo no encontrado');
  }

  if (combo.plotter_print_id) {
    throw Error('Combo ya tiene print');
  }

  const plotterPrintCreation = {
    part_number: combo.part_number,
    created_by_employee_id: employee.employee_id,
  };

  const print = await PlotterPrint.query(trx).insert(plotterPrintCreation);

  // update print name Fusing print id
  const printName = `${print.id}-${print.part_number}`;

  await PlotterPrint.query(trx).updateAndFetchById(print.id, {
    name: printName,
  });

  // update combo using print id
  await PlotterCombo.query(trx).updateAndFetchById(combo.id, {
    plotter_print_id: print.id,
  });

  await PlotterComboLog.query(trx).insert({
    plotter_combo_id: combo.id,
    created_by_employee_id: employee.employee_id,
    type: 'update',
    data: {
      plotter_print_id: print.id,
    },
  });

  await PlotterPrintLog.query(trx).insert({
    plotter_print_id: combo.id,
    created_by_employee_id: employee.employee_id,
    type: 'create',
    data: {
      ...plotterPrintCreation,
      name: printName,
    },
  });

  await PlotterPrintLog.query(trx).insert({
    plotter_print_id: combo.id,
    created_by_employee_id: employee.employee_id,
    type: 'addedCombo',
    data: {
      plotter_combo_id: combo.id,
      name: combo.name,
      description: combo.description,
    },
  });

  const printFull = await getPrint(print.id, { trx });

  return printFull;
};

export const createMultiplesCombos = async (
  employee: Employee,
  input: Partial<PlotterCombosShape>[]
) => {
  if (!input) {
    throw Error('Input es requerido');
  }

  const insertedCombos: PlotterCombo[] = [];

  for (const insert of input) {
    const moInformation = await MoNumber.query()
      .where('mo_id', insert.mo_id)
      .first();

    const styleInformation = await Style.query()
      .where('style_number', moInformation.style)
      .first();
    //if style combos is empty, add new combos
    const getStyleCombos = await StyleCombo.query()
      .join('styles', 'styles.style_id', 'style_combos.style_id')
      .where('styles.style_number', moInformation.style);

    if (getStyleCombos.length === 0 && styleInformation) {
      await createStyleCombos(input, styleInformation);
    }

    insertedCombos.push(
      await knex.transaction(async (trx) => {
        const combo = await PlotterCombo.query(trx).insert({
          mo_id: insert.mo_id,
          combo_number: insert.combo_number,
          part_number: insert.part_number,
          name: insert.name,
          description: insert.description,
          is_laser: insert.is_laser,
          created_by_employee_id: employee.employee_id,
          style_combos: insert.style_combos,
        });

        if (!combo) {
          throw Error('No se pudo crear combos');
        }

        //check if create_print and create it
        if (insert.create_print) {
          await createPrintFromCombo(employee, combo.id, trx);
        }

        await PlotterComboLog.query(trx).insert({
          plotter_combo_id: combo.id,
          created_by_employee_id: employee.employee_id,
          type: 'create',
          data: insert,
        });

        return combo;
      })
    );
  }

  return insertedCombos;
};

const updatePrintInternal = async (
  employee: Employee,
  currentPrint: PlotterPrint,
  input: Partial<PlotterPrintShape>,
  trx: Knex.Transaction
) => {
  if (input.part_number && input.part_number !== currentPrint.part_number) {
    throw Error('No se puede cambiar el part number de un print');
  }

  let newLength = currentPrint.length;
  let newFabricYards = currentPrint.plys_fabric_yards;
  let newTotalSizes = currentPrint.total_sizes;
  let newQuantity = currentPrint.quantity;
  const currentModelsNumber = currentPrint.models_number;
  const currentTotalPieces = currentPrint.total_pieces;

  const combos = await PlotterCombo.query(trx)
    .where('plotter_print_id', currentPrint.id)
    .whereNull('removed_at');

  const checkIsLaser = combos.some((cmb) => +cmb.is_laser === 1);

  if (input.plys && !checkIsLaser) {
    if (!Array.isArray(input.plys)) {
      throw Error('Plys debe ser un array');
    }
    // check each part,  length should be equal to end minus previous end
    let finalLength = 0;
    let totalYard = 0;
    let totalSizes = 0;
    let totalQuantity = 0;
    newFabricYards = 0;
    for (const [index, ply] of input.plys.entries()) {
      if (
        typeof ply.plys !== 'number' ||
        typeof ply.end !== 'number' ||
        typeof ply.length !== 'number' ||
        typeof ply.totalSizes !== 'number'
      ) {
        throw Error(
          'Plys debe ser un array de objetos con propiedades plys, end, length y sizes'
        );
      }
      if (!ply.plys || ply.plys <= 0) {
        throw Error('Ply debe tener al menos 1 ply');
      }
      if (!ply.length || ply.length <= 0) {
        throw Error('Ply debe tener un length mayor a 0');
      }
      if (!ply.totalSizes || ply.totalSizes <= 0) {
        throw Error('Ply debe tener un totalSizes mayor a 0');
      }
      if (!ply.end || ply.end <= 0) {
        throw Error('Ply debe tener un end mayor a 0');
      }
      finalLength = ply.end;
      totalSizes += ply.totalSizes;
      totalQuantity += ply.plys * ply.totalSizes;
      totalYard = currency(
        currency(ply.length, { precision: 4 }).multiply(ply.plys).value,
        { precision: 4 }
      ).divide(INCHES_PER_YARD).value;
      newFabricYards = currency(newFabricYards, { precision: 4 }).add(
        totalYard
      ).value;
      if (index === 0) {
        if (ply.end.toFixed(2) !== ply.length.toFixed(2)) {
          throw Error(`Ply length y end no puede sumar en index ${index}`);
        }
      } else {
        const endCheck = currency(input.plys[index - 1].end, {
          precision: 4,
        }).add(ply.length).value;
        if (ply.end.toFixed(2) !== endCheck.toFixed(2)) {
          throw Error(
            `Ply length y end no puede sumar en index ${index}, ${endCheck} vs ${ply.end}`
          );
        }
      }
    }
    newLength = finalLength;
    newTotalSizes = totalSizes;
    newQuantity = totalQuantity;
  }

  if (
    input.length &&
    !checkIsLaser &&
    input.length.toFixed(2) !== newLength.toFixed(2)
  ) {
    throw Error('Length debe ser igual al Length del print');
  }

  if (input.length && !checkIsLaser && input.length !== newLength) {
    input.length = newLength;
  }

  if (
    input.total_sizes &&
    !checkIsLaser &&
    input.total_sizes !== newTotalSizes
  ) {
    throw Error('Total Sizes debe ser igual al Total Sizes del print');
  }

  if (input.quantity && !checkIsLaser && input.quantity !== newQuantity) {
    throw Error('Quantity debe ser igual al Quantity del print');
  }

  if (input.name && input.name !== currentPrint.name) {
    throw Error('No se puede cambiar el nombre de un print');
  }

  const newUpdate: Partial<PlotterPrint> = {
    plys: input.plys ?? currentPrint.plys,
    width: input.width ?? currentPrint.width,
    utilization: input.utilization ?? currentPrint.utilization,
    quantity: input.quantity ?? newQuantity,
    length: input.length ?? newLength,
    total_sizes: input.total_sizes ?? newTotalSizes,
    plys_fabric_yards: input.plys_fabric_yards ?? newFabricYards,
    models_number: input.models_number ?? currentModelsNumber,
    total_pieces: input.total_pieces ?? currentTotalPieces,
    extra_fabric: input.extra_fabric ?? currentPrint.extra_fabric,
    print_group_sort: input.print_group_sort ?? currentPrint.print_group_sort,
  };

  const print = await PlotterPrint.query(trx).updateAndFetchById(
    currentPrint.id,
    newUpdate
  );

  await PlotterPrintLog.query(trx).insert({
    plotter_print_id: print.id,
    created_by_employee_id: employee.employee_id,
    type: 'update',
    data: newUpdate,
  });

  return print;
};

export const updatePrint = async (
  employee: Employee,
  id: number,
  input: Partial<PlotterPrint>
) => {
  if (!id) {
    throw Error('ID es requerido');
  }

  return await knex.transaction(async (trx) => {
    const currentPrint = await PlotterPrint.query(trx).findById(id);
    if (!currentPrint) {
      throw Error('Print no encontrado');
    }
    const print = await updatePrintInternal(employee, currentPrint, input, trx);

    const printFull = await getPrint(print.id, { trx });

    return printFull;
  });
};

export const updatePrintByName = async (
  employee: Employee,
  input: Partial<PlotterPrint>
) => {
  if (!input.name) {
    throw Error('Descripcion es requerida');
  }

  return await knex.transaction(async (trx) => {
    const currentPrint = await PlotterPrint.query(trx)
      .where('name', input.name)
      .first();
    if (!currentPrint) {
      throw Error('Print no encontrado');
    }

    const print = await updatePrintInternal(employee, currentPrint, input, trx);

    const printFull = await getPrint(print.id, { trx });

    return printFull;
  });
};

export const removeComboFromPrint = async (
  employee: Employee,
  comboId: number
) => {
  if (!comboId) {
    throw Error('Combo ID es requerido');
  }

  return await knex.transaction(async (trx) => {
    const combo = await PlotterCombo.query(trx).findById(comboId);
    if (!combo) {
      throw Error('Combo no encontrado');
    }

    if (!combo.plotter_print_id) {
      throw Error('Combo no tiene print');
    }

    const print = await PlotterPrint.query(trx).findById(
      combo.plotter_print_id
    );

    // update combo using print id
    await PlotterCombo.query(trx).updateAndFetchById(combo.id, {
      plotter_print_id: null,
    });

    if (print) {
      await PlotterPrintLog.query(trx).insert({
        plotter_print_id: combo.id,
        created_by_employee_id: employee.employee_id,
        type: 'removedCombo',
        data: {
          plotter_combo_id: combo.id,
          name: combo.name,
          description: combo.description,
        },
      });
    }

    return combo;
  });
};

export const addComboToPrint = async (
  employee: Employee,
  comboId: number,
  printId: number
) => {
  if (!comboId) {
    throw Error('Combo ID es requerido');
  }

  if (!printId) {
    throw Error('Print ID es requerido');
  }

  return await knex.transaction(async (trx) => {
    const combo = await PlotterCombo.query(trx).findById(comboId);
    if (!combo) {
      throw Error('Combo no encontrado');
    }

    const print = await PlotterPrint.query(trx).findById(printId);
    if (!print) {
      throw Error('Print no encontrado');
    }

    if (combo.plotter_print_id && combo.plotter_print_id !== printId) {
      throw Error('Combo ya tiene print');
    }

    if (combo.part_number !== print.part_number) {
      throw Error('Combo y print deben tener el mismo part number');
    }

    // update combo using print id
    await PlotterCombo.query(trx).updateAndFetchById(combo.id, {
      plotter_print_id: printId,
    });

    await PlotterComboLog.query(trx).insert({
      plotter_combo_id: combo.id,
      created_by_employee_id: employee.employee_id,
      type: 'update',
      data: {
        plotter_print_id: print.id,
      },
    });

    await PlotterPrintLog.query(trx).insert({
      plotter_print_id: combo.id,
      created_by_employee_id: employee.employee_id,
      type: 'addedCombo',
      data: {
        plotter_combo_id: combo.id,
        name: combo.name,
        description: combo.description,
      },
    });

    return combo;
  });
};

const getPrintsWithCombos = async (
  prints: PlotterPrint[],
  options?: {
    trx: Knex.Transaction;
  }
) => {
  const trx = options?.trx ?? undefined;

  const combos = await PlotterCombo.query(trx)
    .whereIn(
      'plotter_print_id',
      prints.map((print) => print.id)
    )
    .whereNull('removed_at');

  const moIds = combos.map((combo) => combo.mo_id);

  const mos = await MoNumber.query(trx).whereIn('mo_id', moIds);

  const mosSizes = await MoSize.query(trx).whereIn('mo_id', moIds);

  const mosFull = await Promise.all(
    mos.map((mo): Partial<MoNumberFull> => {
      const sizes = mosSizes.filter((moSize) => moSize.mo_id === mo.mo_id);
      return {
        ...mo,
        sizes,
      };
    })
  );

  const combosFull = combos.map((combo): ComboFull => {
    const mo = mosFull.find((moFull) => moFull.mo_id === combo.mo_id);
    return {
      ...combo,
      mo,
    };
  });

  const printsFull = prints.map((print): PrintWithCombos => {
    const combos = combosFull.filter(
      (combo) => combo.plotter_print_id === print.id
    );
    return {
      ...print,
      combos,
    };
  });

  return printsFull;
};

export const getPlotterPrintsWithCombosForMo = async (
  moId: number,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  const trx = options?.trx ?? undefined;
  if (!moId) {
    throw Error('MO ID es requerido');
  }

  const combos = await PlotterCombo.query(trx)
    .where('mo_id', moId)
    .whereNull('removed_at');

  const printIds = combos.map((combo) => combo.plotter_print_id);

  const prints = await PlotterPrint.query(trx).whereIn('id', printIds);

  const printsFull = await getPrintsWithCombos(prints, { trx });

  return printsFull;
};

const getRollInternal = async (
  roll: Partial<PlotterRollShape>,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  const trx = options?.trx ?? undefined;

  const prints = await PlotterPrint.query(trx)
    .join(
      'plotter_combos',
      'plotter_prints.id',
      'plotter_combos.plotter_print_id'
    )
    .where('plotter_prints.plotter_roll_id', roll.id)
    .whereNull('plotter_combos.removed_at')
    .groupBy('plotter_prints.id')
    .orderBy('roll_sort', 'desc');

  const plotterPrintsWithCombos = await getPrintsWithCombos(prints, { trx });
  let recipentEmployeeName = '';
  if (roll.recipient_employee_id) {
    const recipientEmployee = await Employee.query(trx)
      .where('employee_id', roll.recipient_employee_id)
      .first();
    if (recipientEmployee) {
      recipentEmployeeName =
        recipientEmployee.first_name + ' ' + recipientEmployee.last_name;
    }
  }

  const rollFull: Partial<RollFull> = {
    ...roll,
    prints: plotterPrintsWithCombos,
    recipient_employee_name: recipentEmployeeName,
  };

  return rollFull;
};

const createRollInternal = async (
  employee: Employee,
  trx: Knex.Transaction
) => {
  const roll = await PlotterRoll.query(trx).insert({
    created_by_employee_id: employee.employee_id,
  });

  // update name to PLR-<id>
  const rollName = `PLR-${roll.id}`;

  await PlotterRoll.query(trx).updateAndFetchById(roll.id, { name: rollName });

  await PlotterRollLog.query(trx).insert({
    plotter_roll_id: roll.id,
    created_by_employee_id: employee.employee_id,
    type: 'create',
    data: {
      name: rollName,
    },
  });

  return roll;
};

const addPrintToRollInternal = async (
  employee: Employee,
  print: PlotterPrint,
  roll: Partial<PlotterRoll>,
  trx: Knex.Transaction
) => {
  // update combo using print id
  if (print.plotter_roll_id && print.plotter_roll_id !== roll.id) {
    throw Error('Print ya tiene rollo');
  }

  if (print.plotter_roll_id === roll.id) {
    return roll;
  }

  const getTotalPrintsInRoll = await PlotterPrint.query(trx).where(
    'plotter_roll_id',
    roll.id
  );

  await PlotterPrint.query(trx).updateAndFetchById(print.id, {
    plotter_roll_id: roll.id,
    roll_sort: +getTotalPrintsInRoll.length + 1,
  });

  await PlotterPrintLog.query(trx).insert({
    plotter_print_id: print.id,
    created_by_employee_id: employee.employee_id,
    type: 'update',
    data: {
      plotter_roll_id: roll.id,
    },
  });

  await PlotterRollLog.query(trx).insert({
    plotter_roll_id: roll.id,
    created_by_employee_id: employee.employee_id,
    type: 'addedPrint',
    data: {
      plotter_print_id: print.id,
      name: print.name,
    },
  });

  return roll;
};

export const getRoll = async (id: number) => {
  if (!id) {
    throw Error('ID es requerido');
  }

  const roll = await PlotterRoll.query().findById(id);
  if (!roll) {
    throw Error('Rollo no encontrado');
  }

  return getRollInternal(roll);
};

export const createRoll = async (employee: Employee) => {
  return await knex.transaction(async (trx) => {
    const roll = await createRollInternal(employee, trx);
    return getRollInternal(roll, { trx });
  });
};

export const addPrintToRoll = async (
  employee: Employee,
  printId: number,
  rollId: number
) => {
  if (!printId) {
    throw Error('Print ID es requerido');
  }

  if (!rollId) {
    throw Error('Roll ID es requerido');
  }

  const trx = await knex.transaction();
  try {
    const print = await PlotterPrint.query(trx).findById(printId);
    if (!print) {
      throw Error('Print no encontrado');
    }

    const roll = await PlotterRoll.query(trx).findById(rollId);
    if (!roll) {
      throw Error('Rollo no encontrado');
    }

    await addPrintToRollInternal(employee, print, roll, trx);

    const rollFull = await getRollInternal(roll, { trx });

    trx.commit();
    return rollFull;
  } catch (error) {
    trx.rollback();
    throw error;
  }
};

export const createRollWithPrint = async (
  employee: Employee,
  printId: number
) => {
  if (!printId) {
    throw Error('Print ID es requerido');
  }

  const trx = await knex.transaction();
  try {
    const print = await PlotterPrint.query(trx).findById(printId);
    if (!print) {
      throw Error('Print no encontrado');
    }

    const roll = await createRollInternal(employee, trx);

    await addPrintToRollInternal(employee, print, roll, trx);

    const rollFull = await getRollInternal(roll, { trx });

    trx.commit();
    return rollFull;
  } catch (error) {
    trx.rollback();
    throw error;
  }
};

export const removePrintFromRoll = async (
  employee: Employee,
  printId: number
) => {
  if (!printId) {
    throw Error('Print ID es requerido');
  }

  return await knex.transaction(async (trx) => {
    const print = await PlotterPrint.query(trx).findById(printId);
    if (!print) {
      throw Error('Print no encontrado');
    }

    if (!print.plotter_roll_id) {
      throw Error('Print no tiene rollo');
    }

    const roll = await PlotterRoll.query(trx).findById(print.plotter_roll_id);

    await PlotterPrint.query(trx).updateAndFetchById(print.id, {
      plotter_roll_id: null,
    });

    // update roll_sort for prints in roll
    const prints = await PlotterPrint.query(trx)
      .where('plotter_roll_id', roll.id)
      .orderBy('roll_sort', 'asc');

    for (const [index, print] of prints.entries()) {
      await PlotterPrint.query(trx).updateAndFetchById(print.id, {
        roll_sort: index + 1,
      });
    }

    if (roll) {
      await PlotterRollLog.query(trx).insert({
        plotter_roll_id: roll.id,
        created_by_employee_id: employee.employee_id,
        type: 'removedPrint',
        data: {
          plotter_print_id: print.id,
          name: print.name,
        },
      });
    }

    const rollFull = await getRollInternal(roll, { trx });

    return rollFull;
  });
};

export const getPrintWithoutRoll = async (options?: {
  trx: Knex.Transaction;
}) => {
  try {
    const trx = options?.trx ?? undefined;
    const prints = await PlotterPrint.query(trx).whereNull('plotter_roll_id');

    if (!prints) {
      throw Error('No hay prints sin rollo');
    }

    const printFullList = await Promise.all(
      prints.map(async (print): Promise<PrintFull> => {
        const combos = await PlotterCombo.query(trx)
          .where('plotter_print_id', print.id)
          .whereNull('removed_at');
        const moIds = combos.map((combo) => combo.mo_id);
        const mos = await MoNumber.query(trx).whereIn('mo_id', moIds);
        const mosSizes = await MoSize.query(trx).whereIn('mo_id', moIds);

        const combosFull = combos.map((combo): ComboFull => {
          const mo = mos.find((mo) => mo.mo_id === combo.mo_id);

          const fullMO: Partial<MoNumberFull> = {
            ...mo,
            sizes: mosSizes.filter((moSize) => moSize.mo_id === mo.mo_id),
          };

          return {
            ...combo,
            mo: fullMO,
          };
        });

        return {
          ...print,
          roll: null,
          combos: combosFull,
        };
      })
    );
    return printFullList;
  } catch (error) {
    console.log(error);
  }
};

export const updateRollSort = async (
  rollId: number,
  prints: Partial<PrintWithCombos[]>,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  try {
    const trx = options?.trx ?? undefined;
    const roll = await PlotterRoll.query(trx).findById(rollId);
    if (!roll) {
      throw Error('Rollo no encontrado');
    }

    const printsToUpdate = prints.filter((print) => print.id);
    for (const [index, print] of printsToUpdate.entries()) {
      await PlotterPrint.query(trx).updateAndFetchById(print.id, {
        roll_sort: index + 1,
      });
    }
    return true;
  } catch (error) {
    console.log(error);
  }
};

export const addPlotterMachineToRoll = async (
  rollId: number,
  matchineNumber: number,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  try {
    const trx = options?.trx ?? undefined;
    const roll = await PlotterRoll.query(trx).updateAndFetchById(rollId, {
      plotter_machine_number: matchineNumber,
    });

    if (!roll) {
      throw Error('Rollo no encontrado');
    }
    return true;
  } catch (error) {
    console.log(error);
  }
};

export const addPlotterPrintToRollByMO = async (
  employee: Employee,
  rollId: number,
  mo: string,
  company?: number,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  const trx = options?.trx ?? undefined;
  const roll = { id: rollId };
  let MO: MoNumber | undefined;
  let groupAreaBarcode = '';
  //check if mo it start with PPMO, APPMO or it contains a backslash
  if (mo.match(/PPMO|APPMO|^\d+\/\d+$/)) {
    let companyCode = company ?? 0;
    let barcode = mo;
    if (mo.match(/PPMO/)) {
      companyCode = 1;
    } else if (mo.match(/APPMO/)) {
      companyCode = 2;
      barcode = mo.replace('APPMO', 'PPMO');
    } else {
      companyCode = 3;
    }

    const getRoll = await getRollInternal(roll, { trx });

    if (getRoll.prints.length > 0) {
      if (getRoll.prints[0].combos[0].mo.company_code !== companyCode) {
        throw Error(
          'No se puede agregar un mo de otra empresa a un rollo de otra empresa'
        );
      }
    }

    //search mo by mo_barcode
    MO = await MoNumber.query(trx)
      .where('mo_barcode', barcode)
      .where('company_code', companyCode)
      .first();
  } else {
    //search mo by num and company_code
    MO = await MoNumber.query(trx)
      .where('num', mo)
      .where('company_code', company)
      .first();
  }

  if (!MO) {
    throw Error('MO no encontrado');
  }

  if (MO.company_code === 1) {
    groupAreaBarcode = 'PPAD83209';
  } else if (MO.company_code === 2) {
    groupAreaBarcode = 'PPAD79202';
  } else {
    groupAreaBarcode = 'C10';
  }

  //get all prints joining with combos
  const prints = await PlotterPrint.query(trx)
    .join(
      'plotter_combos as combos',
      'combos.plotter_print_id',
      'plotter_prints.id'
    )
    .whereNull('plotter_roll_id')
    .where('combos.mo_id', MO.mo_id)
    .orderBy('plotter_prints.print_group_sort', 'asc');

  if (prints.length > 0) {
    //for each mos createScan
    const firstPrint = prints[0];
    const getCombos = await PlotterCombo.query(trx)
      .where('plotter_print_id', firstPrint.id)
      .whereNull('removed_at');

    const moIds = getCombos.map((combo) => combo.mo_id);
    const mos = await MoNumber.query(trx).whereIn('mo_id', moIds);
    for (const newMO of mos) {
      await createScan({
        mo_barcode:
          newMO.company_code === 2 ? `A${newMO.mo_barcode}` : newMO.mo_barcode,
        group_barcode: groupAreaBarcode,
        quantity_reported: newMO.quantity,
        employee_id: employee.employee_id,
        type_action: 'FINISH',
        mo_id: null,
        affected_units: null,
        work_area_group_id: null,
        work_area_id: null,
        work_area_line_id: null,
        partial_option: null,
        work_ticket_id: null,
        update_customer: null,
        work_voucher_id: null,
      });
    }
    for (const print of prints) {
      await addPrintToRollInternal(employee, print, roll, trx);
    }
  }

  return getRollInternal(roll, { trx });
};

export const getPrintsByMO = async (
  mo: string,
  company?: number,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  if (!mo) {
    throw Error('mo es requerido');
  }

  const trx = options?.trx ?? undefined;

  let MO: MoNumber | undefined;
  //check if mo it start with PPMO, APPMO or it contains a backslash
  if (mo.match(/PPMO|APPMO|\\/)) {
    let companyCode = company ?? 0;
    let barcode = mo;
    if (mo.match(/PPMO/)) {
      companyCode = 1;
    } else if (mo.match(/APPMO/)) {
      companyCode = 2;
      barcode = mo.replace('APPMO', 'PPMO');
    } else {
      companyCode = 3;
    }

    //search mo by mo_barcode
    MO = await MoNumber.query(trx)
      .where('mo_barcode', barcode)
      .where('company_code', companyCode)
      .first();
  } else {
    //search mo by num and company_code
    MO = await MoNumber.query(trx)
      .where('num', mo)
      .where('company_code', company)
      .first();
  }

  if (!MO) {
    throw Error('MO no encontrado');
  }

  //get all prints joining with combos
  const prints = await PlotterPrint.query(trx)
    .join(
      'plotter_combos as combos',
      'combos.plotter_print_id',
      'plotter_prints.id'
    )
    .whereNull('combos.removed_at')
    .where('combos.mo_id', MO.mo_id);

  return prints;
};

export const createExcelFileForWarehouse = async (rollId: number) => {
  const roll = await PlotterRoll.query().findById(rollId);
  if (!roll) {
    throw Error('Rollo no encontrado');
  }
  //get all prints joining with combos by roll id
  const fullRoll = await getRollInternal(roll);

  if (fullRoll && fullRoll.prints.length === 0) {
    throw Error('El rollo no tiene prints');
  }

  let row = 2;
  let getMaterials: ManufacturingOrder[] = [];
  const moIdMap = new Set<number>();
  let isVarpro = false;
  let customerName = '';
  let currentComboItem = 0;
  const fileInformation: IExcelFile[] = [];

  for (const print of fullRoll.prints) {
    //get all MOs in combos
    for (const combo of print.combos) {
      if (combo.mo.company_code === 1) {
        isVarpro = true;
        customerName = combo.mo.customer;
        const moId = combo.mo_id;
        if (!moIdMap.has(moId)) {
          moIdMap.add(moId);
        }
      } else {
        customerName = combo.mo.customer;
      }
    }
  }

  //if there are more than 1 mo in the roll
  if (moIdMap.size > 0) {
    //get all mo_ids
    const moIds = Array.from(moIdMap);
    const RestfulPolyApi = process.env.RESTFUL_POLY + '/mos/materials';
    //get all materiales
    getMaterials = await axios
      .post(RestfulPolyApi, {
        mo_ids: moIds,
        categorys: ['Fabric'],
      })
      .then((res) => res.data);
  }

  //create array of excelfile
  for (const print of fullRoll.prints) {
    let returnPlyItems: IPlyLay[] = [];
    let totalCutFabricYards = 0;
    let totalPlyYards = 0;
    let extraFabricIn = 0;
    let safeExtraFabricIn = 0;
    let totalLengthOfPlot = 0;
    let plySections = '';
    let style = '';
    let styleCategory = '';
    let order = '';
    let num = '';
    let numIdList = '';
    let CustomField = '';
    let company_code = 0;

    if (print.combos.length > 0) {
      if (print.plys) {
        returnPlyItems = print.plys.map((plyItem: IPlyLay) => {
          const newlay: IPlyLay = {
            plys: plyItem.plys,
            end: plyItem.end,
            length: plyItem.length,
            totalSizes: plyItem.totalSizes,
          };
          return newlay;
        });

        returnPlyItems.forEach((plyItem) => {
          totalCutFabricYards += plyItem.length * plyItem.plys;
          totalPlyYards += plyItem.plys;
          totalLengthOfPlot += plyItem.length;
          plySections += `|${plyItem.plys}x${plyItem.length}|`;
        });
        //get max plys from returnPlyItems
        let extraInches = 3;
        if (customerName !== '' && customerName === 'SAFARILAND') {
          extraInches = 1.5;
        }
        extraFabricIn = isVarpro
          ? totalPlyYards * extraInches
          : Math.max(...returnPlyItems.map((plyItem) => plyItem.plys)) * 2;

        safeExtraFabricIn = isVarpro
          ? totalPlyYards * 1
          : Math.max(...returnPlyItems.map((plyItem) => plyItem.plys)) * 1;
      }

      if (print.combos.length === 1) {
        const getMo = await MoNumber.query()
          .findById(print.combos[0].mo_id)
          .first();
        num = getMo.num;
        numIdList = getMo.mo_id.toString();
        style = getMo.style;
        styleCategory = getMo.style_category;
        order = getMo.mo_order;
        CustomField = getMo.customer;
        company_code = getMo.company_code;
      } else {
        const getMo = await MoNumber.query()
          .findById(print.combos[0].mo_id)
          .first();
        style = getMo.style;
        styleCategory = getMo.style_category;
        company_code = getMo.company_code;
        const moMap = new Map<number, { num: string; combos: string[] }>();
        // Grouping the combo numbers by mo_id
        print.combos.forEach((combo) => {
          const moId = combo.mo_id;
          const comboNum = combo.combo_number;
          const moNum = combo.mo;
          CustomField = moNum.customer;
          if (!moMap.has(moId)) {
            moMap.set(moId, { num: moNum.num, combos: [comboNum] });
          } else {
            moMap.get(moId)?.combos.push(comboNum);
          }
        });
        // Formatting the output
        num = Array.from(moMap.values())
          .map(({ num, combos }) => `${num}(${combos.join(', ')})`)
          .join(' | ');
        numIdList = Array.from(moMap.keys()).join(', ');
      }

      let ExtraFabric;
      let requiredFabricYards;
      let safeExtraFabricYards;
      let bomYards;
      let cutFabricYards = 0;
      let lenghtOfPlot;
      let plies;
      let requiredYardsManually = 0;
      if (returnPlyItems.length > 0) {
        // METIS - EXTRA FABRIC (yds) (extra fabric for plies)
        ExtraFabric = extraFabricIn / 36;
        // METIS - REAL FABRIC (yds) (required) (is required plus extra fabric for plies)
        requiredFabricYards = (totalCutFabricYards + extraFabricIn) / 36;
        // METIS - SAFE EXTRA REQUIRED (yds)
        safeExtraFabricYards =
          (totalCutFabricYards + extraFabricIn + safeExtraFabricIn) / 36;
        // METIS - BOM
        if (company_code === 1) {
          //get materials where mo_id is equal to print.combos[0].mo_id and print.part_number is equal to materials.part_number
          const mo = getMaterials.find(
            (mo) => mo.mo_id === print.combos[0].mo_id
          );

          const moMaterials = mo?.materials;
          const moMaterial = moMaterials?.find(
            (material) => material.PartNumber === print.part_number
          );
          //if print.quantity is greater than 0
          if (print.quantity > 0) {
            //METIS - BOM REQUIRED (yds)
            bomYards =
              moMaterial && moMaterial.QuantityRequired
                ? Math.abs(moMaterial.QuantityRequired / mo.quantity) *
                  print.quantity
                : 0;
          }
        }
        // METIS - CORTE TELA (yds)
        cutFabricYards = totalCutFabricYards / 36;
        // METIS - LENGHT OF PLOT
        lenghtOfPlot = totalLengthOfPlot / 36;
        // METIS - PLOT PLIES
        plies = plySections;
      }

      //IN CASE IS LASSER PLOTTER
      if (
        print.combos[0].is_laser &&
        print.total_sizes &&
        print.length &&
        print.quantity
      ) {
        //REQUIRED (yds) for lasser
        requiredFabricYards = print.plys_fabric_yards;
      } else if (print.combos[0].is_laser) {
        requiredFabricYards = print.plys_fabric_yards;
      }

      //IN CASE IS PLOTTER
      requiredYardsManually =
        print.total_sizes && print.length && print.quantity
          ? print.total_sizes && print.length && print.quantity
            ? +(
                print.extra_fabric
                  ? ((print.length / 36 + print.extra_fabric) /
                      print.total_sizes) *
                    print.quantity
                  : (print.length / 36 / print.total_sizes) * print.quantity
              ).toFixed(2)
            : 0
          : 0;

      currentComboItem++;
      fileInformation.push({
        customer: CustomField,
        mo: num,
        moIdList: numIdList,
        order: print.combos.length > 1 ? '' : order,
        style: style,
        styleCategory: styleCategory ? styleCategory : 'N/A',
        marker: print.quantity ? print.quantity : 0,
        partNumber: print.part_number,
        extraFabricYards: ExtraFabric ? ExtraFabric : 0,
        requiredYards: requiredFabricYards ? requiredFabricYards : 0,
        safeExtraFabricYards: safeExtraFabricYards ? safeExtraFabricYards : 0,
        bom: bomYards ? bomYards : 0,
        width: print.width ? print.width : 0,
        lengthInch: print.length ? print.length : 0,
        totalSizes: print.total_sizes ? print.total_sizes : 0,
        utilizationPercentage: print.utilization ? print.utilization : 0,
        cutFabricYards: cutFabricYards ? cutFabricYards : 0,
        lengthOfPlotsYards: lenghtOfPlot ? lenghtOfPlot : 0,
        plotPlies: plies ? plies : '',
        plotRoll: print.plotter_roll_id ? print.plotter_roll_id : 0,
        plotOrder: currentComboItem.toString(),
        extraFabricPlotterYards: print.extra_fabric ? print.extra_fabric : 0,
        requiredExtraFabricPlotterYards: requiredYardsManually
          ? requiredYardsManually
          : 0,
      });
    }
  }

  // Create a new instance of a Workbook class
  const wb = new xl.Workbook();

  // Add Worksheets to the workbook
  const ws = wb.addWorksheet('RESUMEN');

  // Define header style
  const headerStyle = wb.createStyle({
    font: {
      color: '#FF0800',
      size: 14,
      bold: true,
    },
    alignment: {
      horizontal: 'center',
    },
    border: {
      left: {
        style: 'thin',
        color: '#000000',
      },
      right: {
        style: 'thin',
        color: '#000000',
      },
      top: {
        style: 'thin',
        color: '#000000',
      },
      bottom: {
        style: 'thin',
        color: '#000000',
      },
    },
    fill: {
      type: 'pattern',
      patternType: 'solid',
      bgColor: '#CCCCCC',
      fgColor: '#CCCCCC',
    },
  });

  // Apply header style to header cells
  const headers = [
    'CUSTOMER',
    'MO',
    'ORDEN',
    'STYLE',
    'STYLE CATEGORY',
    'MAKER',
    'TELA',
    'EXTRA FABRIC(yds)',
    'REQUIRED (yds)',
    'SAFE EXTRA REQUIRED (yds)',
    'BOM',
    'ANCHO',
    'LENGTH(in)',
    'TOTAL SIZES',
    'UTILIZACION',
    'METIS - CORTE TELA (yds)',
    'METIS - LENGHT OF PLOT (yds)',
    'METIS - PLOT PLIES',
    'METIS - PLOT ROLL',
    'METIS - PLOT ORDER',
    'EXTRA FABRIC(yds) - AGREGADO POR PLOTTER',
    'REQUIRED (yds) - AGREGADO POR PLOTTER',
  ];
  headers.forEach((header, index) => {
    ws.cell(1, index + 1)
      .string(header)
      .style(headerStyle);
  });

  // Define data style
  const dataStyle = wb.createStyle({
    font: {
      color: '#000000',
      size: 12,
    },
    numberFormat: '#,##0.00; (#,##0.00); -',
    alignment: {
      horizontal: 'center',
      vertical: 'center',
      wrapText: true,
    },
    border: {
      left: {
        style: 'thin',
        color: '#000000',
      },
      right: {
        style: 'thin',
        color: '#000000',
      },
      top: {
        style: 'dotted',
        color: '#000000',
      },
      bottom: {
        style: 'dotted',
        color: '#000000',
      },
    },
  });
  const dataForNumbers = wb.createStyle({
    font: {
      color: '#000000',
      size: 12,
    },
    numberFormat: '0',
    alignment: {
      horizontal: 'center',
      vertical: 'center',
    },
    border: {
      left: {
        style: 'thin',
        color: '#000000',
      },
      right: {
        style: 'thin',
        color: '#000000',
      },
      top: {
        style: 'dotted',
        color: '#000000',
      },
      bottom: {
        style: 'dotted',
        color: '#000000',
      },
    },
  });

  fileInformation.forEach((rowFile) => {
    //CUSTOMER
    ws.cell(row, 1).string(rowFile.customer).style(dataStyle);
    //MO
    ws.cell(row, 2).string(rowFile.mo).style(dataStyle);
    ws.column(2).setWidth(22);
    //ORDEN
    ws.cell(row, 3).string(rowFile.order);
    //STYLE
    ws.cell(row, 4).string(rowFile.style).style(dataStyle);
    ws.column(4).setWidth(18);
    //STYLE CATEGORY
    ws.cell(row, 5).string(rowFile.styleCategory).style(dataStyle);
    ws.column(5).setWidth(18);
    //MAKER
    ws.cell(row, 6).number(rowFile.marker).style(dataForNumbers);
    //TELA
    ws.cell(row, 7).string(rowFile.partNumber).style(dataStyle);
    ws.column(7).setWidth(36);
    //ANCHO
    ws.cell(row, 12).number(rowFile.width).style(dataStyle);
    //LENGTH(in)
    ws.cell(row, 13).number(rowFile.lengthInch).style(dataStyle);
    //TOTAL SIZES
    ws.cell(row, 14).number(rowFile.totalSizes).style(dataForNumbers);
    //UTILIZACION
    ws.cell(row, 15).number(rowFile.utilizationPercentage).style(dataStyle);
    //EXTRA FABRIC(yds)
    ws.cell(row, 8).number(rowFile.extraFabricYards).style(dataStyle);
    // METIS - REAL FABRIC (yds) (required) (is required plus extra fabric for plies)
    ws.cell(row, 9)
      .number(rowFile.requiredYards)
      .style(dataStyle)
      .style({
        fill: {
          type: 'pattern',
          patternType: 'solid',
          bgColor: '#8EA9DB',
          fgColor: '#8EA9DB',
        },
      });

    // METIS - SAFE EXTRA REQUIRED (yds)
    ws.cell(row, 10).number(rowFile.safeExtraFabricYards).style(dataStyle);
    // METIS - BOM
    ws.cell(row, 11).number(rowFile.bom).style(dataStyle);
    // METIS - CORTE TELA (yds)
    ws.cell(row, 16).number(rowFile.cutFabricYards).style(dataStyle);
    // METIS - LENGHT OF PLOT
    ws.cell(row, 17).number(rowFile.lengthOfPlotsYards).style(dataStyle);
    // METIS - PLOT PLIES
    ws.cell(row, 18).string(rowFile.plotPlies).style(dataStyle);
    ws.column(18).setWidth(31);
    // METIS - PLOT ROLL
    ws.cell(row, 19).number(rowFile.plotRoll).style(dataForNumbers);
    // METIS - PLOT ORDER
    ws.cell(row, 20).string(rowFile.plotOrder).style(dataStyle);
    //EXTRA FABRIC(yds) - AGREGADO POR PLOTTER
    ws.cell(row, 21).number(rowFile.extraFabricPlotterYards).style(dataStyle);
    //REQUIRED (yds) - AGREGADO POR PLOTTER
    ws.cell(row, 22)
      .number(rowFile.requiredExtraFabricPlotterYards)
      .style(dataStyle);
    row++;
  });

  if (customerName === 'ADIDAS') {
    const wsADIDAS = wb.addWorksheet('BODEGA ADIDAS');

    // reduce for molist and partnumber
    const groupInformation = Object.values(
      fileInformation.reduce<Record<string, IExcelFile>>((acc, item) => {
        const key = `${item.moIdList}-${item.partNumber}`;

        if (!acc[key]) {
          acc[key] = { ...item }; // Clonar el objeto original
        } else {
          acc[key].requiredYards += item.requiredYards; // Sumar la cantidad
          // `price` se mantiene igual porque tomamos el primero
        }

        return acc;
      }, {})
    );

    // Define header style
    const ADIDASHeader = [
      'CUSTOMER',
      'MO',
      'STYLE',
      'STYLE CATEGORY',
      'MAKER',
      'TELA',
      'EXTRA FABRIC(yds)',
      'REQUIRED (yds)',
      'SAFE EXTRA REQUIRED (yds)',
      'ANCHO',
    ];
    ADIDASHeader.forEach((header, index) => {
      wsADIDAS
        .cell(1, index + 1)
        .string(header)
        .style(headerStyle);
    });
    row = 2;
    groupInformation.forEach((rowFile) => {
      //CUSTOMER
      wsADIDAS.cell(row, 1).string(rowFile.customer).style(dataStyle);
      //MO
      wsADIDAS.cell(row, 2).string(rowFile.mo).style(dataStyle);
      wsADIDAS.column(2).setWidth(22);
      //STYLE
      wsADIDAS.cell(row, 3).string(rowFile.style).style(dataStyle);
      wsADIDAS.column(3).setWidth(18);
      //STYLE CATEGORY
      wsADIDAS.cell(row, 4).string(rowFile.styleCategory).style(dataStyle);
      wsADIDAS.column(4).setWidth(18);
      //MAKER
      wsADIDAS.cell(row, 5).number(rowFile.marker).style(dataForNumbers);
      //TELA
      wsADIDAS.cell(row, 6).string(rowFile.partNumber).style(dataStyle);
      wsADIDAS.column(6).setWidth(36);
      //ANCHO
      wsADIDAS.cell(row, 10).number(rowFile.width).style(dataStyle);
      //EXTRA FABRIC(yds)
      wsADIDAS.cell(row, 7).number(rowFile.extraFabricYards).style(dataStyle);
      // METIS - REAL FABRIC (yds) (required) (is required plus extra fabric for plies)
      wsADIDAS
        .cell(row, 8)
        .number(rowFile.requiredYards)
        .style(dataStyle)
        .style({
          fill: {
            type: 'pattern',
            patternType: 'solid',
            bgColor: '#8EA9DB',
            fgColor: '#8EA9DB',
          },
        });

      // METIS - SAFE EXTRA REQUIRED (yds)
      wsADIDAS
        .cell(row, 9)
        .number(rowFile.safeExtraFabricYards)
        .style(dataStyle);
      wsADIDAS.cell(row, 10).number(rowFile.width).style(dataStyle);
      row++;
    });
  }

  // Write to a file
  return wb;
};

export const deleteCombo = async (employee: Employee, id: number) => {
  if (!id) {
    throw Error('ID es requerido');
  }

  return await knex.transaction(async (trx) => {
    const currentCombo = await PlotterCombo.query(trx).findById(id);
    if (!currentCombo) {
      throw Error('Combo no encontrado');
    }

    //get all prints joining with combos
    const allCombos = await PlotterCombo.query(trx).where(
      'plotter_print_id',
      currentCombo.plotter_print_id
    );
    if (allCombos && allCombos.length > 1) {
      //foreach allCombos
      let returnItem: PlotterCombo;
      for (const combo of allCombos) {
        //clear print group if exist
        if (combo.plotter_print_id) {
          const print = await PlotterPrint.query(trx).updateAndFetchById(
            combo.plotter_print_id,
            {
              print_group_id: null,
              print_group_sort: null,
            }
          );

          if (print) {
            await PlotterPrintLog.query(trx).insert({
              plotter_print_id: print.id,
              created_by_employee_id: employee.employee_id,
              type: 'delete',
              data: {
                combo_id: combo.id,
                print_group_id: null,
                print_group_sort: null,
                removed_at: new Date(),
              },
            });
          }
        }

        returnItem = await PlotterCombo.query(trx).updateAndFetchById(
          combo.id,
          {
            removed_at: new Date(),
            plotter_print_id: null,
          }
        );
        await PlotterComboLog.query(trx).insert({
          plotter_combo_id: combo.id,
          created_by_employee_id: employee.employee_id,
          type: 'delete',
          data: {
            removed_at: new Date(),
          },
        });
      }
      return returnItem;
    } else {
      const newUpdate = {
        removed_at: new Date(),
      };

      const combo = await PlotterCombo.query(trx).updateAndFetchById(
        id,
        newUpdate
      );

      await PlotterComboLog.query(trx).insert({
        plotter_combo_id: combo.id,
        created_by_employee_id: employee.employee_id,
        type: 'delete',
        data: newUpdate,
      });
      return combo;
    }
  });
};

export const deleteAllComboByMO = async (employee: Employee, id: number) => {
  if (!id) {
    throw Error('ID es requerido');
  }

  return await knex.transaction(async (trx) => {
    const currentCombo = await PlotterCombo.query(trx).where('mo_id', id);
    if (!currentCombo) {
      throw Error('Combo no encontrado');
    }

    for (const combo of currentCombo) {
      const newUpdate = {
        removed_at: new Date(),
      };

      await PlotterCombo.query(trx).updateAndFetchById(combo.id, newUpdate);

      await PlotterComboLog.query(trx).insert({
        plotter_combo_id: combo.id,
        created_by_employee_id: employee.employee_id,
        type: 'delete',
        data: newUpdate,
      });
    }

    return currentCombo;
  });
};

export const checkOutRollService = async (
  rollId: number,
  employee: Employee,
  recipient: number,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  try {
    const trx = options?.trx ?? undefined;
    const roll = await PlotterRoll.query(trx).findById(rollId);
    if (!roll) {
      throw Error('Rollo no encontrado');
    }

    if (roll.checkout_at) {
      throw Error('Rollo ya esta checkeado');
    }

    const updateCheckoutAt = {
      checkout_at: new Date(),
      recipient_employee_id: recipient,
    };

    const updateRoll = await PlotterRoll.query(trx).updateAndFetchById(
      rollId,
      updateCheckoutAt
    );

    await PlotterRollLog.query(trx).insert({
      plotter_roll_id: rollId,
      type: 'checkout',
      data: updateCheckoutAt,
      created_by_employee_id: employee.employee_id,
    });

    return updateRoll;
  } catch (error) {
    return error;
  }
};

export const updatePlotterPrintCutDataUsingMos = async () => {
  // Remove bad shifts
  const removeBadShiftsQuery = `update plotter_prints pp 
  left join work_area_group_shifts wags on wags.id = pp.cut_work_area_group_shift_id 
  set pp.cut_work_area_group_shift_id = null
  where pp.cut_work_area_group_shift_id is not null
  and (wags.removed_at is not null or pp.cut_at < wags.start_datetime_sv or pp.cut_at > wags.end_datetime_sv)`;

  // Update is_laser on plotter prints
  const isLaserUpdateQuery = `update plotter_prints pp
  left join (
  select
    pc.plotter_print_id,
    count(*) as combo_count,
    sum(pc.is_laser) as laser_count
  from plotter_combos pc 
  where pc.removed_at is null
  group by pc.plotter_print_id 
  ) pps on pps.plotter_print_id = pp.id
  set pp.is_laser = 1
  where pp.is_laser is NULL
  and pps.combo_count = pps.laser_count`;

  const isNotLaserUpdateQuery = `update plotter_prints pp 
  left join (
  select
    pc.plotter_print_id,
    count(*) as combo_count,
    sum(pc.is_laser) as laser_count
  from plotter_combos pc 
  where pc.removed_at is null
  group by pc.plotter_print_id 
  ) pps on pps.plotter_print_id = pp.id
  set pp.is_laser = 0
  where pp.is_laser is null
  and pps.combo_count > 0
  and pps.laser_count = 0`;

  // UPdate cut_at at on plotter prints
  const cutAtUpdateQuery = `update plotter_prints pp 
  left join (
  select
    pc.plotter_print_id,
    count(*) as combo_count,
    count(distinct sm.used_group) as group_count,
    sum(sm.scan_count) count_check,
    max(sm.used_group) as using_group,
    min(sm.first_finish) as finished,
    sum(pc.is_laser) as laser_count
  from plotter_combos pc 
  left join (
  select
    ms.mo_id,
    min(ms.sew),
    count(*) as scan_count,
    count(distinct ms.work_area_group_id) as group_count,
    max(ms.work_area_group_id) as used_group,
    min(ms.sew) as first_finish
  from mo_scans ms
  where ms.work_area_id = 12
  and ms.sew is not null
  group by ms.mo_id
  having scan_count = 1
  and group_count = 1
  ) sm on sm.mo_id = pc.mo_id 
  group by pc.plotter_print_id 
  having combo_count = count_check and group_count = 1 and laser_count = 0
  ) fp on fp.plotter_print_id = pp.id
  set pp.cut_at = fp.finished
  where pp.cut_at is null
  and fp.finished is not null
  and pp.is_laser = 0`;

  // update cut group on plotter print
  const plotterPrintCutGroupUpdateQuery = `update plotter_prints pp 
  left join (
  select
    pc.plotter_print_id,
    count(*) as combo_count,
    count(distinct sm.used_group) as group_count,
    sum(sm.scan_count) count_check,
    max(sm.used_group) as using_group,
    min(sm.first_finish) as finished,
    sum(pc.is_laser) as laser_count
  from plotter_combos pc 
  left join (
  select
    ms.mo_id,
    min(ms.sew),
    count(*) as scan_count,
    count(distinct ms.work_area_group_id) as group_count,
    max(ms.work_area_group_id) as used_group,
    min(ms.sew) as first_finish
  from mo_scans ms
  where ms.work_area_id = 12
  and ms.sew is not null
  group by ms.mo_id
  having scan_count = 1
  and group_count = 1
  ) sm on sm.mo_id = pc.mo_id 
  where pc.removed_at is null
  group by pc.plotter_print_id 
  having combo_count = count_check and group_count = 1 and laser_count = 0
  ) fp on fp.plotter_print_id = pp.id
  set pp.cut_work_area_group_id = fp.using_group
  where pp.cut_at is not null
  and pp.cut_work_area_group_id is null
  and fp.plotter_print_id is not null
  and fp.finished is not null
  and fp.using_group is not null
  and pp.is_laser = 0`;

  // update shift on plotter print
  const plotterPrintShiftUpdateQuery = `update plotter_prints pp 
  left join (
    select
      pp.id as plot_id,
      count(*) as shift_count,
      group_concat(wags.id SEPARATOR ',') as group_ids,
      max(wags.id) as last_group_id
    from plotter_prints pp 
    left join work_area_group_shifts wags on wags.work_area_group_id = pp.cut_work_area_group_id and wags.start_datetime_sv < pp.cut_at and wags.end_datetime_sv > pp.cut_at and wags.removed_at is null 
    where wags.id is not null
    group by pp.id
  ) ps on ps.plot_id = pp.id
  set pp.cut_work_area_group_shift_id = ps.last_group_id
  where pp.cut_work_area_group_shift_id is null
  and pp.is_laser = 0
  and ps.shift_count = 1`;

  return await knex.transaction(async () => {
    const removeBadShifts = await knex.raw(removeBadShiftsQuery);
    const isLaserUpdates = await knex.raw(isLaserUpdateQuery);
    const isNotLaserUpdates = await knex.raw(isNotLaserUpdateQuery);
    const cutAtUpdates = await knex.raw(cutAtUpdateQuery);
    const cutGroupUpdates = await knex.raw(plotterPrintCutGroupUpdateQuery);
    const cutShiftUpdates = await knex.raw(plotterPrintShiftUpdateQuery);
    return {
      removeBadShifts,
      isLaserUpdates,
      isNotLaserUpdates,
      cutAtUpdates,
      cutGroupUpdates,
      cutShiftUpdates,
    };
  });
};

export const getSuggestedPartNumber = async (
  styleNumber: string,
  partNumber: string
) => {
  if (!styleNumber) {
    throw Error('Style number es requerido');
  }

  if (!partNumber) {
    throw Error('Part number es requerido');
  }

  const historicalPartQuery = `select pc.part_number as part_number
  from plotter_combos pc 
  left join mo_numbers mo on mo.mo_id = pc.mo_id 
  where mo.style = '${styleNumber}'
  group by pc.part_number`;

  const rawMaterialsQuery = `select pc.part_number as part_number 
  from raw_materials rm
  where rm.part_number = '${partNumber}'`;

  return knex.transaction(async () => {
    const part = await knex.raw(historicalPartQuery);
    const rawMaterials = await knex.raw(rawMaterialsQuery);
    return { part, rawMaterials };
  });
};

export const getPrintGroups = async (
  id: number,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  const trx = options?.trx ?? undefined;
  if (!id) {
    throw Error('ID del grupo es requerido');
  }

  const groups = await PlotterPrint.query(trx).where('print_group_id', id);
  if (!groups) {
    throw Error('Grupo no encontrado');
  }

  const combos = await PlotterCombo.query(trx)
    .join(
      'plotter_prints',
      'plotter_prints.id',
      'plotter_combos.plotter_print_id'
    )
    .where('plotter_prints.print_group_id', id)
    .whereNull('removed_at');

  const moIds = combos.map((combo) => combo.mo_id);

  const mos = await MoNumber.query(trx).whereIn('mo_id', moIds);

  const mosSizes = await MoSize.query(trx).whereIn('mo_id', moIds);

  const mosFull = mos.map((mo): Partial<MoNumberFull> => {
    const sizes = mosSizes.filter((moSize) => moSize.mo_id === mo.mo_id);
    return {
      ...mo,
      sizes,
    };
  });

  const comboFull = combos.map((combo): ComboFull => {
    const mo = mosFull.find((mo) => mo.mo_id === combo.mo_id);
    return {
      ...combo,
      mo,
    };
  });

  let printsWithCombos = groups.map((grp) => {
    const cmbs = comboFull.filter((cmb) => cmb.plotter_print_id === grp.id);

    return {
      ...grp,
      combos: cmbs,
    };
  });

  //create string delimited by comma from column plotter_roll_id in prints where plotter_roll_id is not null
  const rollIdList: string = groups
    .filter((item: PlotterPrint) => item.plotter_roll_id != null)
    .map((item: PlotterPrint) => item.plotter_roll_id)
    .join(',');

  //query roll
  const getArrayOfIds = (rollIdList: string) => {
    return rollIdList.split(',').map((item) => +item);
  };

  const roll =
    rollIdList === ''
      ? null
      : await PlotterRoll.query(trx).whereIn('id', getArrayOfIds(rollIdList));

  if (roll) {
    printsWithCombos = printsWithCombos.map((print) => {
      const rollItem = roll.find((item) => item.id === print.plotter_roll_id);
      return {
        ...print,
        roll: rollItem,
      };
    });
  } else {
    printsWithCombos = printsWithCombos.map((print) => {
      return {
        ...print,
        roll: null,
      };
    });
  }

  return printsWithCombos;
};
