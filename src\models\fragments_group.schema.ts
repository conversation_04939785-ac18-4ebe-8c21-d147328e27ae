import { Model } from '@app/db';

export class WorkFragmentGroups extends Model {
  static get tableName(): string {
    return 'work_fragment_groups';
  }

  id!: number;
  work_fragment_group_type_id!: number;
  work_fragment_group_roll_id!: number;
  work_fragment_group_roll_sort!: number;
}

export class WorkFragmentGroupCustomFields extends Model {
  static get tableName(): string {
    return 'work_fragment_group_custom_fields';
  }

  id!: number;
  name!: string;
  type!: string;
  column_name!: string;
  work_fragment_group_type_id!: number;
  list_values: string | null;
}

export class WorkFragmentGroupRolls extends Model {
  static get tableName(): string {
    return 'work_fragment_group_rolls';
  }

  id!: number;
  work_fragment_group_type_id!: number;
}

export class WorkFragmentGroupTypes extends Model {
  static get tableName(): string {
    return 'work_fragment_group_types';
  }

  id!: number;
  name!: string;
}
