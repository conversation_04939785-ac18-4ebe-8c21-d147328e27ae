import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('mo_twill_laser_jobs', (table) => {
    table
      .integer('mo_twill_laser_varsity_art_job_id', 10)
      .nullable()
      .unsigned();
    table
      .foreign(
        'mo_twill_laser_varsity_art_job_id',
        'mtlvaji_mo_twill_laser_jobs_foreign'
      )
      .references('id')
      .inTable('mo_twill_laser_varsity_art_jobs');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('mo_twill_laser_jobs', (table) => {
    table.dropForeign(
      'mo_twill_laser_varsity_art_job_id',
      'mtlvaji_mo_twill_laser_jobs_foreign'
    );
    table.dropColumn('mo_twill_laser_varsity_art_job_id');
  });
}
