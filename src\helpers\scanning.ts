import dayjs from 'dayjs';
import type { Request, Response } from 'express';

import type { IWorkVouchers } from '@app/interface/tickets.interfaces';
import { MoScans } from '@app/models/tickets.schema';

export async function CreateMOScan(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const supervisor = req.body.barcode_operator;
    const task = req.body.task;
    const format1 = 'YYYY-MM-DD HH:mm:ss';
    const actualDate = new Date();

    // data de los middlewares
    const data = {
      moQuantity: req.body.moQuantity,
      operatorId: req.body.operatorId,
      typeAction: req.body.typeAction,
      client: req.body.client,
    };

    const moId: IWorkVouchers['mo_id'] = req.body.moId;

    if (
      data.typeAction == 'ENTRADA' &&
      req.body.task == 'Sew' &&
      req.body.client == 'VARPRO'
    ) {
      // insertar en mo_scans
      // buscar si existe ya una scan con la mo y el operador
      const getMoScan = await MoScans.query()
        .where('mo_scans.mo_id', moId)
        .where('mo_scans.supervisor', supervisor)
        .where('mo_scans.task_name', task);

      if (getMoScan.length > 0) {
        return res.status(200).json({
          ok: true,
          message: 'Ya existe la tarea, debes realizar una salida',
        });
      } else {
        const dataAddMoScans = {
          mo_id: moId,
          sew_ready: dayjs(actualDate).format(format1),
          supervisor,
          supervisor_code: 1,
          task_name: task,
          poly_status: 2,
        };

        // insertamos entrada
        const addMoScans = await MoScans.query().insert(dataAddMoScans);

        return res.status(200).json({
          ok: true,
          message:
            'No se crea voucher y ticket en tu area, se inserto en mo_scans VARSITY(entrada)',
          data: addMoScans,
        });
      }
    } else if (data.typeAction === 'SALIDA') {
      // insertamos salida
      // buscar si existe ya una scan con la mo y el operador
      const getMoScan: any = await MoScans.query()
        .where('mo_scans.mo_id', moId)
        .where('mo_scans.supervisor', supervisor)
        .where('mo_scans.task_name', task)
        .select('scan_id', 'sew');

      if (getMoScan.length > 0) {
        // validar si sew esta nulo
        if (getMoScan[0].sew != null) {
          return res.status(200).json({
            ok: true,
            message:
              'Ya existe un escaneo asociado a esta MO para esta area. no se puede escanear dos veces',
            data: getMoScan,
          });
        } else getMoScan[0].sew == null;
        {
          // actualizamos entrada
          const addMoScans = await MoScans.query()
            .update({
              sew: dayjs(actualDate).format(format1),
              poly_status: 0,
            })
            .where('scan_id', getMoScan[0].scan_id);

          return res.status(200).json({
            ok: true,
            message:
              'No se crea voucher y ticket en tu area, se actualizo mo scan',
            data: addMoScans,
          });
        }
      } else {
        // caso que no exista entrada
        const dataAddMoScans = {
          mo_id: moId,
          sew_ready: dayjs(actualDate).format(format1),
          sew: dayjs(actualDate).format(format1),
          supervisor,
          supervisor_code: 1,
          task_name: task,
          poly_status: 0,
        };

        const addMoScans = await MoScans.query().insert(dataAddMoScans);

        return res.status(200).json({
          ok: true,
          message:
            'No se crea voucher y ticket en tu area, se inserto en mo_scans ',
          data: addMoScans,
        });
      }
    } else {
      return res.status(500).json({
        ok: false,
        message: `No existe el tipo de accion ${data.typeAction}`,
      });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}
