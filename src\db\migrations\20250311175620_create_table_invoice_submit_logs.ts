import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(
    'invoice_submit_logs',
    (table: Knex.TableBuilder) => {
      table.increments('id').unsigned().primary();
      table.integer('invoice_id').unsigned().notNullable();
      table.timestamp('request_timestamp').notNullable();
      table.string('request_url').notNullable();
      table.string('request_body').notNullable();
      table.string('response_status').notNullable();
      table.string('response_body').notNullable();
      table.string('request_user').notNullable();

      table
        .timestamp('created_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      table
        .timestamp('updated_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

      table
        .foreign('invoice_id', 'fk_invoice_submit_logs_invoice_id')
        .references('id')
        .inTable('invoices');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('invoice_submit_logs');
}
