import type { Request, Response } from 'express';

import {
  Machine,
  MachineStateLog,
  MachineStatus,
} from '@app/models/machines.schema';
import { WorkAreaLines } from '@app/models/tickets.schema';
import { buildLogger } from '@app/settings';

const logger = buildLogger('machines.controller');

export async function getMachinesByArea(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { work_area_id }: { work_area_id: number } = req.body;

  try {
    // verificamos si se envio el id del area
    if (work_area_id === undefined || work_area_id === null) {
      return res.status(500).json({
        ok: false,
        message: 'Area no enviada en la peticion',
      });
    }
    // retornamos las maquinas del area recibida, NOTA: la maquina tiene que estar asignada a una linea en work_area_lines.
    const returnMachineByArea = await Machine.query()
      .join(
        'machine_types',
        'machines.machine_type_id',
        '=',
        'machine_types.machine_type_id'
      )
      .join(
        'work_machine_statuses',
        'machines.status',
        '=',
        'work_machine_statuses.id'
      )
      .join(
        'work_area_lines',
        'machines.work_area_line_id',
        '=',
        'work_area_lines.id'
      )
      .where('work_area_lines.work_area_id', work_area_id)
      .select(
        'machines.machine_id',
        'machines.machine',
        'machine_types.name as machine_type_name',
        'work_machine_statuses.name as status'
      )
      .castTo<
        {
          machine_id: number;
          machine: string;
          machine_type_name: string;
          status: string;
        }[]
      >();

    if (returnMachineByArea.length > 0) {
      // retornamos la lista
      return res.status(200).json({
        ok: true,
        machines: returnMachineByArea,
      });
    }

    // retornamos null en caso de no encontrar nada
    return res.status(200).json({
      ok: true,
      machines: null,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export const getMachineByBarcode = async (
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> => {
  const { barcode } = req.params;

  try {
    if (!barcode) {
      return res.status(400).json({
        ok: false,
        message: 'No se envio el codigo de barras',
      });
    }

    const machine = await Machine.query().findOne({ barcode });

    if (!machine) {
      return res.status(404).json({
        ok: false,
        message: 'No se encontro la maquina',
      });
    }

    return res.status(200).json({
      ok: true,
      data: machine,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
};

export async function getMachineStatuses(
  _req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const getWorkMachineStatuses = await MachineStatus.query()
      .select('work_machine_statuses.id', 'work_machine_statuses.name')
      .castTo<{ id: number; name: string }[]>();

    return res.status(200).json({
      ok: true,
      data: getWorkMachineStatuses,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      data: null,
    });
  }
}

export async function updateMachineStatus(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const {
    machine_id,
    status_id,
    log_comment,
  }: { machine_id: number; status_id: number; log_comment: string } = req.body;
  let prevState;

  try {
    // verificamos si se envio el id
    if (
      machine_id === undefined ||
      machine_id === null ||
      isNaN(Number(machine_id))
    ) {
      return res.status(500).json({
        ok: false,
        message: 'Id de maquina no enviada en la peticion',
      });
    }
    if (
      status_id === undefined ||
      status_id === null ||
      isNaN(Number(status_id))
    ) {
      return res.status(500).json({
        ok: false,
        message: 'Id de status no enviado en la peticion',
      });
    }

    // check for machine
    const foundMachine = await Machine.query().findById(Number(machine_id));
    if (!foundMachine) {
      return res.status(404).json({
        ok: false,
        message: 'No se encontro la maquina',
      });
    }

    const foundStatus = await MachineStatus.query().findById(status_id);
    if (!foundStatus) {
      return res.status(404).json({
        ok: false,
        message: 'No se encontro el status',
      });
    }

    // obtener el ultimo registro para poner el estado previo
    const getLastRecord = await MachineStateLog.query()
      .where('machine_id', foundMachine.machine_id)
      .orderBy('state_machine_id', 'desc')
      .limit(1);

    if (getLastRecord.length > 0) {
      prevState = getLastRecord[0].cur_state;
    }

    // update machine table
    const updateMachineTable = await Machine.query()
      .update({
        status: status_id,
      })
      .where('machine_id ', foundMachine.machine_id);

    if (updateMachineTable > 0) {
      // new log
      const dataNewLog = {
        machine_id: foundMachine.machine_id,
        prev_state: prevState,
        cur_state: status_id,
        log_comment:
          log_comment === undefined || log_comment === null
            ? null
            : log_comment,
      };
      const addLog = await MachineStateLog.query().insert(dataNewLog);

      // retornamos null en caso de no encontrar nada
      return res.status(200).json({
        ok: true,
        data: addLog,
      });
    }

    return res.status(200).json({
      ok: true,
      data: null,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getMachineStatusLog(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { machine_id }: { machine_id: number } = req.body;

  try {
    // verificamos si se envio el id del area
    if (machine_id === undefined || machine_id === null) {
      return res.status(500).json({
        ok: false,
        message: 'machine_id no enviada en la peticion',
      });
    }
    // retornamos las maquinas del area recibida, NOTA: la maquina tiene que estar asignada a una linea en work_area_lines.
    const returnMachineState = await MachineStateLog.query().where(
      'machine_state_logs.machine_id',
      machine_id
    );

    if (returnMachineState.length > 0) {
      // retornamos la lista
      return res.status(200).json({
        ok: true,
        machines: returnMachineState,
      });
    }

    // retornamos null en caso de no encontrar nada
    return res.status(200).json({
      ok: true,
      machines: null,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getActiveMachinesByArea(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { areaID } = req.params as { areaID: string };

  if (!areaID) {
    logger.error('No se envio el ID del area');

    return res.status(400).json({
      ok: false,
      message: 'No se envio el ID del area',
    });
  }

  try {
    const machinesByArea = await Machine.query()
      .join(
        'work_area_lines',
        'machines.work_area_line_id',
        'work_area_lines.id'
      )
      .join(
        'work_areas',
        'work_area_lines.work_area_id',
        'work_areas.work_area_id'
      )
      .where('work_areas.work_area_id', areaID)
      .where('machines.status', 1)
      .select([
        {
          id: 'machines.machine_id',
          name: 'work_area_lines.name',
        },
      ])
      .castTo<{ id: number; name: string }[]>();

    if (machinesByArea.length === 0) {
      logger.error('No se encontraron maquinas en el area');

      return res.status(400).json({
        ok: false,
        message: 'No se encontraron maquinas en el area',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Maquinas encontradas',
      data: machinesByArea,
      total: machinesByArea.length,
    });
  } catch (error) {
    logger.error(`Error: ${error}`);

    if (error instanceof Error) {
      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      ok: false,
      message: `Error, ${error.message}`,
    });
  }
}

export async function getActiveMachineLinesByArea(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { areaID } = req.params as { areaID: string };

  if (!areaID) {
    logger.error('No se envio el ID del area');

    return res.status(400).json({
      ok: false,
      message: 'No se envio el ID del area',
    });
  }

  try {
    const machinesByArea = await WorkAreaLines.query()
      .join(
        'work_areas',
        'work_area_lines.work_area_id',
        'work_areas.work_area_id'
      )
      .where('work_areas.work_area_id', areaID)
      .whereNull('work_area_lines.removed_at')
      .select([
        {
          id: 'work_area_lines.id',
          name: 'work_area_lines.name',
        },
      ])
      .castTo<{ id: number; name: string }[]>();

    if (machinesByArea.length === 0) {
      logger.error('No se encontraron maquinas en el area');

      return res.status(400).json({
        ok: false,
        message: 'No se encontraron maquinas en el area',
      });
    }

    return res.status(200).json({
      ok: true,
      data: machinesByArea,
      total: machinesByArea.length,
    });
  } catch (error) {
    logger.error(`Error: ${error}`);

    if (error instanceof Error) {
      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      ok: false,
      message: `Error, ${error.message}`,
    });
  }
}

export async function getActiveLaserMachinesByArea(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const machinesByArea = await Machine.query()
      .where('machines.machine_type_id', 7)
      .where('machines.status', 1)
      .select([
        {
          id: 'machines.machine_id',
          name: 'machines.machine',
        },
      ])
      .castTo<{ id: number; name: string }[]>();

    if (machinesByArea.length === 0) {
      logger.error('No se encontraron maquinas en el area');

      return res.status(400).json({
        ok: false,
        message: 'No se encontraron maquinas en el area',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Maquinas encontradas',
      data: machinesByArea,
      total: machinesByArea.length,
    });
  } catch (error) {
    logger.error(`Error: ${error}`);

    if (error instanceof Error) {
      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      ok: false,
      message: `Error, ${error.message}`,
    });
  }
}
