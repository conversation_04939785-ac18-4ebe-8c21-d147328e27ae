import { Model } from '@app/db';

export class RepairPartsUsers extends Model {
  static get tableName(): string {
    return 'repair_parts_users';
  }

  id: number;
  employee_id: number;
  repair_part_building_id: number;
  password: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export class RepairParts extends Model {
  static get tableName(): string {
    return 'repair_parts';
  }

  part_id?: number;
  category_id: number;
  part_name: string;
  quantity: number;
  description: string;
  is_active: boolean;
  part_status: string;
  brand: string;
  type_of_currency?: string;
  price: number;
  part_code: string;
  barcode?: string;
  building_id: number;
  min_stock: number;
  location_id: number;
  measurement?: string;
  created_at?: string;
}

export class RepairPartsLogs extends Model {
  static get tableName(): string {
    return 'repair_parts_logs';
  }

  id: number;
  employee_id: number;
  repair_part_id: number;
  quantity: number;
  new_quantity: number;
  created_at: string;
  updated_at: string;
}

export class RepairPartsMachine extends Model {
  static get tableName(): string {
    return 'repair_parts_machine';
  }

  id: number;
  code_machine: string;
  serie_machine: string;
  model_machine: string;
  type_machine: string;
  machine: string;
}

export class RepairPartCategories extends Model {
  static get tableName(): string {
    return 'repair_part_categories';
  }

  category_id: number;
  category: string;
  is_active: boolean;
  category_status: string;
}

export class RepairPartLocations extends Model {
  static get tableName(): string {
    return 'repair_part_locations';
  }

  location_ids: number;
  location: string;
  location_status: string;
  building_id: number;
}

export class RepairPartPurchase extends Model {
  static get tableName(): string {
    return 'repair_part_purchase';
  }

  purchase_id: number;
  supplier_id: number;
  part_id: number;
  quantity: number;
  price: number;
  delivery_note: string;
  invoice_number: string;
  invoice_type: string;
  purchase_status: string;
  created_at: string;
  received_at: string;
}

export class RepairPartPurchaseLog extends Model {
  static get tableName(): string {
    return 'repair_part_purchase_log';
  }

  log_id: number;
  part_id: number;
  old_price: number;
  new_price: number;
  old_quantity: number;
  new_quantity: number;
  created_at: string;
  delivery_note: string;
}

export class RepairPartRequests extends Model {
  static get tableName(): string {
    return 'repair_part_requests';
  }

  request_id: number;
  request_number: number;
  machine: string;
  serial: string;
  model: string;
  machine_type: string;
  mechanic_id: number;
  supervisor_id: number;
  user_id: number;
  client: string;
  created_at: string;
  updated_at: string;
}

export class RepairPartRequestItems extends Model {
  static get tableName(): string {
    return 'repair_part_request_items';
  }

  item_id: number;
  request_id: number;
  part_id: number;
  quantity: number;
  request_item_status: string;
  created_at: string;
}

export class RepairPartSuppliers extends Model {
  static get tableName(): string {
    return 'repair_part_suppliers';
  }

  supplier_id: number;
  supplier: string;
  company: string;
  telephone: string;
  email: string;
  address: string;
  status: string;
}
