import { Router } from 'express';

import {
  getRepos,
  productionBulkPage,
  productionPage,
  updateRepoInvoice,
} from '@app/controllers/repo.controller';

const reposRouter = Router();

reposRouter.route('/repoProductionPage').get(productionPage);
reposRouter.route('/reposProductionPage').get(productionBulkPage);
reposRouter.route('/getRepos/:id').get(getRepos);
reposRouter.route('/updateRepo').post(updateRepoInvoice);

export { reposRouter };
