import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(
    'mo_twill_laser_job_decorations',
    (table: Knex.TableBuilder) => {
      table.increments('id').unsigned().primary();
      table.integer('mo_twill_laser_job_id', 10).unsigned().notNullable();
      table
        .integer('mo_twill_laser_decoration_id', 10)
        .unsigned()
        .notNullable();
      table.boolean('is_active').notNullable().defaultTo(true);
      table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
      table
        .timestamp('updated_at')
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
      table
        .foreign('mo_twill_laser_job_id', 'mtljid_mo_twill_laser_jobs_foreign')
        .references('id')
        .inTable('mo_twill_laser_jobs');
      table
        .foreign(
          'mo_twill_laser_decoration_id',
          'mtljdid_mo_twill_laser_decorations_foreign'
        )
        .references('id')
        .inTable('mo_twill_laser_decorations');
      table.unique(['mo_twill_laser_job_id', 'mo_twill_laser_decoration_id'], {
        indexName: 'mtljid_mtljdid_unique',
      });
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('mo_twill_laser_job_decorations');
}
