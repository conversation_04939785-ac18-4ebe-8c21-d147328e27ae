import type { NextFunction, Request, Response } from 'express';
import { verify } from 'jsonwebtoken';

import { buildLogger, config } from '@app/settings';

const logger = buildLogger('middlewares/jwt.middlewares.ts');

export const validateJWT = (
  req: Request,
  res: Response,
  next: NextFunction
): string | void | Response<unknown, Record<string, unknown>> => {
  const { tokenHeaderKey, accessToken } = config.app;

  const token = req.header(tokenHeaderKey);

  if (!token) {
    logger.error('No hay token en la petición');

    return res.status(401).json({
      ok: false,
      message: 'No hay token en la petición',
    });
  }

  try {
    const { area_id, employee_id } = verify(token, accessToken) as {
      area_id: number;
      employee_id: number;
    };

    if (employee_id && area_id) {
      req.body.tokenInfo = { area_id, employee_id };

      return next();
    } else {
      logger.error('Token no válido');

      return res.status(401).json({
        ok: false,
        message: 'Token no válido',
      });
    }
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(401).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Ocurrio un error al verificar el token de acceso');

    return res.status(401).json({
      ok: false,
      message: 'Ocurrio un error al verificar el token de acceso',
    });
  }
};

export const validateJWTRepairParts = (
  req: Request,
  res: Response,
  next: NextFunction
): string | void | Response<unknown, Record<string, unknown>> => {
  const { accessToken } = config.app;

  const token = req.headers.authorization.split(' ')[1];

  if (!token) {
    logger.error('No hay token en la petición');

    return res.status(401).json({
      ok: false,
      message: 'No hay token en la petición',
    });
  }

  try {
    const { area_id, employee_id } = verify(token, accessToken) as {
      area_id: number;
      employee_id: number;
    };

    if (employee_id && area_id) {
      req.body.tokenInfo = { area_id, employee_id };

      return next();
    } else {
      logger.error('Token no válido');

      return res.status(401).json({
        ok: false,
        message: 'Token no válido',
      });
    }
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(401).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error('Ocurrio un error al verificar el token de acceso');

    return res.status(401).json({
      ok: false,
      message: 'Ocurrio un error al verificar el token de acceso',
    });
  }
};
