import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('warehouse_pull_sessions', (table) => {
    table.integer('employee_id', 11).notNullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('warehouse_pull_sessions', (table) => {
    table.integer('employee_id', 10).notNullable().alter();
  });
}
