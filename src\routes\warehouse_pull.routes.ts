import { Router } from 'express';

import {
  addOrderPartsToSession,
  addOrdersToSession,
  createAllocation,
  createNewSession,
  csvMaterials,
  finishDownload,
  finishDownloadSession,
  getAllocations,
  getContainersByPartNumber,
  getInProgressOrders,
  getMaterialsByMos,
  getMaterialsBySession,
  getOrderMaterials,
  getOrdersWithAllocation,
  getSessionInfo,
  getSessionTypes,
  getSessionsActiveByEmployee,
  roundedQuantity,
  startDownload,
  updateAllocation,
} from '@app/controllers/warehouse_pull.controller';
import {
  AddOrdersToSessionSchema,
  CreateAllocationSchema,
  CreateNewSessionSchema,
  CreatePullSessionOrderPartsSchema,
  DownloadsSchema,
  FinishDownloadSchema,
  GetOrdersWithoutAllocationSchema,
  RoundedQuantitySchema,
  UpdateAllocationSchema,
  sesssionIDSchema,
} from '@app/interface/zod_schemas';
import { validateData } from '@app/middlewares/validateData.middleware';

const warehousePullRouter = Router();

warehousePullRouter.route('/session/types').get(getSessionTypes);
warehousePullRouter
  .route('/session/add-orders')
  .post(validateData(AddOrdersToSessionSchema), addOrdersToSession);
warehousePullRouter
  .route('/materials')
  .post(validateData(sesssionIDSchema), getMaterialsByMos);
warehousePullRouter
  .route('/containers/:partNumber')
  .get(getContainersByPartNumber);
warehousePullRouter
  .route('/session/add-materials')
  .post(
    validateData(CreatePullSessionOrderPartsSchema),
    addOrderPartsToSession
  );
warehousePullRouter
  .route('/session/materials/rounded-qty')
  .post(validateData(RoundedQuantitySchema), roundedQuantity);
warehousePullRouter.route('/session/info/:sessionID').get(getSessionInfo);
warehousePullRouter
  .route('/session/create')
  .post(validateData(CreateNewSessionSchema), createNewSession);
warehousePullRouter
  .route('/sessions/:employeeID')
  .get(getSessionsActiveByEmployee);
warehousePullRouter
  .route('/session/materials/:sessionID')
  .get(getMaterialsBySession);
warehousePullRouter.route('/allocations/:sessionID').get(getAllocations);
warehousePullRouter
  .route('/allocation/update')
  .patch(validateData(UpdateAllocationSchema), updateAllocation);
warehousePullRouter
  .route('/allocation')
  .post(validateData(CreateAllocationSchema), createAllocation);
warehousePullRouter
  .route('/orders/with-allocation')
  .post(
    validateData(GetOrdersWithoutAllocationSchema),
    getOrdersWithAllocation
  );
warehousePullRouter
  .route('/downloads')
  .post(validateData(DownloadsSchema), startDownload);
warehousePullRouter
  .route('/downloads/:downloadID/finish')
  .patch(validateData(FinishDownloadSchema), finishDownload);
warehousePullRouter
  .route('/downloads/finish/:downloadID')
  .get(finishDownloadSession);
warehousePullRouter.route('/downloads/csv/:downloadID').get(csvMaterials);
warehousePullRouter.route('/orders/:moID/materials/:id').get(getOrderMaterials);
warehousePullRouter.route('/downloads/orders').post(getInProgressOrders);

export { warehousePullRouter };
