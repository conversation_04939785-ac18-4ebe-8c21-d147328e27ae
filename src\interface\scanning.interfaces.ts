export interface ICheckJobBarcodeRequest {
  mo_id: number;
  job_barcode: string;
  voucher_id: number;
  type_action: string;
  badge_barcode: string;
  line_name: string;
  quantity: number;
  // company_code?: string; (Optional property)
}
export interface ICheckBadgeBarcodeRequest {
  badge_barcode: string;
  work_area_id: number;
  work_area_group_id: number;
  company_code: number;
  last_work_area_ticket_id: number;
  last_work_ticket_area_id: number;
  last_work_area_ticket_finish: string;
  type_action: string;
  update_customer: boolean;
  style_id: number;
  fragment_id: number;
  is_repo: boolean;
  work_area_line_id: number;
}
export interface IFindMO {
  mo_id: number;
  quantity: number;
  mo_status: string;
  num: string;
  customer: string;
  style: string;
  style_id: number;
  mo_order: string;
  required_date: string;
  style_category: string;
  product_category: string;
  company_code: number;
  affected_units: number;
}

export interface ICheckJob {
  ok: boolean;
  message: string;
  data?:
    | undefined
    | {
        findMO: IFindMO;
        voucherId: number;
        lastWorkAreaTicketId: number;
        lastWorkTicketAreaId: number;
        lastWorkAreaTicketFinish: string;
        voucherCode: string;
        isRepo: boolean;
        repoId: number;
        jobBarcode: string;
        fragmentId: number;
        action: string;
        quantity: number;
      };
}
