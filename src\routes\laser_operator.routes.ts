import { Router } from 'express';

import {
  addLayersToJob,
  createJobProduction,
  createLaserTwillJob,
  createNewConsumptionToJob,
  deleteConsumptionToJob,
  deleteProduction,
  getAllProductionsByJob,
  getAllStatusesForJob,
  getConsumptionsToJob,
  getLaserTwillJobs,
  productionsByEmployee,
  updateConsumptionToJob,
} from '@app/controllers/laser_operator.controller';

const laserOperatorRouter = Router();

laserOperatorRouter.route('/statuses').get(getAllStatusesForJob);
laserOperatorRouter.route('/').post(createLaserTwillJob);
laserOperatorRouter.route('/layers/:jobID').post(addLayersToJob);
laserOperatorRouter
  .route('/production/:jobID')
  .post(createJobProduction)
  .get(getAllProductionsByJob);
laserOperatorRouter.route('/production/:productionID').delete(deleteProduction);
laserOperatorRouter
  .route('/consumption/:jobID')
  .post(createNewConsumptionToJob)
  .get(getConsumptionsToJob);
laserOperatorRouter
  .route('/consumption/:consumptionID')
  .patch(updateConsumptionToJob)
  .delete(deleteConsumptionToJob);
laserOperatorRouter
  .route('/:moID/:childMoID/:companyCode')
  .get(getLaserTwillJobs);
laserOperatorRouter
  .route('/jobs/productions/employee/:id')
  .get(productionsByEmployee);

export { laserOperatorRouter };
