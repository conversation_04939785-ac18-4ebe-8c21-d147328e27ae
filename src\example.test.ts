describe('Some example tests', () => {
  it('should throw not implemented error', () => {
    expect(() => {
      throw new Error('Not implemented');
    }).toThrowError('Not implemented');
  });

  it('should throw any error', () => {
    expect(() => {
      throw new Error('Some crazy error happened');
    }).toThrowError();
  });

  it('should equal 4', () => {
    expect(2 + 2).toEqual(4);
  });

  it('should not equal 5', () => {
    expect(2 + 2).not.toEqual(5);
  });

  it('should be true', () => {
    expect(true).toBeTruthy();
  });

  it('should be false', () => {
    expect(false).toBeFalsy();
  });

  it('should be null', () => {
    expect(null).toBeNull();
  });

  it('should be undefined', () => {
    expect(undefined).toBeUndefined();
  });

  it('should be defined', () => {
    expect(1).toBeDefined();
  });

  it('should be greater than 0', () => {
    expect(1).toBeGreaterThan(0);
  });

  it('should be greater than or equal to 1', () => {
    expect(1).toBeGreaterThanOrEqual(1);
  });

  it('should be less than 2', () => {
    expect(1).toBeLessThan(2);
  });

  it('should be less than or equal to 1', () => {
    expect(1).toBeLessThanOrEqual(1);
  });

  it('should be close to 0.3', () => {
    expect(0.1 + 0.2).toBeCloseTo(0.3);
  });

  it('should contain "world"', () => {
    expect('hello world').toContain('world');
  });

  it('should not contain "world"', () => {
    expect('hello world').not.toContain('foo');
  });

  it('should match regex', () => {
    expect('hello world').toMatch(/world$/);
  });
});
