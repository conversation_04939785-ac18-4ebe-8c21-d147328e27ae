import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('invoices', (table: Knex.TableBuilder) => {
    table.increments('id').unsigned().primary();
    table.integer('invoice_type_id').unsigned().notNullable();
    table.string('control_number').notNullable();
    table.string('generation_code').notNullable();
    table.string('status').notNullable();
    table.integer('model_type').notNullable();
    table.integer('operation_type').notNullable();
    table.string('contingency_type').nullable().defaultTo(null);
    table.string('contingency_reason').nullable().defaultTo(null);
    table.timestamp('invoice_date').notNullable();
    table.integer('currency_type').notNullable();
    table.text('signature').notNullable();
    table.string('received').nullable().defaultTo(null);

    table
      .timestamp('created_at')
      .notNullable()
      .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    table
      .timestamp('updated_at')
      .notNullable()
      .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

    table
      .foreign('invoice_type_id', 'fk_invoices_type_invoice_type_id')
      .references('id')
      .inTable('invoice_types');
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('invoices');
}
