import { Model } from '@app/db';

export class WarehousePullSessions extends Model {
  static get tableName(): string {
    return 'warehouse_pull_sessions';
  }

  id!: number;
  employee_id!: number;
  warehouse_pull_session_type_id!: number;
  finished_at?: Date;
  default_reason?: string;
  default_comment?: string;
  status!: string;
  customer!: string;
  is_reposition?: boolean;
  created_at?: Date;
  updated_at?: Date;
}

export class WarehousePullSessionAllocations extends Model {
  static get tableName(): string {
    return 'warehouse_pull_session_allocations';
  }

  id!: number;
  warehouse_pull_session_order_part_id!: number;
  container_code!: string;
  quantity!: number;
  reason?: string;
  comment?: string;
  poly_updated_at?: Date;
  poly_updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
}

export class WarehousePullSessionOrders extends Model {
  static get tableName(): string {
    return 'warehouse_pull_session_orders';
  }

  id!: number;
  warehouse_pull_session_id!: number;
  mo_id!: number;
  work_area_voucher_id?: number;
  default_reason?: string;
  default_comment?: string;
  created_at?: Date;
  updated_at?: Date;
}

export class WarehousePullSessionOrderParts extends Model {
  static get tableName(): string {
    return 'warehouse_pull_session_order_parts';
  }

  id!: number;
  warehouse_pull_session_order_id!: number;
  part_number!: string;
  sub_category!: string;
  units!: string;
  quantity_bom!: number;
  quantity_rounded!: number;
  quantity_pulling!: number;
  created_at?: Date;
  updated_at?: Date;
}

export class WarehouseDownloaderOrders extends Model {
  static get tableName(): string {
    return 'warehouse_downloader_orders';
  }

  id!: number;
  employee_id!: number;
  mo_id!: number;
  finished_at?: Date;
  created_at?: Date;
  updated_at?: Date;
}

export class WarehousePullSessionTypes extends Model {
  static get tableName(): string {
    return 'warehouse_pull_session_types';
  }

  id!: number;
  name!: string;
  description?: string;
  is_active?: boolean;
  created_at?: Date;
  updated_at?: Date;
}
