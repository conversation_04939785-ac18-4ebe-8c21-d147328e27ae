import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'plotter_combos',
    (table: Knex.TableBuilder) => {
      table.boolean('is_laser').defaultTo(false);
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'plotter_combos',
    (table: Knex.TableBuilder) => {
      table.dropColumn('is_laser');
    }
  );
}
