import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(
    'invoice_items',
    (table: Knex.TableBuilder) => {
      table.increments('id').unsigned().primary();
      table.integer('invoice_id').unsigned().notNullable();
      table.integer('item_number', 4).notNullable();
      table.integer('item_type', 4).nullable().defaultTo(null);
      table.float('quantity', 11, 8).nullable().defaultTo(null);
      table.string('item_code').nullable().defaultTo(null);
      table.string('document_number').nullable().defaultTo(null);
      table.integer('measurement').notNullable();
      table.text('description').notNullable();
      table.float('unit_price', 11, 8).notNullable();
      table.float('discount', 11, 8).nullable().defaultTo(null);
      table.string('taxes_code').nullable().defaultTo(null);
      table.float('taxed_sales', 11, 8).nullable().defaultTo(null);
      table.string('taxes').nullable().defaultTo(null);
      table.float('untaxed_sales', 11, 8).nullable().defaultTo(null);
      table.float('buy', 11, 8).nullable().defaultTo(null);
      table.float('sales_not_subjected', 11, 8).nullable().defaultTo(null);
      table.float('exempt_sales', 11, 8).nullable().defaultTo(null);
      table.float('suggested_price', 11, 8).nullable().defaultTo(null);
      table.float('vat_per_item', 11, 8).nullable().defaultTo(null);

      table
        .timestamp('created_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      table
        .timestamp('updated_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

      table
        .foreign('invoice_id', 'fk_invoice_items_invoice_id')
        .references('id')
        .inTable('invoices');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('invoice_items');
}
