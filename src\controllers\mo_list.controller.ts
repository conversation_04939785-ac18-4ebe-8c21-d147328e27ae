import dayjs from 'dayjs';
import type { Request, Response } from 'express';
import { raw, ref } from 'objection';
import { v4 as uuidv4 } from 'uuid';

import { Employee } from '@app/models/employee.schema';
import {
  MoNumber,
  WorkActivityLog,
  WorkAreaLists,
  WorkAreaTickets,
  WorkAreas,
  WorkInventoryBins,
  WorkListMos,
  WorkLists,
  WorkVoucherGroups,
  WorkVouchers,
} from '@app/models/tickets.schema';

export async function getMoInfoBatch(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const { mo, client }: { mo: string; client: number } = req.body;
    const formatDate = 'YYYY-MM-DD';

    const searchMo: any = await MoNumber.query()
      .where('mo_numbers.mo_barcode', mo)
      .where('mo_numbers.company_code', client)
      .whereNotIn('mo_numbers.mo_status', [
        'Void',
        'Cancelled',
        'Materials',
        'Complete',
      ])
      .select(
        'mo_numbers.mo_id',
        'mo_numbers.customer',
        'mo_numbers.mo_order',
        'mo_numbers.num',
        'mo_numbers.required_date',
        'mo_numbers.style',
        'mo_numbers.quantity'
      );

    if (searchMo.length > 0) {
      return res.status(200).json({
        ok: true,
        mo_id: searchMo[0].mo_id,
        customer: searchMo[0].customer,
        mo_order: searchMo[0].mo_order,
        num: searchMo[0].num,
        required_date: dayjs(searchMo[0].required_date).format(formatDate),
        style: searchMo[0].style,
        quantity: searchMo[0].quantity,
      });
    } else {
      return res
        .status(204)
        .json({ ok: true, message: 'No se encontro la mo' });
    }
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function getMosListbyVolumen(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const {
      mo: { client, mos },
    }: {
      mo: { client: number; mos: string[] };
    } = req.body;
    const formatDate = 'YYYY-MM-DD';

    const data = [];

    for (let i = 0; i < mos.length; i++) {
      if (mos[i]) {
        const getMoInfo: any = await MoNumber.query()
          .whereNotIn('mo_numbers.mo_status', [
            'Void',
            'Cancelled',
            'Materials',
            'Complete',
          ])
          .where('mo_numbers.num', mos[i])
          .where('mo_numbers.company_code', client)
          .select(
            'mo_numbers.mo_id',
            'mo_numbers.customer',
            'mo_numbers.mo_order',
            'mo_numbers.num',
            'mo_numbers.required_date',
            'mo_numbers.style',
            'mo_numbers.quantity'
          );

        data.push({
          mo_id: getMoInfo[0].mo_id,
          customer: getMoInfo[0].customer,
          mo_order: getMoInfo[0].mo_order,
          num: getMoInfo[0].num,
          required_date: dayjs(getMoInfo[0].required_date).format(formatDate),
          style: getMoInfo[0].style,
          quantity: getMoInfo[0].quantity,
        });
      }
    }

    return res.status(200).json({
      ok: true,
      data,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function addListMo(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const {
      codeEmployee,
      typeBatch,
      area,
    }: { codeEmployee: number; typeBatch: string; area: number } = req.body;

    const newBatch: any = await WorkLists.query().insert({
      type: typeBatch,
      employee_id: codeEmployee,
      work_area_id: area,
    });

    if (!newBatch)
      return res
        .status(204)
        .json({ ok: false, message: 'No se logro crear la lista' });

    const newBatchArea: any = await WorkAreaLists.query().insert({
      work_list_id: newBatch.id,
      work_area_id: area,
    });

    if (!newBatchArea)
      return res.status(204).json({
        ok: false,
        message: 'No se logro crear la lista en tu area',
      });

    const ticketLog = await WorkActivityLog.query().insert({
      employee_id: codeEmployee,
      work_area_id: area,
      module_name: 'moList',
      module_id: newBatchArea.id,
      activity: 'MoListCreated',
      data: JSON.stringify({}),
    });

    if (!ticketLog)
      return res.status(204).json({
        ok: false,
        message: 'No se logro crear el historial',
      });

    return res.status(201).json({
      ok: true,
      moListId: newBatch.id,
      moListAreaId: newBatchArea.id,
    });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function addMoListArea(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const {
      area,
      list,
      codeEmployee,
    }: { area: number; list: number; codeEmployee: number } = req.body;

    const newBatchArea: any = await WorkAreaLists.query().insert({
      work_list_id: list,
      work_area_id: area,
    });

    if (!newBatchArea)
      return res.status(204).json({
        ok: false,
        message: 'No se logro crear la lista en tu area',
      });

    const ticketLogArea = await WorkActivityLog.query().insert({
      employee_id: codeEmployee,
      work_area_id: area,
      module_name: 'moList',
      module_id: newBatchArea.id,
      activity: 'MoListCreated',
      data: JSON.stringify({}),
    });

    if (!ticketLogArea)
      return res.status(204).json({
        ok: false,
        message: 'No se logro crear el historial',
      });

    const getNameArea: any = await WorkAreas.query()
      .where('work_areas.work_area_id', area)
      .select('work_areas.area_name');

    const ticketLog = await WorkActivityLog.query().insert({
      employee_id: codeEmployee,
      work_area_id: area,
      module_name: 'moList',
      module_id: list,
      activity: 'MoListAreaAdded',
      data: JSON.stringify({
        areaNew: getNameArea[0].area_name,
      }),
    });

    if (!ticketLog)
      return res.status(204).json({
        ok: false,
        message: 'No se logro crear el historial',
      });

    return res.status(201).json({
      ok: true,
      newBatchArea,
    });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function moListDeleteItem(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const {
      moListId,
      codeEmployee,
      area,
    }: { moListId: number; codeEmployee: number; area: number } = req.body;

    const formatDate = 'YYYY-MM-DD HH:mm:ss';
    const actualDate = new Date();

    const getInfoMoListItem: any = await WorkListMos.query()
      .join('work_lists', 'work_list_mos.work_list_id', 'work_lists.id')
      .join('work_area_lists', 'work_lists.id', 'work_area_lists.work_list_id')
      .where('work_lists.work_area_id', area)
      .where('work_list_mos.id', moListId)
      .select(
        { workListIdMain: 'work_lists.id' },
        { workListMoId: 'work_list_mos.id' },
        'work_lists.work_list_id',
        'work_list_mos.mo_id'
      );

    const getMoInfo: any = await MoNumber.query()
      .whereNotIn('mo_numbers.mo_status', [
        'Void',
        'Cancelled',
        'Materials',
        'Complete',
      ])
      .where('mo_numbers.mo_id', getInfoMoListItem[0].mo_id)
      .select('mo_numbers.mo_id', 'mo_numbers.num');

    if (getInfoMoListItem.length > 0) {
      const deleteItemlistMo = await WorkListMos.query()
        .update({
          removed_at: dayjs(actualDate).format(formatDate),
        })
        .where('work_list_mos.id', moListId);

      const ticketLog = await WorkActivityLog.query().insert({
        employee_id: codeEmployee,
        work_area_id: area,
        module_name: 'moList',
        module_id: getInfoMoListItem[0].work_list_id,
        activity: 'MoListDeleteItem',
        data: JSON.stringify({
          itemDeleted: getMoInfo[0].num,
        }),
      });

      if (!ticketLog)
        return res.status(204).json({
          ok: false,
          message: 'No se logro crear el historial',
        });

      return res.status(200).json({ ok: true, deleteItemlistMo });
    } else {
      return res.status(403).json({
        ok: false,
        message: 'No tienes permisos para eliminar la mo de la lista',
      });
    }
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}
interface DataWorkAreaList {
  area_name: string;
  comment: string;
  created_at: string;
  employee_id: number;
  workListId: number;
  type: string;
  work_list_id: number;
  workAreaListId: number;
}

interface ICountMoVouchers {
  isRepo: string;
  areaTicketId: number;
  moId: number;
  areaTicket: number;
  ticketStatus: string;
  ticketStatusId: number;
  expDate: string;
  nextArea: number;
  voucherType: string;
  workAreaTicketsId: number;
  finishedAtTicket: string;
  workVoucherId: number;
}
interface IdataWorkArea {
  area_name: string;
  comment?: string;
  created_at: string;
  customer?: string;
  employee_id: number;
  id?: number;
  mo_id: number;
  mo_order?: string;
  mo_status?: string;
  nameAreaList?: string;
  nameEmployee?: string;
  num?: string;
  quantity?: number;
  required_date?: string;
  style?: string;
  type: string;
  work_list_id: number;
  workAreaListId: number;
  workListId: number;
  workListMoId?: number;
}

interface ITicketAreaWithMoList {
  areaTicket: string;
  areaTicketId: number;
  expDate: string;
  finishedAtTicket: string;
  isRepo: number;
  mo_id?: number;
  moId: number;
  nextArea: number;
  ticketStatus: string;
  ticketStatusId: number;
  voucherType: string;
  work_area_id: number;
  work_area_ticket_status_id: number;
  workAreaTicketsId: number;
  workVoucherId: number;
  mo_status?: string;
}
export async function getListArea(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const {
      area,
      areaFilter,
      voucherType,
    }: { area: number; areaFilter: string; voucherType: string } = req.body;
    const listInfo = [];
    let getDataWorkListMo;

    if (areaFilter === 'all' && voucherType === 'all') {
      const getDataWorkAreaLists = (await WorkAreaLists.query()
        .join('work_lists', 'work_area_lists.work_list_id', 'work_lists.id')
        .join(
          'work_areas',
          'work_area_lists.work_area_id',
          'work_areas.work_area_id'
        )
        .select(
          { workAreaListId: 'work_area_lists.id' },
          'work_areas.area_name',
          'work_area_lists.comment',
          'work_area_lists.work_list_id',
          'work_area_lists.created_at',
          'work_lists.type',
          'work_lists.employee_id',
          { workListId: 'work_lists.id' }
        )
        .where('work_area_lists.work_area_id', area)
        .whereNull(
          'work_area_lists.removed_at'
        )) as unknown as DataWorkAreaList[];

      for (let i = 0; i < getDataWorkAreaLists.length; i++) {
        const countVouchersMoAreaLop: ICountMoVouchers[] = [];
        const countVouchersCompleteMoAreaLop: ICountMoVouchers[] = [];

        getDataWorkListMo = (await WorkListMos.query()
          .join('mo_numbers', 'work_list_mos.mo_id', 'mo_numbers.mo_id')
          .select('mo_numbers.mo_id')
          .where(
            'work_list_mos.work_list_id',
            getDataWorkAreaLists[i].workListId
          )
          .whereNull('work_list_mos.removed_at')) as unknown as {
          mo_id: number;
        }[];

        for (let j = 0; j < getDataWorkListMo.length; j++) {
          const searchTicketAreaWithMoList = (await WorkAreaTickets.query()
            .join(
              'work_vouchers',
              'work_area_tickets.work_voucher_id',
              'work_vouchers.id'
            )
            .join(
              'work_voucher_types',
              'work_vouchers.work_voucher_type_id',
              'work_voucher_types.id'
            )
            .join(
              'work_areas',
              'work_area_tickets.work_area_id',
              'work_areas.work_area_id'
            )
            .join(
              'work_area_ticket_statuses',
              'work_area_tickets.work_area_ticket_status_id',
              'work_area_ticket_statuses.id'
            )
            .where('work_vouchers.mo_id', getDataWorkListMo[j].mo_id)
            .select([
              { isRepo: 'work_vouchers.is_repo' },
              { areaTicketId: 'work_areas.work_area_id' },
              { moId: 'work_vouchers.mo_id' },

              { areaTicket: 'work_areas.area_name' },
              {
                ticketStatus: 'work_area_ticket_statuses.name',
              },
              {
                ticketStatusId: 'work_area_ticket_statuses.work_status_id',
              },
              { expDate: 'work_area_tickets.exp_finish_date' },
              WorkAreas.query()
                .where(
                  'work_areas.work_area_id',
                  ref('work_area_tickets.next_work_area_id')
                )
                .select('work_areas.area_name')
                .as('nextArea'),
              { voucherType: 'work_voucher_types.name' },
              { workAreaTicketsId: 'work_area_tickets.id' },
              { finishedAtTicket: 'work_area_tickets.finished_at' },
              { workVoucherId: 'work_vouchers.id' },
            ])) as unknown as ICountMoVouchers[];

          // obtenemos el total de vouchers en nuestra area como fuera de ella y obtenemos las mos que no tienen ningun voucher
          for (let k = 0; k < searchTicketAreaWithMoList.length; k++) {
            if (searchTicketAreaWithMoList[k]?.areaTicketId === area) {
              if (searchTicketAreaWithMoList[k]?.ticketStatusId !== 110) {
                countVouchersMoAreaLop.push(searchTicketAreaWithMoList[k]);
              }
              if (searchTicketAreaWithMoList[k]?.ticketStatusId === 100) {
                countVouchersCompleteMoAreaLop.push(
                  searchTicketAreaWithMoList[k]
                );
              }
            }
          }
        }

        listInfo.push({
          workAreaListId: getDataWorkAreaLists[i].workAreaListId,
          comment: getDataWorkAreaLists[i].comment,
          created_at: getDataWorkAreaLists[i].created_at,
          type: getDataWorkAreaLists[i].type,
          workListId: getDataWorkAreaLists[i].workListId,
          workAreaNameOrigin: getDataWorkAreaLists[i].area_name,
          totalVoucherArea: countVouchersMoAreaLop.length,
          totalVoucherCompleteArea: countVouchersCompleteMoAreaLop.length,
          totalVoucherActiveArea: countVouchersMoAreaLop.filter(
            (voucher: ICountMoVouchers) => {
              if (!voucher?.finishedAtTicket) {
                return true;
              }
            }
          ).length,
        });
      }

      return res.json({ ok: true, data: listInfo });
    } else if (areaFilter !== 'all' && voucherType === 'all') {
      const getDataWorkAreaLists = (await WorkAreaLists.query()
        .join('work_lists', 'work_area_lists.work_list_id', 'work_lists.id')
        .join(
          'work_areas',
          'work_area_lists.work_area_id',
          'work_areas.work_area_id'
        )
        .select(
          { workAreaListId: 'work_area_lists.id' },
          'work_areas.area_name',
          'work_area_lists.comment',
          'work_area_lists.work_list_id',
          'work_area_lists.created_at',
          'work_lists.type',
          'work_lists.employee_id',
          { workListId: 'work_lists.id' }
        )
        .where('work_area_lists.work_area_id', areaFilter)
        .whereNull('work_area_lists.removed_at')) as unknown as IdataWorkArea[];

      for (let i = 0; i < getDataWorkAreaLists.length; i++) {
        const countVouchersMoAreaLop = [];
        const countVouchersCompleteMoAreaLop = [];

        getDataWorkListMo = await WorkListMos.query()
          .join('mo_numbers', 'work_list_mos.mo_id', 'mo_numbers.mo_id')
          .select({ workListMoId: 'work_list_mos.id' }, 'mo_numbers.mo_id')
          .where(
            'work_list_mos.work_list_id',
            getDataWorkAreaLists[i].workListId
          )
          .whereNull('work_list_mos.removed_at');

        for (let j = 0; j < getDataWorkListMo.length; j++) {
          const searchTicketAreaWithMoList = (await WorkAreaTickets.query()
            .join(
              'work_vouchers',
              'work_area_tickets.work_voucher_id',
              'work_vouchers.id'
            )
            .join(
              'work_voucher_types',
              'work_vouchers.work_voucher_type_id',
              'work_voucher_types.id'
            )
            .join(
              'work_areas',
              'work_area_tickets.work_area_id',
              'work_areas.work_area_id'
            )
            .join(
              'work_area_ticket_statuses',
              'work_area_tickets.work_area_ticket_status_id',
              'work_area_ticket_statuses.id'
            )
            .where('work_vouchers.mo_id', getDataWorkListMo[j].mo_id)
            .select([
              { isRepo: 'work_vouchers.is_repo' },
              { areaTicketId: 'work_areas.work_area_id' },
              { moId: 'work_vouchers.mo_id' },

              { areaTicket: 'work_areas.area_name' },
              {
                ticketStatus: 'work_area_ticket_statuses.name',
              },
              {
                ticketStatusId: 'work_area_ticket_statuses.work_status_id',
              },
              { expDate: 'work_area_tickets.exp_finish_date' },
              WorkAreas.query()
                .where(
                  'work_areas.work_area_id',
                  ref('work_area_tickets.next_work_area_id')
                )
                .select('work_areas.area_name')
                .as('nextArea'),
              { voucherType: 'work_voucher_types.name' },
              { workAreaTicketsId: 'work_area_tickets.id' },
              { finishedAtTicket: 'work_area_tickets.finished_at' },
              { workVoucherId: 'work_vouchers.id' },
            ])) as unknown as ITicketAreaWithMoList[];

          // obtenemos el total de vouchers en nuestra area como fuera de ella y obtenemos las mos que no tienen ningun voucher
          for (let k = 0; k < searchTicketAreaWithMoList.length; k++) {
            if (searchTicketAreaWithMoList[k]?.areaTicketId === area) {
              if (searchTicketAreaWithMoList[k]?.ticketStatusId !== 110) {
                countVouchersMoAreaLop.push(searchTicketAreaWithMoList[k]);
              }
              if (searchTicketAreaWithMoList[k]?.ticketStatusId === 100) {
                countVouchersCompleteMoAreaLop.push(
                  searchTicketAreaWithMoList[k]
                );
              }
            }
          }
        }

        listInfo.push({
          workAreaListId: getDataWorkAreaLists[i].workAreaListId,
          comment: getDataWorkAreaLists[i].comment,
          created_at: getDataWorkAreaLists[i].created_at,
          type: getDataWorkAreaLists[i].type,
          workListId: getDataWorkAreaLists[i].workListId,
          workAreaNameOrigin: getDataWorkAreaLists[i].area_name,
          totalVoucherArea: countVouchersMoAreaLop.length,
          totalVoucherCompleteArea: countVouchersCompleteMoAreaLop.length,
          totalVoucherActiveArea: countVouchersMoAreaLop.filter(
            (voucher: { finishedAtTicket: boolean }) => {
              if (!voucher.finishedAtTicket) {
                return true;
              }
            }
          ).length,
        });
      }

      return res.json({ ok: true, data: listInfo });
    } else if (areaFilter === 'all' && voucherType !== 'all') {
      const getDataWorkAreaLists = (await WorkAreaLists.query()
        .join('work_lists', 'work_area_lists.work_list_id', 'work_lists.id')
        .join(
          'work_areas',
          'work_area_lists.work_area_id',
          'work_areas.work_area_id'
        )
        .join(
          'work_list_mos',
          'work_area_lists.id',
          'work_list_mos.work_list_id'
        )
        .join('work_vouchers', 'work_list_mos.mo_id', 'work_vouchers.mo_id')
        .join(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .select(
          { workAreaListId: 'work_area_lists.id' },
          'work_areas.area_name',
          'work_area_lists.comment',
          'work_area_lists.work_list_id',
          'work_area_lists.created_at',
          'work_lists.type',
          'work_lists.employee_id',
          { workListId: 'work_lists.id' }
        )
        .where('work_voucher_types.id', voucherType)
        .whereNull('work_area_lists.removed_at')) as unknown as IdataWorkArea[];

      for (let i = 0; i < getDataWorkAreaLists.length; i++) {
        const countVouchersMoAreaLop = [];
        const countVouchersCompleteMoAreaLop = [];

        getDataWorkListMo = await WorkListMos.query()
          .join('mo_numbers', 'work_list_mos.mo_id', 'mo_numbers.mo_id')
          .select({ workListMoId: 'work_list_mos.id' }, 'mo_numbers.mo_id')
          .where(
            'work_list_mos.work_list_id',
            getDataWorkAreaLists[i].workListId
          )
          .whereNull('work_list_mos.removed_at');

        for (let j = 0; j < getDataWorkListMo.length; j++) {
          const searchTicketAreaWithMoList = (await WorkAreaTickets.query()
            .join(
              'work_vouchers',
              'work_area_tickets.work_voucher_id',
              'work_vouchers.id'
            )
            .join(
              'work_voucher_types',
              'work_vouchers.work_voucher_type_id',
              'work_voucher_types.id'
            )
            .join(
              'work_areas',
              'work_area_tickets.work_area_id',
              'work_areas.work_area_id'
            )
            .join(
              'work_area_ticket_statuses',
              'work_area_tickets.work_area_ticket_status_id',
              'work_area_ticket_statuses.id'
            )
            .where('work_vouchers.mo_id', getDataWorkListMo[j].mo_id)
            .select([
              { isRepo: 'work_vouchers.is_repo' },
              { areaTicketId: 'work_areas.work_area_id' },
              { moId: 'work_vouchers.mo_id' },

              { areaTicket: 'work_areas.area_name' },
              {
                ticketStatus: 'work_area_ticket_statuses.name',
              },
              {
                ticketStatusId: 'work_area_ticket_statuses.work_status_id',
              },
              { expDate: 'work_area_tickets.exp_finish_date' },
              WorkAreas.query()
                .where(
                  'work_areas.work_area_id',
                  ref('work_area_tickets.next_work_area_id')
                )
                .select('work_areas.area_name')
                .as('nextArea'),
              { voucherType: 'work_voucher_types.name' },
              { workAreaTicketsId: 'work_area_tickets.id' },
              { finishedAtTicket: 'work_area_tickets.finished_at' },
              { workVoucherId: 'work_vouchers.id' },
            ])) as unknown as ITicketAreaWithMoList[];

          // obtenemos el total de vouchers en nuestra area como fuera de ella y obtenemos las mos que no tienen ningun voucher
          for (let k = 0; k < searchTicketAreaWithMoList.length; k++) {
            if (searchTicketAreaWithMoList[k]?.areaTicketId === area) {
              if (searchTicketAreaWithMoList[k]?.ticketStatusId !== 110) {
                countVouchersMoAreaLop.push(searchTicketAreaWithMoList[k]);
              }
              if (searchTicketAreaWithMoList[k]?.ticketStatusId === 100) {
                countVouchersCompleteMoAreaLop.push(
                  searchTicketAreaWithMoList[k]
                );
              }
            }
          }
        }

        listInfo.push({
          workAreaListId: getDataWorkAreaLists[i].workAreaListId,
          comment: getDataWorkAreaLists[i].comment,
          created_at: getDataWorkAreaLists[i].created_at,
          type: getDataWorkAreaLists[i].type,
          workListId: getDataWorkAreaLists[i].workListId,
          workAreaNameOrigin: getDataWorkAreaLists[i].area_name,
          totalVoucherArea: countVouchersMoAreaLop.length,
          totalVoucherCompleteArea: countVouchersCompleteMoAreaLop.length,
          totalVoucherActiveArea: countVouchersMoAreaLop.filter(
            (voucher: { finishedAtTicket: boolean }) => {
              if (!voucher.finishedAtTicket) {
                return true;
              }
            }
          ).length,
        });
      }

      return res.json({ ok: true, data: listInfo });
    } else if (areaFilter !== 'all' && voucherType !== 'all') {
      const getDataWorkAreaLists = (await WorkAreaLists.query()
        .join('work_lists', 'work_area_lists.work_list_id', 'work_lists.id')
        .join(
          'work_areas',
          'work_area_lists.work_area_id',
          'work_areas.work_area_id'
        )
        .join(
          'work_list_mos',
          'work_area_lists.id',
          'work_list_mos.work_list_id'
        )
        .join('work_vouchers', 'work_list_mos.mo_id', 'work_vouchers.mo_id')
        .join(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .select(
          { workAreaListId: 'work_area_lists.id' },
          'work_areas.area_name',
          'work_area_lists.comment',
          'work_area_lists.work_list_id',
          'work_area_lists.created_at',
          'work_lists.type',
          'work_lists.employee_id',
          { workListId: 'work_lists.id' }
        )
        .where('work_area_lists.work_area_id', areaFilter)
        .where('work_voucher_types.id', voucherType)
        .whereNull('work_area_lists.removed_at')) as unknown as IdataWorkArea[];

      for (let i = 0; i < getDataWorkAreaLists.length; i++) {
        const countVouchersMoAreaLop = [];
        const countVouchersCompleteMoAreaLop = [];

        getDataWorkListMo = await WorkListMos.query()
          .join('mo_numbers', 'work_list_mos.mo_id', 'mo_numbers.mo_id')
          .select({ workListMoId: 'work_list_mos.id' }, 'mo_numbers.mo_id')
          .where(
            'work_list_mos.work_list_id',
            getDataWorkAreaLists[i].workListId
          )
          .whereNull('work_list_mos.removed_at');

        for (let j = 0; j < getDataWorkListMo.length; j++) {
          const searchTicketAreaWithMoList = (await WorkAreaTickets.query()
            .join(
              'work_vouchers',
              'work_area_tickets.work_voucher_id',
              'work_vouchers.id'
            )
            .join(
              'work_voucher_types',
              'work_vouchers.work_voucher_type_id',
              'work_voucher_types.id'
            )
            .join(
              'work_areas',
              'work_area_tickets.work_area_id',
              'work_areas.work_area_id'
            )
            .join(
              'work_area_ticket_statuses',
              'work_area_tickets.work_area_ticket_status_id',
              'work_area_ticket_statuses.id'
            )
            .where('work_vouchers.mo_id', getDataWorkListMo[j].mo_id)
            .select([
              { isRepo: 'work_vouchers.is_repo' },
              { areaTicketId: 'work_areas.work_area_id' },
              { moId: 'work_vouchers.mo_id' },

              { areaTicket: 'work_areas.area_name' },
              {
                ticketStatus: 'work_area_ticket_statuses.name',
              },
              {
                ticketStatusId: 'work_area_ticket_statuses.work_status_id',
              },
              { expDate: 'work_area_tickets.exp_finish_date' },
              WorkAreas.query()
                .where(
                  'work_areas.work_area_id',
                  ref('work_area_tickets.next_work_area_id')
                )
                .select('work_areas.area_name')
                .as('nextArea'),
              { voucherType: 'work_voucher_types.name' },
              { workAreaTicketsId: 'work_area_tickets.id' },
              { finishedAtTicket: 'work_area_tickets.finished_at' },
              { workVoucherId: 'work_vouchers.id' },
            ])) as unknown as ITicketAreaWithMoList[];

          // obtenemos el total de vouchers en nuestra area como fuera de ella y obtenemos las mos que no tienen ningun voucher
          for (let k = 0; k < searchTicketAreaWithMoList.length; k++) {
            if (searchTicketAreaWithMoList[k]?.areaTicketId === area) {
              if (searchTicketAreaWithMoList[k]?.ticketStatusId !== 110) {
                countVouchersMoAreaLop.push(searchTicketAreaWithMoList[k]);
              }
              if (searchTicketAreaWithMoList[k]?.ticketStatusId === 100) {
                countVouchersCompleteMoAreaLop.push(
                  searchTicketAreaWithMoList[k]
                );
              }
            }
          }
        }

        listInfo.push({
          workAreaListId: getDataWorkAreaLists[i].workAreaListId,
          comment: getDataWorkAreaLists[i].comment,
          created_at: getDataWorkAreaLists[i].created_at,
          type: getDataWorkAreaLists[i].type,
          workListId: getDataWorkAreaLists[i].workListId,
          workAreaNameOrigin: getDataWorkAreaLists[i].area_name,
          totalVoucherArea: countVouchersMoAreaLop.length,
          totalVoucherCompleteArea: countVouchersCompleteMoAreaLop.length,
          totalVoucherActiveArea: countVouchersMoAreaLop.filter(
            (voucher: { finishedAtTicket: boolean }) => {
              if (!voucher.finishedAtTicket) {
                return true;
              }
            }
          ).length,
        });
      }

      return res.json({ ok: true, data: listInfo });
    } else {
      return res.status(400).json({
        ok: false,
        message: 'areaFilter or voucherType incorrect',
      });
    }
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}
export async function getAllListArea(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const {
      area,
      areaFilter,
      voucherType,
    }: { area: number; areaFilter: string; voucherType: string } = req.body;
    const listInfo = [];
    let getDataWorkListMo;

    if (areaFilter === 'all' && voucherType === 'all') {
      const getDataWorkAreaLists = (await WorkAreaLists.query()
        .join('work_lists', 'work_area_lists.work_list_id', 'work_lists.id')
        .join(
          'work_areas',
          'work_area_lists.work_area_id',
          'work_areas.work_area_id'
        )
        .select(
          { workAreaListId: 'work_area_lists.id' },
          'work_areas.area_name',
          'work_area_lists.comment',
          'work_area_lists.work_list_id',
          'work_area_lists.created_at',
          'work_lists.type',
          'work_lists.employee_id',
          { workListId: 'work_lists.id' }
        )
        .where(
          'work_area_lists.work_area_id',
          area
        )) as unknown as IdataWorkArea[];

      for (let i = 0; i < getDataWorkAreaLists.length; i++) {
        const countVouchersMoAreaLop = [];

        getDataWorkListMo = await WorkListMos.query()
          .join('mo_numbers', 'work_list_mos.mo_id', 'mo_numbers.mo_id')
          .select('mo_numbers.mo_id')
          .where(
            'work_list_mos.work_list_id',
            getDataWorkAreaLists[i].workListId
          )
          .whereNull('work_list_mos.removed_at');

        for (let j = 0; j < getDataWorkListMo.length; j++) {
          const searchTicketAreaWithMoList = (await WorkAreaTickets.query()
            .join(
              'work_vouchers',
              'work_area_tickets.work_voucher_id',
              'work_vouchers.id'
            )
            .join(
              'work_voucher_types',
              'work_vouchers.work_voucher_type_id',
              'work_voucher_types.id'
            )
            .join(
              'work_areas',
              'work_area_tickets.work_area_id',
              'work_areas.work_area_id'
            )
            .join(
              'work_area_ticket_statuses',
              'work_area_tickets.work_area_ticket_status_id',
              'work_area_ticket_statuses.id'
            )
            .where('work_vouchers.mo_id', getDataWorkListMo[j].mo_id)
            .select([
              { isRepo: 'work_vouchers.is_repo' },
              { areaTicketId: 'work_areas.work_area_id' },
              { moId: 'work_vouchers.mo_id' },

              { areaTicket: 'work_areas.area_name' },
              {
                ticketStatus: 'work_area_ticket_statuses.name',
              },
              {
                ticketStatusId: 'work_area_ticket_statuses.work_status_id',
              },
              { expDate: 'work_area_tickets.exp_finish_date' },
              WorkAreas.query()
                .where(
                  'work_areas.work_area_id',
                  ref('work_area_tickets.next_work_area_id')
                )
                .select('work_areas.area_name')
                .as('nextArea'),
              { voucherType: 'work_voucher_types.name' },
              { workAreaTicketsId: 'work_area_tickets.id' },
              { finishedAtTicket: 'work_area_tickets.finished_at' },
              { workVoucherId: 'work_vouchers.id' },
            ])) as unknown as ITicketAreaWithMoList[];

          // obtenemos el total de vouchers en nuestra area como fuera de ella y obtenemos las mos que no tienen ningun voucher
          for (let k = 0; k < searchTicketAreaWithMoList.length; k++) {
            if (searchTicketAreaWithMoList[k]?.areaTicketId === area) {
              if (searchTicketAreaWithMoList[k]?.ticketStatusId !== 110) {
                countVouchersMoAreaLop.push(searchTicketAreaWithMoList[k]);
              }
            }
          }
        }

        listInfo.push({
          workAreaListId: getDataWorkAreaLists[i].workAreaListId,
          comment: getDataWorkAreaLists[i].comment,
          created_at: getDataWorkAreaLists[i].created_at,
          type: getDataWorkAreaLists[i].type,
          workListId: getDataWorkAreaLists[i].workListId,
          workAreaNameOrigin: getDataWorkAreaLists[i].area_name,
          totalVoucherArea: countVouchersMoAreaLop.length,
          totalVoucherActiveArea: countVouchersMoAreaLop.filter(
            (voucher: { finishedAtTicket: boolean }) => {
              if (!voucher?.finishedAtTicket) {
                return true;
              }
            }
          ).length,
        });
      }

      return res.json({ ok: true, data: listInfo });
    } else if (areaFilter !== 'all' && voucherType === 'all') {
      const getDataWorkAreaLists = (await WorkAreaLists.query()
        .join('work_lists', 'work_area_lists.work_list_id', 'work_lists.id')
        .join(
          'work_areas',
          'work_area_lists.work_area_id',
          'work_areas.work_area_id'
        )
        .select(
          { workAreaListId: 'work_area_lists.id' },
          'work_areas.area_name',
          'work_area_lists.comment',
          'work_area_lists.work_list_id',
          'work_area_lists.created_at',
          'work_lists.type',
          'work_lists.employee_id',
          { workListId: 'work_lists.id' }
        )
        .where(
          'work_area_lists.work_area_id',
          areaFilter
        )) as unknown as IdataWorkArea[];

      for (let i = 0; i < getDataWorkAreaLists.length; i++) {
        const countVouchersMoAreaLop = [];

        getDataWorkListMo = await WorkListMos.query()
          .join('mo_numbers', 'work_list_mos.mo_id', 'mo_numbers.mo_id')
          .select({ workListMoId: 'work_list_mos.id' }, 'mo_numbers.mo_id')
          .where(
            'work_list_mos.work_list_id',
            getDataWorkAreaLists[i].workListId
          )
          .whereNull('work_list_mos.removed_at');

        for (let j = 0; j < getDataWorkListMo.length; j++) {
          const searchTicketAreaWithMoList = (await WorkAreaTickets.query()
            .join(
              'work_vouchers',
              'work_area_tickets.work_voucher_id',
              'work_vouchers.id'
            )
            .join(
              'work_voucher_types',
              'work_vouchers.work_voucher_type_id',
              'work_voucher_types.id'
            )
            .join(
              'work_areas',
              'work_area_tickets.work_area_id',
              'work_areas.work_area_id'
            )
            .join(
              'work_area_ticket_statuses',
              'work_area_tickets.work_area_ticket_status_id',
              'work_area_ticket_statuses.id'
            )
            .where('work_vouchers.mo_id', getDataWorkListMo[j].mo_id)
            .select([
              { isRepo: 'work_vouchers.is_repo' },
              { areaTicketId: 'work_areas.work_area_id' },
              { moId: 'work_vouchers.mo_id' },

              { areaTicket: 'work_areas.area_name' },
              {
                ticketStatus: 'work_area_ticket_statuses.name',
              },
              {
                ticketStatusId: 'work_area_ticket_statuses.work_status_id',
              },
              { expDate: 'work_area_tickets.exp_finish_date' },
              WorkAreas.query()
                .where(
                  'work_areas.work_area_id',
                  ref('work_area_tickets.next_work_area_id')
                )
                .select('work_areas.area_name')
                .as('nextArea'),
              { voucherType: 'work_voucher_types.name' },
              { workAreaTicketsId: 'work_area_tickets.id' },
              { finishedAtTicket: 'work_area_tickets.finished_at' },
              { workVoucherId: 'work_vouchers.id' },
            ])) as unknown as ITicketAreaWithMoList[];

          // obtenemos el total de vouchers en nuestra area como fuera de ella y obtenemos las mos que no tienen ningun voucher
          for (let k = 0; k < searchTicketAreaWithMoList.length; k++) {
            if (searchTicketAreaWithMoList[k]?.areaTicketId === area) {
              if (searchTicketAreaWithMoList[k]?.ticketStatusId !== 110) {
                countVouchersMoAreaLop.push(searchTicketAreaWithMoList[k]);
              }
            }
          }
        }

        listInfo.push({
          workAreaListId: getDataWorkAreaLists[i].workAreaListId,
          comment: getDataWorkAreaLists[i].comment,
          created_at: getDataWorkAreaLists[i].created_at,
          type: getDataWorkAreaLists[i].type,
          workListId: getDataWorkAreaLists[i].workListId,
          workAreaNameOrigin: getDataWorkAreaLists[i].area_name,
          totalVoucherArea: countVouchersMoAreaLop.length,
          totalVoucherActiveArea: countVouchersMoAreaLop.filter(
            (voucher: { finishedAtTicket: boolean }) => {
              if (!voucher?.finishedAtTicket) {
                return true;
              }
            }
          ).length,
        });
      }

      return res.json({ ok: true, data: listInfo });
    } else if (areaFilter === 'all' && voucherType !== 'all') {
      const getDataWorkAreaLists = (await WorkAreaLists.query()
        .join('work_lists', 'work_area_lists.work_list_id', 'work_lists.id')
        .join(
          'work_areas',
          'work_area_lists.work_area_id',
          'work_areas.work_area_id'
        )
        .join(
          'work_list_mos',
          'work_area_lists.id',
          'work_list_mos.work_list_id'
        )
        .join('work_vouchers', 'work_list_mos.mo_id', 'work_vouchers.mo_id')
        .join(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .select(
          { workAreaListId: 'work_area_lists.id' },
          'work_areas.area_name',
          'work_area_lists.comment',
          'work_area_lists.work_list_id',
          'work_area_lists.created_at',
          'work_lists.type',
          'work_lists.employee_id',
          { workListId: 'work_lists.id' }
        )
        .where(
          'work_voucher_types.id',
          voucherType
        )) as unknown as IdataWorkArea[];

      for (let i = 0; i < getDataWorkAreaLists.length; i++) {
        const countVouchersMoAreaLop = [];

        getDataWorkListMo = await WorkListMos.query()
          .join('mo_numbers', 'work_list_mos.mo_id', 'mo_numbers.mo_id')
          .select({ workListMoId: 'work_list_mos.id' }, 'mo_numbers.mo_id')
          .where(
            'work_list_mos.work_list_id',
            getDataWorkAreaLists[i].workListId
          )
          .whereNull('work_list_mos.removed_at');

        for (let j = 0; j < getDataWorkListMo.length; j++) {
          const searchTicketAreaWithMoList = (await WorkAreaTickets.query()
            .join(
              'work_vouchers',
              'work_area_tickets.work_voucher_id',
              'work_vouchers.id'
            )
            .join(
              'work_voucher_types',
              'work_vouchers.work_voucher_type_id',
              'work_voucher_types.id'
            )
            .join(
              'work_areas',
              'work_area_tickets.work_area_id',
              'work_areas.work_area_id'
            )
            .join(
              'work_area_ticket_statuses',
              'work_area_tickets.work_area_ticket_status_id',
              'work_area_ticket_statuses.id'
            )
            .where('work_vouchers.mo_id', getDataWorkListMo[j].mo_id)
            .select([
              { isRepo: 'work_vouchers.is_repo' },
              { areaTicketId: 'work_areas.work_area_id' },
              { moId: 'work_vouchers.mo_id' },

              { areaTicket: 'work_areas.area_name' },
              {
                ticketStatus: 'work_area_ticket_statuses.name',
              },
              {
                ticketStatusId: 'work_area_ticket_statuses.work_status_id',
              },
              { expDate: 'work_area_tickets.exp_finish_date' },
              WorkAreas.query()
                .where(
                  'work_areas.work_area_id',
                  ref('work_area_tickets.next_work_area_id')
                )
                .select('work_areas.area_name')
                .as('nextArea'),
              { voucherType: 'work_voucher_types.name' },
              { workAreaTicketsId: 'work_area_tickets.id' },
              { finishedAtTicket: 'work_area_tickets.finished_at' },
              { workVoucherId: 'work_vouchers.id' },
            ])) as unknown as ITicketAreaWithMoList[];

          // obtenemos el total de vouchers en nuestra area como fuera de ella y obtenemos las mos que no tienen ningun voucher
          for (let k = 0; k < searchTicketAreaWithMoList.length; k++) {
            if (searchTicketAreaWithMoList[k]?.areaTicketId === area) {
              if (searchTicketAreaWithMoList[k]?.ticketStatusId !== 110) {
                countVouchersMoAreaLop.push(searchTicketAreaWithMoList[k]);
              }
            }
          }
        }

        listInfo.push({
          workAreaListId: getDataWorkAreaLists[i].workAreaListId,
          comment: getDataWorkAreaLists[i].comment,
          created_at: getDataWorkAreaLists[i].created_at,
          type: getDataWorkAreaLists[i].type,
          workListId: getDataWorkAreaLists[i].workListId,
          workAreaNameOrigin: getDataWorkAreaLists[i].area_name,
          totalVoucherArea: countVouchersMoAreaLop.length,
          totalVoucherActiveArea: countVouchersMoAreaLop.filter(
            (voucher: { finishedAtTicket: boolean }) => {
              if (!voucher?.finishedAtTicket) {
                return true;
              }
            }
          ).length,
        });
      }

      return res.json({ ok: true, data: listInfo });
    } else if (areaFilter !== 'all' && voucherType !== 'all') {
      const getDataWorkAreaLists = (await WorkAreaLists.query()
        .join('work_lists', 'work_area_lists.work_list_id', 'work_lists.id')
        .join(
          'work_areas',
          'work_area_lists.work_area_id',
          'work_areas.work_area_id'
        )
        .join(
          'work_list_mos',
          'work_area_lists.id',
          'work_list_mos.work_list_id'
        )
        .join('work_vouchers', 'work_list_mos.mo_id', 'work_vouchers.mo_id')
        .join(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .select(
          { workAreaListId: 'work_area_lists.id' },
          'work_areas.area_name',
          'work_area_lists.comment',
          'work_area_lists.work_list_id',
          'work_area_lists.created_at',
          'work_lists.type',
          'work_lists.employee_id',
          { workListId: 'work_lists.id' }
        )
        .where('work_area_lists.work_area_id', areaFilter)
        .where(
          'work_voucher_types.id',
          voucherType
        )) as unknown as IdataWorkArea[];

      for (let i = 0; i < getDataWorkAreaLists.length; i++) {
        const countVouchersMoAreaLop = [];

        getDataWorkListMo = await WorkListMos.query()
          .join('mo_numbers', 'work_list_mos.mo_id', 'mo_numbers.mo_id')
          .select({ workListMoId: 'work_list_mos.id' }, 'mo_numbers.mo_id')
          .where(
            'work_list_mos.work_list_id',
            getDataWorkAreaLists[i].workListId
          )
          .whereNull('work_list_mos.removed_at');

        for (let j = 0; j < getDataWorkListMo.length; j++) {
          const searchTicketAreaWithMoList = (await WorkAreaTickets.query()
            .join(
              'work_vouchers',
              'work_area_tickets.work_voucher_id',
              'work_vouchers.id'
            )
            .join(
              'work_voucher_types',
              'work_vouchers.work_voucher_type_id',
              'work_voucher_types.id'
            )
            .join(
              'work_areas',
              'work_area_tickets.work_area_id',
              'work_areas.work_area_id'
            )
            .join(
              'work_area_ticket_statuses',
              'work_area_tickets.work_area_ticket_status_id',
              'work_area_ticket_statuses.id'
            )
            .where('work_vouchers.mo_id', getDataWorkListMo[j].mo_id)
            .select([
              { isRepo: 'work_vouchers.is_repo' },
              { areaTicketId: 'work_areas.work_area_id' },
              { moId: 'work_vouchers.mo_id' },

              { areaTicket: 'work_areas.area_name' },
              {
                ticketStatus: 'work_area_ticket_statuses.name',
              },
              {
                ticketStatusId: 'work_area_ticket_statuses.work_status_id',
              },
              { expDate: 'work_area_tickets.exp_finish_date' },
              WorkAreas.query()
                .where(
                  'work_areas.work_area_id',
                  ref('work_area_tickets.next_work_area_id')
                )
                .select('work_areas.area_name')
                .as('nextArea'),
              { voucherType: 'work_voucher_types.name' },
              { workAreaTicketsId: 'work_area_tickets.id' },
              { finishedAtTicket: 'work_area_tickets.finished_at' },
              { workVoucherId: 'work_vouchers.id' },
            ])) as unknown as ITicketAreaWithMoList[];

          // obtenemos el total de vouchers en nuestra area como fuera de ella y obtenemos las mos que no tienen ningun voucher
          for (let k = 0; k < searchTicketAreaWithMoList.length; k++) {
            if (searchTicketAreaWithMoList[k]?.areaTicketId === area) {
              if (searchTicketAreaWithMoList[k]?.ticketStatusId !== 110) {
                countVouchersMoAreaLop.push(searchTicketAreaWithMoList[k]);
              }
            }
          }
        }

        listInfo.push({
          workAreaListId: getDataWorkAreaLists[i].workAreaListId,
          comment: getDataWorkAreaLists[i].comment,
          created_at: getDataWorkAreaLists[i].created_at,
          type: getDataWorkAreaLists[i].type,
          workListId: getDataWorkAreaLists[i].workListId,
          workAreaNameOrigin: getDataWorkAreaLists[i].area_name,
          totalVoucherArea: countVouchersMoAreaLop.length,
          totalVoucherActiveArea: countVouchersMoAreaLop.filter(
            (voucher: { finishedAtTicket: boolean }) => {
              if (!voucher?.finishedAtTicket) {
                return true;
              }
            }
          ).length,
        });
      }

      return res.json({ ok: true, data: listInfo });
    } else {
      return res.status(400).json({
        ok: false,
        message: 'areaFilter or voucherType incorrect',
      });
    }
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function getTicketInfoMoList(req: Request, res: Response) {
  try {
    const {
      area,
      listAreaId,
      viewAllTickets,
    }: { area: number; listAreaId: number; viewAllTickets: string } = req.body;

    const dataResponse = [];
    const dataAreaTickets = [];
    const createTicketsMosEmpty = [];

    const countVouchersMo = [];
    const countVouchersMoArea = [];

    const getDataWorkAreaLists = (await WorkAreaLists.query()
      .join('work_lists', 'work_area_lists.work_list_id', 'work_lists.id')
      .join(
        'work_areas',
        'work_area_lists.work_area_id',
        'work_areas.work_area_id'
      )
      .select(
        { workAreaListId: 'work_area_lists.id' },
        'work_area_lists.comment',
        'work_area_lists.work_list_id ',
        'work_area_lists.created_at',
        'work_areas.area_name',
        'work_lists.type',
        Employee.query()
          .where('employees.employee_id', ref('work_lists.employee_id'))
          .select('employees.first_name')
          .as('nameEmployee'),
        WorkAreas.query()
          .where('work_areas.work_area_id', ref('work_lists.work_area_id'))
          .select('work_areas.area_name')
          .as('nameAreaList'),
        'work_lists.employee_id',
        'work_lists.id'
      )
      .where('work_area_lists.id', listAreaId)
      .where('work_area_lists.work_area_id', area)
      .whereNull('work_area_lists.removed_at')) as unknown as IdataWorkArea[];

    for (let i = 0; i < getDataWorkAreaLists.length; i++) {
      const getDataWorkListMo = (await WorkListMos.query()
        .join('mo_numbers', 'work_list_mos.mo_id', 'mo_numbers.mo_id')
        .select(
          { workListMoId: 'work_list_mos.id' },
          'mo_numbers.mo_id',
          'mo_numbers.customer',
          'mo_numbers.num',
          'mo_numbers.mo_status',
          'mo_numbers.required_date',
          'mo_numbers.quantity',
          'mo_numbers.style',
          'mo_numbers.mo_order'
        )
        .where('work_list_mos.work_list_id', getDataWorkAreaLists[i].id)
        .whereNull('work_list_mos.removed_at')) as unknown as IdataWorkArea[];

      for (let j = 0; j < getDataWorkListMo.length; j++) {
        if (viewAllTickets) {
          const countVouchersMoLop = [];
          const countVouchersMoAreaLop = [];
          const searchTicketAreaWithMoList = (await WorkAreaTickets.query()
            .join(
              'work_vouchers',
              'work_area_tickets.work_voucher_id',
              'work_vouchers.id'
            )
            .join(
              'work_voucher_types',
              'work_vouchers.work_voucher_type_id',
              'work_voucher_types.id'
            )
            .join(
              'work_areas',
              'work_area_tickets.work_area_id',
              'work_areas.work_area_id'
            )
            .join(
              'work_area_ticket_statuses',
              'work_area_tickets.work_area_ticket_status_id',
              'work_area_ticket_statuses.id'
            )
            .where('work_vouchers.mo_id', getDataWorkListMo[j].mo_id)
            .select([
              WorkInventoryBins.query()
                .where(
                  'work_inventory_bins.id',
                  ref('work_area_tickets.work_inventory_location_id')
                )
                .select('work_inventory_bins.name')
                .as('location'),
              { isRepo: 'work_vouchers.is_repo' },
              'work_areas.work_area_id',
              'work_vouchers.mo_id',
              { areaTicket: 'work_areas.area_name' },
              {
                ticketStatus: 'work_area_ticket_statuses.name',
              },
              'work_area_tickets.work_area_ticket_status_id',
              { expDate: 'work_area_tickets.exp_finish_date' },
              WorkAreas.query()
                .where(
                  'work_areas.work_area_id',
                  ref('work_area_tickets.next_work_area_id')
                )
                .select('work_areas.area_name')
                .as('nextArea'),
              { voucherType: 'work_voucher_types.name' },
              { workAreaTicketsId: 'work_area_tickets.id' },
              { finishedAtTicket: 'work_area_tickets.finished_at' },
              { workVoucherId: 'work_vouchers.id' },
            ])) as unknown as ITicketAreaWithMoList[];

          // obtenemos el total de vouchers en nuestra area como fuera de ella y obtenemos las mos que no tienen ningun voucher
          for (let k = 0; k < searchTicketAreaWithMoList.length; k++) {
            if (searchTicketAreaWithMoList[k]?.work_area_id === area) {
              if (
                searchTicketAreaWithMoList[k]?.work_area_ticket_status_id ===
                110
              ) {
                createTicketsMosEmpty.push(
                  searchTicketAreaWithMoList[k]?.mo_id
                );
              } else {
                countVouchersMoArea.push(searchTicketAreaWithMoList[k]);
                countVouchersMoAreaLop.push(searchTicketAreaWithMoList[k]);
              }
            } else {
              countVouchersMo.push(searchTicketAreaWithMoList[k]);
              countVouchersMoLop.push(searchTicketAreaWithMoList[k]);
            }
          }

          const countVoucherActiveLop = [
            ...countVouchersMoAreaLop,
            ...countVouchersMoLop,
          ].filter((voucher: ITicketAreaWithMoList): true => {
            if (!voucher.finishedAtTicket) {
              return true;
            }
          }).length;

          const dataFiltering = [];

          for (let i = 0; i < searchTicketAreaWithMoList.length; i++) {
            const searchTicketOtherArea = (await WorkAreaTickets.query()
              .join(
                'work_vouchers',
                'work_area_tickets.work_voucher_id',
                'work_vouchers.id'
              )
              .join(
                'work_area_ticket_statuses',
                'work_area_tickets.work_area_ticket_status_id ',
                'work_area_ticket_statuses.id'
              )
              .join(
                'work_areas',
                'work_area_tickets.work_area_id ',
                'work_areas.work_area_id'
              )
              .where(
                'work_area_tickets.work_voucher_id',
                searchTicketAreaWithMoList[i].workVoucherId
              )
              .where('work_area_ticket_statuses.work_status_id', 50)
              .orWhere('work_area_ticket_statuses.work_status_id', 10)
              .select(['work_areas.area_name'])) as unknown as {
              area_name: string;
            }[];

            const dataLogSearch = [];
            let activityLog;

            for (let j = 0; j < searchTicketAreaWithMoList.length; j++) {
              activityLog = (await WorkActivityLog.query()
                .join(
                  'employees',
                  'work_activity_log.employee_id',
                  'employees.employee_id'
                )
                .where('work_activity_log.activity', 'TicketStatusChanged')
                .where(
                  'work_activity_log.module_id',
                  searchTicketAreaWithMoList[j].workAreaTicketsId
                )
                .select([
                  { lastUpdateStatusDate: 'work_activity_log.updated_at' },
                  { lastUpdateStatusEmployee: 'employees.first_name' },
                ])) as unknown as {
                lastUpdateStatusDate: string;
                lastUpdateStatusEmployee: string;
              }[];

              dataLogSearch.push({
                ...searchTicketAreaWithMoList[j],
                ...activityLog[0],
                lastUpdateStatusDate: activityLog[0]?.lastUpdateStatusDate
                  ? dayjs(
                      activityLog[0]?.lastUpdateStatusDate as string
                    ).format('DD/MM/YYYY')
                  : '00-00-0000',
              });
            }
            dataFiltering.push({
              ...dataLogSearch[i],
              ...searchTicketAreaWithMoList[i],

              finishedAtTicket: searchTicketAreaWithMoList[i].finishedAtTicket
                ? dayjs(searchTicketAreaWithMoList[i].finishedAtTicket).format(
                    'DD-MM-YYYY'
                  )
                : '00-00-0000',
              AreaActual: searchTicketOtherArea[0]?.area_name,
              isRepo:
                searchTicketAreaWithMoList[i].isRepo === 1 ? 'REPO' : 'NO REPO',
            });
          }

          dataAreaTickets.push({
            customer: getDataWorkListMo[j].customer,
            workListMoId: getDataWorkListMo[j].workListMoId,
            num: getDataWorkListMo[j].num,
            mo_status: getDataWorkListMo[j].mo_status,
            required_date: dayjs(
              getDataWorkListMo[j].required_date as string
            ).format('DD-MM-yyyy'),
            quantity: getDataWorkListMo[j].quantity,
            style: getDataWorkListMo[j].style,
            mo_order: getDataWorkListMo[j].mo_order,
            mo_id: getDataWorkListMo[j].mo_id,
            ticket: dataFiltering || [],
            totalVoucherArea: countVouchersMoAreaLop.length,
            totalVoucherActiveArea: countVoucherActiveLop,
            totalVoucherOtherAreas: countVouchersMoLop.length,
            moVouchersArea: `${countVouchersMoAreaLop.length} (${countVoucherActiveLop})`,
            moVouchersTotal: `${
              countVouchersMoLop.length + countVouchersMoAreaLop.length
            }`,
          });
        }
      }

      dataResponse.push({
        id: getDataWorkAreaLists[i].workAreaListId,
        workListId: getDataWorkAreaLists[i].workListId,
        comment: getDataWorkAreaLists[i].comment,
        created_at: getDataWorkAreaLists[i].created_at,
        area_name: getDataWorkAreaLists[i].area_name,
        nameAreaList: getDataWorkAreaLists[i].nameAreaList,
        nameEmployee: getDataWorkAreaLists[i].nameEmployee,
        type: getDataWorkAreaLists[i].type,
        employee_id: getDataWorkAreaLists[i].employee_id,
        areaTickets: dataAreaTickets,
      });
    }

    // obtenemos el total de mos activas de la lista
    const countMosActive = dataResponse[0].areaTickets.filter(
      (mo: ITicketAreaWithMoList): boolean => {
        return mo.mo_status === 'In Production';
      }
    ).length;

    // obtenemos el total de vouchers activos
    const countVoucherActive = [
      ...countVouchersMoArea,
      ...countVouchersMo,
    ].filter((voucher: ITicketAreaWithMoList): boolean => {
      if (!voucher.finishedAtTicket) {
        return true;
      }
    }).length;

    const counts = {
      countMos: dataResponse[0].areaTickets.length,
      countMosActive,
      countVouchers: [...countVouchersMoArea, ...countVouchersMo].length,
      countVouchersActive: countVoucherActive,
    };

    // obtenemos el total de mos que no tienen voucher
    for (let i = 0; i < dataResponse[0].areaTickets.length; i++) {
      if (
        dataResponse[0].areaTickets[i].ticket?.length === 0 &&
        dataResponse[0].areaTickets[i].mo_status === 'In Production'
      ) {
        createTicketsMosEmpty.push(dataResponse[0].areaTickets[i].mo_id);
      }
    }

    return res.json({
      ok: true,
      data: dataResponse,
      counts,
      createTicketsMosEmpty,
    });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function searchMoList(req: Request, res: Response) {
  try {
    const { mo } = req.body;
    const moData: any = await MoNumber.query()
      .select(
        'mo_numbers.mo_id',
        'mo_numbers.customer',
        'mo_numbers.num',
        'mo_numbers.mo_status',
        'mo_numbers.required_date',
        'mo_numbers.quantity',
        'mo_numbers.style',
        'mo_numbers.mo_order'
      )
      .whereNotIn('mo_numbers.mo_status', [
        'Void',
        'Cancelled',
        'Materials',
        'Complete',
      ])
      .where('mo_numbers.num', 'like', `${mo}%`);

    if (moData) {
      return res.status(200).json({ ok: true, data: moData });
    }
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function addMosList(req: Request, res: Response) {
  try {
    const { moListData, moListId, typeAccion, client, area, codeEmployee } =
      req.body;

    let addItemsMoList;

    const getDataWorkAreaLists: any = await WorkAreaLists.query()
      .join('work_lists', 'work_area_lists.work_list_id', 'work_lists.id')
      .select({ workListId: 'work_lists.id' })
      .where('work_area_lists.id', moListId)
      .where('work_area_lists.work_area_id', area);

    if (typeAccion === 'MO') {
      for (let i = 0; i < moListData.length; i++) {
        if (moListData[i]) {
          const getMoInfo: any = await MoNumber.query()
            .whereNotIn('mo_numbers.mo_status', [
              'Void',
              'Cancelled',
              'Materials',
              'Complete',
            ])
            .where('mo_numbers.num', moListData[i])
            .where('mo_numbers.company_code', client)
            .select('mo_numbers.mo_id', 'mo_numbers.num');

          addItemsMoList = await WorkListMos.query().insert({
            work_list_id: getDataWorkAreaLists[0].workListId,
            mo_id: getMoInfo[0].mo_id,
          });

          const ticketLog = await WorkActivityLog.query().insert({
            employee_id: codeEmployee,
            work_area_id: area,
            module_name: 'moList',
            module_id: moListId,
            activity: 'MoListAddedItem',
            data: JSON.stringify({
              itemAdded: getMoInfo[0].num,
            }),
          });

          if (!ticketLog)
            return res.status(204).json({
              ok: false,
              message: 'No se logro crear el historial',
            });
        }
      }
    } else if (typeAccion === 'BARCODE') {
      for (let i = 0; i < moListData.length; i++) {
        if (moListData[i]) {
          addItemsMoList = await WorkListMos.query().insert({
            work_list_id: getDataWorkAreaLists[0].workListId,
            mo_id: moListData[i].mo_id,
          });

          const ticketLog = await WorkActivityLog.query().insert({
            employee_id: codeEmployee,
            work_area_id: area,
            module_name: 'moList',
            module_id: moListId,
            activity: 'MoListAddedItem',
            data: JSON.stringify({
              itemAdded: moListData[i].num,
            }),
          });

          if (!ticketLog)
            return res.status(204).json({
              ok: false,
              message: 'No se logro crear el historial',
            });
        }
      }
    } else {
      addItemsMoList = await WorkListMos.query().insert({
        work_list_id: getDataWorkAreaLists[0].workListId,
        mo_id: moListData,
      });

      const moInfo: any = await MoNumber.query()
        .select('mo_numbers.num')
        .where('mo_numbers.mo_id', moListData);

      const ticketLog = await WorkActivityLog.query().insert({
        employee_id: codeEmployee,
        work_area_id: area,
        module_name: 'moList',
        module_id: moListId,
        activity: 'MoListAddedItem',
        data: JSON.stringify({
          itemAdded: moInfo[0].num,
        }),
      });

      if (!ticketLog)
        return res.status(204).json({
          ok: false,
          message: 'No se logro crear el historial',
        });
    }

    return res.status(201).json({
      ok: true,
      addItemsMoList,
      getDataWorkAreaLists,
    });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function addCommentMoListArea(req: Request, res: Response) {
  try {
    const { commentList, listAreaId, codeEmployee, area } = req.body;

    const addComment = await WorkAreaLists.query()
      .update({
        comment: commentList,
      })
      .where('work_area_lists.id', listAreaId);

    const ticketLog = await WorkActivityLog.query().insert({
      employee_id: codeEmployee,
      work_area_id: area,
      module_name: 'moList',
      module_id: listAreaId,
      activity: 'MoListCommentUpdated',
      data: JSON.stringify({
        newComment: commentList,
      }),
    });

    if (addComment && ticketLog) {
      return res
        .status(200)
        .json({ ok: true, message: 'Se agrego el comentario' });
    }
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function getLogOfMoList(req: Request, res: Response) {
  try {
    const { listAreaId } = req.body;

    const getLogs = await WorkActivityLog.query()
      .select(
        'work_activity_log.id',
        'work_activity_log.activity',
        'work_activity_log.data',
        'work_activity_log.created_at',
        Employee.query()
          .where('employees.employee_id', ref('work_activity_log.employee_id'))
          .select('employees.first_name')
          .as('userName')
      )
      .where('work_activity_log.module_name', 'moList')
      .where('work_activity_log.module_id', listAreaId)
      .orderBy('created_at', 'desc');

    return res.status(200).json({
      ok: true,
      data: getLogs,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getMosbyMoList(req: Request, res: Response) {
  try {
    const { moList, voucherType, areaSelected } = req.body;

    const data = [];

    for (let i = 0; i < moList.length; i++) {
      for (let j = 0; j < voucherType.length; j++) {
        const getMoInfo: any = await MoNumber.query()
          .where('mo_numbers.mo_id', moList[i])
          .select(
            'mo_numbers.mo_id',
            'mo_numbers.po_number',
            'mo_numbers.order_type',
            'mo_numbers.mo_order',
            'mo_numbers.num',
            'mo_numbers.mo_status',
            'mo_numbers.style',
            'mo_numbers.quantity',
            'mo_numbers.style_category',
            'mo_numbers.ItemDescription8',
            'mo_numbers.customer',
            'mo_numbers.company_code'
          );

        if (getMoInfo) {
          const filterVoucher: any = await WorkVouchers.query()
            .where('work_vouchers.mo_id', moList[i])
            .where('work_vouchers.work_voucher_type_id', voucherType[j].value)
            .select('work_vouchers.id');

          if (filterVoucher.length > 0) {
            const filterAreaVoucher: any = await WorkAreaTickets.query()
              .join(
                'work_areas',
                'work_area_tickets.work_area_id',
                'work_areas.work_area_id'
              )
              .join(
                'work_area_ticket_statuses',
                'work_area_tickets.work_area_ticket_status_id',
                'work_area_ticket_statuses.id'
              )
              .join(
                'work_statuses',
                'work_area_ticket_statuses.work_status_id',
                'work_statuses.id'
              )
              .where('work_area_tickets.work_voucher_id', filterVoucher[0].id)
              .where('work_area_tickets.work_area_id', areaSelected)
              .where('work_statuses.id', '<>', '110')
              .select('work_area_tickets.id', 'work_area_ticket_statuses.name');

            if (filterAreaVoucher.length > 0) {
              data.push({
                exist: 1,
                infoMo: 'DUPLICADO',
                repo: 'NO',
                isMain: false,
                mo_id: getMoInfo[0].mo_id,
                statusTicketDuplicate: `${filterAreaVoucher[0].id} - ${filterAreaVoucher[0].name}`,
                po_number: getMoInfo[0].po_number || 'N/A',
                uuid: uuidv4(),
                voucherType: voucherType[j].label,
                voucherTypeId: voucherType[j].value,
                mo_order: getMoInfo[0].mo_order,
                order_type: getMoInfo[0].order_type || 'N/A',
                num: getMoInfo[0].num,
                mo_status: getMoInfo[0].mo_status,
                style: getMoInfo[0].style,
                quantity: getMoInfo[0].quantity,
                style_category: getMoInfo[0].style_category,
                ItemDescription8: getMoInfo[0].ItemDescription8,
                customer: getMoInfo[0].customer || 'N/A',
                company_code: getMoInfo[0].company_code,
                voucherScan: false,
              });
            } else {
              data.push({
                infoMo: 'NUEVO',
                repo: 'NO',
                isMain: false,
                mo_id: getMoInfo[0].mo_id,
                statusTicketDuplicate: '',
                po_number: getMoInfo[0].po_number || 'N/A',
                uuid: uuidv4(),
                voucherType: voucherType[j].label,
                voucherTypeId: voucherType[j].value,
                mo_order: getMoInfo[0].mo_order,
                order_type: getMoInfo[0].order_type || 'N/A',
                num: getMoInfo[0].num,
                mo_status: getMoInfo[0].mo_status,
                style: getMoInfo[0].style,
                quantity: getMoInfo[0].quantity,
                style_category: getMoInfo[0].style_category,
                ItemDescription8: getMoInfo[0].ItemDescription8,
                customer: getMoInfo[0].customer || 'N/A',
                company_code: getMoInfo[0].company_code,
                voucherScan: false,
              });
            }
          } else {
            data.push({
              infoMo: 'NUEVO',
              repo: 'NO',
              isMain: false,
              mo_id: getMoInfo[0].mo_id,
              po_number: getMoInfo[0].po_number || 'N/A',
              uuid: uuidv4(),
              voucherType: voucherType[j].label,
              voucherTypeId: voucherType[j].value,
              mo_order: getMoInfo[0].mo_order,
              order_type: getMoInfo[0].order_type || 'N/A',
              num: getMoInfo[0].num,
              mo_status: getMoInfo[0].mo_status,
              style: getMoInfo[0].style,
              quantity: getMoInfo[0].quantity,
              style_category: getMoInfo[0].style_category,
              ItemDescription8: getMoInfo[0].ItemDescription8,
              customer: getMoInfo[0].customer || 'N/A',
              company_code: getMoInfo[0].company_code,
              voucherScan: false,
            });
          }
        }
      }
    }

    return res.status(200).json({
      ok: true,
      data: data,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getMosListFilter(req: Request, res: Response) {
  try {
    const { workListId } = req.body;

    const getDataWorkListMo = await WorkListMos.query()
      .join('mo_numbers', 'work_list_mos.mo_id', 'mo_numbers.mo_id')
      .select('mo_numbers.num')
      .where('work_list_mos.work_list_id', workListId)
      .whereNull('work_list_mos.removed_at');

    if (getDataWorkListMo.length > 0) {
      return res.status(200).json({
        ok: true,
        data: getDataWorkListMo,
      });
    } else {
      return res.status(200).json({
        ok: false,
        data: [],
      });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function moListDelete(req: Request, res: Response) {
  const { moListId, codeEmployee, area } = req.body;
  const formatDate = 'YYYY-MM-DD HH:mm:ss';
  const actualDate = new Date();

  try {
    const deleteItemlistMo = await WorkAreaLists.query()
      .update({
        removed_at: dayjs(actualDate).format(formatDate),
      })
      .where('work_area_lists.id', moListId);

    const ticketLog = await WorkActivityLog.query().insert({
      employee_id: codeEmployee,
      work_area_id: area,
      module_name: 'moList',
      module_id: moListId,
      activity: 'MoListClosed',
      data: JSON.stringify({}),
    });

    if (!ticketLog)
      return res.status(204).json({
        ok: false,
        message: 'No se logro crear el historial',
      });

    return res.status(200).json({ ok: true, deleteItemlistMo });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

// endpoint para obtener todas las MOS de un lote
export async function getAllTheMosOfTheBatch(req: Request, res: Response) {
  try {
    const { listId } = req.body;

    const getDataWorkListMo = await WorkListMos.query()
      .join('mo_numbers', 'work_list_mos.mo_id', 'mo_numbers.mo_id')
      .select(
        { workListMoId: 'work_list_mos.id' },
        'mo_numbers.mo_id',
        'mo_numbers.customer',
        'mo_numbers.num',
        'mo_numbers.mo_status',
        'mo_numbers.required_date',
        'mo_numbers.quantity',
        'mo_numbers.style',
        'mo_numbers.mo_order'
      )
      .where('work_list_mos.work_list_id', listId)
      .whereNull('work_list_mos.removed_at');

    if (getDataWorkListMo.length > 0) {
      return res.status(200).json({
        ok: true,
        totalRows: getDataWorkListMo.length,
        data: getDataWorkListMo,
      });
    } else {
      return res.status(200).json({
        ok: false,
        totalRows: getDataWorkListMo.length,
        data: [],
      });
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

// endpoint para obtener la informacion de un lote
export async function getInfoGeneralOfTheBatch(req: Request, res: Response) {
  try {
    const { listAreaId, area } = req.body;

    const getDataWorkAreaLists = await WorkAreaLists.query()
      .join('work_lists', 'work_area_lists.work_list_id', 'work_lists.id')
      .join(
        'work_areas',
        'work_area_lists.work_area_id',
        'work_areas.work_area_id'
      )
      .select(
        'work_area_lists.comment',
        'work_area_lists.created_at',
        'work_areas.area_name',
        'work_lists.type',
        Employee.query()
          .where('employees.employee_id', ref('work_lists.employee_id'))
          .select('employees.first_name')
          .as('nameEmployee'),
        WorkAreas.query()
          .where('work_areas.work_area_id', ref('work_lists.work_area_id'))
          .select('work_areas.area_name')
          .as('nameAreaList'),
        'work_lists.employee_id'
      )
      .where('work_area_lists.id', listAreaId)
      .where('work_area_lists.work_area_id', area)
      .whereNull('work_area_lists.removed_at');

    if (getDataWorkAreaLists.length > 0) {
      return res.status(200).json({
        ok: true,
        totalRows: getDataWorkAreaLists.length,
        data: getDataWorkAreaLists,
      });
    } else {
      return res.status(200).json({
        ok: false,
        totalRows: getDataWorkAreaLists.length,
        data: [],
      });
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

// endpoint para obtener los boletos de un lote
export async function getAllTheTicketsInABatch(req: Request, res: Response) {
  try {
    const { moIds, listId } = req.body;

    const tickets = [];
    const ticketsError = [];

    for (let i = 0; i < moIds.length; i++) {
      const searchTicketAreaWithMoList = await WorkAreaTickets.query()
        .join(
          'work_vouchers',
          'work_area_tickets.work_voucher_id',
          'work_vouchers.id'
        )
        .join(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .join(
          'work_areas',
          'work_area_tickets.work_area_id',
          'work_areas.work_area_id'
        )
        .join(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .leftJoin('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
        .leftJoin(
          'work_list_mos',
          'mo_numbers.mo_id',
          '=',
          'work_list_mos.mo_id'
        )
        .leftJoin(
          WorkActivityLog.query()
            .select('employees.first_name', 'work_activity_log.updated_at', {
              ticket_id: 'latest_work_activity.module_id',
            })
            .join(
              WorkActivityLog.query()
                .select('work_activity_log.module_id')
                // @ts-ignore
                .max({ max_activity_id: 'work_activity_log.id' })
                .where('work_activity_log.activity', 'TicketStatusChanged')
                .groupBy('work_activity_log.module_id')
                .as('latest_work_activity'),
              'latest_work_activity.max_activity_id',
              'work_activity_log.id'
            )
            .leftJoin(
              'employees',
              'employees.employee_id',
              'work_activity_log.employee_id'
            )
            .whereNotNull('latest_work_activity.module_id')
            .as('last_activity'),
          'last_activity.ticket_id',
          'work_area_tickets.id'
        )
        .where('work_vouchers.mo_id', moIds[i])
        .where('work_list_mos.work_list_id', listId)
        .select([
          'mo_numbers.num',
          'mo_numbers.quantity',
          'mo_numbers.customer',
          { lista: 'work_list_mos.work_list_id' },
          { last_activity_status_employee: 'last_activity.first_name' },
          { last_activity_status_date: 'last_activity.updated_at' },
          WorkInventoryBins.query()
            .where(
              'work_inventory_bins.id',
              ref('work_area_tickets.work_inventory_location_id')
            )
            .select('work_inventory_bins.name')
            .as('location'),
          raw(
            "CASE WHEN work_vouchers.is_repo = 1 THEN 'ES REPOSICION' ELSE 'NO ES REPOSICION' END"
          ).as('is_repo'),
          { areaTicketId: 'work_areas.work_area_id' },
          { moId: 'work_vouchers.mo_id' },
          { areaTicket: 'work_areas.area_name' },
          {
            ticketStatus: 'work_area_ticket_statuses.name',
          },
          {
            ticketStatusId: 'work_area_ticket_statuses.work_status_id',
          },
          { expDate: 'work_area_tickets.exp_finish_date' },
          WorkAreas.query()
            .where(
              'work_areas.work_area_id',
              ref('work_area_tickets.next_work_area_id')
            )
            .select('work_areas.area_name')
            .as('nextArea'),
          { voucherType: 'work_voucher_types.name' },
          { workAreaTicketsId: 'work_area_tickets.id' },
          { finishedAtTicket: 'work_area_tickets.finished_at' },
          { workVoucherId: 'work_vouchers.id' },
        ]);

      if (searchTicketAreaWithMoList.length > 0) {
        tickets.push(...searchTicketAreaWithMoList);
      } else {
        ticketsError.push({
          error: `No se encontraron tickets para el mo ${moIds[i]}`,
          moId: moIds[i],
        });
      }
    }

    return res.status(200).json({
      ok: true,
      totalRows: tickets.length,
      data: { tickets, ticketsError },
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

// endpoint para obtener todas las MOS Activas de un lote
export async function getAllTheMosActiveOfTheBatch(
  req: Request,
  res: Response
) {
  try {
    const { listId } = req.body;

    const getDataWorkListMo = await WorkListMos.query()
      .leftJoin('mo_numbers', 'work_list_mos.mo_id', 'mo_numbers.mo_id')
      .select('mo_numbers.mo_id', 'mo_numbers.mo_status')
      .whereNotIn('mo_numbers.mo_status', [
        'Void',
        'Cancelled',
        'Materials',
        'Complete',
      ])
      .where('work_list_mos.work_list_id', listId)
      .whereNull('work_list_mos.removed_at');

    if (getDataWorkListMo.length > 0) {
      return res.status(200).json({
        ok: true,
        totalRows: getDataWorkListMo.length,
        data: getDataWorkListMo,
      });
    } else {
      return res.status(200).json({
        ok: false,
        totalRows: getDataWorkListMo.length,
        data: [],
      });
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function searchVouchersByMo(req: Request, res: Response) {
  try {
    const { mo, area } = req.body;
    console.log(mo, area);

    if (mo.includes('/') || mo.includes('-') || mo.includes('PP')) {
      const barcode = mo.includes('-') ? mo.replace('-', '/') : mo;

      const vouchers = await WorkVouchers.query()
        .select(
          { mo: 'mo_numbers.num' },
          'mo_numbers.mo_id',
          'mo_numbers.customer',
          { voucher_id: 'work_vouchers.id' },
          { is_repo: 'work_vouchers.is_repo' },
          { voucher_type: 'work_voucher_types.name' },
          { group: 'work_vouchers.work_voucher_group_id' }
        )
        .join(
          'work_area_tickets',
          'work_vouchers.id',
          'work_area_tickets.work_voucher_id'
        )
        .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
        .join(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .join(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .join(
          'work_statuses',
          'work_area_ticket_statuses.work_status_id',
          'work_statuses.id'
        )
        .whereNotIn('mo_numbers.mo_status', [
          'Void',
          'Cancelled',
          'Materials',
          'Complete',
        ])
        .whereNotIn('work_statuses.id', [100, 105, 110])
        .where('work_area_tickets.work_area_id', +area)
        .where('mo_numbers.mo_barcode', 'like', `%${barcode}%`);

      if (vouchers.length === 0) {
        return res.status(200).json({
          ok: false,
          totalRows: 0,
          data: [],
        });
      }

      return res.status(200).json({
        ok: true,
        totalRows: vouchers.length,
        data: vouchers,
      });
    }

    const vouchers = await WorkVouchers.query()
      .select(
        { mo: 'mo_numbers.num' },
        'mo_numbers.mo_id',
        'mo_numbers.customer',
        { voucher_id: 'work_vouchers.id' },
        { is_repo: 'work_vouchers.is_repo' },
        { voucher_type: 'work_voucher_types.name' },
        { group: 'work_vouchers.work_voucher_group_id' }
      )
      .join(
        'work_area_tickets',
        'work_vouchers.id',
        'work_area_tickets.work_voucher_id'
      )
      .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
      .join(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .join(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id',
        'work_area_ticket_statuses.id'
      )
      .join(
        'work_statuses',
        'work_area_ticket_statuses.work_status_id',
        'work_statuses.id'
      )
      .whereNotIn('mo_numbers.mo_status', [
        'Void',
        'Cancelled',
        'Materials',
        'Complete',
      ])
      .whereNotIn('work_statuses.id', [100, 105, 110])
      .where('work_area_tickets.work_area_id', +area)
      .where('mo_numbers.num', 'like', `%${mo}%`);

    if (vouchers.length === 0) {
      return res.status(200).json({
        ok: false,
        totalRows: 0,
        data: [],
      });
    }

    return res.status(200).json({
      ok: true,
      totalRows: vouchers.length,
      data: vouchers,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({ ok: false });
  }
}

export async function deleteVoucherGroup(req: Request, res: Response) {
  const { id } = req.params;
  const { area, employee_id } = req.body;

  if (!id || isNaN(Number(id))) {
    return res.status(400).json({ ok: false, message: 'Invalid ID' });
  }
  const useWorkVoucherGroupId = Number(id);

  try {
    // get all vouchers in group
    const vouchers = await WorkVouchers.query().where(
      'work_voucher_group_id',
      useWorkVoucherGroupId
    );

    // delete all vouchers in group(use transaction)

    await WorkVouchers.transaction(async (trx) => {
      // change work_voucher_group_id to null for all vouchers
      for (let i = 0; i < vouchers.length; i++) {
        await WorkVouchers.query(trx)
          .update({ work_voucher_group_id: null })
          .where('id', vouchers[i].id);
      }

      await WorkVoucherGroups.query()
        .update({ removed_at: new Date() })
        .where('id', useWorkVoucherGroupId);

      await WorkActivityLog.query().insert({
        work_area_id: area,
        employee_id: employee_id,
        activity: 'Delete voucher group',
        module_id: useWorkVoucherGroupId,
        module_name: 'Voucher group',
        data: JSON.stringify({ id: useWorkVoucherGroupId }),
      });
    });

    return res.status(200).json({ ok: true });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ ok: false, message: error.message });
  }
}
