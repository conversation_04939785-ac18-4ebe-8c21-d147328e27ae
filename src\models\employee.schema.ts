import type { ModelObject } from 'objection';

import { Model } from '@app/db';

export class Employee extends Model {
  static get tableName(): string {
    return 'employees';
  }

  static get idColumn() {
    return 'employee_id';
  }

  employee_id!: number;
  status!: number;
  first_name!: string;
  last_name!: string;
  department!: string;
  short_name!: string;
  nickname!: string;
  work_area_id!: number;
  dui!: string;
  nit!: string;
  isss!: string;
  employment_date!: Date;
  image!: string;
  employee_contact!: string;
  emergency_number!: string;
  gender!: string;
  barcode!: number;
  created_at!: Date;
  updated_at!: Date;
  emp_barcode!: number;
  rfid_code!: number;
  section!: string;
  title!: string;
  departments_id!: number;
  title_id!: number;
}
export type EmployeeShape = ModelObject<Employee>;

export class WorkTypeSkills extends Model {
  static tableName = 'work_type_skills';

  id: number;
  work_type_id: number;
  name: string;
  color: string;
  parent_skill_id: number;
  level_up_from_id: number;
  created_at: Date;
  updated_at: Date;
}

export class WorkTypeEmployeeSkills extends Model {
  static tableName = 'work_type_employee_skills';

  id: number;
  created_at: Date;
  employee_id: number;
  work_type_skill_id: number;
  awarded_by_employee_id: number;
}
