import { Router } from 'express';

import {
  addMosToPrintId,
  createPrintId,
  deleteMosFromPrintId,
  deletePrintId,
  getPrintIdById,
  getPrintIdByName,
  getPrintIds,
  updatePrintId,
} from '@app/controllers/printer.controller';

const printRouter: Router = Router();

printRouter.route('/create/printID').post(createPrintId);
printRouter.route('/add/mos/printID/:id').post(addMosToPrintId);
printRouter.route('/delete/mo/printID/:id').delete(deleteMosFromPrintId);
printRouter.route('/delete/printID/:id').delete(deletePrintId);
printRouter.route('/get/printIDs').get(getPrintIds);
printRouter.route('/get/printID/:id').get(getPrintIdById);
printRouter.route('/get/printID/name/:name').get(getPrintIdByName);
printRouter.route('/update/printID/:id').patch(updatePrintId);

export { printRouter };
