import moment from 'moment';

import { knex } from '@app/db';
import { MoInvoice, MoInvoiceItem } from '@app/models/mo_invoice.schema';

import type { MadeItem, ReportData } from './config';

export const updateDatabaseWithCprData = async (
  reportData: ReportData,
  itemsMade: MadeItem[],
  filename: string,
  options?: {
    force: boolean;
  }
) => {
  let force = options?.force ?? false;
  console.log('update database with cpr data');

  const runDateTime = moment(
    `${reportData.run_date} ${reportData.run_time}`,
    'YYYY/MM/DD HH:mm:ss'
  ).toDate();

  console.log('run date time', runDateTime);

  // update database
  // check if invoice was already uploaded
  const existingInvoice = await MoInvoice.query()
    .where('invoice_type', reportData.report_type)
    .where('from_date', reportData.report_start)
    .where('to_date', reportData.report_end)
    .first();

  if (existingInvoice) {
    // if existing invoice is misisng a file name, act like force is true
    console.log('existing invoice', JSON.stringify(existingInvoice, null, 2));
    if (!existingInvoice.filename) {
      console.log('missing filename, forcing update');
      force = true;
    }
    // check if same file just for report
    if (+existingInvoice.run_datetime === +runDateTime && !force) {
      console.log('same file, not updating');
      return;
    }
    // if new runDateTime is older, throw error saying older file
    if (+existingInvoice.run_datetime > +runDateTime && !force) {
      console.log(
        'run date is older',
        existingInvoice.run_datetime,
        runDateTime
      );
      throw new Error(
        `run date is older than last saved, run with newest file of date ${existingInvoice.run_datetime}`
      );
    }
    // if new runDateTime is newer than existing, clear previous data
    console.log('deleting previous values for new values');
    await MoInvoiceItem.query()
      .delete()
      .where('mo_invoice_id', existingInvoice.id);
    await MoInvoice.query().deleteById(existingInvoice.id);
  }

  console.log('inserting new invoice');
  return await knex.transaction(async (trx) => {
    const invoice = await MoInvoice.query(trx).insert({
      invoice_type: reportData.report_type,
      accepted_format_type: reportData.accepted_format_type,
      from_date: reportData.report_start,
      to_date: reportData.report_end,
      run_datetime: runDateTime,
      pay_date: reportData.pay_date,
      contractor_number: reportData.invoice_number,
      filename,
      accepted_pages: reportData.accepted_pages.join(','),
      rejected_pages: reportData.rejected_pages.join(','),
      summary_pages: reportData.summary_pages.join(','),
    });

    const itemsToInsert = [];
    for (const item of itemsMade) {
      if (!item.vch) {
        console.log('item', item);
        throw new Error('vch is missing');
      }
      if (!item.order) {
        console.log('item', item);
        throw new Error('order is missing');
      }
      const vchFull = item.vch.toString().padStart(3, '0');
      const moString = `${item.order}/${vchFull}`;

      itemsToInsert.push({
        mo_invoice_id: invoice.id,
        page_number: item.page,
        row_pixel: item.row,
        mo_number: moString,
        order_number: item.order.toString(),
        order_line_number: vchFull,
        style_number: item.style,
        qty: item.qty,
        unit_price: item.unit,
        total_price: item.total,
        class_code: item.class_code,
        class_name: item.class_name,
        ltr: item.ltr,
        size: item.size,
        CP: item.CP,
        stars: item.stars,
        cut_price_part: item.cut,
        sew_price_part: item.sew,
        sub_price_part: item.sub_labor,
        prt_price_part: item.prt,
        prs_price_part: item.prs,
        art_price_part: item.art,
        factory_code: item.factory,
      });
    }

    // await MoInvoiceItem.query(trx).insert(itemsToInsert);
    await knex
      .batchInsert('mo_invoice_items', itemsToInsert, 1000)
      .transacting(trx);

    console.log('inserted items');

    // update statement to add mo_id to items using mo_numbers table mo_id column
    await trx.raw(
      `
      UPDATE mo_invoice_items
      INNER JOIN mo_numbers ON mo_invoice_items.mo_number = mo_numbers.num
      SET mo_invoice_items.mo_id = mo_numbers.mo_id
      WHERE mo_invoice_items.mo_invoice_id = ?
    `,
      [invoice.id]
    );

    // check if any mo_numbers are missing on new invoice items
    // const missingMoNumbers = await MoInvoiceItem.query(trx)
    //   .whereNull('mo_id')
    //   .where('mo_invoice_id', invoice.id);

    // if (missingMoNumbers.length) {
    //   const moNumbers = missingMoNumbers.map((item) => item.mo_number).join(',');
    //   console.log('missing mo numbers', missingMoNumbers, moNumbers);
    //   throw new Error('missing mo numbers');
    // }

    console.log('updated mo numbers');

    return invoice;
  });
};
