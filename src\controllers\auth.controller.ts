import { compareSync, genSaltSync, hashSync } from 'bcryptjs';
import type { Request, Response } from 'express';
import type { z } from 'zod';

import { generateJWT } from '@app/helpers';
import type {
  RegisterSchema,
  RepairPartsLoginSchema,
  WarehouseDownloaderLoginSchema,
  WarehousePullLoginSchema,
  sesssionIDSchema,
} from '@app/interface/zod_schemas';
import { Machine } from '@app/models/machines.schema';
import { RepairPartsUsers } from '@app/models/repair_parts.schema';
import { RhinestoneUser } from '@app/models/rhinestone.schema';
import { Buildings } from '@app/models/tickets.schema';
import {
  WarehouseDownloaderOrders,
  WarehousePullSessionAllocations,
  WarehousePullSessionOrderParts,
  WarehousePullSessionOrders,
  WarehousePullSessions,
} from '@app/models/warehouse_pull';
import { buildLogger } from '@app/settings';

const logger = buildLogger('controllers/auth.controller.ts');

export const renewalToken = async (req: Request, res: Response) => {
  const { area_id, employee_id } = req.body.tokenInfo as unknown as {
    area_id: number;
    employee_id: number;
  };

  try {
    const token = generateJWT({
      area_id,
      employee_id,
      exp: '1d',
    });

    if (!token) {
      logger.error('No se puede generar el token');

      return res.status(400).json({
        ok: false,
        message: 'No se puede generar el token',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Se actualizó el token correctamente',
      token,
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(500).json({ ok: false, error: error.message });
    }
    logger.error('Ocurrio un error en el servidor');

    return res
      .status(500)
      .json({ ok: false, error: 'Ocurrio un error en el servidor' });
  }
};

export const loginApp = async (req: Request, res: Response) => {
  try {
    const { employeeID, password } = req.body as unknown as {
      employeeID: number;
      password: string;
    };

    if (!employeeID || !password) {
      logger.error('No se enviaron los datos necesarios');

      return res.status(400).json({
        ok: false,
        message: 'No se enviaron los datos necesarios',
      });
    }

    const user = await RhinestoneUser.query()
      .innerJoin(
        'employees',
        'rhinestones_users.employee_id',
        'employees.employee_id'
      )
      .innerJoin(
        'rhinestones_areas',
        'rhinestones_users.rhinestones_area_id',
        'rhinestones_areas.id'
      )
      .select([
        { id: 'rhinestones_users.id' },
        { name: 'employees.first_name' },
        { area: 'rhinestones_areas.name' },
        {
          area_id: 'rhinestones_users.rhinestones_area_id',
        },
        { password: 'rhinestones_users.password' },
      ])
      .where('rhinestones_users.employee_id', employeeID)
      .where('rhinestones_users.is_active', true)
      .first()
      .castTo<{
        id: number;
        name: string;
        area: string;
        area_id: number;
        password: string;
      }>();

    if (!user) {
      logger.error(`No existe el usuario ${employeeID}`);

      return res.status(400).json({
        ok: false,
        message: 'Error en el usuario o contraseña',
      });
    }

    const validPassword = compareSync(password, user.password);

    if (!validPassword) {
      logger.error(`Contraseña incorrecta ${employeeID}`);

      return res.status(400).json({
        ok: false,
        message: 'Error en el usuario o contraseña',
      });
    }

    const token = generateJWT({
      area_id: user.area_id,
      employee_id: employeeID,
      exp: '1d',
    });

    if (!token) {
      logger.error('No se puede generar el token');

      return res.status(400).json({
        ok: false,
        message: 'No se puede generar el token',
      });
    }

    return res.status(200).json({
      ok: true,
      message: `Bienvenido ${user.name}`,
      data: {
        token,
        logged: true,
        id: user.id,
        name: user.name,
        area: user.area,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(500).json({ ok: false, error: error.message });
    }

    logger.error('Ocurrio un error en el servidor');

    return res
      .status(500)
      .json({ ok: false, error: 'Ocurrio un error en el servidor' });
  }
};

export const registerApp = async (
  req: Request<unknown, unknown, z.infer<typeof RegisterSchema>>,
  res: Response
) => {
  try {
    const { employeeID, password, areaID } = req.body;

    const user = await RhinestoneUser.query()
      .where('employee_id', employeeID)
      .where('is_active', true)
      .where('rhinestones_area_id', areaID)
      .first();

    if (user) {
      logger.error(`Ya existe el usuario ${employeeID}`);

      return res.status(400).json({
        ok: false,
        message: 'Ya existe el usuario',
      });
    }

    const salt = genSaltSync();
    const hashedPassword = hashSync(password, salt);

    const newUser = await RhinestoneUser.query().insert({
      employee_id: employeeID,
      password: hashedPassword,
      rhinestones_area_id: areaID,
    });

    if (!newUser) {
      logger.error(`No se puede crear el usuario ${employeeID}`);

      return res.status(400).json({
        ok: false,
        message: 'No se puede crear el usuario',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Se creo el usuario correctamente',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(500).json({ ok: false, error: error.message });
    }

    logger.error('Ocurrio un error en el servidor');

    return res
      .status(500)
      .json({ ok: false, error: 'Ocurrio un error en el servidor' });
  }
};

export const changePasswordApp = async (req: Request, res: Response) => {
  try {
    const { employee_id } = req.body.tokenInfo as unknown as {
      area_id: number;
      employee_id: number;
    };
    const { password, newPassword, confirmPassword } = req.body as unknown as {
      password: string;
      newPassword: string;
      confirmPassword: string;
    };

    if (!password || !newPassword || !confirmPassword) {
      logger.error('No se enviaron los datos necesarios');

      return res.status(400).json({
        ok: false,
        message: 'No se enviaron los datos necesarios',
      });
    }

    const user = await RhinestoneUser.query().findById(+employee_id);

    if (!user) {
      logger.error(`No existe el usuario ${employee_id}`);

      return res.status(400).json({
        ok: false,
        message: 'Verifique los datos',
      });
    }

    const validPassword = compareSync(password, user.password);

    if (!validPassword) {
      logger.error(`Contraseña incorrecta ${employee_id}`);

      return res.status(400).json({
        ok: false,
        message: 'Verifique los datos',
      });
    }
    const comparePasswords = password === newPassword;

    if (comparePasswords) {
      logger.error(
        `La nueva contraseña no puede ser igual a la anterior ${employee_id}`
      );

      return res.status(400).json({
        ok: false,
        message: 'La nueva contraseña no puede ser igual a la anterior',
      });
    }

    const validNewPassword = newPassword === confirmPassword;

    if (!validNewPassword) {
      logger.error(`Las contraseñas no coinciden ${employee_id}`);

      return res.status(400).json({
        ok: false,
        message: 'Las contraseñas no coinciden',
      });
    }

    const salt = genSaltSync();
    const hashedPassword = hashSync(newPassword, salt);

    const updatedUser = await RhinestoneUser.query().patchAndFetchById(
      +employee_id,
      {
        password: hashedPassword,
      }
    );

    if (!updatedUser) {
      logger.error(`No se puede actualizar la contraseña ${employee_id}`);

      return res.status(400).json({
        ok: false,
        message: 'No se puede actualizar la contraseña',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Se actualizó la contraseña correctamente',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(500).json({ ok: false, error: error.message });
    }

    logger.error('Ocurrio un error en el servidor');

    return res
      .status(500)
      .json({ ok: false, error: 'Ocurrio un error en el servidor' });
  }
};

export const loginLaserOperatorApp = async (req: Request, res: Response) => {
  try {
    const { employeeID, password, machineID } = req.body as unknown as {
      employeeID: number;
      password: string;
      machineID: number;
    };

    if (!employeeID || !password || !machineID) {
      logger.error(
        'No se enviaron los datos necesarios para el inicio de sesión'
      );

      return res.status(400).json({
        ok: false,
        message: 'No se enviaron los datos necesarios',
      });
    }

    const machine = await Machine.query()
      .where('machine_id', machineID)
      .where('status', 1);

    if (!machine) {
      logger.error(`No existe la maquina ${machineID}`);

      return res.status(400).json({
        ok: false,
        message: 'Verifique los datos',
      });
    }

    const user = await RhinestoneUser.query()
      .innerJoin(
        'employees',
        'rhinestones_users.employee_id',
        'employees.employee_id'
      )
      .innerJoin(
        'rhinestones_areas',
        'rhinestones_users.rhinestones_area_id',
        'rhinestones_areas.id'
      )
      .where('rhinestones_users.employee_id', employeeID)
      .where('rhinestones_users.is_active', true)
      .select([
        { id: 'rhinestones_users.id' },
        { name: 'employees.first_name' },
        { area: 'rhinestones_areas.name' },
        {
          area_id: 'rhinestones_users.rhinestones_area_id',
        },
        { password: 'rhinestones_users.password' },
      ])
      .first()
      .castTo<{
        id: number;
        name: string;
        area: string;
        area_id: number;
        password: string;
      }>();

    if (!user) {
      logger.error(`No existe el usuario ${employeeID}`);

      return res.status(400).json({
        ok: false,
        message: 'Error en el usuario o contraseña',
      });
    }

    const validPassword = compareSync(password, user.password as string);

    if (!validPassword) {
      logger.error(`Contraseña incorrecta ${employeeID}`);

      return res.status(400).json({
        ok: false,
        message: 'Error en el usuario o contraseña',
      });
    }

    const token = generateJWT({
      area_id: user.area_id,
      employee_id: employeeID,
      exp: '1d',
    });

    if (!token) {
      logger.error('No se puede generar el token');

      return res.status(400).json({
        ok: false,
        message: 'No se puede generar el token',
      });
    }

    return res.status(200).json({
      ok: true,
      message: `Bienvenido ${user.name}`,
      data: {
        token,
        logged: true,
        id: employeeID,
        name: user.name,
        area: user.area,
        machine,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(500).json({ ok: false, error: error.message });
    }

    logger.error('Ocurrio un error en el servidor');

    return res
      .status(500)
      .json({ ok: false, error: 'Ocurrio un error en el servidor' });
  }
};

export const registerLaserOperatorApp = async (req: Request, res: Response) => {
  try {
    const { employeeID, password, areaID } = req.body as unknown as {
      employeeID: number;
      password: string;
      areaID: number;
    };

    if (!employeeID || !password || !areaID) {
      logger.error('No se enviaron los datos necesarios');

      return res.status(400).json({
        ok: false,
        message: 'No se enviaron los datos necesarios',
      });
    }

    const user = await RhinestoneUser.query()
      .where('employee_id', employeeID)
      .where('is_active', true)
      .first();

    if (user) {
      logger.error(`Ya existe el usuario ${employeeID}`);

      return res.status(400).json({
        ok: false,
        message: 'Ya existe el usuario',
      });
    }

    const salt = genSaltSync();
    const hashedPassword = hashSync(password, salt);

    const newUser = await RhinestoneUser.query().insert({
      employee_id: employeeID,
      password: hashedPassword,
      rhinestones_area_id: areaID,
    });

    if (!newUser) {
      logger.error(`No se puede crear el usuario ${employeeID}`);

      return res.status(400).json({
        ok: false,
        message: 'No se puede crear el usuario',
      });
    }

    const token = generateJWT({
      area_id: areaID,
      employee_id: employeeID,
      exp: '1d',
    });

    if (!token) {
      logger.error('No se puede generar el token');

      return res.status(400).json({
        ok: false,
        message: 'No se puede generar el token',
      });
    }

    const getUserInfo = await RhinestoneUser.query()
      .innerJoin(
        'employees',
        'rhinestones_users.employee_id',
        'employees.employee_id'
      )
      .innerJoin(
        'rhinestones_areas',
        'rhinestones_users.rhinestones_area_id',
        'rhinestones_areas.id'
      )
      .select([
        { name: 'employees.first_name' },
        { area: 'rhinestones_areas.name' },
      ])
      .where('rhinestones_users.employee_id', employeeID)
      .where('rhinestones_users.is_active', true)
      .first()
      .castTo<{
        name: string;
        area: string;
      }>();

    return res.status(200).json({
      ok: true,
      message: 'Se generó el token correctamente',
      data: {
        token,
        logged: true,
        id: newUser.id,
        // TODO: fixed to correct value
        name: getUserInfo.name,
        area: getUserInfo.area,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(500).json({ ok: false, error: error.message });
    }

    logger.error('Ocurrio un error en el servidor');

    return res
      .status(500)
      .json({ ok: false, error: 'Ocurrio un error en el servidor' });
  }
};

export const changePasswordLaserOperatorApp = async (
  req: Request,
  res: Response
) => {
  try {
    const { employee_id } = req.body.tokenInfo as unknown as {
      area_id: number;
      employee_id: number;
    };
    const { password, newPassword, confirmPassword } = req.body as unknown as {
      password: string;
      newPassword: string;
      confirmPassword: string;
    };

    if (!password || !newPassword || !confirmPassword) {
      logger.error('No se enviaron los datos necesarios');

      return res.status(400).json({
        ok: false,
        message: 'No se enviaron los datos necesarios',
      });
    }

    const user = await RhinestoneUser.query().findById(+employee_id);

    if (!user) {
      logger.error(`No existe el usuario ${employee_id}`);

      return res.status(400).json({
        ok: false,
        message: 'Verifique los datos',
      });
    }

    const validPassword = compareSync(password, user.password);

    if (!validPassword) {
      logger.error(`Contraseña incorrecta ${employee_id}`);

      return res.status(400).json({
        ok: false,
        message: 'Verifique los datos',
      });
    }
    const comparePasswords = password === newPassword;

    if (comparePasswords) {
      logger.error(
        `La nueva contraseña no puede ser igual a la anterior ${employee_id}`
      );

      return res.status(400).json({
        ok: false,
        message: 'La nueva contraseña no puede ser igual a la anterior',
      });
    }

    const validNewPassword = newPassword === confirmPassword;

    if (!validNewPassword) {
      logger.error(`Las contraseñas no coinciden ${employee_id}`);

      return res.status(400).json({
        ok: false,
        message: 'Las contraseñas no coinciden',
      });
    }

    const salt = genSaltSync();
    const hashedPassword = hashSync(newPassword, salt);

    const updatedUser = await RhinestoneUser.query().patchAndFetchById(
      +employee_id,
      {
        password: hashedPassword,
      }
    );

    if (!updatedUser) {
      logger.error(`No se puede actualizar la contraseña ${employee_id}`);

      return res.status(400).json({
        ok: false,
        message: 'No se puede actualizar la contraseña',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Se actualizó la contraseña correctamente',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(500).json({ ok: false, error: error.message });
    }

    logger.error('Ocurrio un error en el servidor');

    return res
      .status(500)
      .json({ ok: false, error: 'Ocurrio un error en el servidor' });
  }
};

export const loginWarehousePullApp = async (
  req: Request<unknown, unknown, z.infer<typeof WarehousePullLoginSchema>>,
  res: Response
) => {
  try {
    const { employeeID, password } = req.body;

    const user = await RhinestoneUser.query()
      .innerJoin(
        'employees',
        'rhinestones_users.employee_id',
        'employees.employee_id'
      )
      .innerJoin(
        'rhinestones_areas',
        'rhinestones_users.rhinestones_area_id',
        'rhinestones_areas.id'
      )
      .select([
        { id: 'rhinestones_users.id' },
        { name: 'employees.first_name' },
        { area: 'rhinestones_areas.name' },
        {
          area_id: 'rhinestones_areas.id',
        },
        { password: 'rhinestones_users.password' },
      ])
      .where('rhinestones_users.employee_id', employeeID)
      .where('rhinestones_users.rhinestones_area_id', 3)
      .where('rhinestones_users.is_active', true)
      .where('rhinestones_areas.is_active', true)
      .first()
      .castTo<{
        id: number;
        name: string;
        area: string;
        area_id: number;
        password: string;
      }>();

    if (!user) {
      logger.error(`No existe el usuario ${employeeID}`);

      return res.status(400).json({
        ok: false,
        message: 'Error en el usuario o contraseña',
      });
    }

    const validPassword = compareSync(password, user.password);

    if (!validPassword) {
      logger.error(`Contraseña incorrecta ${employeeID}`);

      return res.status(400).json({
        ok: false,
        message: 'Error en el usuario o contraseña',
      });
    }

    return res.status(201).json({
      ok: true,
      message: `Bienvenido ${user.name}`,
      data: {
        id: user.id,
        employeeID,
        name: user.name,
        area: user.area,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(500).json({ ok: false, error: error.message });
    }

    logger.error('Ocurrio un error en el servidor');

    return res
      .status(500)
      .json({ ok: false, error: 'Ocurrio un error en el servidor' });
  }
};

export const logoutWarehousePullApp = async (
  req: Request<unknown, unknown, z.infer<typeof sesssionIDSchema>>,
  res: Response
) => {
  try {
    const { sessionID } = req.body;

    const session = await WarehousePullSessions.query()
      .where('id', sessionID)
      .whereNull('finished_at')
      .first()
      .castTo<{ id: number }>();

    if (!session) {
      logger.error(`No se encontro la sesion: ${sessionID}`);

      return res.status(400).json({
        ok: false,
        message: `No se encontro la sesion: ${sessionID}`,
      });
    }

    const partsValidation = await WarehousePullSessionOrderParts.query()
      .select([
        'warehouse_pull_session_order_parts.id',
        'warehouse_pull_session_order_parts.sub_category',
        'mo_numbers.customer',
        'warehouse_pull_session_order_parts.part_number',
        WarehousePullSessionAllocations.query()
          .whereColumn(
            'warehouse_pull_session_order_part_id',
            'warehouse_pull_session_order_parts.id'
          )
          .sum('quantity')
          .as('totalAllocated'),
      ])
      .innerJoin(
        'warehouse_pull_session_orders',
        'warehouse_pull_session_orders.id',
        'warehouse_pull_session_order_parts.warehouse_pull_session_order_id'
      )
      .innerJoin(
        'mo_numbers',
        'mo_numbers.mo_id',
        'warehouse_pull_session_orders.mo_id'
      )
      .whereIn(
        'warehouse_pull_session_order_parts.warehouse_pull_session_order_id',
        WarehousePullSessionOrders.query()
          .where('warehouse_pull_session_id', sessionID)
          .select('id')
      )
      .castTo<
        {
          id: number;
          sub_category: string;
          customer: string;
          part_number: string;
          totalAllocated: number;
        }[]
      >();

    if (partsValidation.length === 0) {
      logger.error(`No se encontraron partes para la sesion: ${sessionID}`);

      return res.status(400).json({
        ok: false,
        message: `No se encontraron partes para la sesion: ${sessionID}`,
      });
    }

    const allPartsHaveAllocations = partsValidation.every((part) => {
      if (
        part.sub_category.toLowerCase() === 'carelabel' ||
        part.part_number.toUpperCase() === 'SBR ID LABEL -PRINTED LABEL'
      )
        return true;

      return part.totalAllocated !== null && part.totalAllocated > 0;
    });

    if (!allPartsHaveAllocations) {
      await WarehousePullSessions.query()
        .where('id', sessionID)
        .update({ status: 'pending' });

      return res.status(200).json({
        ok: true,
        message: 'Sesion pendiente, faltan materiales por descargar',
      });
    }

    await WarehousePullSessions.query()
      .where('id', sessionID)
      .update({ status: 'in_progress', finished_at: new Date() });

    return res.status(200).json({
      ok: true,
      message: 'Sesion cerrada',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(500).json({ ok: false, error: error.message });
    }

    logger.error('Ocurrio un error en el servidor');

    return res
      .status(500)
      .json({ ok: false, error: 'Ocurrio un error en el servidor' });
  }
};

export const loginWarehouseDownloaderApp = async (
  req: Request<
    unknown,
    unknown,
    z.infer<typeof WarehouseDownloaderLoginSchema>
  >,
  res: Response
) => {
  try {
    const { employeeID, password } = req.body;

    const user = await RhinestoneUser.query()
      .innerJoin(
        'employees',
        'rhinestones_users.employee_id',
        'employees.employee_id'
      )
      .innerJoin(
        'rhinestones_areas',
        'rhinestones_users.rhinestones_area_id',
        'rhinestones_areas.id'
      )
      .select([
        { id: 'rhinestones_users.id' },
        { name: 'employees.first_name' },
        { area: 'rhinestones_areas.name' },
        {
          area_id: 'rhinestones_users.rhinestones_area_id',
        },
        { password: 'rhinestones_users.password' },
      ])
      .where('rhinestones_users.employee_id', employeeID)
      .where('rhinestones_users.rhinestones_area_id', 4)
      .where('rhinestones_users.is_active', true)
      .where('rhinestones_areas.is_active', true)
      .first()
      .castTo<{
        id: number;
        name: string;
        area: string;
        area_id: number;
        password: string;
      }>();

    if (!user) {
      logger.error(`No existe el usuario ${employeeID}`);

      return res.status(401).json({
        ok: false,
        message: 'Error en el usuario o contraseña',
      });
    }

    const validPassword = compareSync(password, user.password);

    if (!validPassword) {
      logger.error(`Contraseña incorrecta ${employeeID}`);

      return res.status(400).json({
        ok: false,
        message: 'Error en el usuario o contraseña',
      });
    }

    const doesDownloadActive = await WarehouseDownloaderOrders.query()
      .where('employee_id', employeeID)
      .where('finished_at', null)
      .select('id', 'mo_id')
      .first()
      .castTo<{ id: number; mo_id: number }>();

    if (doesDownloadActive) {
      return res.status(200).json({
        ok: true,
        message: `Bienvenido ${user.name}`,
        data: {
          id: user.id,
          employeeID,
          name: user.name,
          area: user.area,
          downloadActive: {
            id: doesDownloadActive.id,
            mo_id: doesDownloadActive.mo_id,
          },
        },
      });
    }

    return res.status(200).json({
      ok: true,
      message: `Bienvenido ${user.name}`,
      data: {
        id: user.id,
        employeeID,
        name: user.name,
        area: user.area,
        downloadActive: null,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(500).json({ ok: false, error: error.message });
    }

    logger.error('Ocurrio un error en el servidor');

    return res
      .status(500)
      .json({ ok: false, error: 'Ocurrio un error en el servidor' });
  }
};

export async function loginRepairParts(
  req: Request<unknown, unknown, z.infer<typeof RepairPartsLoginSchema>>,
  res: Response
) {
  try {
    const { password, userID } = req.body;

    const user = await RepairPartsUsers.query()
      .innerJoin(
        'employees',
        'repair_parts_users.employee_id',
        'employees.employee_id'
      )
      .innerJoin(
        'buildings',
        'repair_parts_users.repair_part_building_id ',
        'buildings.building_id'
      )
      .where('repair_parts_users.employee_id', userID)
      .select([
        { id: 'repair_parts_users.id' },
        { name: 'employees.first_name' },
        { area: 'buildings.building' },
        { area_id: 'buildings.building_id' },
        { password: 'repair_parts_users.password' },
        { isActive: 'repair_parts_users.is_active' },
      ])
      .first()
      .castTo<{
        id: number;
        name: string;
        area: string;
        area_id: number;
        password: string;
        isActive: boolean;
      }>();

    if (!user || !user.isActive) {
      logger.error(`No existe el usuario ${userID}`);

      return res.status(401).json({
        ok: false,
        message: 'Error en el usuario o contraseña',
      });
    }

    const validPassword = compareSync(password, user.password);

    if (!validPassword) {
      logger.error(`Contraseña incorrecta ${userID}`);

      return res.status(400).json({
        ok: false,
        message: 'Error en el usuario o contraseña',
      });
    }

    const token = generateJWT({
      area_id: user.area_id,
      employee_id: userID,
      exp: '1y',
    });

    return res.status(200).json({
      ok: true,
      message: `Bienvenido ${user.name}`,
      data: {
        area: user.area,
        username: user.name,
        token,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(500).json({ ok: false, error: error.message });
    }

    logger.error('Ocurrio un error en el servidor');

    return res
      .status(500)
      .json({ ok: false, error: 'Ocurrio un error en el servidor' });
  }
}

export const registerAppRepairParts = async (
  req: Request<unknown, unknown, z.infer<typeof RegisterSchema>>,
  res: Response
) => {
  try {
    const { employeeID, password, areaID } = req.body;

    const user = await RepairPartsUsers.query()
      .where('employee_id', employeeID)
      .where('is_active', true)
      .where('repair_part_building_id', areaID)
      .first();

    if (user) {
      logger.error(`Ya existe el usuario ${employeeID} en el area ${areaID}`);

      return res.status(400).json({
        ok: false,
        message: 'Ya existe el usuario',
      });
    }

    const searchArea = await Buildings.query()
      .where('building_id', areaID)
      .select(['building_id'])
      .first()
      .castTo<{ building_id: number }>();

    if (!searchArea) {
      logger.error(`No existe el area ${areaID}`);

      return res.status(400).json({
        ok: false,
        message: 'No existe el area',
      });
    }

    const salt = genSaltSync();
    const hashedPassword = hashSync(password, salt);

    const newUser = await RepairPartsUsers.query()
      .insert({
        employee_id: employeeID,
        password: hashedPassword,
        repair_part_building_id: areaID,
      })
      .castTo<{ id: number }>();

    if (!newUser) {
      logger.error(`No se puede crear el usuario ${employeeID}`);

      return res.status(400).json({
        ok: false,
        message: 'No se puede crear el usuario',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Se creo el usuario correctamente',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      return res.status(500).json({ ok: false, error: error.message });
    }

    logger.error('Ocurrio un error en el servidor');

    return res
      .status(500)
      .json({ ok: false, error: 'Ocurrio un error en el servidor' });
  }
};
