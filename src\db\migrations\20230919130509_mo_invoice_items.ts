import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(
    'mo_invoice_items',
    (table: Knex.TableBuilder) => {
      // @ts-ignore
      table.collate('utf8mb4_unicode_ci');
      table.increments('id').unsigned().primary();
      table
        .timestamp('created_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      table
        .timestamp('updated_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
      table.integer('mo_invoice_id').unsigned().notNullable();
      table.integer('mo_id').unsigned().notNullable();
      table.integer('page_number').unsigned();
      table.integer('row_pixel').unsigned();
      table.string('mo_number').notNullable();
      table.string('order_number').notNullable();
      table.string('order_line_number').notNullable();
      table.string('style_number').notNullable();
      table.integer('qty').unsigned().notNullable();
      table.decimal('unit_price', 10, 4).notNullable();
      table.decimal('total_price', 10, 4).notNullable();
      table.string('class_code');
      table.string('class_name');
      table.integer('ltr');
      table.decimal('size', 6, 2);
      table.string('CP');
      table.decimal('cut_price_part', 10, 4);
      table.decimal('sew_price_part', 10, 4);
      table.decimal('sub_price_part', 10, 4);
      table.decimal('prt_price_part', 10, 4);
      table.decimal('prs_price_part', 10, 4);
      table.decimal('art_price_part', 10, 4);
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('mo_invoice_items');
}
