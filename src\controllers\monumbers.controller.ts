import axios from 'axios';
import { log } from 'console';
import type { Request, Response } from 'express';
import { fn, raw, ref } from 'objection';
import { v4 as uuidv4 } from 'uuid';
import type { z } from 'zod';

import type { IMoVoucher } from '@app/interface/monumbers';
import type { SearchOrderByMoOrVoucherSchema } from '@app/interface/zod_schemas';
import { MoSize } from '@app/models/pedreria.schema';
import { RhinestoneOrder } from '@app/models/rhinestone.schema';
import {
  MoNumber,
  MoScans,
  MoVouchers,
  WorkActivityLog,
  WorkAreaTicketStatuses,
  WorkAreaTickets,
  WorkAreas,
  WorkNotes,
  WorkVoucherCompanyIgnoreUpdates,
  WorkVoucherGroups,
  WorkVoucherTypes,
  WorkVouchers,
} from '@app/models/tickets.schema';
import { WarehousePullSessions } from '@app/models/warehouse_pull';
import { getMoInfoByBarcodeAndCompany } from '@app/services/monumbers';
import { createScan } from '@app/services/scanning';
import { createVoucherAndTicket } from '@app/services/voucher';

const RestfulApi = process.env.RESTFUL;
export interface IFabricView {
  ActualWidt: number;
  Barcode: 'string';
  ComponentFabricWidth: string;
  Customer: string;
  FabricWidth: string;
  PartNumber: string;
}

// Obtenemos la ruta del voucher enviando el barcode y exportamos la función
export async function getVoucherBarcode(req: Request, res: Response) {
  try {
    // Consulta a la base de datos por medio del schema
    // Con req.params.postBarcode obtemos el mo_barcode que nos envian por parametro en la url
    const mo_barcode: IMoVoucher['mo_barcode'] = req.params.postBarcode;
    const voucher = await MoVouchers.query()
      .joinRelated('voucher')
      .where('mo_barcode', mo_barcode)
      .where('mo_vouchers.file_status', 'Active')
      .select(
        'mo_vouchers.file_uuid',
        'mo_vouchers.file_status',
        'mo_vouchers.mo_id'
      )
      .first()
      .castTo<{
        file_uuid: string;
        file_status: string;
        mo_id: number;
      }>();

    if (voucher.file_uuid.length > 0) {
      const uri = `http://vouchers.varpro.org/FULL/${voucher.file_uuid}.pdf`;

      return res.status(200).json({
        uri,
        status: voucher.file_status,
        ok: true,
      });
    }
  } catch (error) {
    // Mensaje de error si la consulta falla
    return res.status(400).json({
      ok: false,
      message: 'Error, datos no validos',
      error,
    });
  }
}

// Obtenemos la ruta del voucher enviando el mo number y exportamos la función
export async function getVoucherMo(req: Request, res: Response) {
  try {
    // Consulta a la base de datos por medio del schema
    // Con req.params.postBarcode obtemos el mo_order que nos envian por parametro en la url
    const mo_number: IMoVoucher['mo_barcode'] = req.params.postMo;
    // Con req.body obtemos la data que nos envian
    const company_code: IMoVoucher['company_code'] = req.body.company;
    log(req.body.company + ' ' + req.params.postMo);
    const voucher = await MoVouchers.query()
      .joinRelated('voucher')
      .where('num', mo_number)
      .where('company_code', company_code)
      .where('mo_vouchers.file_status', 'Active')
      .select(
        'mo_vouchers.file_uuid',
        'mo_vouchers.file_status',
        'mo_vouchers.mo_id'
      )
      .first()
      .castTo<{
        file_uuid: string;
        file_status: string;
        mo_id: number;
      }>();

    if (voucher.file_uuid.length > 0) {
      const uri = `http://vouchers.varpro.org/FULL/${voucher.file_uuid}.pdf`;

      return res.status(200).json({
        uri,
        status: voucher.file_status,
        ok: true,
      });
    }
  } catch (error) {
    // Mensaje de error si la consulta falla
    return res.status(400).json({
      ok: false,
      message: 'Error, datos no validos',
      error,
    });
  }
}

// Obtenemos la ruta de todos los vouchers con MO activas y exportamos la función
export async function getActiveVoucherList(req: Request, res: Response) {
  try {
    // Con req.body obtemos la data que nos envian
    const company_code: IMoVoucher['company_code'] = req.body.company;
    // console.log(company_code);
    // obtenemos la lista de todas las mo que estan activas y ok to produce
    const voucher = await MoVouchers.query()
      .join('mo_numbers', 'mo_vouchers.mo_id', 'mo_numbers.mo_id')
      .leftJoin('styles', 'mo_numbers.style', '=', 'styles.style_number')
      .leftJoin('style_documents', function () {
        this.on('style_documents.style_id', '=', 'styles.style_id')
          .onIn('style_documents.is_removed', [false])
          .onIn('style_documents.style_document_type_id', [1]);
      })
      .where('mo_numbers.company_code', company_code)
      .where('mo_vouchers.file_status', 'Active')
      .whereNotIn('mo_numbers.mo_status', [
        'Void',
        'Cancelled',
        'Materials',
        'Complete',
        'Committed',
      ])
      .select(
        'mo_numbers.num',
        'mo_numbers.mo_order',
        'mo_numbers.customer',
        'mo_numbers.required_date',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.style_category',
        'mo_numbers.po_number',
        'mo_numbers.ItemDescription8',
        'mo_numbers.mo_barcode',
        'mo_numbers.sched_start',
        'mo_vouchers.file_uuid',
        'mo_vouchers.file_status',
        'mo_vouchers.mo_id',
        'style_documents.file_uuid as minimarkers',
        'style_documents.file_extension'
      )
      .castTo<
        {
          num: string;
          mo_order: string;
          customer: string;
          required_date: string;
          style: string;
          quantity: number;
          style_category: string;
          po_number: string;
          ItemDescription8: string;
          mo_barcode: string;
          sched_start: string;
          file_uuid: string;
          file_status: string;
          mo_id: number;
          minimarkers?: string | null;
          file_extension?: string | null;
        }[]
      >();

    // console.log(voucher.length);
    // testing knex
    if (voucher.length > 0) {
      /* for (let key in voucher) {
        var splitDate = voucher[key].required_date;
        var d = new Date(splitDate);
        const yr = new Intl.DateTimeFormat('en', { year: 'numeric' }).format(d)
        const mo = new Intl.DateTimeFormat('en', { month: 'numeric' }).format(d)
        const dy = new Intl.DateTimeFormat('en', { day: 'numeric' }).format(d)
        voucher[key].required_date =  yr + "-" + mo + "-" + dy;
        
      }     */

      return res.status(200).json({
        voucher,
        status: 'ok',
        ok: true,
      });
    }
  } catch (error) {
    // Mensaje de error si la consulta falla
    return res.status(400).json({
      ok: false,
      message:
        'Error, datos no validos ' + error + ' Company : ' + req.body.company,
      error,
    });
  }
}

interface QueryParamsMo {
  company?: string | string[];
  status?: string | string[];
}

export async function getMos(req: Request, res: Response) {
  const { company, status }: QueryParamsMo = req.query;

  const where: QueryParamsMo = {
    company: company ?? ['1', '2', '3'],
    status: status ?? [
      'In Production',
      'Materials Issue',
      'Sublimation',
      'Materials OK',
    ],
  };

  if (company) {
    where.company = Array.isArray(company) ? company : company?.split(',');
  }

  if (status) {
    where.status = Array.isArray(status) ? status : status?.split(',');
  }

  try {
    const getMos = await MoNumber.query()
      .whereNotIn('mo_numbers.mo_status', [
        'Void',
        'Cancelled',
        'Materials',
        'Complete',
      ])
      .whereIn('mo_numbers.company_code', [...where.company])
      .whereIn('mo_numbers.mo_status', [...where.status])
      .select(
        'mo_numbers.mo_id',
        fn
          .coalesce(
            MoVouchers.query()
              .select('mo_vouchers.file_uuid')
              .where('mo_vouchers.mo_id', ref('mo_numbers.mo_id'))
              .where('mo_vouchers.file_status', 'Active')
              .limit(1),
            null
          )
          .as('uuid'),
        { company: 'mo_numbers.company_code' },
        fn.coalesce(ref('mo_numbers.material_date'), null).as('material_date'),
        fn.coalesce(ref('mo_numbers.required_date'), null).as('required_date'),
        fn.coalesce(ref('mo_numbers.order_type'), 'N/A').as('order_type'),
        fn.coalesce(ref('mo_numbers.mo_status'), 'N/A').as('mo_status'),
        fn.coalesce(ref('mo_numbers.po_number'), 'N/A').as('po_number'),
        fn.coalesce(ref('mo_numbers.num'), 'N/A').as('num'),
        fn.coalesce(ref('mo_numbers.mo_order'), 'N/A').as('mo_order'),
        fn.coalesce(ref('mo_numbers.style'), 'N/A').as('style'),
        fn.coalesce(ref('mo_numbers.quantity'), 'N/A').as('quantity'),
        fn
          .coalesce(ref('mo_numbers.style_category'), 'N/A')
          .as('style_category'),
        fn
          .coalesce(ref('mo_numbers.ItemDescription8'), 'N/A')
          .as('ItemDescription8'),
        fn.coalesce(ref('mo_numbers.customer'), 'N/A').as('customer'),
        fn.coalesce(ref('mo_numbers.mo_barcode'), 'N/A').as('mo_barcode')
      )
      .castTo<
        {
          mo_id: number;
          uuid: string;
          company: number;
          material_date: string;
          required_date: string;
          order_type: string;
          mo_status: string;
          po_number: string;
          num: string;
          mo_order: string;
          style: string;
          quantity: number;
          style_category: string;
          ItemDescription8: string;
          customer: string;
          mo_barcode: string;
        }[]
      >();

    if (getMos.length === 0) {
      return res.status(204).json({
        ok: false,
        data: [],
      });
    }

    return res.status(200).json({
      ok: true,
      data: getMos,
      totalItems: getMos.length,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getInfoMoArea(req: Request, res: Response) {
  try {
    const { moID, voucherType, area } = req.body as {
      moID: number;
      voucherType: string;
      area: string;
    };

    const getIdVoucherType = await WorkVoucherTypes.query()
      .where('work_voucher_types.name', voucherType)
      .select('work_voucher_types.id')
      .castTo<{ id: number }[]>();

    const getVoucherInfo = await WorkVouchers.query()
      .join(
        'work_area_tickets',
        'work_vouchers.id',
        'work_area_tickets.work_voucher_id'
      )
      .where('work_vouchers.work_voucher_type_id', getIdVoucherType[0].id)
      .where('work_vouchers.mo_id', moID)
      .where('work_area_tickets.work_area_id', area);

    if (getVoucherInfo.length > 0) {
      return res.status(200).json({
        ok: true,
        voucherType,
      });
    } else {
      return res.status(200).json({ ok: false });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

// funcion para la vista de crear tickets - escanear voucher fisico
export async function getMosbyPPMO(req: Request, res: Response) {
  try {
    const { ppmo: ppmosGiven, voucherType, areaSelected } = req.body;

    const useAreaSelectedId = Number(areaSelected);
    if (!areaSelected || isNaN(useAreaSelectedId)) {
      return res.status(400).json({
        ok: false,
        message: 'El area no es valida',
      });
    }

    if (voucherType && !Array.isArray(voucherType)) {
      return res.status(400).json({
        ok: false,
        message: 'El voucherType debe ser un array',
      });
    }

    if (!ppmosGiven || !Array.isArray(ppmosGiven)) {
      return res.status(400).json({
        ok: false,
        message: 'El ppmo debe ser un array',
      });
    }

    const ppmo: { ppmo: string }[] = [];

    for (let i = 0; i < ppmosGiven.length; i++) {
      if (typeof ppmosGiven[i].ppmo !== 'string') {
        return res.status(400).json({
          ok: false,
          message: 'El ppmo debe ser un array de strings',
        });
      }
      ppmo.push({ ppmo: ppmosGiven[i].ppmo });
    }

    const data = [];
    const dataError = [];
    let companyCode: number;
    let isMain: { name: string }[];
    const dataResponse = [];

    if (voucherType?.length > 0) {
      for (let i = 0; i < voucherType.length; i++) {
        const voucherTypeId = Number(voucherType[i]);
        if (isNaN(voucherTypeId)) {
          return res.status(400).json({
            ok: false,
            message: 'El voucherType debe ser un array de numeros',
          });
        }
        isMain = await WorkVoucherCompanyIgnoreUpdates.query()
          .join(
            'work_types',
            'work_voucher_company_ignore_updates.work_type_id',
            'work_types.id'
          )
          .join(
            'work_voucher_types',
            'work_voucher_company_ignore_updates.work_voucher_type_id',
            'work_voucher_types.id'
          )
          .join('work_areas', 'work_types.id', 'work_areas.work_type_id')
          .where('work_areas.work_area_id', useAreaSelectedId)
          .where('work_voucher_types.id', voucherTypeId)
          .select('work_voucher_types.name')
          .castTo<{ name: string }[]>();

        const getNameVoucherType = await WorkVoucherTypes.query()
          .where('work_voucher_types.id', voucherTypeId)
          .select('work_voucher_types.name')
          .castTo<{ name: string }[]>();

        for (let j = 0; j < ppmo.length; j++) {
          // se agrego el company_code de acuerdo al codigo de barra de la MO
          let barcode: string;

          if (ppmo[j].ppmo.startsWith('P')) {
            companyCode = 1;
            barcode = ppmo[j].ppmo;
          } else if (ppmo[j].ppmo.startsWith('A')) {
            companyCode = 2;
            barcode = ppmo[j].ppmo.slice(1);
          } else {
            companyCode = 3;
            barcode = ppmo[j].ppmo.replace('-', '/');
          }

          if (
            barcode.substr(0, 5) !== 'MEVBR' ||
            barcode.substr(0, 4) !== 'MEVB'
          ) {
            const getMoInfo = await MoNumber.query()
              .whereNotIn('mo_numbers.mo_status', [
                'Void',
                'Cancelled',
                'Materials',
                'Complete',
              ])
              .where('mo_numbers.mo_barcode', barcode)
              .where('mo_numbers.company_code', companyCode)
              .select(
                'mo_numbers.mo_id',
                'mo_numbers.po_number',
                'mo_numbers.order_type',
                'mo_numbers.mo_order',
                'mo_numbers.num',
                'mo_numbers.mo_status',
                'mo_numbers.style',
                'mo_numbers.quantity',
                'mo_numbers.style_category',
                'mo_numbers.ItemDescription8',
                'mo_numbers.customer',
                'mo_numbers.company_code'
              )
              .castTo<
                {
                  mo_id: number;
                  po_number: string;
                  order_type: string;
                  mo_order: string;
                  num: string;
                  mo_status: string;
                  style: string;
                  quantity: number;
                  style_category: string;
                  ItemDescription8: string;
                  customer: string;
                  company_code: number;
                }[]
              >();

            const getVoucherInfo = await WorkVouchers.query()
              .join(
                'work_area_tickets',
                'work_vouchers.id',
                'work_area_tickets.work_voucher_id'
              )
              .join(
                'work_areas',
                'work_area_tickets.work_area_id',
                'work_areas.work_area_id'
              )
              .join(
                'work_area_ticket_statuses',
                'work_area_tickets.work_area_ticket_status_id',
                'work_area_ticket_statuses.id'
              )
              .join(
                'work_statuses',
                'work_area_ticket_statuses.work_status_id',
                'work_statuses.id'
              )
              .where('work_vouchers.work_voucher_type_id', voucherTypeId)
              .where('work_statuses.id', '<>', '110')
              .where('work_vouchers.mo_id', getMoInfo[0].mo_id)
              .select('work_vouchers.id', 'work_area_ticket_statuses.name')
              .castTo<{ id: number; name: string }[]>();

            if (getVoucherInfo.length > 0) {
              data.push({
                exist: 1,
                isMain: isMain ? 'true' : 'false',
                infoMo: 'DUPLICADO',
                ppmo: ppmo[j].ppmo,
                statusTicketDuplicate: `${getVoucherInfo[0].id} - ${getVoucherInfo[0].name}`,
                voucherType: getNameVoucherType[0].name,
                voucherTypeId: voucherType[i],
                repo: 'NO',
                mo_id: getMoInfo[0].mo_id,
                po_number: getMoInfo[0].po_number || 'N/A',
                mo_order: getMoInfo[0].mo_order,
                order_type: getMoInfo[0].order_type || 'N/A',
                num: getMoInfo[0].num,
                mo_status: getMoInfo[0].mo_status,
                style: getMoInfo[0].style,
                quantity: getMoInfo[0].quantity,
                style_category: getMoInfo[0].style_category,
                ItemDescription8: getMoInfo[0].ItemDescription8,
                customer: getMoInfo[0].customer || 'N/A',
                company_code: getMoInfo[0].company_code,
                voucherScan: false,
              });
            } else {
              data.push({
                exist: 0,
                infoMo: 'NUEVO',
                isMain: isMain ? 'true' : 'false',
                ppmo: ppmo[j].ppmo,
                statusTicketDuplicate: '',
                voucherType: getNameVoucherType[0].name,
                voucherTypeId: voucherType[i],
                repo: 'NO',
                mo_id: getMoInfo[0].mo_id,
                po_number: getMoInfo[0].po_number || 'N/A',
                mo_order: getMoInfo[0].mo_order,
                order_type: getMoInfo[0].order_type || 'N/A',
                num: getMoInfo[0].num,
                mo_status: getMoInfo[0].mo_status,
                style: getMoInfo[0].style,
                quantity: getMoInfo[0].quantity,
                style_category: getMoInfo[0].style_category,
                ItemDescription8: getMoInfo[0].ItemDescription8,
                customer: getMoInfo[0].customer || 'N/A',
                company_code: getMoInfo[0].company_code,
                voucherScan: false,
              });
            }
          } else {
            // ? logica para escanear vouchers
            // obtenermos el voucherId
            // TODO: Use split to get the voucherId
            const voucher =
              ppmo[j].ppmo.substr(0, 5) === 'MEVBR'
                ? ppmo[j].ppmo.substr(5)
                : ppmo[j].ppmo.substr(4);

            // verificamos que no exista un ticket en el area con el voucher
            const searchTicketInArea = await WorkAreaTickets.query()
              .where('work_area_tickets.work_voucher_id', voucher)
              .where('work_area_tickets.work_area_id', useAreaSelectedId);

            if (searchTicketInArea.length > 0) {
              dataError.push(ppmo[j].ppmo);
            } else {
              // obtenemos el ultimo ticket creado con el voucher
              const getLastTicketInfo = await WorkAreaTickets.query()
                .select('work_area_tickets.id', {
                  prevArea: 'work_area_tickets.work_area_id',
                })
                .where('work_area_tickets.work_voucher_id', voucher)
                .limit(1)
                .orderBy('work_area_tickets.id', 'desc')
                .castTo<{ id: number; prevArea: number }[]>();

              // obtenemos el status completo del area anterior
              const getStatusCompletoPrevArea: any =
                await WorkAreaTicketStatuses.query()
                  .where('work_area_id', getLastTicketInfo[0].prevArea)
                  .where('name', 'Completo')
                  .orWhere('work_status_id', 100)
                  .select('id')
                  .limit(1);

              // obtenemos la informacion de la mo asociada al voucher
              const getMoInfo = await WorkVouchers.query()
                .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
                .join(
                  'work_voucher_types',
                  'work_vouchers.work_voucher_type_id',
                  'work_voucher_types.id'
                )
                .whereNotIn('mo_numbers.mo_status', [
                  'Void',
                  'Cancelled',
                  'Materials',
                  'Complete',
                ])
                .where('work_vouchers.id', voucher)
                .select(
                  'mo_numbers.mo_id',
                  'mo_numbers.po_number',
                  'mo_numbers.order_type',
                  'mo_numbers.mo_order',
                  'mo_numbers.num',
                  'mo_numbers.mo_status',
                  'mo_numbers.style',
                  'mo_numbers.quantity',
                  'mo_numbers.style_category',
                  'mo_numbers.ItemDescription8',
                  'mo_numbers.customer',
                  'mo_numbers.company_code',
                  'work_voucher_types.name',
                  'work_vouchers.is_repo',
                  {
                    voucherTypeId: 'work_voucher_types.id',
                  }
                )
                .castTo<
                  {
                    mo_id: number;
                    po_number: string;
                    order_type: string;
                    mo_order: string;
                    num: string;
                    mo_status: string;
                    style: string;
                    quantity: number;
                    style_category: string;
                    ItemDescription8: string;
                    customer: string;
                    company_code: number;
                    name: string;
                    is_repo: boolean;
                    voucherTypeId: number;
                  }[]
                >();

              if (
                getStatusCompletoPrevArea.length > 0 &&
                getMoInfo.length > 0
              ) {
                data.push({
                  ppmo: ppmo[j].ppmo,
                  ticketId: getLastTicketInfo[0].id,
                  voucherScan: true,
                  voucherType: getMoInfo[0].name,
                  repo: getMoInfo[0].is_repo ? 'SI' : 'NO',
                  voucherId: voucher,
                  mo_id: getMoInfo[0].mo_id,
                  order_type: getMoInfo[0].order_type || 'N/A',
                  po_number: getMoInfo[0].po_number || 'N/A',
                  mo_order: getMoInfo[0].mo_order,
                  num: getMoInfo[0].num,
                  mo_status: getMoInfo[0].mo_status,
                  style: getMoInfo[0].style,
                  quantity: getMoInfo[0].quantity,
                  style_category: getMoInfo[0].style_category,
                  ItemDescription8: getMoInfo[0].ItemDescription8,
                  customer: getMoInfo[0].customer || 'N/A',
                  company_code: getMoInfo[0].company_code,
                  statusCompleteAreaPrev: getStatusCompletoPrevArea[0].id,
                  areaPrev: getLastTicketInfo[0].prevArea,
                  location: 'N/A',
                });
              } else {
                dataError.push(ppmo[i].ppmo);
              }
            }
          }
        }
      }

      for (let i = 0; i < data.length; i++) {
        dataResponse.push({
          ...data[i],
          uuid: uuidv4(),
        });
      }

      return res.status(200).json({
        ok: true,
        data: dataResponse,
        dataError,
      });
    } else {
      const getDefaultVoucherType = await WorkAreas.query()
        .join(
          'work_voucher_types',
          'work_areas.default_work_voucher_type_id',
          'work_voucher_types.id'
        )
        .where('work_areas.work_area_id', useAreaSelectedId)
        .whereNull('work_voucher_types.removed_at')
        .select('work_areas.default_work_voucher_type_id')
        .castTo<
          {
            default_work_voucher_type_id: number;
          }[]
        >();

      if (getDefaultVoucherType.length > 0) {
        isMain = await WorkVoucherCompanyIgnoreUpdates.query()
          .join(
            'work_types',
            'work_voucher_company_ignore_updates.work_type_id',
            'work_types.id'
          )
          .join(
            'work_voucher_types',
            'work_voucher_company_ignore_updates.work_voucher_type_id',
            'work_voucher_types.id'
          )
          .join('work_areas', 'work_types.id', 'work_areas.work_type_id')
          .where('work_areas.work_area_id', useAreaSelectedId)
          .where(
            'work_voucher_types.id',
            getDefaultVoucherType[0].default_work_voucher_type_id
          )
          .select('work_voucher_types.name')
          .castTo<{ name: string }[]>();

        const getNameVoucherType = await WorkVoucherTypes.query()
          .where(
            'work_voucher_types.id',
            getDefaultVoucherType[0].default_work_voucher_type_id
          )
          .select('work_voucher_types.name')
          .castTo<{ name: string }[]>();

        for (let i = 0; i < ppmo.length; i++) {
          // se agrego el company_code de acuerdo al codigo de barra de la MO
          let barcode: string;

          if (ppmo[i].ppmo.startsWith('P')) {
            companyCode = 1;
            barcode = ppmo[i].ppmo;
          } else if (ppmo[i].ppmo.startsWith('A')) {
            companyCode = 2;
            barcode = ppmo[i].ppmo.slice(1);
          } else {
            companyCode = 3;
            barcode = ppmo[i].ppmo.replace('-', '/');
            console.log(barcode);
          }

          if (
            barcode.substr(0, 5) !== 'MEVBR' ||
            barcode.substr(0, 4) !== 'MEVB'
          ) {
            const getMoInfo = await MoNumber.query()
              .whereNotIn('mo_numbers.mo_status', [
                'Void',
                'Cancelled',
                'Materials',
                'Complete',
              ])
              .where('mo_numbers.mo_barcode', barcode)
              .where('mo_numbers.company_code', companyCode)
              .select(
                'mo_numbers.mo_id',
                'mo_numbers.po_number',
                'mo_numbers.order_type',
                'mo_numbers.mo_order',
                'mo_numbers.num',
                'mo_numbers.mo_status',
                'mo_numbers.style',
                'mo_numbers.quantity',
                'mo_numbers.style_category',
                'mo_numbers.ItemDescription8',
                'mo_numbers.customer',
                'mo_numbers.company_code'
              )
              .castTo<
                {
                  mo_id: number;
                  po_number: string;
                  order_type: string;
                  mo_order: string;
                  num: string;
                  mo_status: string;
                  style: string;
                  quantity: number;
                  style_category: string;
                  ItemDescription8: string;
                  customer: string;
                  company_code: number;
                }[]
              >();

            const getVoucherInfo = await WorkVouchers.query()
              .join(
                'work_area_tickets',
                'work_vouchers.id',
                'work_area_tickets.work_voucher_id'
              )
              .join(
                'work_areas',
                'work_area_tickets.work_area_id',
                'work_areas.work_area_id'
              )
              .join(
                'work_area_ticket_statuses',
                'work_area_tickets.work_area_ticket_status_id',
                'work_area_ticket_statuses.id'
              )
              .join(
                'work_statuses',
                'work_area_ticket_statuses.work_status_id',
                'work_statuses.id'
              )
              .where(
                'work_vouchers.work_voucher_type_id',

                getDefaultVoucherType[0].default_work_voucher_type_id
              )
              .where('work_statuses.id', '<>', '110')
              .where('work_vouchers.mo_id', getMoInfo[0].mo_id)
              .select('work_vouchers.id', 'work_area_ticket_statuses.name')
              .castTo<{ id: number; name: string }[]>();

            if (getVoucherInfo.length > 0) {
              data.push({
                exist: 1,
                isMain: isMain ? 'true' : 'false',
                infoMo: 'DUPLICADO',
                ppmo: ppmo[i].ppmo,
                statusTicketDuplicate: `${getVoucherInfo[0].id} - ${getVoucherInfo[0].name}`,
                voucherType: getNameVoucherType[0].name,
                voucherTypeId:
                  getDefaultVoucherType[0].default_work_voucher_type_id,
                repo: 'NO',
                mo_id: getMoInfo[0].mo_id,
                po_number: getMoInfo[0].po_number || 'N/A',
                mo_order: getMoInfo[0].mo_order,
                order_type: getMoInfo[0].order_type || 'N/A',
                num: getMoInfo[0].num,
                mo_status: getMoInfo[0].mo_status,
                style: getMoInfo[0].style,
                quantity: getMoInfo[0].quantity,
                style_category: getMoInfo[0].style_category,
                ItemDescription8: getMoInfo[0].ItemDescription8,
                customer: getMoInfo[0].customer || 'N/A',
                company_code: getMoInfo[0].company_code,
                voucherScan: false,
              });
            } else {
              data.push({
                exist: 0,
                infoMo: 'NUEVO',
                isMain: isMain ? 'true' : 'false',
                ppmo: ppmo[i].ppmo,
                statusTicketDuplicate: '',
                voucherType: getNameVoucherType[0].name,
                voucherTypeId:
                  getDefaultVoucherType[0].default_work_voucher_type_id,
                repo: 'NO',
                mo_id: getMoInfo[0].mo_id,
                po_number: getMoInfo[0].po_number || 'N/A',
                mo_order: getMoInfo[0].mo_order,
                order_type: getMoInfo[0].order_type || 'N/A',
                num: getMoInfo[0].num,
                mo_status: getMoInfo[0].mo_status,
                style: getMoInfo[0].style,
                quantity: getMoInfo[0].quantity,
                style_category: getMoInfo[0].style_category,
                ItemDescription8: getMoInfo[0].ItemDescription8,
                customer: getMoInfo[0].customer || 'N/A',
                company_code: getMoInfo[0].company_code,
                voucherScan: false,
              });
            }
          } else {
            // ? logica para escanear vouchers
            // obtenermos el voucherId
            const voucher =
              ppmo[i].ppmo.substr(0, 5) === 'MEVBR'
                ? ppmo[i].ppmo.substr(5)
                : ppmo[i].ppmo.substr(4);

            // verificamos que no exista un ticket en el area con el voucher
            const searchTicketInArea = await WorkAreaTickets.query()
              .where('work_area_tickets.work_voucher_id', voucher)
              .where('work_area_tickets.work_area_id', useAreaSelectedId);

            if (searchTicketInArea.length > 0) {
              dataError.push(ppmo[i].ppmo);
            } else {
              // obtenemos el ultimo ticket creado con el voucher
              const getLastTicketInfo = await WorkAreaTickets.query()
                .select('work_area_tickets.id', {
                  prevArea: 'work_area_tickets.work_area_id',
                })
                .where('work_area_tickets.work_voucher_id', voucher)
                .limit(1)
                .orderBy('work_area_tickets.id', 'desc')
                .castTo<{ id: number; prevArea: number }[]>();

              // obtenemos el status completo del area anterior
              const getStatusCompletoPrevArea =
                await WorkAreaTicketStatuses.query()
                  .where('work_area_id', getLastTicketInfo[0].prevArea)
                  .where('name', 'Completo')
                  .orWhere('work_status_id', 100)
                  .select('id')
                  .limit(1)
                  .castTo<{ id: number }[]>();

              // obtenemos la informacion de la mo asociada al voucher
              const getMoInfo = await WorkVouchers.query()
                .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
                .join(
                  'work_voucher_types',
                  'work_vouchers.work_voucher_type_id',
                  'work_voucher_types.id'
                )
                .whereNotIn('mo_numbers.mo_status', [
                  'Void',
                  'Cancelled',
                  'Materials',
                  'Complete',
                ])
                .where('work_vouchers.id', voucher)
                .select(
                  'mo_numbers.mo_id',
                  'mo_numbers.po_number',
                  'mo_numbers.order_type',
                  'mo_numbers.mo_order',
                  'mo_numbers.num',
                  'mo_numbers.mo_status',
                  'mo_numbers.style',
                  'mo_numbers.quantity',
                  'mo_numbers.style_category',
                  'mo_numbers.ItemDescription8',
                  'mo_numbers.customer',
                  'mo_numbers.company_code',
                  'work_voucher_types.name',
                  'work_vouchers.is_repo',
                  {
                    voucherTypeId: 'work_voucher_types.id',
                  }
                )
                .castTo<
                  {
                    mo_id: number;
                    po_number: string;
                    order_type: string;
                    mo_order: string;
                    num: string;
                    mo_status: string;
                    style: string;
                    quantity: number;
                    style_category: string;
                    ItemDescription8: string;
                    customer: string;
                    company_code: number;
                    name: string;
                    is_repo: boolean;
                    voucherTypeId: number;
                  }[]
                >();

              if (
                getStatusCompletoPrevArea.length > 0 &&
                getMoInfo.length > 0
              ) {
                data.push({
                  ppmo: ppmo[i].ppmo,
                  ticketId: getLastTicketInfo[0].id,
                  voucherScan: true,
                  voucherType: getMoInfo[0].name,
                  repo: getMoInfo[0].is_repo ? 'SI' : 'NO',
                  voucherId: voucher,
                  mo_id: getMoInfo[0].mo_id,
                  order_type: getMoInfo[0].order_type || 'N/A',
                  po_number: getMoInfo[0].po_number || 'N/A',
                  mo_order: getMoInfo[0].mo_order,
                  num: getMoInfo[0].num,
                  mo_status: getMoInfo[0].mo_status,
                  style: getMoInfo[0].style,
                  quantity: getMoInfo[0].quantity,
                  style_category: getMoInfo[0].style_category,
                  ItemDescription8: getMoInfo[0].ItemDescription8,
                  customer: getMoInfo[0].customer || 'N/A',
                  company_code: getMoInfo[0].company_code,
                  statusCompleteAreaPrev: getStatusCompletoPrevArea[0].id,
                  areaPrev: getLastTicketInfo[0].prevArea,
                  location: 'N/A',
                });
              } else {
                dataError.push(ppmo[i].ppmo);
              }
            }
          }
        }

        const dataResponse = [];

        for (let i = 0; i < data.length; i++) {
          dataResponse.push({
            ...data[i],
            uuid: uuidv4(),
          });
        }

        return res.status(200).json({
          ok: true,
          data: dataResponse,
          dataError,
        });
      } else {
        return res.status(500).json({
          ok: false,
          data: 'No existe un Voucher Type por defecto',
        });
      }
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

// funcion para obtener la info de una lista de MOS en METIS
export async function getMosbyVolumen(req: Request, res: Response) {
  try {
    const {
      mo: { client, mos, groupId },
      voucherType,
      areaSelected,
    } = req.body;

    const data = [];
    const dataError = [];
    let getGroup: { newGroup: string; newGroupId: number }[];
    let isMain: { name: string }[];
    let barcode: string;
    const useCompanyCode = !client ? undefined : Number(client);
    if (useCompanyCode && isNaN(useCompanyCode)) {
      return res.status(400).json({
        ok: false,
        message: 'El client debe ser un numero',
      });
    }

    if (groupId) {
      if (isNaN(Number(groupId))) {
        return res.status(400).json({
          ok: false,
          message: 'El groupId debe ser un numero',
        });
      }
      getGroup = await WorkVoucherGroups.query()
        .where('work_voucher_groups.id', Number(groupId))
        .select([
          {
            newGroup: 'work_voucher_groups.name',
          },
          {
            newGroupId: 'work_voucher_groups.id',
          },
        ])
        .castTo<{ newGroup: string; newGroupId: number }[]>();
    }

    if (isNaN(Number(areaSelected))) {
      return res.status(400).json({
        ok: false,
        message: 'El areaSelected debe ser un numero',
      });
    }
    const useWorkAreaId = Number(areaSelected);

    if (voucherType) {
      if (isNaN(Number(voucherType))) {
        return res.status(400).json({
          ok: false,
          message: 'El voucherType debe ser un numero',
        });
      }
      const useVoucherTypeId = Number(voucherType);
      isMain = await WorkVoucherCompanyIgnoreUpdates.query()
        .join(
          'work_types',
          'work_voucher_company_ignore_updates.work_type_id',
          'work_types.id'
        )
        .join(
          'work_voucher_types',
          'work_voucher_company_ignore_updates.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .join('work_areas', 'work_types.id', 'work_areas.work_type_id')
        .where('work_areas.work_area_id', useWorkAreaId)
        .where('work_voucher_types.id', useVoucherTypeId)
        .select('work_voucher_types.name')
        .castTo<{ name: string }[]>();

      const getNameVoucherType = await WorkVoucherTypes.query()
        .where('work_voucher_types.id', useVoucherTypeId)
        .select('work_voucher_types.name')
        .castTo<{ name: string }[]>();

      for (let i = 0; i < mos.length; i++) {
        if (!mos[i]) {
          continue;
        }
        const moValue = mos[i];
        if (typeof moValue !== 'string') {
          return res.status(400).json({
            ok: false,
            message: 'El mo debe ser un array de strings',
          });
        }
        if (!useCompanyCode) {
          return res.status(400).json({
            ok: false,
            message: 'El client debe ser un numero',
          });
        }

        if (moValue.includes('-') || moValue.includes('/')) {
          barcode = moValue.replace('-', '/');
        } else {
          barcode = moValue;
        }

        const getMoInfo = await MoNumber.query()
          .whereNotIn('mo_numbers.mo_status', [
            'Void',
            'Cancelled',
            'Materials',
            'Complete',
          ])
          .where('mo_numbers.num', barcode)
          .where('mo_numbers.company_code', useCompanyCode)
          .select(
            'mo_numbers.mo_id',
            'mo_numbers.po_number',
            'mo_numbers.order_type',
            'mo_numbers.mo_order',
            'mo_numbers.num',
            'mo_numbers.mo_status',
            'mo_numbers.style',
            'mo_numbers.quantity',
            'mo_numbers.style_category',
            'mo_numbers.ItemDescription8',
            'mo_numbers.customer',
            'mo_numbers.company_code'
          )
          .castTo<
            {
              mo_id: number;
              po_number: string;
              order_type: string;
              mo_order: string;
              num: string;
              mo_status: string;
              style: string;
              quantity: number;
              style_category: string;
              ItemDescription8: string;
              customer: string;
              company_code: number;
            }[]
          >();

        if (getMoInfo.length > 0) {
          const getVoucherInfo = await WorkVouchers.query()
            .join(
              'work_area_tickets',
              'work_vouchers.id',
              'work_area_tickets.work_voucher_id'
            )
            .join(
              'work_areas',
              'work_area_tickets.work_area_id',
              'work_areas.work_area_id'
            )
            .join(
              'work_area_ticket_statuses',
              'work_area_tickets.work_area_ticket_status_id',
              'work_area_ticket_statuses.id'
            )
            .join(
              'work_statuses',
              'work_area_ticket_statuses.work_status_id',
              'work_statuses.id'
            )
            .where('work_vouchers.work_voucher_type_id', useVoucherTypeId)
            .where('work_statuses.id', '<>', '110')
            .where('work_vouchers.mo_id', getMoInfo[0].mo_id)
            .where('work_area_tickets.work_area_id', useWorkAreaId)
            .select('work_area_tickets.id', 'work_area_ticket_statuses.name')
            .castTo<{ id: number; name: string }[]>();

          if (getVoucherInfo.length > 0) {
            data.push({
              exist: 1,
              isMain: isMain.length > 0 ? 'false' : 'true',
              infoMo: 'DUPLICADO',
              statusTicketDuplicate: `${getVoucherInfo[0].id} - ${getVoucherInfo[0].name}`,
              voucherType: getNameVoucherType[0].name,
              voucherTypeId: useVoucherTypeId,
              repo: 'NO',
              mo_id: getMoInfo[0].mo_id,
              po_number: getMoInfo[0].po_number || 'N/A',
              order_type: getMoInfo[0].order_type || 'N/A',
              mo_order: getMoInfo[0].mo_order,
              num: getMoInfo[0].num,
              mo_status: getMoInfo[0].mo_status,
              style: getMoInfo[0].style,
              quantity: getMoInfo[0].quantity,
              style_category: getMoInfo[0].style_category,
              ItemDescription8: getMoInfo[0].ItemDescription8,
              customer: getMoInfo[0].customer || 'N/A',
              company_code: getMoInfo[0].company_code,
              newGroup: getGroup?.length > 0 ? getGroup[0].newGroup : '',
              newGroupId: getGroup?.length > 0 ? getGroup[0].newGroupId : '',
            });
          } else {
            data.push({
              infoMo: 'NUEVO',
              isMain: isMain.length > 0 ? 'false' : 'true',
              voucherType: getNameVoucherType[0].name,
              statusTicketDuplicate: '',
              voucherTypeId: useVoucherTypeId,
              repo: 'NO',
              mo_id: getMoInfo[0].mo_id,
              po_number: getMoInfo[0].po_number || 'N/A',
              order_type: getMoInfo[0].order_type || 'N/A',
              mo_order: getMoInfo[0].mo_order,
              num: getMoInfo[0].num,
              mo_status: getMoInfo[0].mo_status,
              style: getMoInfo[0].style,
              quantity: getMoInfo[0].quantity,
              style_category: getMoInfo[0].style_category,
              ItemDescription8: getMoInfo[0].ItemDescription8,
              customer: getMoInfo[0].customer || 'N/A',
              company_code: getMoInfo[0].company_code,
              newGroup: getGroup?.length > 0 ? getGroup[0].newGroup : '',
              newGroupId: getGroup?.length > 0 ? getGroup[0].newGroupId : '',
            });
          }
        } else {
          dataError.push(mos[i]);
        }
      }

      const dataResponse = [];

      for (let i = 0; i < data.length; i++) {
        dataResponse.push({
          ...data[i],
          uuid: uuidv4(),
        });
      }

      return res.status(200).json({
        ok: true,
        data: dataResponse,
        dataError,
      });
    } else {
      const getDefaultVoucherType = await WorkAreas.query()
        .join(
          'work_voucher_types',
          'work_areas.default_work_voucher_type_id',
          'work_voucher_types.id'
        )
        .where('work_areas.work_area_id', useWorkAreaId)
        .whereNull('work_voucher_types.removed_at')
        .select('work_areas.default_work_voucher_type_id')
        .castTo<
          {
            default_work_voucher_type_id: number;
          }[]
        >();

      if (getDefaultVoucherType.length > 0) {
        isMain = await WorkVoucherCompanyIgnoreUpdates.query()
          .join(
            'work_types',
            'work_voucher_company_ignore_updates.work_type_id',
            'work_types.id'
          )
          .join(
            'work_voucher_types',
            'work_voucher_company_ignore_updates.work_voucher_type_id',
            'work_voucher_types.id'
          )
          .join('work_areas', 'work_types.id', 'work_areas.work_type_id')
          .where('work_areas.work_area_id', useWorkAreaId)
          .where(
            'work_voucher_types.id',
            getDefaultVoucherType[0].default_work_voucher_type_id
          )
          .select('work_voucher_types.name')
          .castTo<{ name: string }[]>();

        const getNameVoucherType = await WorkVoucherTypes.query()
          .where(
            'work_voucher_types.id',
            getDefaultVoucherType[0].default_work_voucher_type_id
          )
          .select('work_voucher_types.name')
          .castTo<{ name: string }[]>();

        for (let i = 0; i < mos.length; i++) {
          if (!mos[i]) {
            continue;
          }
          const moValue = mos[i];
          if (typeof moValue !== 'string') {
            return res.status(400).json({
              ok: false,
              message: 'El mo debe ser un array de strings',
            });
          }

          if (moValue.includes('-') || moValue.includes('/')) {
            barcode = moValue.replace('-', '/');
          } else {
            barcode = moValue;
          }

          if (!useCompanyCode) {
            return res.status(400).json({
              ok: false,
              message: 'El client debe ser un numero',
            });
          }

          const getMoInfo = await MoNumber.query()
            .whereNotIn('mo_numbers.mo_status', [
              'Void',
              'Cancelled',
              'Materials',
              'Complete',
            ])
            .where('mo_numbers.num', barcode)
            .where('mo_numbers.company_code', useCompanyCode)
            .select(
              'mo_numbers.mo_id',
              'mo_numbers.po_number',
              'mo_numbers.order_type',
              'mo_numbers.mo_order',
              'mo_numbers.num',
              'mo_numbers.mo_status',
              'mo_numbers.style',
              'mo_numbers.quantity',
              'mo_numbers.style_category',
              'mo_numbers.ItemDescription8',
              'mo_numbers.customer',
              'mo_numbers.company_code'
            )
            .castTo<
              {
                mo_id: number;
                po_number: string;
                order_type: string;
                mo_order: string;
                num: string;
                mo_status: string;
                style: string;
                quantity: number;
                style_category: string;
                ItemDescription8: string;
                customer: string;
                company_code: number;
              }[]
            >();

          if (getMoInfo.length > 0) {
            const getVoucherInfo = await WorkVouchers.query()
              .join(
                'work_area_tickets',
                'work_vouchers.id',
                'work_area_tickets.work_voucher_id'
              )
              .join(
                'work_areas',
                'work_area_tickets.work_area_id',
                'work_areas.work_area_id'
              )
              .join(
                'work_area_ticket_statuses',
                'work_area_tickets.work_area_ticket_status_id',
                'work_area_ticket_statuses.id'
              )
              .join(
                'work_statuses',
                'work_area_ticket_statuses.work_status_id',
                'work_statuses.id'
              )
              .where(
                'work_vouchers.work_voucher_type_id',
                getDefaultVoucherType[0].default_work_voucher_type_id
              )
              .where('work_statuses.id', '<>', '110')
              .where('work_vouchers.mo_id', getMoInfo[0].mo_id)
              .where('work_area_tickets.work_area_id', useWorkAreaId)
              .select('work_area_tickets.id', 'work_area_ticket_statuses.name')
              .castTo<{ id: number; name: string }[]>();

            if (getVoucherInfo.length > 0) {
              data.push({
                exist: 1,
                isMain: isMain.length > 0 ? 'false' : 'true',
                infoMo: 'DUPLICADO',
                statusTicketDuplicate: `${getVoucherInfo[0].id} - ${getVoucherInfo[0].name}`,
                voucherType: getNameVoucherType[0].name,
                voucherTypeId:
                  getDefaultVoucherType[0].default_work_voucher_type_id,
                repo: 'NO',
                mo_id: getMoInfo[0].mo_id,
                po_number: getMoInfo[0].po_number || 'N/A',
                order_type: getMoInfo[0].order_type || 'N/A',
                mo_order: getMoInfo[0].mo_order,
                num: getMoInfo[0].num,
                mo_status: getMoInfo[0].mo_status,
                style: getMoInfo[0].style,
                quantity: getMoInfo[0].quantity,
                style_category: getMoInfo[0].style_category,
                ItemDescription8: getMoInfo[0].ItemDescription8,
                customer: getMoInfo[0].customer || 'N/A',
                company_code: getMoInfo[0].company_code,
                newGroup: getGroup?.length > 0 ? getGroup[0].newGroup : '',
                newGroupId: getGroup?.length > 0 ? getGroup[0].newGroupId : '',
              });
            } else {
              data.push({
                infoMo: 'NUEVO',
                isMain: isMain.length > 0 ? 'false' : 'true',
                voucherType: getNameVoucherType[0].name,
                statusTicketDuplicate: '',
                voucherTypeId:
                  getDefaultVoucherType[0].default_work_voucher_type_id,
                repo: 'NO',
                mo_id: getMoInfo[0].mo_id,
                po_number: getMoInfo[0].po_number || 'N/A',
                order_type: getMoInfo[0].order_type || 'N/A',
                mo_order: getMoInfo[0].mo_order,
                num: getMoInfo[0].num,
                mo_status: getMoInfo[0].mo_status,
                style: getMoInfo[0].style,
                quantity: getMoInfo[0].quantity,
                style_category: getMoInfo[0].style_category,
                ItemDescription8: getMoInfo[0].ItemDescription8,
                customer: getMoInfo[0].customer || 'N/A',
                company_code: getMoInfo[0].company_code,
                newGroup: getGroup?.length > 0 ? getGroup[0].newGroup : '',
                newGroupId: getGroup?.length > 0 ? getGroup[0].newGroupId : '',
              });
            }
          } else {
            dataError.push(mos[i]);
          }
        }

        const dataResponse = [];

        for (let i = 0; i < data.length; i++) {
          dataResponse.push({
            ...data[i],
            uuid: uuidv4(),
          });
        }

        return res.status(200).json({
          ok: true,
          data: dataResponse,
          dataError,
        });
      } else {
        return res.status(500).json({
          ok: false,
          data: 'No existe un Voucher Type por defecto',
        });
      }
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getInfoOfMo(req: Request, res: Response) {
  try {
    const { mo } = req.body;
    if (!mo || isNaN(Number(mo))) {
      return res.status(400).json({
        ok: false,
        message: 'El mo debe ser un numero',
      });
    }

    const getInfo = await MoNumber.query()
      .select(
        { mo: 'mo_numbers.num' },
        { moExport: 'mo_numbers.sched_start' },
        { quantity: 'mo_numbers.quantity' },
        { order: 'mo_numbers.mo_order' },
        { style: 'mo_numbers.style' }
      )
      .where('mo_numbers.mo_id', Number(mo))
      .castTo<
        {
          mo: string;
          moExport: string;
          quantity: number;
          order: string;
          style: string;
        }[]
      >();

    return res.status(200).json({
      ok: true,
      data: getInfo,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getCommentsOfMo(req: Request, res: Response) {
  try {
    const { mo } = req.body;
    if (!mo || isNaN(Number(mo))) {
      return res.status(400).json({
        ok: false,
        message: 'El mo debe ser un numero',
      });
    }

    const getCommets = await MoNumber.query()
      .join('work_notes', 'mo_numbers.mo_id', 'work_notes.mo_id')
      .join('employees', 'work_notes.employee_id ', 'employees.employee_id')
      .select(
        { id: 'work_notes.id' },
        { user: 'employees.first_name' },
        { note: 'work_notes.note' },
        { created_at: 'work_notes.created_at' }
      )
      .where('mo_numbers.mo_id', Number(mo))
      .orderBy('work_notes.created_at', 'desc')
      .castTo<
        {
          id: number;
          user: string;
          note: string;
          created_at: string;
        }[]
      >();

    return res.status(200).json({
      ok: true,
      data: getCommets,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getAllTheVouchersFromAListOfMos(
  req: Request,
  res: Response
) {
  try {
    const { mos } = req.body;
    const error = [];
    const data = [];

    if (!Array.isArray(mos)) {
      return res.status(400).json({
        ok: false,
        message: 'El mo debe ser un array de strings',
      });
    }

    for (let i = 0; i < mos.length; i++) {
      const moValue = mos[i];
      if (typeof moValue !== 'string') {
        return res.status(400).json({
          ok: false,
          message: 'El mo debe ser un array de strings',
        });
      }
      const getMoInfo = await WorkAreaTickets.query()
        .select([
          // TODO: sub queries here are probably slow
          MoScans.query()
            .select('mo_scans.sew')
            .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
            .whereNull('mo_scans.removed_at')
            .limit(1)
            .as('mo_scan'),
          // TODO: sub queries here are probably slow
          MoScans.query()
            .select('work_area_groups.name')

            .as('group_scan')
            .leftJoin(
              'work_area_groups',
              'mo_scans.work_area_group_id',
              'work_area_groups.id'
            )
            .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
            .whereNull('mo_scans.removed_at')
            .limit(1),
          // TODO: sub queries here are probably slow
          MoScans.query()
            .select('work_area_lines.name')
            .as('line_scan')
            .leftJoin(
              'work_area_lines',
              'mo_scans.work_area_line_id',
              'work_area_lines.id'
            )
            .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
            .whereNull('mo_scans.removed_at')
            .limit(1),
          { id: 'work_area_tickets.id' },
          { voucher_id: 'work_vouchers.id' },
          {
            voucher_type: 'work_voucher_types.name',
          },
          'work_area_tickets.created_at',
          { group: 'work_area_groups.name' },
          { line: 'work_area_lines.name' },
          {
            last_activity_status_employee: 'last_activity.first_name',
          },
          {
            last_activity_status_date: 'last_activity.updated_at',
          },
          'mo_numbers.mo_id',
          'mo_numbers.mo_status',
          'mo_numbers.material_date',
          'mo_numbers.mo_order',
          'mo_numbers.required_date',
          'mo_numbers.ItemDescription8',
          'mo_numbers.num',
          'mo_numbers.po_numbers',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.customer',
          {
            voucher_group: 'work_voucher_groups.name',
          },
          raw(
            "CASE WHEN work_vouchers.is_repo = 1 THEN 'ES REPOSICION' ELSE 'NO ES REPOSICION' END"
          ).as('is_repo'),
          raw(
            "CASE WHEN work_vouchers.is_primary = 1 THEN 'PRINCIPAL' ELSE 'NO PRINCIPAL' END"
          ).as('is_primary'),
          'work_area_tickets.work_inventory_location_id',
          'work_area_tickets.work_voucher_id',
          // TODO: subquery here is probably slow
          fn
            .coalesce(
              WorkNotes.query()
                .select('work_notes.note')
                .orderBy('work_notes.id', 'desc')
                .where(
                  'work_notes.work_area_ticket_id',
                  ref('work_area_tickets.id')
                )
                .limit(1),
              'Sin comentario'
            )
            .as('last_note'),
          'last_packet.file_uuid',
          'voucher_ticket_counts.ticket_count',
          'work_area_tickets.exp_finish_date',
          'work_area_tickets.prev_work_area_id',
          'work_area_tickets.next_work_area_id',
          { ticket_area: 'work_areas.area_name' },
          {
            voucher_plate_name: 'work_voucher_plates.name',
          },
          {
            ticket_status_name: 'work_area_ticket_statuses.name',
          },
          { status_global: 'work_statuses.name' },
          {
            bin_location_name: 'work_inventory_bins.name',
          },
          {
            next_area_name: 'next_work_area.area_name',
          },
          {
            prev_area_name: 'prev_work_area.area_name',
          },
        ])
        .leftJoin(
          'work_areas',
          'work_area_tickets.work_area_id',
          'work_areas.work_area_id'
        )
        .leftJoin(
          'work_vouchers',
          'work_area_tickets.work_voucher_id',
          'work_vouchers.id'
        )
        .leftJoin('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
        .leftJoin(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .leftJoin(
          'work_voucher_groups',
          'work_vouchers.work_voucher_group_id',
          'work_voucher_groups.id'
        )
        .leftJoin(
          'work_area_groups',
          'work_area_tickets.exp_work_area_group_id',
          'work_area_groups.id'
        )
        .leftJoin(
          'work_area_lines',
          'work_area_tickets.exp_work_area_line_id',
          'work_area_lines.id'
        )
        .leftJoin(
          'work_voucher_plates',
          'work_vouchers.work_voucher_plate_id',
          'work_voucher_plates.id'
        )
        .leftJoin(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          '=',
          'work_area_ticket_statuses.id'
        )
        .leftJoin(
          'work_statuses',
          'work_area_ticket_statuses.work_status_id',
          '=',
          'work_statuses.id'
        )
        .leftJoin(
          'work_inventory_bins',
          'work_area_tickets.work_inventory_location_id',
          'work_inventory_bins.id'
        )
        .leftJoin(
          raw(`work_areas next_work_area ON
          next_work_area.work_area_id =
          work_area_tickets.next_work_area_id`)
        )
        .leftJoin(
          raw(`work_areas prev_work_area ON
          prev_work_area.work_area_id =
          work_area_tickets.prev_work_area_id`)
        )
        .leftJoin(
          WorkActivityLog.query()
            .select('employees.first_name', 'work_activity_log.updated_at', {
              ticket_id: 'latest_work_activity.module_id',
            })
            .join(
              WorkActivityLog.query()
                .select('work_activity_log.module_id')
                .max({
                  // TODO: check on this
                  // @ts-ignore
                  max_activity_id: 'work_activity_log.id',
                })
                .where('work_activity_log.activity', 'TicketStatusChanged')
                .groupBy('work_activity_log.module_id')
                .as('latest_work_activity'),
              'latest_work_activity.max_activity_id',
              'work_activity_log.id'
            )
            .leftJoin(
              'employees',
              'employees.employee_id',
              'work_activity_log.employee_id'
            )
            .whereNotNull('latest_work_activity.module_id')
            .as('last_activity'),
          'last_activity.ticket_id',
          'work_area_tickets.id'
        )
        .leftJoin(
          MoVouchers.query()
            .join(
              MoVouchers.query()
                .select('mo_vouchers.mo_id')
                .max({
                  // TODO: check on this
                  // @ts-ignore
                  last_id: 'mo_vouchers.id',
                })
                .where('file_status', 'Active')
                .groupBy('mo_vouchers.mo_id')
                .as('latest_voucher'),
              'latest_voucher.last_id',
              'mo_vouchers.id'
            )
            .whereNotNull('latest_voucher.last_id')
            .as('last_packet'),
          'last_packet.mo_id',
          'work_vouchers.mo_id'
        )
        .leftJoin(
          WorkAreaTickets.query()
            .select('work_area_tickets.work_voucher_id')
            .count({
              ticket_count: 'work_area_tickets.id',
            })
            .groupBy('work_area_tickets.work_voucher_id')
            .as('voucher_ticket_counts'),
          'voucher_ticket_counts.work_voucher_id',
          'work_area_tickets.work_voucher_id'
        )
        .where('mo_numbers.num', moValue)
        .castTo<
          {
            mo_scan: string;
            group_scan: string;
            line_scan: string;
            id: number;
            voucher_id: number;
            voucher_type: string;
            created_at: string;
            group: string;
            line: string;
            last_activity_status_employee: string;
            last_activity_status_date: string;
            mo_id: number;
            mo_status: string;
            material_date: string;
            mo_order: string;
            required_date: string;
            ItemDescription8: string;
            num: string;
            po_numbers: string;
            style: string;
            quantity: number;
            customer: string;
            voucher_group: string;
            is_repo: string;
            is_primary: string;
            work_inventory_location_id: number;
            work_voucher_id: number;
            last_note: string;
            file_uuid: string | null | undefined;
            ticket_count?: number;
            exp_finish_date?: Date;
            prev_work_area_id?: number;
            next_work_area_id?: number;
            ticket_area?: string;
            voucher_plate_name?: string;
            ticket_status_name?: string;
            status_global?: string;
            bin_location_name?: string;
            next_area_name?: string;
            prev_area_name?: string;
          }[]
        >();

      if (getMoInfo.length > 0) {
        data.push(...getMoInfo);
      } else {
        error.push(mos[i]);
      }
    }

    return res.status(200).json({
      ok: true,
      data,
      error,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getMo(req: Request, res: Response) {
  try {
    const { mo } = req.query;

    let barcode: string;
    let companyCode: number;

    if (typeof mo !== 'string') {
      return res.status(400).json({
        ok: false,
        message: 'El mo debe ser un string',
      });
    }

    if (mo.includes('/') || mo.includes('PP')) {
      if (mo.startsWith('P')) {
        companyCode = 1;
        barcode = mo;
      } else if (mo.startsWith('A')) {
        companyCode = 2;
        barcode = mo.slice(1);
      } else {
        companyCode = 3;
        barcode = mo.replace('-', '/');
      }
      const getMos = await MoNumber.query()
        .select([
          'mo_numbers.mo_id',
          'mo_numbers.company_code',
          'mo_numbers.mo_status',
          'mo_numbers.material_date',
          'mo_numbers.mo_order',
          'mo_numbers.required_date',
          'mo_numbers.ItemDescription8',
          'mo_numbers.num',
          'mo_numbers.po_numbers',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.customer',
          'last_packet.file_uuid',
        ])
        .leftJoin(
          MoVouchers.query()
            .join(
              MoVouchers.query()
                .select('mo_vouchers.mo_id')
                .max({
                  // @ts-ignore
                  last_id: 'mo_vouchers.id',
                })
                .where('file_status', 'Active')
                .groupBy('mo_vouchers.mo_id')
                .as('latest_voucher'),
              'latest_voucher.last_id',
              'mo_vouchers.id'
            )
            .whereNotNull('latest_voucher.last_id')
            .as('last_packet'),
          'last_packet.mo_id',
          'mo_numbers.mo_id'
        )
        .where('mo_numbers.mo_barcode', barcode)
        .where('mo_numbers.company_code', companyCode)
        .castTo<
          {
            mo_id: number;
            company_code: number;
            mo_status: string;
            material_date: string;
            mo_order: string;
            required_date: string;
            ItemDescription8: string;
            num: string;
            po_numbers: string;
            style: string;
            quantity: number;
            customer: string;
            file_uuid: string | null | undefined;
          }[]
        >();

      const mosInfo = getMos.map((mo: { mo_status: string }) => {
        if (
          ['Void', 'Cancelled', 'Materials', 'Complete'].includes(mo.mo_status)
        ) {
          return { ...mo, active: false };
        }

        return { ...mo, active: true };
      });

      if (getMos.length === 0) {
        return res.status(500).json({
          ok: false,
          message: 'No se encontro la MO',
        });
      }

      return res.status(200).json({
        ok: true,
        data: mosInfo,
      });
    } else if (mo.includes('MEVB') || mo.includes('MEPB')) {
      const voucher_id = mo.substr(4);

      const getMos = await WorkVouchers.query()
        .select([
          'mo_numbers.mo_id',
          'mo_numbers.company_code',
          'mo_numbers.mo_status',
          'mo_numbers.material_date',
          'mo_numbers.mo_order',
          'mo_numbers.required_date',
          'mo_numbers.ItemDescription8',
          'mo_numbers.num',
          'mo_numbers.po_numbers',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.customer',
          'last_packet.file_uuid',
          'work_vouchers.id',
        ])
        .leftJoin('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
        .leftJoin(
          MoVouchers.query()
            .join(
              MoVouchers.query()
                .select('mo_vouchers.mo_id')
                .max({
                  // TODO: check on this
                  // @ts-ignore
                  last_id: 'mo_vouchers.id',
                })
                .where('file_status', 'Active')
                .groupBy('mo_vouchers.mo_id')
                .as('latest_voucher'),
              'latest_voucher.last_id',
              'mo_vouchers.id'
            )
            .whereNotNull('latest_voucher.last_id')
            .as('last_packet'),
          'last_packet.mo_id',
          'mo_numbers.mo_id'
        )
        .where('work_vouchers.id', voucher_id)
        .castTo<
          {
            mo_id: number;
            company_code: number;
            mo_status: string;
            material_date: string;
            mo_order: string;
            required_date: string;
            ItemDescription8: string;
            num: string;
            po_numbers: string;
            style: string;
            quantity: number;
            customer: string;
            file_uuid: string | null | undefined;
            // TODO: should be work_vouchers_id or not here
            id: number;
          }[]
        >();

      const mosInfo = getMos.map((mo: { mo_status: string }) => {
        if (
          ['Void', 'Cancelled', 'Materials', 'Complete'].includes(mo.mo_status)
        ) {
          return { ...mo, active: false };
        }

        return { ...mo, active: true };
      });

      if (getMos.length === 0) {
        return res.status(500).json({
          ok: false,
          message: 'No se encontro la MO',
        });
      }

      return res.status(200).json({
        ok: true,
        data: mosInfo,
      });
    } else {
      const getMos = await MoNumber.query()
        .select([
          'mo_numbers.mo_id',
          'mo_numbers.company_code',
          'mo_numbers.mo_status',
          'mo_numbers.material_date',
          'mo_numbers.mo_order',
          'mo_numbers.required_date',
          'mo_numbers.ItemDescription8',
          'mo_numbers.num',
          'mo_numbers.po_numbers',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.customer',
          'last_packet.file_uuid',
        ])
        .leftJoin(
          MoVouchers.query()
            .join(
              MoVouchers.query()
                .select('mo_vouchers.mo_id')
                .max({
                  // TODO: check on this
                  // @ts-ignore
                  last_id: 'mo_vouchers.id',
                })
                .where('file_status', 'Active')
                .groupBy('mo_vouchers.mo_id')
                .as('latest_voucher'),
              'latest_voucher.last_id',
              'mo_vouchers.id'
            )
            .whereNotNull('latest_voucher.last_id')
            .as('last_packet'),
          'last_packet.mo_id',
          'mo_numbers.mo_id'
        )
        .where('mo_numbers.num', mo)
        .castTo<
          {
            mo_id: number;
            company_code: number;
            mo_status: string;
            material_date: string;
            mo_order: string;
            required_date: string;
            ItemDescription8: string;
            num: string;
            po_numbers: string;
            style: string;
            quantity: number;
            customer: string;
            file_uuid: string | null | undefined;
          }[]
        >();

      const mosInfo = getMos.map((mo) => {
        if (
          ['Void', 'Cancelled', 'Materials', 'Complete'].includes(mo.mo_status)
        ) {
          return { ...mo, active: false };
        }

        return { ...mo, active: true };
      });

      if (getMos.length === 0) {
        return res.status(500).json({
          ok: false,
          message: 'No se encontro la MO',
        });
      }

      return res.status(200).json({
        ok: true,
        data: mosInfo,
      });
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getTicketByMoToChangeStatus(req: Request, res: Response) {
  try {
    const { mo, area, status, updateCustomer, nextArea, employeeId, group } =
      req.body;

    let barcode: string;
    let companyCode: number;

    if (typeof mo !== 'string') {
      return res.status(400).json({
        ok: false,
        message: 'El mo debe ser un string',
      });
    }

    const useWorkAreaId =
      area === undefined ? undefined : area === null ? null : Number(area);
    if (useWorkAreaId && isNaN(useWorkAreaId)) {
      return res.status(400).json({
        ok: false,
        message: 'El area debe ser un numero',
      });
    }

    const useNextWorkAreaId =
      nextArea === undefined
        ? undefined
        : nextArea === null
        ? null
        : Number(nextArea);
    if (useNextWorkAreaId && isNaN(useNextWorkAreaId)) {
      return res.status(400).json({
        ok: false,
        message: 'El next area debe ser un numero',
      });
    }

    const useEmployeeId =
      employeeId === undefined
        ? undefined
        : employeeId === null
        ? null
        : Number(employeeId);
    if (useEmployeeId && isNaN(useEmployeeId)) {
      return res.status(400).json({
        ok: false,
        message: 'El employee debe ser un numero',
      });
    }

    const useStatusId =
      status === undefined
        ? undefined
        : status === null
        ? null
        : Number(status);
    if (useStatusId && isNaN(useStatusId)) {
      return res.status(400).json({
        ok: false,
        message: 'El status debe ser un numero',
      });
    }

    const useGroupId =
      group === undefined ? undefined : group === null ? null : Number(group);
    if (useGroupId && isNaN(useGroupId)) {
      return res.status(400).json({
        ok: false,
        message: 'El group debe ser un numero',
      });
    }

    if (mo.includes('/') || mo.includes('PP') || mo.includes('-')) {
      if (mo.startsWith('P')) {
        companyCode = 1;
        barcode = mo;
      } else if (mo.startsWith('A')) {
        companyCode = 2;
        barcode = mo.slice(1);
      } else {
        companyCode = 3;
        barcode = mo.replace('-', '/');
      }

      if (!useWorkAreaId) {
        return res.status(400).json({
          ok: false,
          message: 'El area debe ser un numero',
        });
      }

      const getMos = await WorkAreaTickets.query()
        .select([
          'mo_numbers.mo_status',
          'mo_numbers.company_code',
          'mo_numbers.mo_order',
          'mo_numbers.num',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.customer',
          'last_packet.file_uuid',
          { voucher_id: 'work_vouchers.id' },
          { ticket_id: 'work_area_tickets.id' },
          {
            voucher_type_name: 'work_voucher_types.name',
          },
          {
            status_name: 'work_area_ticket_statuses.name',
          },
          { area_name: 'work_areas.area_name' },
        ])
        .leftJoin(
          'work_areas',
          'work_area_tickets.work_area_id',
          'work_areas.work_area_id'
        )
        .leftJoin(
          'work_vouchers',
          'work_area_tickets.work_voucher_id',
          'work_vouchers.id'
        )
        .leftJoin('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
        .leftJoin(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .leftJoin(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .leftJoin(
          MoVouchers.query()
            .join(
              MoVouchers.query()
                .select('mo_vouchers.mo_id')
                .max({
                  // TODO: check on this
                  // @ts-ignore
                  last_id: 'mo_vouchers.id',
                })
                .where('file_status', 'Active')
                .groupBy('mo_vouchers.mo_id')
                .as('latest_voucher'),
              'latest_voucher.last_id',
              'mo_vouchers.id'
            )
            .whereNotNull('latest_voucher.last_id')
            .as('last_packet'),
          'last_packet.mo_id',
          'mo_numbers.mo_id'
        )
        .whereNotIn('work_area_ticket_statuses.work_status_id', [100, 105, 110])
        .where('mo_numbers.mo_barcode', barcode)
        .where('mo_numbers.company_code', companyCode)
        .where('work_area_tickets.work_area_id', useWorkAreaId)
        .castTo<
          {
            mo_status: string;
            company_code: number;
            mo_order: string;
            num: string;
            style: string;
            quantity: number;
            customer: string;
            file_uuid: string | null | undefined;
            voucher_id: number;
            ticket_id: number;
            voucher_type_name: string;
            status_name: string;
            area_name: string;
          }[]
        >();

      if (getMos.length === 0) {
        //create ticket with complete status and update poly
        const get_mo_info = await getMoInfoByBarcodeAndCompany(
          barcode,
          companyCode
        );
        const voucher_type_id = await WorkAreas.query()
          .where('work_area_id', useWorkAreaId)
          .select('default_work_voucher_type_id')
          .first()
          .castTo<{
            default_work_voucher_type_id: number;
          }>();
        const response = await createVoucherAndTicket({
          is_repo: false,
          get_mo_info: get_mo_info,
          work_area_id: useWorkAreaId,
          voucher_type_id: voucher_type_id.default_work_voucher_type_id,
          is_primary: false,
          work_area_group_id: null,
          next_work_area_id: useNextWorkAreaId,
          work_area_line_id: null,
          location_id: null,
          employee_id: useEmployeeId,
          ticket_status_id: useStatusId,
          comments: null,
        });
        if (response.ok) {
          if (updateCustomer) {
            //create scan
            const scan = await createScan({
              mo_id: null,
              mo_barcode: barcode,
              group_barcode: group,
              quantity_reported: null,
              employee_id: employeeId,
              work_area_line_id: null,
              type_action: 'FINISH',
              work_area_group_id: null,
              work_area_id: null,
              update_customer: null,
              partial_option: null,
              affected_units: null,
              work_voucher_id: response.voucher,
              work_ticket_id: response.ticket_id,
            });
            if (scan.ok) {
              return res.status(200).json({
                ok: true,
                ticket_created: true,
                data: 'Se creo ticket con el status seleccionado y se creo el scan',
              });
            } else {
              return res.status(500).json({
                ok: true,
                message: 'Se creo ticket pero no pudo crear el scan',
              });
            }
          }
          return res.status(200).json({
            ok: true,
            ticket_created: true,
            data: 'Se creo ticket con el status seleccionado',
          });
        }
        return res.status(500).json({
          ok: false,
          message: 'No se encontraron tickets para la MO',
        });
      }

      const mosInfo = getMos.map((mo: { mo_status: string }) => {
        if (
          ['Void', 'Cancelled', 'Materials', 'Complete'].includes(mo.mo_status)
        ) {
          return {
            ...mo,
            active: false,
            status,
          };
        }

        return { ...mo, active: true, status };
      });

      return res.status(200).json({
        ok: true,
        data: mosInfo,
      });
    } else {
      return res.status(400).json({
        ok: false,
        message: `No es MO Barcode ${mo}`,
      });
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getTicketByMoToChangeLocation(
  req: Request,
  res: Response
) {
  try {
    const { mo, area, location }: any = req.body;

    let barcode;
    let companyCode;

    console.log(req.body);

    if (mo.includes('/') || mo.includes('PP')) {
      if (mo.startsWith('P')) {
        companyCode = 1;
        barcode = mo;
      } else if (mo.startsWith('A')) {
        companyCode = 2;
        barcode = mo.slice(1);
      } else {
        companyCode = 3;
        barcode = mo.replace('-', '/');
      }
      const getMos: any = await WorkAreaTickets.query()
        .select([
          'mo_numbers.mo_status',
          'mo_numbers.company_code',
          'mo_numbers.mo_order',
          'mo_numbers.num',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.customer',
          'last_packet.file_uuid',
          { voucher_id: 'work_vouchers.id' },
          { ticket_id: 'work_area_tickets.id' },
          {
            voucher_type_name: 'work_voucher_types.name',
          },
          {
            location_name: 'work_inventory_bins.name',
          },
          { area_name: 'work_areas.area_name' },
        ])
        .leftJoin(
          'work_areas',
          'work_area_tickets.work_area_id',
          'work_areas.work_area_id'
        )
        .leftJoin(
          'work_vouchers',
          'work_area_tickets.work_voucher_id',
          'work_vouchers.id'
        )
        .leftJoin('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
        .leftJoin(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .leftJoin(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .leftJoin(
          'work_inventory_bins',
          'work_area_tickets.work_inventory_location_id',
          'work_inventory_bins.id'
        )
        .leftJoin(
          MoVouchers.query()
            .join(
              MoVouchers.query()
                .select('mo_vouchers.mo_id')
                .max({
                  // @ts-ignore
                  last_id: 'mo_vouchers.id',
                })
                .where('file_status', 'Active')
                .groupBy('mo_vouchers.mo_id')
                .as('latest_voucher'),
              'latest_voucher.last_id',
              'mo_vouchers.id'
            )
            .whereNotNull('latest_voucher.last_id')
            .as('last_packet'),
          'last_packet.mo_id',
          'mo_numbers.mo_id'
        )
        .whereNotIn('work_area_ticket_statuses.work_status_id', [100, 105, 110])
        .where('mo_numbers.mo_barcode', barcode)
        .where('mo_numbers.company_code', companyCode)
        .where('work_area_tickets.work_area_id', area);

      if (getMos.length === 0) {
        return res.status(500).json({
          ok: false,
          message: 'No se encontraron tickets para la MO',
        });
      }

      const mosInfo = getMos.map((mo: { mo_status: string }) => {
        if (
          ['Void', 'Cancelled', 'Materials', 'Complete'].includes(mo.mo_status)
        ) {
          return {
            ...mo,
            active: false,
            location,
          };
        }

        return {
          ...mo,
          active: true,
          location,
        };
      });

      return res.status(200).json({
        ok: true,
        data: mosInfo,
      });
    } else {
      return res.status(400).json({
        ok: false,
        message: `No es MO Barcode ${mo}`,
      });
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getMoStatus(req: Request, res: Response) {
  try {
    const mosStatus = await MoNumber.query()
      .distinct({ name: 'mo_status' })
      .whereNotIn('mo_status', ['Void', 'Cancelled', 'Hold', 'Complete']);

    return res.status(200).json({
      ok: true,
      data: mosStatus,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function searchMo(req: Request, res: Response) {
  const { mo } = req.query as { mo: string };

  const query = mo.split(' ');

  const num = query[0];
  const order = query[1];

  try {
    if (query.length > 1) {
      const mos = await MoNumber.query()
        .select([
          { id: 'mo_numbers.mo_id' },
          'mo_numbers.mo_status',
          'mo_numbers.style',
          'mo_numbers.quantity',
          'mo_numbers.customer',
          'mo_numbers.num',
          'mo_numbers.mo_order',
        ])
        .orWhere('mo_numbers.num', 'like', `${num}%`)
        .orWhere('mo_numbers.mo_order', 'like', `${order}%`)
        .limit(250);

      if (mos.length === 0) {
        return res.status(400).json({
          ok: false,
          message: 'No se encontraron MOs',
        });
      }

      return res.status(200).json({
        ok: true,
        data: mos,
        totalItems: mos.length,
      });
    }

    const mos = await MoNumber.query()
      .select([
        { id: 'mo_numbers.mo_id' },
        'mo_numbers.mo_status',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.customer',
        'mo_numbers.num',
        'mo_numbers.mo_order',
      ])
      .where('mo_numbers.num', 'like', `${num}%`)
      .orWhere('mo_numbers.mo_order', 'like', `${num}%`)
      .limit(250);

    if (mos.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'No se encontraron MOs',
      });
    }

    return res.status(200).json({
      ok: true,
      data: mos,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function searchMoByNum(req: Request, res: Response) {
  const { mo, client } = req.params;
  try {
    // obtenemos todas la mo con el numero de mo que se envia
    const mos = await MoNumber.query()
      .where('company_code', client)
      .whereNotIn('mo_status', ['Void', 'Cancelled', 'Hold', 'Complete'])
      .where('num', 'like', `${mo}%`)
      .orWhere('mo_barcode', 'like', `${mo}%`)
      .select([{ id: 'mo_id' }, 'mo_status', 'num', 'customer']);

    if (mos.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'No se encontraron MOs',
      });
    }

    return res.status(200).json({
      ok: true,
      data: mos,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: `Error, no se encontro la MO ${mo}`,
    });
  }
}

export async function GetOrdersActiveByMo(req: Request, res: Response) {
  const { mo } = req.query as { mo: string };

  try {
    const mos = await RhinestoneOrder.query()
      .innerJoin('mo_numbers', 'rhinestones_orders.mo_id', 'mo_numbers.mo_id')
      .where('mo_numbers.company_code', 3)
      .whereNotIn('mo_numbers.mo_status', [
        'Void',
        'Cancelled',
        'Hold',
        'Complete',
      ])
      .where('rhinestones_orders.is_active', 1)
      .where('mo_numbers.num', 'like', `${mo}%`)
      .whereNull('rhinestones_orders.machine_id')
      .select([
        { id: 'rhinestones_orders.id' },
        'mo_numbers.mo_status',
        'mo_numbers.num',
        'mo_numbers.quantity',
        'rhinestones_orders.machine_id',
      ])
      .castTo<
        {
          id: number;
          mo_status: string;
          num: string;
          quantity: number;
          machine_id: number | null;
        }[]
      >();

    console.log(mos);

    if (mos.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'No se encontraron MOs',
      });
    }

    return res.status(200).json({
      ok: true,
      data: mos,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: `Error, no se encontro la MO ${mo}`,
    });
  }
}

export async function searchMoByNumPlotterView(req: Request, res: Response) {
  const { mo, client } = req.body;

  let getPolyInformation: IFabricView[] = [];
  try {
    const mos = await MoNumber.query()
      .where('company_code', +client)
      .where('num', '=', `${mo}`)
      .select(
        'mo_id',
        'num',
        'mo_order',
        'mo_status',
        'style',
        'quantity',
        'customer',
        'required_date'
      )
      .first()
      .castTo<{
        mo_id: number;
        num: string;
        mo_order: string;
        mo_status: string;
        style: string;
        quanity: number;
        customer: string;
        required_date: string;
      }>();
    if (!mos) {
      return res.status(400).json({
        ok: false,
        message: 'No se encontraron MOs',
      });
    }
    //get fabric information from poly when client is 1
    if (+client === 1) {
      //consume poly
      getPolyInformation = await axios
        .post(RestfulApi, { mo })
        .then((res) => {
          return res.data;
        })
        .catch((err) => {
          console.log(err);
          return [];
        });
    }
    return res.status(200).json({
      ok: true,
      data: { mos, getPolyInformation },
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: `Error, no se encontro la MO ${mo}`,
    });
  }
}

// Obtenemos la ruta del voucher enviando el mo number y exportamos la función
export async function getVoucherMoByMoid(req: Request, res: Response) {
  try {
    // Consulta a la base de datos por medio del schema
    // Con req.params.postBarcode obtemos el mo_order que nos envian por parametro en la url
    const mo_number: IMoVoucher['mo_barcode'] = req.params.postMo;
    const voucher = await MoVouchers.query()
      .join('mo_numbers', 'mo_vouchers.mo_id', 'mo_numbers.mo_id')
      .where('mo_vouchers.mo_id', mo_number)
      .where('mo_vouchers.file_status', 'Active')
      .select(
        'mo_vouchers.file_uuid',
        'mo_vouchers.file_status',
        'mo_vouchers.mo_id'
      )
      .castTo<{
        file_uuid: string;
        file_status: string;
        mo_id: number;
      }>()
      .first();

    if (voucher) {
      return res.status(200).json({
        data: voucher,
        ok: true,
      });
    }
    return res.status(400).json({
      ok: false,
      error: 'No se encontro voucher para la MO',
    });
  } catch (error) {
    console.log(error);

    // Mensaje de error si la consulta falla
    return res.status(400).json({
      ok: false,
      message: 'Error, datos no validos',
      error,
    });
  }
}

export async function getInfoMoActive(req: Request, res: Response) {
  try {
    const { company, mo: lookingForMo } = req.body;

    if (!lookingForMo) throw new Error('La mo es obligatoria');

    if (company) {
      const mo = await MoNumber.query()
        .whereNotIn('mo_status', ['Void', 'Cancelled', 'Hold', 'Complete'])
        .whereIn('company_code', [2])
        .andWhere((builder) => {
          builder
            .where('num', 'like', `${lookingForMo}%`)
            .orWhere('mo_order', 'like', `${lookingForMo}%`)
            .orWhere('mo_barcode', 'like', `${lookingForMo}%`);
        })
        .select([
          { id: 'mo_id' },
          'mo_status',
          'style',
          'quantity',
          'customer',
          'num',
          'mo_order',
        ])
        .first();

      if (!mo) {
        return res.status(400).json({
          ok: false,
          message: 'No se encontraron MOs',
        });
      }

      return res.status(200).json({
        ok: true,
        data: mo,
      });
    }

    const mo = await MoNumber.query()
      .whereNotIn('mo_status', ['Void', 'Cancelled', 'Hold', 'Complete'])
      .whereIn('company_code', [1, 3])
      .andWhere((builder) => {
        builder
          .where('num', 'like', `${lookingForMo}%`)
          .orWhere('mo_barcode', 'like', `${lookingForMo}%`);
      })
      .select([
        { id: 'mo_id' },
        'mo_status',
        'style',
        'quantity',
        'customer',
        'num',
        'mo_order',
      ])
      .first();

    if (!mo) {
      return res.status(400).json({
        ok: false,
        message: 'No se encontraron MOs',
      });
    }

    return res.status(200).json({
      ok: true,
      data: mo,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: `Error, no se encontro la MO ${req.query.mo}`,
    });
  }
}

export async function getMosActive(req: Request, res: Response) {
  try {
    const { company, mo } = req.query as unknown as {
      company: number;
      mo: string;
    };

    if (!mo) throw new Error('La mo es obligatoria');

    const mos = await MoNumber.query()
      .whereNotIn('mo_status', ['Void', 'Cancelled', 'Hold', 'Complete'])
      .whereIn('company_code', [company])
      .andWhere((builder): void => {
        builder
          .where('num', 'like', `${mo}%`)
          .orWhere('mo_order', 'like', `${mo}%`)
          .orWhere(
            'mo_barcode',
            'like',
            `${company === 2 ? `A${mo}` : `${mo}`}%`
          );
      })
      .select([
        { id: 'mo_id' },
        'mo_status',
        'style',
        'quantity',
        'customer',
        'num',
        'mo_order',
        'parent_mo_id',
        'required_date',
      ]);

    if (!mo) {
      return res.status(400).json({
        ok: false,
        message: 'No se encontraron MOs',
      });
    }

    return res.status(200).json({
      ok: true,
      data: mos,
      total: mos.length,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: `Error, no se encontro la MO ${req.query.mo}`,
    });
  }
}

export async function getMainAndSecondaryVouchersFromMo(
  req: Request,
  res: Response
) {
  try {
    const { moID } = req.params as unknown as { moID: number };

    const mainVoucher = (await MoNumber.query()
      .where('mo_id', moID)
      .select([
        { id: 'mo_id' },
        'mo_status',
        'style',
        'quantity',
        'customer',
        'num',
        'mo_order',
        'company_code',
      ])
      .first()) as unknown as {
      id: number;
      mo_status: string;
      style: string;
      quantity: number;
      customer: string;
      num: string;
      mo_order: string;
      company_code: number;
    };

    if (mainVoucher.company_code === 3) {
      const secondaryVouchers = await MoNumber.query()
        .where('parent_mo_id', moID)
        .select([
          { id: 'mo_id' },
          'mo_status',
          'style',
          'quantity',
          'customer',
          'num',
          'mo_order',
        ]);

      if (secondaryVouchers.length === 0) {
        return res.status(400).json({
          ok: false,
          message: 'No se encontraron los vouchers secundarios',
        });
      }

      return res.status(200).json({
        ok: true,
        data: {
          mainVoucher,
          secondaryVouchers,
        },
        message: 'Vouchers encontrados',
      });
    }

    return res.status(200).json({
      ok: true,
      data: {
        mainVoucher,
        secondaryVouchers: [],
      },
      message: 'Voucher encontrado',
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function searchOrderByMoOrVoucher(
  req: Request<
    unknown,
    unknown,
    z.infer<typeof SearchOrderByMoOrVoucherSchema>
  >,
  res: Response
) {
  try {
    const { query, searchOption, customer, sessionID } = req.body;

    const session = await WarehousePullSessions.query()
      .where('id', sessionID)
      .whereNull('finished_at')
      .select(['id', 'customer'])
      .first()
      .castTo<{ id: number; customer: string }>();

    if (!session) {
      return res.status(400).json({
        ok: false,
        message: 'No se encontro la sesion',
      });
    }

    if (searchOption === 'mo') {
      const isAdidas = session.customer === 'Adidas';

      const order = await MoNumber.query()
        .whereNotIn('mo_status', ['Void', 'Cancelled', 'Hold', 'Complete'])
        .where('customer', customer)
        .where('customer', session.customer)
        .andWhere((builder) => {
          builder
            .where('num', 'like', `${query}%`)
            .orWhere('mo_order', 'like', `${query}%`)
            .orWhere(
              'mo_barcode',
              'like',
              `${isAdidas ? `A${query}` : `${query}`}%`
            );
        })
        .select([
          'mo_id',
          'mo_status',
          'style',
          'quantity',
          'customer',
          'num',
          'mo_order',
        ])
        .first();

      if (!order) {
        return res.status(400).json({
          ok: false,
          message: 'No se encontraron MOs',
        });
      }

      return res.status(200).json({
        ok: true,
        data: {
          voucher_id: null,
          ...order,
        },
        message: 'MO encontrada',
      });
    }

    const order = await WorkVouchers.query()
      .join('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
      .whereNotIn('mo_numbers.mo_status', [
        'Void',
        'Cancelled',
        'Hold',
        'Complete',
      ])
      .where('mo_numbers.customer', session.customer)
      .andWhere('work_vouchers.id', Number(query.replace(/[^0-9]/g, '')))
      .select([
        { voucher_id: 'work_vouchers.id' },
        'mo_numbers.mo_id',
        'mo_numbers.mo_status',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.customer',
        'mo_numbers.num',
        'mo_numbers.mo_order',
      ])
      .first();

    if (!order) {
      return res.status(400).json({
        ok: false,
        message: `No se encontro la orden del voucher ${query}`,
      });
    }

    return res.status(200).json({
      ok: true,
      data: order,
      message: 'Voucher encontrado',
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function getSizesByMo(req: Request, res: Response) {
  try {
    const { moID } = req.params as unknown as { moID: number };

    const sizes = await MoSize.query()
      .where('mo_id', moID)
      .select(['size_name', 'size_quantity'])
      .castTo<{ size_name: string; size_quantity: number }[]>();

    if (sizes.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'No se encontraron tallas',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Tallas encontradas',
      data: sizes,
    });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}
