import type Objection from 'objection';

import { Model } from '@app/db';

class <PERSON><PERSON>umber extends Model {
  static get tableName(): string {
    return 'mo_numbers';
  }

  static get relationMappings(): {
    uuid: {
      relation: Objection.RelationType;
      modelClass: typeof MoVoucher;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      uuid: {
        relation: Model.BelongsToOneRelation,
        modelClass: MoVoucher,
        join: {
          from: 'mo_numbers.mo_id',
          to: 'mo_vouchers.mo_id',
        },
      },
    };
  }
}

class MoVoucher extends Model {
  static get tableName(): string {
    return 'mo_vouchers';
  }

  static get relationMappings(): {
    voucher: {
      relation: Objection.RelationType;
      modelClass: typeof MoNumber;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      voucher: {
        relation: Model.HasManyRelation,
        modelClass: MoNumber,
        join: {
          from: 'mo_vouchers.mo_id',
          to: 'mo_numbers.mo_id',
        },
      },
    };
  }
}
