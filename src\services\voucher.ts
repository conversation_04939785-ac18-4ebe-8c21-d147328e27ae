import type { PartialModelGraph } from 'objection';
import type Objection from 'objection';

import type { WorkAreaTickets } from '@app/models/tickets.schema';
import {
  WorkActivityLog,
  WorkAreaTicketStatuses,
  WorkNotes,
  WorkVouchers,
} from '@app/models/tickets.schema';

export async function workVoucherTransactions(
  data: {
    get_mo_info: { mo_id: number; num: string };
    work_area_id: number;
    voucher_type_id: number;
    is_primary: boolean;
    work_area_group_id: number;
    next_work_area_id: number;
    work_area_line_id: number;
    location_id: number;
    employee_id: number;
    is_repo: boolean;
    ticket_status_id: number;
  },
  options?: {
    trx?: Objection.Transaction;
  }
) {
  const trx = options?.trx ?? (await WorkVouchers.startTransaction());
  try {
    // TODO: insert workVouchersWorkTickets work ticket seperately as a single insert
    const voucher = await WorkVouchers.query(trx)
      .insertGraph({
        mo_id: data.get_mo_info?.mo_id,
        work_voucher_type_id: data.voucher_type_id,
        is_primary: data.is_primary,
        is_repo: data.is_repo,
        workVouchersWorkTickets: [
          {
            work_area_id: data.work_area_id,
            made_by_mo_scan: 0,
            notify_company: 1,
            is_company_notified: 0,
            exp_work_area_group_id: data.work_area_group_id || null,
            next_work_area_id: data.next_work_area_id || null,
            exp_work_area_line_id: data.work_area_line_id || null,
            work_area_ticket_status_id: data.ticket_status_id,
            work_inventory_location_id: data.location_id
              ? data.location_id
              : null,
          },
        ],
        // Why this PartialModelGraph is needed I dont know but threw an error without it
      } as PartialModelGraph<WorkVouchers>)
      .castTo<WorkVouchers & { workVouchersWorkTickets: WorkAreaTickets[] }>();

    if (!voucher) {
      throw new Error('No voucher created');
    }
    if (voucher.workVouchersWorkTickets.length > 1) {
      throw new Error('More than one work area ticket created');
    }
    const useWorkAreaTicket = voucher.workVouchersWorkTickets[0];
    if (!useWorkAreaTicket) {
      throw new Error('No work area ticket created');
    }

    await WorkActivityLog.query(trx).insert({
      employee_id: data.employee_id,
      work_area_id: data.work_area_id,
      module_name: 'ticket',
      module_id: useWorkAreaTicket.id,
      activity: 'TicketCreated',
      data: JSON.stringify({}),
    });

    if (!options?.trx) {
      await trx.commit();
    }

    if (voucher) {
      return {
        ok: true,
        mo: data.get_mo_info.num,
        voucher: voucher.id,
        is_primary: voucher.is_primary,
        voucher_type_id: voucher.work_voucher_type_id,
        ticket_id: useWorkAreaTicket.id,
        message: 'Felicidades, se creo el voucher y el ticket en el area',
      };
    } else {
      return {
        ok: false,
        message: 'Error, No puede crear el voucher y el ticket en el area',
      };
    }
  } catch (error) {
    if (!options?.trx) {
      await trx.rollback();
    }
    return {
      ok: false,
      message: 'Error, No puede crear el voucher y el ticket en el area',
    };
  }
}

export async function createVoucherAndTicket(data: {
  is_repo: boolean;
  get_mo_info: { mo_id: number; num: string };
  work_area_id: number;
  voucher_type_id: number;
  is_primary: boolean;
  work_area_group_id: number;
  next_work_area_id: number;
  work_area_line_id: number;
  location_id: number;
  employee_id: number;
  ticket_status_id: number;
  comments: string | null;
}) {
  const get_status = await WorkAreaTicketStatuses.query()
    .where('work_area_id', data.work_area_id)
    .where('name', 'Nuevo')
    .select('id')
    .first()
    .castTo<{ id: number }>();

  if (
    data.ticket_status_id !== undefined &&
    data.ticket_status_id !== null &&
    data.ticket_status_id > 0
  ) {
    // do nothing
  } else if (get_status) {
    data.ticket_status_id = get_status.id;
  } else {
    data.ticket_status_id = undefined;
  }

  if (data.is_repo) {
    if (data.get_mo_info) {
      if (get_status) {
        const response = await workVoucherTransactions(data);
        if (data.comments && data.comments !== '') {
          await WorkNotes.query().insert({
            mo_id: data.get_mo_info.mo_id,
            work_area_ticket_id: response.ticket_id,
            note: data.comments,
            employee_id: data.employee_id,
          });
        }

        return response;
      } else {
        const create_status = await WorkAreaTicketStatuses.query().insert({
          work_area_id: data.work_area_id,
          name: 'Nuevo',
          work_status_id: 50,
          sequence: 1000,
        });

        if (create_status) {
          const response = await workVoucherTransactions(data);
          if (data.comments && data.comments !== '') {
            await WorkNotes.query().insert({
              mo_id: data.get_mo_info.mo_id,
              work_area_ticket_id: response.ticket_id,
              note: data.comments,
              employee_id: data.employee_id,
            });
          }
          return response;
        } else {
          return {
            ok: false,
            message: "Error, No puede crear el estado del ticket 'NUEVO'",
          };
        }
      }
    } else {
      return {
        ok: false,
        message:
          'Error en la MO, recuerda que es necesario que la MO este activa',
      };
    }
  } else {
    if (data.get_mo_info) {
      const getVoucherInfo = await WorkVouchers.query()
        .join(
          'work_area_tickets',
          'work_vouchers.id',
          'work_area_tickets.work_voucher_id'
        )
        .join(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .join(
          'work_statuses',
          'work_area_ticket_statuses.work_status_id',
          'work_statuses.id'
        )
        .where('work_statuses.id', '<>', '110')
        .where('work_vouchers.mo_id', data.get_mo_info.mo_id)
        .where('work_vouchers.work_voucher_type_id', data.voucher_type_id)
        .where('work_area_tickets.work_area_id', data.work_area_id)
        .select('work_vouchers.id')
        .castTo<{ id: number }[]>();

      if (getVoucherInfo.length > 0) {
        return {
          ok: false,
          message:
            'Error, ya existe un voucher y ticket con el mismo voucher type en el area',
        };
      } else {
        if (get_status) {
          const response = await workVoucherTransactions(data);
          if (data.comments && data.comments !== '') {
            await WorkNotes.query().insert({
              mo_id: data.get_mo_info.mo_id,
              work_area_ticket_id: response.ticket_id,
              note: data.comments,
              employee_id: data.employee_id,
            });
          }
          return response;
        } else {
          const create_status = await WorkAreaTicketStatuses.query().insert({
            work_area_id: data.work_area_id,
            name: 'Nuevo',
            work_status_id: 50,
            sequence: 1000,
          });

          if (create_status) {
            const response = await workVoucherTransactions(data);
            if (data.comments && data.comments !== '') {
              await WorkNotes.query().insert({
                mo_id: data.get_mo_info.mo_id,
                work_area_ticket_id: response.ticket_id,
                note: data.comments,
                employee_id: data.employee_id,
              });
            }
            return response;
          } else {
            return {
              ok: false,
              message: "Error, No puede crear el estado del ticket 'NUEVO'",
            };
          }
        }
      }
    } else {
      return {
        ok: false,
        message:
          'Error en la MO, recuerda que es necesario que la MO este activa',
      };
    }
  }
}
