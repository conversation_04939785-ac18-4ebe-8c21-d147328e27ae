import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'plotter_prints',
    (table: Knex.TableBuilder) => {
      table.integer('total_pieces').nullable();
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'plotter_prints',
    (table: Knex.TableBuilder) => {
      table.dropColumn('total_pieces');
    }
  );
}
