import axios from 'axios';

import { WorkAreaTickets } from '@app/models/tickets.schema';

interface MoMaterial {
  CategoryName: 'Supplies' | 'Trim' | 'Care Labels' | 'Thread' | 'Fabric';
  ComponentName: string; // 'FLECHAS PLASTICAS';
  CustomerNumber: string; // 'Freisein';
  DatabaseUnits: string; // 'each';
  NominalCost: number; // 0.0008;
  PONumber: string; // '';
  POScheduleDate: string; // '';
  PartNumber: string; // 'FLECHAS PLASTICAS';
  QuantityAdjust: number; // 0;
  QuantityAllocated: number; // 12150;
  QuantityOnHand: number; // 40000;
  QuantityOrdered: number; // 60000;
  QuantityRequired: number; // 75;
  QuantityWithdrawn: number; // 0;
  Shortage: number; // 0;
  StockOnHand: number; // 40000;
  StockWarehouse: string; // 'VarPro';
  SubcategoryName: string; // 'Manufactura Plastica';
}

interface MoMaterialInfo {
  mo_id: number;
  customer: string;
  num: string;
  style: string;
  quantity: number;
  mo_status: string;
  mo_order: string;
  required_date: string;
  materials: MoMaterial[];
}

export interface BatchTicketData extends MoMaterialInfo {
  work_area_ticket_id: number;
  voucher_id: number;
}

export interface BatchSuggestion {
  tickets: BatchTicketData[];
  totalQuantity: number;
  partNumbers: string[];
  sharedPartNumbers: string[];
}

export interface BatchSuggestionCondensed {
  tickets: Omit<BatchTicketData, 'materials'>[];
  total_quantity: number;
  part_numbers: {
    category_name: string;
    part_number: string;
    total_material_quantity: number;
    ticket_material: {
      ticket_id: number;
      quantity: number;
    }[];
  }[];
  shared_part_numbers: string[];
}

const getMoAndMaterials = async (
  mo_ids: number[]
): Promise<MoMaterialInfo[]> => {
  const url = 'http://restfulapi.varpro.org/PolyService.svc/mos/materials';
  const data = await axios.post(url, {
    mo_ids: mo_ids,
  });
  return data.data;
};

// find best match of 2 tickets based on shared materials
const findBestTicketMaterialsMatch = (
  tickets: BatchTicketData[],
  min_material_count: number,
  max_material_count: number,
  max_unit_quantity: number
): {
  bestMatch: BatchTicketData[];
  sharedPartNumbers: string[];
  allPartNumbers: string[];
} => {
  let bestMatch: BatchTicketData[] = [];
  let sharedPartNumbers: string[] = [];
  for (let i = 0; i < tickets.length; i++) {
    const ticketA = tickets[i];
    for (let j = i + 1; j < tickets.length; j++) {
      const ticketB = tickets[j];
      const foundSharedPartNumbers = ticketA.materials.filter((materialA) => {
        return ticketB.materials.some((materialB) => {
          return materialA.PartNumber === materialB.PartNumber;
        });
      });
      if (
        foundSharedPartNumbers.length >= min_material_count &&
        foundSharedPartNumbers.length > sharedPartNumbers.length &&
        ticketB.materials.length + ticketA.materials.length <=
          max_material_count &&
        ticketA.quantity + ticketB.quantity <= max_unit_quantity
      ) {
        bestMatch = [ticketA, ticketB];
        sharedPartNumbers = foundSharedPartNumbers.map(
          (material) => material.PartNumber
        );
      }
    }
  }

  if (bestMatch.length === 0 || sharedPartNumbers.length === 0) {
    return null;
  }

  const allPartNumbers: string[] = [
    ...new Set([
      ...bestMatch[0].materials.map((material) => material.PartNumber),
      ...bestMatch[1].materials.map((material) => material.PartNumber),
    ]),
  ];

  return { bestMatch, sharedPartNumbers, allPartNumbers };
};

const findBestTicketForBatch = (
  usingBatch: BatchSuggestion,
  tickets: BatchTicketData[],
  max_material_count: number,
  max_unit_quantity: number
): {
  bestMatch: BatchTicketData;
  sharedPartNumbers: string[];
  allPartNumbers: string[];
} => {
  const batchQuantity = usingBatch.tickets.reduce(
    (acc, curr) => acc + curr.quantity,
    0
  );
  let bestMatch: BatchTicketData = null;
  let sharedPartNumbers: string[] = [];
  for (let i = 0; i < tickets.length; i++) {
    const ticket = tickets[i];
    const foundSharedPartNumbers = ticket.materials.filter((material) => {
      return usingBatch.partNumbers.some((partNumber) => {
        return material.PartNumber === partNumber;
      });
    });

    if (
      foundSharedPartNumbers.length > sharedPartNumbers.length &&
      ticket.materials.length + usingBatch.partNumbers.length <=
        max_material_count &&
      ticket.quantity + batchQuantity <= max_unit_quantity
    ) {
      bestMatch = ticket;
      sharedPartNumbers = foundSharedPartNumbers.map(
        (material) => material.PartNumber
      );
    }
  }

  if (bestMatch === null || sharedPartNumbers.length === 0) {
    return null;
  }

  // get distinct part numbers from both tickets
  const allPartNumbers: string[] = [
    ...new Set([
      ...bestMatch.materials.map((material) => material.PartNumber),
      ...usingBatch.partNumbers,
    ]),
  ];

  const batchSharedPartNumbers = [
    ...new Set([...sharedPartNumbers, ...usingBatch.sharedPartNumbers]),
  ];

  return {
    bestMatch,
    sharedPartNumbers: batchSharedPartNumbers,
    allPartNumbers,
  };
};

const getNewBatchSuggestion = (
  batchTicketData: BatchTicketData[],
  options?: {
    max_ticket_count?: number;
    min_material_count?: number;
    max_material_count?: number;
    max_unit_quantity?: number;
  }
): BatchSuggestion => {
  const {
    max_ticket_count = 10,
    min_material_count = 3,
    max_material_count = 50,
    max_unit_quantity = 300,
  } = options || {};

  // find best match of 2 tickets based on shared materials
  console.log('finding best match');
  const findingBestMatch = findBestTicketMaterialsMatch(
    batchTicketData,
    min_material_count,
    max_material_count,
    max_unit_quantity
  );

  if (!findingBestMatch) {
    console.log('no best match found');
    return null;
  }

  const { bestMatch, sharedPartNumbers, allPartNumbers } = findingBestMatch;

  // if no match found, return null
  if (bestMatch.length === 0) {
    console.log('no match found');
    return null;
  }

  // create new batch suggestion

  const newBatch: BatchSuggestion = {
    tickets: bestMatch,
    totalQuantity: bestMatch.reduce((acc, curr) => acc + curr.quantity, 0),
    partNumbers: allPartNumbers,
    sharedPartNumbers: sharedPartNumbers,
  };
  console.log('creating new batch');

  // find more tickets that share materials with the best match
  let remainingTickets = batchTicketData;
  const continueLooking = true;
  while (continueLooking) {
    remainingTickets = remainingTickets.filter((ticket) => {
      return !newBatch.tickets.some((bestMatchTicket) => {
        return (
          bestMatchTicket.work_area_ticket_id === ticket.work_area_ticket_id
        );
      });
    });
    console.log('looking for more tickets', remainingTickets.length);

    if (newBatch.tickets.length >= max_ticket_count) {
      break;
    }

    if (remainingTickets.length === 0) {
      break;
    }

    const nextMatch = findBestTicketForBatch(
      newBatch,
      remainingTickets,
      max_material_count,
      max_unit_quantity
    );

    if (nextMatch === null) {
      break;
    }

    // if next match is found, add to batch suggestions
    newBatch.tickets.push(nextMatch.bestMatch);
    newBatch.totalQuantity += nextMatch.bestMatch.quantity;
    newBatch.partNumbers = nextMatch.allPartNumbers;
    newBatch.sharedPartNumbers = nextMatch.sharedPartNumbers;
  }

  return newBatch;
};

const getBatchSuggestions = async (
  batchTicketData: BatchTicketData[],
  options?: {
    max_ticket_count?: number;
    min_material_count?: number;
    max_material_count?: number;
    max_unit_quantity?: number;
  }
): Promise<BatchSuggestion[]> => {
  const batchSuggestions: BatchSuggestion[] = [];

  let remainingTickets = batchTicketData;

  let lookingForBatches = true;
  while (lookingForBatches) {
    remainingTickets = remainingTickets.filter((ticket) => {
      return !batchSuggestions.some((batch) => {
        return batch.tickets.some((batchTicket) => {
          return batchTicket.work_area_ticket_id === ticket.work_area_ticket_id;
        });
      });
    });
    console.log('remainingTickets', remainingTickets.length);

    const newBatch = getNewBatchSuggestion(remainingTickets, options);

    if (!newBatch) {
      lookingForBatches = false;
      break;
    }

    batchSuggestions.push(newBatch);
  }

  return batchSuggestions;
};

export const getBatchTicketData = async (
  warehouse_work_area_id: number,
  work_area_ticket_status_id: number[],
  work_voucher_type_id: number[],
  options?: {
    material_categories?: string[];
    ignore_zero_requested?: boolean;
  }
): Promise<BatchTicketData[]> => {
  const LIMIT_POLY_PULL = 200;
  const material_categories = options?.material_categories || null;
  const ignore_zero_requested = options?.ignore_zero_requested || false;

  console.log('getting tickets', material_categories, ignore_zero_requested);

  try {
    // get tickets
    const tickets = await WorkAreaTickets.query()
      .leftJoin(
        'work_vouchers',
        'work_vouchers.id',
        'work_area_tickets.work_voucher_id'
      )
      .leftJoin('mo_numbers', 'mo_numbers.mo_id', 'work_vouchers.mo_id')
      .where('work_area_ticket_status_id', 'IN', work_area_ticket_status_id)
      .where('work_voucher_type_id', 'IN', work_voucher_type_id)
      .where('work_area_id', warehouse_work_area_id)
      .where('mo_numbers.mo_status', '!=', 'Complete')
      .where('mo_numbers.mo_status', '!=', 'Cancelled')
      .where('mo_numbers.mo_status', '!=', 'Void')
      .where('company_code', '=', 1)
      .orderBy('work_area_tickets.created_at', 'desc')
      .select(
        'work_area_tickets.id',
        'work_vouchers.mo_id',
        'work_area_tickets.work_voucher_id',
        'quantity'
      )
      .castTo<
        {
          id: number;
          mo_id: number;
          voucher_id: number;
          quantity: number;
        }[]
      >();

    if (tickets.length === 0) {
      return [];
    }

    console.log('tickets found for batching', tickets.length);

    // for testing right now
    let useTickets = tickets;
    if (tickets.length > LIMIT_POLY_PULL) {
      useTickets = tickets.slice(0, LIMIT_POLY_PULL);
    }

    // get mo materials
    const mo_ids = useTickets.map((ticket) => ticket.mo_id);

    console.log('mo_ids', mo_ids);

    const mosAndMaterials = await getMoAndMaterials(mo_ids);

    // create batch tickets
    const batch_tickets: BatchTicketData[] = useTickets.reduce(
      (acc, ticket) => {
        const foundMo = mosAndMaterials.find(
          (moWithMaterials) => moWithMaterials.mo_id === ticket.mo_id
        );

        if (!foundMo) {
          return acc;
        }

        let materials = foundMo.materials;

        if (!materials || materials.length === 0) {
          return acc;
        }

        if (ignore_zero_requested) {
          materials = materials.filter((material) => {
            return material.QuantityRequired > 0;
          });
        }

        if (material_categories && material_categories.length > 0) {
          materials = materials.filter((material) => {
            return material_categories.includes(material.CategoryName);
          });
        }

        acc.push({
          ...foundMo,
          work_area_ticket_id: ticket.id,
          voucher_id: ticket.voucher_id,
          materials,
        });

        return acc;
      },
      [] as BatchTicketData[]
    );

    return batch_tickets;
  } catch (error) {
    console.log(error);
    return [];
  }
};

export const getWarehouseBatchSuggestions = async (
  work_area_id: number,
  work_area_ticket_status_id: number[],
  work_voucher_type_id: number[],
  options?: {
    material_categories?: string[];
    max_ticket_count?: number;
    min_ticket_count?: number;
    max_material_count?: number;
    max_unit_quantity?: number;
  }
) => {
  const materialCategories = options?.material_categories || null;

  // get batch tickets
  const batch_tickets = await getBatchTicketData(
    work_area_id,
    work_area_ticket_status_id,
    work_voucher_type_id,
    {
      material_categories: materialCategories,
      ignore_zero_requested: true,
    }
  );

  // create batch suggestions
  const batch_suggestions = await getBatchSuggestions(batch_tickets, options);

  // condense batch suggestions
  const condensedBatchSuggestions: BatchSuggestionCondensed[] = [];
  for (const batch of batch_suggestions) {
    const condensedTickets: BatchSuggestionCondensed['tickets'] = [];
    const ticketMaterialHash: {
      [partNumber: string]: {
        work_area_ticket_id: number;
        mo_id: number;
        voucher_id: number;
        material: MoMaterial;
      }[];
    } = {};
    let totalQuantity = 0;
    for (const ticket of batch.tickets) {
      totalQuantity += ticket.quantity;
      const { materials, ...baseTicket } = ticket;
      condensedTickets.push(baseTicket);

      for (const material of materials) {
        if (!ticketMaterialHash[material.PartNumber]) {
          ticketMaterialHash[material.PartNumber] = [];
        }
        ticketMaterialHash[material.PartNumber].push({
          work_area_ticket_id: ticket.work_area_ticket_id,
          mo_id: ticket.mo_id,
          voucher_id: ticket.voucher_id,
          material: material,
        });
      }
    }
    const returnedPartNumbers: BatchSuggestionCondensed['part_numbers'] = [];
    for (const partNumber of batch.partNumbers) {
      const materialTickets = ticketMaterialHash[partNumber];
      if (!materialTickets || materialTickets.length === 0) {
        throw new Error(
          `Could not find material tickets for part number ${partNumber}`
        );
      }

      const totalMaterialQuantity = materialTickets.reduce(
        (acc, condensedTicket) => {
          const foundMaterial = condensedTicket.material;

          acc += foundMaterial.QuantityRequired;

          return acc;
        },
        0
      );

      const baseMaterial = materialTickets[0].material;

      const partTicketMaterials: BatchSuggestionCondensed['part_numbers'][0] = {
        category_name: baseMaterial.CategoryName as string,
        part_number: baseMaterial.PartNumber,
        total_material_quantity: totalMaterialQuantity,
        ticket_material: materialTickets.map((materialTicket) => ({
          ticket_id: materialTicket.work_area_ticket_id,
          quantity: materialTicket.material.QuantityRequired,
        })),
      };

      returnedPartNumbers.push(partTicketMaterials);
    }

    condensedBatchSuggestions.push({
      total_quantity: totalQuantity,
      tickets: condensedTickets,
      part_numbers: returnedPartNumbers,
      shared_part_numbers: batch.sharedPartNumbers,
    });
  }
  return condensedBatchSuggestions;
};
