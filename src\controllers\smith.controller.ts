import type { Request, Response } from 'express';

import { buildLogger } from '@app/settings';

const logger = buildLogger('controller:smith');

interface EmployeePrint {
  id: number;
  name: string;
}

export const getEmployeeByArea = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    if (!id) {
      logger.error('id is required');
      res.status(400).json({ message: 'id is required' });
      return;
    }

    const employees: EmployeePrint[] = [
      { id: 10624, name: 'KARLA YESENIA AVALOS MOLINA' },
      { id: 10951, name: '<PERSON><PERSON><PERSON> ICELA FLORES ALVARADO' },
      { id: 10149, name: '<PERSON><PERSON><PERSON><PERSON><PERSON> DE JESUS ASCENCIO MARROQUIN' },
      { id: 9604, name: 'WALTER ERNESTO AVALOS LIZAMA' },
      { id: 9274, name: 'DAVID OSWALDO ZARCEÑO CHAMUL' },
      { id: 13335, name: 'JOSUE DAVID LOPEZ RIVERA' },
      { id: 13334, name: 'CHRISTIAN ALEXANDER VASQUEZ MORALES' },
      { id: 2079, name: 'ANA MARIA PEREZ JUAREZ' },
      { id: 6473, name: 'LAURA MARITZA MEJIA VASQUEZ' },
      { id: 4098, name: 'KEVIN ALEXANDER LUE ASCENCIO' },
    ];

    return res.status(200).json({
      ok: true,
      message: 'Employees by area',
      data: employees,
    });
  } catch (error) {
    logger.error(`Error: ${error}`);

    res.status(500).json({
      ok: false,
      message: 'Internal Server Error',
    });
  }
};
