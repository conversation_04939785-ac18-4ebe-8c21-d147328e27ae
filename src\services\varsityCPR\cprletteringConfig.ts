import type {
  ClassHeader,
  FilePart,
  LineItemInfo,
  MadeItem,
  ReportData,
} from './config';

const mainItemCheck = (parts: FilePart[], curItem: MadeItem) => {
  if (!curItem.vch) {
    throw new Error('vch is missing');
  }
  if (!curItem.order) {
    throw new Error('order is missing');
  }
  if (!curItem.style) {
    throw new Error('style is missing');
  }
  if (!curItem.qty) {
    throw new Error('qty is missing');
  }
};

const classHeaderCheck = (
  parts: FilePart[],
  curItem: Partial<MadeItem>,
  curHeader: Partial<ClassHeader>
) => {
  console.log('classHeaderCheck', curHeader);
  if (!curHeader.class_code) {
    throw new Error('class_code is missing');
  }
  if (!curHeader.class_name) {
    throw new Error('class_name is missing');
  }
};

const reportDatesCheck = (
  parts: FilePart[],
  curItem: Partial<MadeItem>,
  curHeader: Partial<ClassHeader>,
  curReport: ReportData
) => {
  // check if each value is a date and greater than 2000 and less than 2100
  if (!curReport.report_start) {
    // 2023/08/06
    throw new Error('report_start is missing');
  }
  if (!curReport.report_end) {
    // 2023/08/06
    throw new Error('report_end is missing');
  }
  if (!curReport.pay_date) {
    // 11/20/2023
    throw new Error('pay_date is missing');
  }
};

export const cprlettering: { [lineName: string]: LineItemInfo } = {
  topHeader: {
    type: 'header',
    reportType: 'cprlettering',
    reportFormat: 'normal',
    expected: [
      { key: 'run_date', x: 0.65 }, // "text":" 8/14/23",
      { key: null, x: 10.828 }, // "text":"CUT LETTER",
      { key: null, x: 14.128 }, // CONTRACTOR PAY REPORT
      { key: null, x: 20.862 }, // "text":"FOR COMPANY#:",
      { key: null, x: 25.303 }, // "text":"01",
      { key: null, x: 35.481 }, // "text":"PAGE",
      { key: 'page', x: 37.062 }, // "text":"  16",
    ],
    allowWithoutReportType: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 7 &&
        parts[0].x === 0.65 &&
        parts[1].text === 'CUT LETTER'
      ) {
        return true;
      }
    },
  },
  topHeaderBorder: {
    type: 'header',
    reportType: 'cprlettering',
    reportFormat: 'border',
    expected: [
      { key: 'run_date', x: 2 }, // '12/13/21',
      { key: null, x: 11.867 }, // 'CUT LETTER',
      { key: null, x: 15.052 }, // 'CONTRACTOR PAY REPORT',
      { key: null, x: 21.581 }, // 'FOR COMPANY#:',
      { key: null, x: 25.876 }, // '01',
      { key: null, x: 35.747 }, // 'PAGE',
      { key: 'page', x: 37.256 }, // '   1'
    ],
    allowWithoutReportType: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 7 &&
        parts[0].x === 2 &&
        parts[1].text === 'CUT LETTER'
      ) {
        return true;
      }
    },
  },
  topHeaderThin: {
    type: 'header',
    reportType: 'cprlettering',
    reportFormat: 'thin',
    expected: [
      { key: 'run_date', x: 0.65 }, // '11/20/23                           ',
      { key: null, x: 12.378 }, // 'CUT LETTER ',
      { key: null, x: 15.828 }, // 'CONTRACTOR PAY REPORT  ',
      { key: null, x: 23.416 }, // 'FOR COMPANY#:  ',
      { key: null, x: 28.246 }, // '01                                 ',
      { key: null, x: 39.974 }, // 'PAGE ',
      { key: 'page', x: 41.353 }, // '   1'
    ],
    allowWithoutReportType: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 7 &&
        parts[0].x === 0.65 &&
        parts[1].text === 'CUT LETTER '
      ) {
        return true;
      }
    },
  },
  topHeaderFat: {
    type: 'header',
    reportType: 'cprlettering',
    reportFormat: 'fat',
    expected: [
      { key: 'run_date', buffer: 0.5, combineTillNext: true, x: 1.008 }, // text: '9/'     },
      { key: 'run_date', buffer: 0.5, combineTillNext: true, x: 1.636 }, // text: '25/'     },
      { key: 'run_date', buffer: 0.5, combineTillNext: true, x: 2.523 }, // text: '23'     },
      { key: null, x: 12.856 }, // text: 'CUT '     },
      { key: null, x: 14.214 }, // text: 'LETTER'     },
      { key: null, x: 16.435 }, // text: 'CONTRACTOR '     },
      { key: null, x: 20.213 }, // text: 'PAY '     },
      { key: null, x: 21.539 }, // text: 'REPORT'     },
      { key: null, x: 24.339 }, // text: 'FOR '     },
      { key: null, x: 25.719 }, // text: 'COMPANY#:'     },
      { key: null, x: 29.515 }, // text: '01'     },
      { key: null, x: 42.002 }, // text: 'PAGE'     },
      { key: 'page', x: 44.415, buffer: 0.8 }, // text: '1'     }
    ],
    allowWithoutReportType: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 13 &&
        parts[0].x < 1.5 &&
        parts[3].text === 'CUT ' &&
        parts[4].text === 'LETTER' &&
        parts[5].text === 'CONTRACTOR '
      ) {
        return true;
      }
    },
  },
  topHeaderFatBorder: {
    type: 'header',
    reportType: 'cprlettering',
    reportFormat: 'fatborder',
    expected: [
      { key: 'run_date', x: 2.339, buffer: 0.5, combineTillNext: true }, // '3/',
      // { key: 'run_date', x: 2.94 }, // '09/',
      // { key: 'run_date', x: 3.8019999999999996 }, // '21',
      { key: null, x: 13.513 }, // 'CUT ',
      { key: null, x: 14.82 }, // 'LETTER',
      { key: null, x: 16.956 }, // 'CONTRACTOR ',
      { key: null, x: 20.614 }, // 'PAY ',
      { key: null, x: 21.889 }, // 'REPORT',
      { key: null, x: 24.574 }, // 'FOR ',
      { key: null, x: 25.902 }, // 'COMPANY#:',
      { key: null, x: 29.554 }, // '01',
      { key: null, x: 41.295 }, // 'PAGE',
      { key: 'page', x: 43.973, buffer: 1 }, // '1'
    ],
    allowWithoutReportType: true,
    rowTypeCondition: (parts) => {
      if (
        parts.length === 13 &&
        parts[0].x > 1.5 &&
        parts[3].text === 'CUT ' &&
        parts[4].text === 'LETTER' &&
        parts[5].text === 'CONTRACTOR '
      ) {
        return true;
      }
    },
  },
  topRunData: {
    type: 'header',
    reportFormat: 'normal',
    expected: [
      { key: 'run_time', x: 0.65 }, // "text":"13:42:05",
      { key: null, x: 17.705 }, // CUT HOUSE:
      { key: null, x: 21.292 }, // PC
      { key: 'invoice_number', x: 35.194 }, // "text":"ACR302PR",
    ],
    rowTypeCondition: (parts, lastRowType) => {
      if (
        lastRowType === 'topHeader' &&
        parts.length === 4 &&
        parts[1].text.trim() === 'CUT HOUSE:' &&
        parts[2].text.trim() === 'PC'
      ) {
        return true;
      }
    },
  },
  topRunDataBorder: {
    type: 'header',
    reportFormat: 'border',
    expected: [
      { key: 'run_time', x: 2 }, // '13:45:40',
      { key: null, x: 18.553 }, // 'CUT HOUSE:',
      { key: null, x: 22.013 }, // 'PC',
      { key: 'invoice_number', x: 35.506 }, // 'LTR910PR'
    ],
    rowTypeCondition: (parts, lastRowType) => {
      if (
        lastRowType === 'topHeaderBorder' &&
        parts.length === 4 &&
        parts[1].text.trim() === 'CUT HOUSE:' &&
        parts[2].text.trim() === 'PC'
      ) {
        return true;
      }
    },
  },
  topRunDataThin: {
    type: 'header',
    reportFormat: 'thin',
    expected: [
      { key: 'run_time', x: 0.65 }, // '10:19:09                                                   ',
      { key: null, x: 20.657 }, // 'CUT HOUSE:  ',
      { key: null, x: 24.451 }, // 'PC                                              ',
      { key: 'invoice_number', x: 40.664 }, // 'LTR910PR'
    ],
    rowTypeCondition: (parts, lastRowType) => {
      if (
        lastRowType === 'topHeaderThin' &&
        parts.length === 4 &&
        parts[1].text.trim() === 'CUT HOUSE:' &&
        parts[2].text.trim() === 'PC'
      ) {
        return true;
      }
    },
  },
  topRunDataFat: {
    type: 'header',
    reportFormat: 'fat',
    expected: [
      { key: 'run_time', buffer: 0.5, combineTillNext: true, x: 0.65 }, //  text: '10:',
      { key: 'run_time', merged: true, x: 1.547 }, //  text: '03:',
      { key: 'run_time', merged: true, x: 2.434 }, //  text: '46',
      { key: null, x: 21.391 }, //  text: 'CUT ',
      { key: null, x: 22.749 }, //  text: 'HOUSE:',
      { key: null, x: 25.55 }, //  text: 'PC',
      { key: 'invoice_number', x: 42.764 }, //  text: 'LTR910PR'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length === 7 &&
        parts[3].text === 'CUT ' &&
        parts[4].text === 'HOUSE:' &&
        parts[5].text === 'PC'
      ) {
        return true;
      }
      return false;
    },
  },
  topRunDataFatBorder: {
    type: 'header',
    reportFormat: 'fatborder',
    expected: [
      { key: 'run_time', x: 2, buffer: 0.5, combineTillNext: true }, // '12:',
      // { key: null, x: 2.863 }, // '00:',
      // { key: null, x: 3.725 }, // '16',
      { key: null, x: 21.534 }, // 'CUT ',
      { key: null, x: 22.84 }, // 'HOUSE:',
      { key: null, x: 25.526 }, // 'PC',
      { key: 'invoice_number', x: 41.701 }, // 'LTR910PR'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length === 7 &&
        parts[3].text === 'CUT ' &&
        parts[4].text === 'HOUSE:' &&
        parts[5].text === 'PC'
      ) {
        return true;
      }
      return false;
    },
  },
  topReportNameAccepted: {
    type: 'header',
    reportFormat: 'normal',
    pageType: 'accepted',
    expected: [
      { key: null, x: 16.198 }, // "text":"ACCEPTED PAY TRANSACTIONS",
    ],
    matchPartIndexToExpected: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length === 1 &&
        parts[0].text.trim() === 'ACCEPTED PAY TRANSACTIONS'
      ) {
        return true;
      }
    },
  },
  topReportNameAcceptedBorder: {
    type: 'header',
    reportFormat: 'border',
    pageType: 'accepted',
    expected: [
      { key: null, x: 17.103 }, // "text":"ACCEPTED PAY TRANSACTIONS",
    ],
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'border' &&
        parts.length === 1 &&
        parts[0].text.trim() === 'ACCEPTED PAY TRANSACTIONS'
      ) {
        return true;
      }
    },
  },
  topReportNameAcceptedThin: {
    type: 'header',
    reportFormat: 'thin',
    pageType: 'accepted',
    expected: [
      { key: null, x: 19.202 }, // "text":"ACCEPTED PAY TRANSACTIONS",
    ],
    matchPartIndexToExpected: true,
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 1 &&
        parts[0].text.trim() === 'ACCEPTED PAY TRANSACTIONS'
      ) {
        return true;
      }
    },
  },
  topReportNameAcceptedFat: {
    type: 'header',
    reportFormat: 'fat',
    pageType: 'accepted',
    expected: [
      { key: null, x: 20.052 }, // text: 'ACCEPTED '
      { key: null, x: 23.045 }, // text: 'PAY '
      { key: null, x: 24.37 }, // text: 'TRANSACTI'
      { key: null, x: 27.336 }, // text: 'ONS'
    ],
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length === 4 &&
        parts[0].text.trim() === 'ACCEPTED' &&
        parts[1].text.trim() === 'PAY' &&
        parts[2].text.trim() === 'TRANSACTI' &&
        parts[3].text.trim() === 'ONS'
      ) {
        return true;
      }
    },
  },
  topReportNameAcceptedFatBorder: {
    type: 'header',
    reportFormat: 'fatborder',
    pageType: 'accepted',
    expected: [
      { key: null, x: 20.224 }, // 'ACCEPTED ',
      { key: null, x: 23.12 }, // 'PAY ',
      { key: null, x: 24.399 }, // 'TRANSACTI',
      { key: null, x: 27.273 }, // 'ONS'
    ],
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length === 4 &&
        parts[0].text.trim() === 'ACCEPTED' &&
        parts[1].text.trim() === 'PAY' &&
        parts[2].text.trim() === 'TRANSACTI' &&
        parts[3].text.trim() === 'ONS'
      ) {
        return true;
      }
    },
  },
  topReportRange: {
    type: 'header',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 9.607 }, // "text":"CUT BETWEEN:",
      { key: 'report_start', x: 13.767 }, // "text":"2023/08/06",
      { key: null, x: 17.353 }, // "text":"TO",
      { key: 'report_end', x: 18.643 }, // "text":"2023/08/12",
      { key: null, x: 22.225 }, // "text":"TO BE PAID ON:",
      { key: 'pay_date', x: 26.958 }, // "text":" 8/14/2023",
    ],
    infoCheckFunc: reportDatesCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length === 6 &&
        parts[0].text === 'CUT BETWEEN:' &&
        parts[0].x === 9.607
      ) {
        return true;
      }
    },
  },
  topReportRangeBorder: {
    type: 'header',
    reportFormat: 'border',
    expected: [
      { key: null, x: 10.695 }, // 'CUT BETWEEN:',
      { key: 'report_start', x: 14.712 }, // '2021/12/05',
      { key: null, x: 18.176 }, // 'TO',
      { key: 'report_end', x: 19.406 }, // '2021/12/11',
      { key: null, x: 22.87 }, // 'TO BE PAID ON:',
      { key: 'pay_date', x: 27.448 }, // '12/13/2021'
    ],
    infoCheckFunc: reportDatesCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'border' &&
        parts.length === 6 &&
        parts[0].text === 'CUT BETWEEN:' &&
        parts[0].x === 10.695
      ) {
        return true;
      }
    },
  },
  topReportRangeThin: {
    type: 'header',
    reportFormat: 'thin',
    expected: [
      { key: null, x: 11.268 }, // 'CUT BETWEEN:  ',
      { key: 'report_start', x: 15.753 }, // '2023/11/12  ',
      { key: null, x: 19.547 }, // 'TO  ',
      { key: 'report_end', x: 20.582 }, // '2023/11/18  ',
      { key: null, x: 24.376 }, // 'TO BE PAID ON:  ',
      { key: 'pay_date', x: 29.55 }, // '11/20/2023'
    ],
    infoCheckFunc: reportDatesCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 6 &&
        parts[0].text === 'CUT BETWEEN:  ' &&
        parts[0].x === 11.268
      ) {
        return true;
      }
    },
  },
  topReportRangeFat: {
    type: 'header',
    reportFormat: 'fat',
    expected: [
      { key: null, x: 11.785 }, // text: 'CUT '
      { key: null, x: 13.138 }, // text: 'BETWEEN:'
      { key: 'report_start', combineTillNext: true, x: 16.61 }, // text: '2023/'
      { key: 'report_start', x: 18.045 }, // text: '09/'
      { key: 'report_start', x: 18.942 }, // text: '17'
      { key: null, x: 20.291 }, // text: 'TO'
      { key: 'report_end', combineTillNext: true, x: 21.667 }, // text: '2023/'
      { key: 'report_end', x: 23.097 }, // text: '09/'
      { key: 'report_end', x: 23.999 }, // text: '23'
      { key: null, x: 25.348 }, // text: 'TO '
      { key: null, x: 26.378 }, // text: 'BE '
      { key: null, x: 27.387 }, // text: 'PAI'
      { key: null, x: 28.395 }, // text: 'D '
      { key: null, x: 29.103 }, // text: 'ON:'
      {
        key: 'pay_date',
        combineTillNext: true,
        min: 30.8,
        max: 31.7,
        x: 31.262,
      }, // text: '9/'
      { key: 'pay_date', x: 31.89 }, // text: '25/'
      { key: 'pay_date', x: 32.787 }, // text: '2023'
    ],
    infoCheckFunc: reportDatesCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length === 17 &&
        parts[0].text === 'CUT ' &&
        parts[1].text === 'BETWEEN:'
      ) {
        return true;
      }
    },
  },
  topReportRangeFatBorder: {
    type: 'header',
    reportFormat: 'fatborder',
    expected: [
      { key: null, x: 12.46 }, // 'CUT ',
      { key: null, x: 13.766 }, // 'BETWEEN:',
      { key: 'report_start', x: 17.104, combineTillNext: true }, // '2021/',
      // { key: null, x: 18.485 }, // '02/',
      // { key: null, x: 19.343 }, // '28',
      { key: null, x: 20.617 }, // 'TO',
      { key: 'report_end', x: 21.917, combineTillNext: true }, // '2021/',
      // { key: null, x: 23.303 }, // '03/',
      // { key: null, x: 24.161 }, // '06',
      { key: null, x: 25.435 }, // 'TO ',
      { key: null, x: 26.427 }, // 'BE ',
      { key: null, x: 27.393 }, // 'PAI',
      { key: null, x: 28.359 }, // 'D ',
      { key: null, x: 29.033 }, // 'ON:',
      { key: 'pay_date', x: 31.085, combineTillNext: true, min: 29.5, max: 34 }, // '3/',
      // { key: null, x: 31.686 }, // '09/',
      // { key: null, x: 32.549 }, // '2021'
    ],
    infoCheckFunc: reportDatesCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length === 17 &&
        parts[0].text === 'CUT ' &&
        parts[1].text === 'BETWEEN:'
      ) {
        return true;
      }
    },
  },
  topColumnHeaders: {
    type: 'header',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 0.65 }, // "text":"VND",
      { key: null, x: 1.944 }, // "text":"CP/CLS",
      { key: null, x: 4.098 }, // "text":"CUST#",
      { key: null, x: 6.825 }, // "text":"STYLE",
      { key: null, x: 11.558 }, // "text":"ORDER#",
      { key: null, x: 14.276 }, // "text":"VCH#",
      { key: null, x: 15.857 }, // "text":"#LET",
      { key: null, x: 17.428 }, // "text":"D/C",
      { key: null, x: 18.717 }, // "text":"R/C",
      { key: null, x: 20.012 }, // "text":"SIZ",
      { key: null, x: 22.156 }, // "text":"QTY",
      { key: null, x: 26.029 }, // "text":"CUT $",
      { key: null, x: 29.903 }, // "text":"PRESEW $",
      { key: null, x: 34.636 }, // "text":"TOTAL COST",
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length === 14 &&
        parts[0].text === 'VND' &&
        parts[0].x === 0.65
      ) {
        return true;
      }
    },
  },
  topColumnHeadersBorder: {
    type: 'header',
    reportFormat: 'border',
    expected: [
      { key: null, x: 2 }, // 'VND',
      { key: null, x: 3.235 }, // 'CP/CLS',
      { key: null, x: 5.306 }, // 'CUST#',
      { key: null, x: 7.933999999999999 }, // 'STYLE',
      { key: null, x: 12.512 }, // 'ORDER#',
      { key: null, x: 15.136 }, // 'VCH#',
      { key: null, x: 16.645 }, // '#LET',
      { key: null, x: 18.159 }, // 'D/C',
      { key: null, x: 19.389 }, // 'R/C',
      { key: null, x: 20.62 }, // 'SIZ',
      { key: null, x: 22.686 }, // 'QTY',
      { key: null, x: 26.428 }, // 'CUT $',
      { key: null, x: 30.166 }, // 'PRESEW $',
      { key: null, x: 34.74 }, // 'TOTAL COST'
    ],
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'border' &&
        parts.length === 14 &&
        parts[0].text === 'VND' &&
        parts[0].x === 2
      ) {
        return true;
      }
    },
  },
  topColumnHeadersThin: {
    type: 'header',
    reportFormat: 'thin',
    expected: [
      { key: null, x: 0.65 }, // 'VND ',
      { key: null, x: 1.685 }, // 'CP/CLS ',
      { key: null, x: 3.755 }, // 'CUST#    ',
      { key: null, x: 6.514 }, // 'STYLE           ',
      { key: null, x: 11.688 }, // 'ORDER#   ',
      { key: null, x: 14.448 }, // 'VCH# ',
      { key: null, x: 15.828 }, // '#LET ',
      { key: null, x: 17.207 }, // 'D/C ',
      { key: null, x: 18.242 }, // 'R/C ',
      { key: null, x: 19.277 }, // 'SIZ    ',
      { key: null, x: 21.347 }, // 'QTY          ',
      { key: null, x: 25.486 }, // 'CUT $        ',
      { key: null, x: 29.625 }, // 'PRESEW $        ',
      { key: null, x: 34.8 }, // 'TOTAL COST'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 14 &&
        parts[0].text === 'VND ' &&
        parts[0].x === 0.65
      ) {
        return true;
      }
    },
  },
  topColumnHeadersFat: {
    type: 'header',
    reportFormat: 'fat',
    expected: [
      { key: null, x: 0.65 }, // text: 'VND',
      { key: null, x: 2.039 }, // text: 'CP/',
      { key: null, x: 3.074 }, // text: 'CLS',
      { key: null, x: 4.415 }, // text: 'CUST#',
      { key: null, x: 7.524 }, // text: 'STYLE',
      { key: null, x: 13.048 }, // text: 'ORDER#',
      { key: null, x: 16.23 }, // text: 'VCH#',
      { key: null, x: 17.973 }, // text: '#LET',
      { key: null, x: 19.546 }, // text: 'D/',
      { key: null, x: 20.259 }, // text: ' C ',
      { key: null, x: 20.976 }, // text: 'R/',
      { key: null, x: 21.684 }, // text: ' C ',
      { key: null, x: 22.405 }, // text: 'SI',
      { key: null, x: 23.086 }, // text: 'Z',
      { key: null, x: 24.878 }, // text: 'QTY',
      { key: null, x: 29.501 }, // text: 'CUT ',
      { key: null, x: 30.859 }, // text: '$',
      { key: null, x: 34.089 }, // text: 'PRESEW ',
      { key: null, x: 36.544 }, // text: '$',
      { key: null, x: 39.784 }, // text: 'TOTAL ',
      { key: null, x: 41.701 }, // text: 'COST'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (format === 'fat' && parts.length === 21 && parts[0].text === 'VND') {
        return true;
      }
    },
  },
  topColumnHeadersFatBorder: {
    type: 'header',
    reportFormat: 'fatborder',
    expected: [
      { key: null, x: 2 }, // 'VND',
      { key: null, x: 3.331 }, // 'CP/',
      { key: null, x: 4.324 }, // 'CLS',
      { key: null, x: 5.604 }, // 'CUST#',
      { key: null, x: 8.575 }, // 'STYLE',
      { key: null, x: 13.804 }, // 'ORDER#',
      { key: null, x: 16.855 }, // 'VCH#',
      { key: null, x: 18.526 }, // '#LET',
      { key: null, x: 20.037 }, // 'D/',
      { key: null, x: 20.716 }, // ' C ',
      { key: null, x: 21.389 }, // 'R/',
      { key: null, x: 22.063 }, // ' C ',
      { key: null, x: 22.741 }, // 'SI',
      { key: null, x: 23.394 }, // 'Z',
      { key: null, x: 25.079 }, // 'QTY',
      { key: null, x: 29.442 }, // 'CUT ',
      { key: null, x: 30.748 }, // '$',
      { key: null, x: 33.785 }, // 'PRESEW ',
      { key: null, x: 36.162 }, // '$',
      { key: null, x: 39.199 }, // 'TOTAL ',
      { key: null, x: 41.054 }, // 'COST'
    ],
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length === 21 &&
        parts[0].text === 'VND'
      ) {
        return true;
      }
    },
  },
  main: {
    type: 'item',
    reportFormat: 'normal',
    expected: [
      { key: 'factory', x: 0.65 },
      { key: 'class', x: 1.944 },
      { key: 'cust_num', x: 3.8120000000000003 },
      { key: 'style', x: 6.816 },
      { key: 'order', x: 11.544 },
      { key: 'vch', x: 14.271 },
      { key: 'let', x: 15.852 },
      { key: 'rc', x: 18.569 },
      { key: 'siz', x: 19.429 },
      { key: 'qty', x: 21 },
      { key: 'cut', x: 24.301 },
      { key: 'sew', x: 29.024 },
      { key: 'total', x: 34.039 },
    ],
    infoCheckFunc: mainItemCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length >= 11 &&
        parts.length <= 14 &&
        (parts[0].text === 'PX' || parts[0].text === 'NS') &&
        parts[0].x === 0.65
      ) {
        return true;
      }
    },
  },
  mainBorder: {
    type: 'item',
    reportFormat: 'border',
    expected: [
      { key: 'factory', x: 2 }, // 'PX',
      { key: 'class', x: 3.23 }, // '/JVT',
      { key: 'cust_num', x: 5.018 }, // '34967562',
      { key: 'style', x: 7.92 }, // 'JVTFFBL2',
      { key: 'order', x: 12.494 }, // '12891554',
      { key: 'vch', x: 15.122 }, // '002',
      { key: 'let', x: 16.636 }, // '   3',
      { key: 'rc', x: 19.148 }, // 'R',
      { key: 'siz', x: 20.099 }, // '0300',
      { key: 'qty', x: 21.609 }, // '      8',
      { key: 'cut', x: 24.789 }, // '        9.60',
      { key: 'sew', x: 29.363 }, // '         .00',
      { key: 'total', x: 34.215 }, // '         9.60'
    ],
    infoCheckFunc: mainItemCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'border' &&
        parts.length >= 11 &&
        parts.length <= 14 &&
        parts[0].text === 'PX' &&
        parts[0].x === 2
      ) {
        return true;
      }
    },
  },
  mainThin: {
    type: 'item',
    reportFormat: 'thin',
    expected: [
      { key: 'factory', x: 0.65 }, // 'PX  ',
      { key: 'class', x: 1.685 }, // '/JVT  ',
      { key: 'cust_num', x: 3.41 }, // '34969824  ',
      { key: 'style', x: 6.514 }, // 'JVTGT2          ',
      { key: 'order', x: 11.688 }, // '14314676 ',
      { key: 'vch', x: 14.448 }, // '003  ',
      { key: 'let', x: 15.828 }, // '   6        ',
      { key: 'rc', x: 18.932, min: 18, max: 19 }, // 'R  ', OPTIONAL x: 18.587,
      { key: 'siz', x: 19.622, min: 19.2, max: 20 }, // '0300 ', x: 19.967,  x: 19.277,
      { key: 'qty', x: 21.002, min: 20.5, max: 22 }, // '      1    ', x: 21.347, x: 20.657,
      { key: 'cut', x: 24.451, min: 24, max: 25 }, // '        2.40    ', x: 24.796, x: 24.106,
      { key: 'sew', x: 29.625, min: 29, max: 30 }, // '         .00     ', x: 29.97, x: 29.28,
      { key: 'total', x: 35.144, min: 34.5, max: 36 }, // '         2.40' x: 35.489, x: 34.8
    ],
    infoCheckFunc: mainItemCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length >= 11 &&
        parts.length <= 14 &&
        parts[0].text === 'PX  ' &&
        parts[0].x === 0.65
      ) {
        return true;
      }
    },
  },
  mainFat: {
    type: 'item',
    reportFormat: 'fat',
    expected: [
      { key: 'factory', x: 0.65 }, // text: 'PX' ,
      { key: null, x: 2.052 }, // text: '/' ,
      { key: 'class', x: 2.416, buffer: 0.5 }, // text: 'JVT' ,
      { key: 'cust_num', x: 4.06, buffer: 0.8 }, // text: '38884700' ,
      { key: 'style', x: 7.02, combineTillNext: true }, // text: 'JVTGT2' ,
      { key: 'order', x: 12.508, buffer: 1 }, // text: '12773701' ,
      { key: 'vch', x: 15.115, buffer: 0.5 }, // text: '004' ,
      { key: 'let', x: 17.613, buffer: 0.8 }, // text: '8' ,
      { key: 'rc', x: 19.963, buffer: 0.5 }, // text: 'C' ,
      { key: 'siz', x: 21.043, buffer: 0.5 }, // text: '0300' ,
      { key: 'qty', x: 24.564, buffer: 1 }, // text: '12' ,
      // { key: 'qty', x: 24.723 }, // text: '1' ,
      { key: 'cut', x: 29.0, buffer: 2, combineTillNext: true }, // text: '38.' ,
      // { key: 'cut', x: 29.396  }, // text: '2.' ,
      // { key: 'cut', x: 29.845 }, // text: '40' ,
      // { key: 'cut', x: 30.024 }, // text: '40' ,
      {
        key: 'sew',
        x: 35.142,
        buffer: 1,
        combineTillNext: true,
        ignoreRepeat: true,
      }, // text: '.' ,
      // { key: 'sew', x: 35.32 }, // text: '.' ,
      // { key: 'sew', x: 35.5 }, // text: '00' ,
      // { key: 'sew', x: 35.678 }, // text: '00' ,
      {
        key: 'total',
        x: 40.801,
        buffer: 2,
        combineTillNext: true,
        ignoreRepeat: true,
      }, // text: '38.' ,
      // { key: 'total', x: 41.337 }, // text: '38.' ,
      // { key: 'total', x: 41.702 }, // text: '40'
      // { key: 'total', x: 41.97 }, // text: '40'
    ],
    infoCheckFunc: mainItemCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts[0].x === 0.65 &&
        parts[0].text === 'PX' &&
        parts[1].text === '/' &&
        parts.length >= 15
      ) {
        return true;
      }
    },
  },
  mainFatBorder: {
    type: 'item',
    reportFormat: 'fatborder',
    expected: [
      { key: 'factory', x: 2 }, // 'PX',
      { key: null, x: 3.326 }, // '/',
      { key: 'class', x: 3.66 }, // 'L10',
      { key: 'cust_num', x: 5.196, buffer: 0.5 }, // '40130500',
      { key: 'style', x: 8.041, min: 7.5, max: 10, combineTillNext: true }, // 'HTC3308W',
      { key: 'order', x: 13.096, min: 13, max: 14 }, // '55900788', x: 13.356, x: 13.224,
      { key: 'vch', x: 15.607, buffer: 0.5 }, // '005',
      { key: 'let', x: 18.155, min: 17, max: 19 }, // '2', x: 17.868, x: 18.512,
      { key: 'rc', x: 20.102, buffer: 0.5 }, // text: 'C' ,
      { key: 'siz', x: 21.192, buffer: 0.5 }, // '0500',
      { key: 'qty', x: 24.679, buffer: 0.5 }, // '3',
      { key: 'cut', x: 29.068, buffer: 2, combineTillNext: true }, // '3.',
      // { key: 'cut', x: 29.668 }, // '21',
      {
        key: 'sew',
        x: 34.652,
        buffer: 1,
        combineTillNext: true,
        ignoreRepeat: true,
      }, // '.',
      // { key: null, x: 34.991 }, // '00',
      {
        key: 'total',
        x: 40.315,
        buffer: 2,
        combineTillNext: true,
        ignoreRepeat: true,
      }, // '3.',
      // { key: null, x: 40.911 }, // '21'
    ],
    infoCheckFunc: mainItemCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts[0].x === 2 &&
        parts[0].text === 'PX' &&
        parts[1].text === '/' &&
        parts.length >= 15
      ) {
        return true;
      }
    },
  },
  classHeader: {
    type: 'classHeader',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 1.01 },
      { key: null, x: 1.048 },
      { key: 'class_code', x: 4.348 },
      { key: null, x: 4.962 },
      { key: null, x: 5 },
      { key: 'class_name', x: 6.867 },
      { key: null, x: 7.115 },
      { key: null, x: 7.152 },
    ],
    infoCheckFunc: classHeaderCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length >= 8 &&
        parts.length <= 9 &&
        parts[0].text === 'ITEM CLASS' &&
        parts[0].x === 1.01
      ) {
        return true;
      }
    },
  },
  classHeaderBorder: {
    type: 'classHeader',
    reportFormat: 'border',
    specialPartProcess: (parts) => {
      const newParts = parts.filter((part) => !part.text.startsWith('__'));
      return [newParts, null, null];
    },
    expected: [
      { key: null, x: 2.338 }, // 'ITEM CLASS',
      { key: null, x: 2.375 }, // '__________',
      { key: null, x: 2.375 }, // 'ITEM CLASS',
      { key: null, x: 5.56 }, // 'JVT',
      { key: null, x: 6.05 }, // '___',
      { key: 'class_code', x: 6.087 }, // 'JVT',
      { key: null, x: 7.880000000000001 }, // 'JV LEAGUE TWILL',
      { key: null, x: 8.075 }, // '____________________',
      { key: 'class_name', x: 8.112 }, // 'JV LEAGUE TWILL     '
    ],
    infoCheckFunc: classHeaderCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'border' &&
        parts.length >= 8 &&
        parts.length <= 9 &&
        parts[0].text === 'ITEM CLASS' &&
        parts[0].x === 2.338
      ) {
        return true;
      }
    },
  },
  classHeaderThin: {
    type: 'classHeader',
    reportFormat: 'thin',
    specialPartProcess: (parts) => {
      const newParts = parts.filter((part) => !part.text.startsWith('__'));
      return [newParts, null, null];
    },
    expected: [
      { key: null, x: 0.9199999999999999 }, // 'ITEM CLASS',
      { key: null, x: 0.958 }, // '__________',
      { key: null, x: 0.958 }, // 'ITEM CLASS ',
      { key: null, x: 3.9130000000000003 }, // '___',
      { key: null, x: 3.95 }, // 'JVT   ',
      { key: 'class_code', x: 4.407 }, // 'JVT',
      { key: null, x: 5.54 }, // '____________________',
      { key: null, x: 5.577 }, // 'JV LEAGUE TWILL     ',
      { key: 'class_name', x: 5.675 }, // 'JV LEAGUE TWILL'
    ],
    infoCheckFunc: classHeaderCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 9 &&
        parts[0].text === 'ITEM CLASS' &&
        parts[0].x === 0.9199999999999999
      ) {
        return true;
      }
    },
  },
  classHeaderFat: {
    type: 'classHeader',
    reportFormat: 'fat',
    ignorePartFunc: (part) => {
      if (part.text.includes('___')) {
        return true;
      }
      return false;
    },
    expected: [
      { key: null, x: 1.01 }, // text: 'I' ,
      { key: null, x: 1.048 }, // text: '__________' ,
      { key: null, x: 1.048 }, // text: 'I' ,
      { key: null, x: 1.368 }, // text: 'TEM ' ,
      { key: null, x: 1.406 }, // text: 'TEM ' ,
      { key: null, x: 2.743 }, // text: 'CLASS' ,
      { key: null, x: 2.781 }, // text: 'CLASS' ,
      {
        key: 'class_code',
        x: 4.766,
        buffer: 0.6,
        combineTillNext: true,
        ignoreRepeat: true,
      }, // text: 'JVT' ,
      // { key: null, x: 4.962 }, // text: '___' ,
      // { key: null, x: 5 }, // text: 'JVT' ,
      { key: null, x: 6.993 }, // text: 'JV ' ,
      { key: null, x: 7.115 }, // text: '____________________' ,
      {
        key: 'class_name',
        x: 7.152,
        buffer: 1,
        combineTillNext: true,
        ignoreRepeat: true,
      }, // text: 'JV ' ,
      // { key: 'class_name', x: 7.916 }, // text: 'LEAGUE ' ,
      // { key: null, x: 8.076 }, // text: 'LEAGUE ' ,
      // { key: 'class_name', x: 10.237 }, // text: 'TWI' ,
      // { key: null, x: 10.397 }, // text: 'TWI' ,
      // { key: 'class_name', x: 11.348, combineEnd: true }, // text: 'LL' ,
      // { key: null, x: 11.507 }, // text: 'LL '
    ],
    infoCheckFunc: classHeaderCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length > 7 &&
        parts[0].text === 'I' &&
        parts[3].text === 'TEM ' &&
        parts[6].text === 'CLASS'
      ) {
        return true;
      }
    },
  },
  classHeaderFatBorder: {
    type: 'classHeader',
    reportFormat: 'fatborder',
    ignorePartFunc: (part) => {
      if (part.text.includes('___')) {
        return true;
      }
      return false;
    },
    expected: [
      { key: null, x: 2.338 }, // 'I',
      { key: null, x: 2.375 }, // '__________',
      { key: null, x: 2.375 }, // 'I',
      { key: null, x: 2.676 }, // 'TEM ',
      { key: null, x: 2.714 }, // 'TEM ',
      { key: null, x: 4.004 }, // 'CLASS',
      { key: null, x: 4.041 }, // 'CLASS',
      {
        key: 'class_code',
        x: 5.943,
        buffer: 0.6,
        combineTillNext: true,
        ignoreRepeat: true,
      }, // 'L10',
      // { key: null, x: 6.05 }, // '___',
      // { key: null, x: 6.087 }, // 'L10',
      {
        key: 'class_name',
        x: 7.962,
        buffer: 1,
        combineTillNext: true,
        ignoreRepeat: true,
      }, // 'CUSTOM ',
      // { key: null, x: 8.075 }, // '____________________',
      // { key: null, x: 8.112 }, // 'CUSTOM ',
      // { key: null, x: 10.34 }, // 'VI',
      // { key: null, x: 10.49 }, // 'VI',
      // { key: null, x: 10.993 }, // 'NYL ',
      // { key: null, x: 11.143 }, // 'NYL ',
      // { key: null, x: 12.247 }, // 'TRANSFE',
      // { key: null, x: 12.397 }, // 'TRANSFE'
    ],
    infoCheckFunc: classHeaderCheck,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length > 7 &&
        parts[0].text === 'I' &&
        parts[3].text === 'TEM ' &&
        parts[6].text === 'CLASS'
      ) {
        return true;
      }
    },
  },
  classTotal: {
    type: 'classTotal',
    reportFormat: 'normal',
    expected: [
      { key: null, x: 1.01 }, // "text":"ITEM CLASS TOTALS >>>>>>>>",
      { key: null, x: 1.048 }, // "text":"ITEM CLASS TOTALS >>>>>>>>",
      { key: 'sum_item_count', x: 9.219 }, // "text":"     4",
      { key: null, x: 11.075 }, // "text":"     4 ",
      { key: null, x: 14.662 }, // "text":"ACCEPTED:",
      { key: null, x: 15.425 }, // "text":"ACCEPTED:",
      { key: 'sum_unit_count', x: 21.877 }, // "text":"   318",
      { key: null, x: 23.292 }, // "text":"   318",
      { key: 'sum_cut', x: 27.448 }, // "text":"           631.38",
      { key: null, x: 28.363 }, // "text":"           631.38 "",
      { key: 'sum_sew', x: 33.095 }, // "text":"           .00",
      { key: null, x: 34.07 }, // "text":"          .00",
      { key: 'sum_total', x: 39.376 }, // "text":"       500.00",
      { key: null, x: 40.58 }, // "text":"    3,045.30 ",
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'normal' &&
        parts.length === 14 &&
        parts[0].x === 1.01 &&
        parts[0].text.includes('ITEM CLASS TOTALS')
      ) {
        return true;
      }
    },
  },
  classTotalBorder: {
    type: 'classTotal',
    reportFormat: 'border',
    expected: [
      { key: null, x: 2.338 }, // 'ITEM CLASS TOTALS>>>>>>>>>',
      { key: null, x: 2.375 }, // 'ITEM CLASS TOTALS>>>>>>>>>',
      { key: 'sum_item_count', x: 10.296 }, // '        2',
      { key: null, x: 11.787 }, // '        2 ',
      { key: null, x: 15.251 }, // 'ACCEPTED:',
      { key: null, x: 15.875 }, // 'ACCEPTED:',
      { key: 'sum_unit_count', x: 22.125 }, // '         13',
      { key: null, x: 23.263 }, // '         13 ',
      { key: 'sum_cut', x: 27.279 }, // '        25.60',
      { key: null, x: 28.025 }, // '        25.60 ',
      { key: 'sum_sew', x: 32.599 }, // '          .00',
      { key: null, x: 33.387 }, // '          .00 ',
      { key: 'sum_total', x: 38.518 }, // '        25.60',
      { key: null, x: 39.5 }, // '        25.60 '
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'border' &&
        parts.length === 14 &&
        parts[0].x === 2.338 &&
        parts[0].text.includes('ITEM CLASS TOTALS')
      ) {
        return true;
      }
    },
  },
  classTotalThin: {
    type: 'classTotal',
    reportFormat: 'thin',
    expected: [
      { key: null, x: 0.9199999999999999 }, // 'ITEM CLASS TOTALS>>>>>>>>>',
      { key: null, x: 0.958 }, // 'ITEM CLASS TOTALS>>>>>>>>>  ',
      { key: 'sum_item_count', x: 8.533 }, // '        5   ',
      { key: null, x: 10.271 }, // '        5',
      { key: null, x: 11.833 }, // 'ACCEPTED:             ',
      { key: null, x: 12.327 }, // 'ACCEPTED:',
      { key: 'sum_unit_count', x: 17.78 }, // '         13   ',
      { key: null, x: 19.076 }, // '         13',
      { key: null, x: 21.62 }, // '        22.00   ',
      { key: 'sum_cut', x: 22.264 }, // '        22.00',
      { key: null, x: 25.932 }, // '          .00     ',
      { key: 'sum_sew', x: 26.794 }, // '          .00',
      { key: 'sum_total', x: 30.868 }, // '        22.00 ',
      { key: null, x: 31.796999999999997 }, // '        22.00'
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'thin' &&
        parts.length === 14 &&
        parts[0].x === 0.9199999999999999 &&
        parts[0].text.includes('ITEM CLASS TOTALS')
      ) {
        return true;
      }
    },
  },
  classTotalFat: {
    type: 'classTotal',
    reportFormat: 'fat',
    expected: [
      { key: null, x: 1.01 }, // text: 'I',
      { key: null, x: 1.048 }, // text: 'I',
      { key: null, x: 1.368 }, // text: 'TEM ',
      { key: null, x: 1.406 }, // text: 'TEM ',
      { key: null, x: 2.743 }, // text: 'CLASS ',
      { key: null, x: 2.781 }, // text: 'CLASS ',
      { key: null, x: 4.688 }, // text: 'TOTALS>>>>>>>>>',
      { key: null, x: 4.726 }, // text: 'TOTALS>>>>>>>>>',
      {
        key: 'sum_item_count',
        x: 12.822,
        buffer: 1,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // text: '4',
      // { key: null, x: 13.947 }, // text: '4 ',
      { key: null, x: 15.301 }, // text: 'ACCEPTED:',
      { key: null, x: 15.425 }, // text: 'ACCEPTED:',
      {
        key: 'sum_unit_count',
        x: 26.326,
        buffer: 2,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // text: '29',
      // { key: null, x: 26.523 }, // text: '29 ',
      {
        key: 'sum_cut',
        x: 30.513,
        buffer: 2,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // text: '88.',
      {
        key: 'sum_sew',
        x: 37.143,
        buffer: 1,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // text: '.',
      {
        key: 'sum_total',
        x: 42.733,
        buffer: 2,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // text: '88.',
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts[0].text === 'I' &&
        parts[3].text === 'TEM ' &&
        parts[4].text === 'CLASS ' &&
        parts[6].text === 'TOTALS>>>>>>>>>'
      ) {
        return true;
      }
    },
  },
  classTotalFatBorder: {
    type: 'classTotal',
    reportFormat: 'fatborder',
    expected: [
      { key: null, x: 2.338 }, // 'I',
      { key: null, x: 2.375 }, // 'I',
      { key: null, x: 2.676 }, // 'TEM ',
      { key: null, x: 2.714 }, // 'TEM ',
      { key: null, x: 4.004 }, // 'CLASS ',
      { key: null, x: 4.041 }, // 'CLASS ',
      { key: null, x: 5.88 }, // 'TOTALS>>>>>>>>>',
      { key: null, x: 5.918 }, // 'TOTALS>>>>>>>>>',
      {
        key: 'sum_item_count',
        x: 13.658,
        buffer: 1,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '1',
      // { key: null, x: 14.485 }, // '1 ',
      { key: null, x: 15.759 }, // 'ACCEPTED:',
      { key: null, x: 15.875 }, // 'ACCEPTED:',
      {
        key: 'sum_unit_count',
        x: 26.539,
        buffer: 2,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '3',
      // { key: null, x: 26.638 }, // '3 ',
      {
        key: 'sum_cut',
        x: 30.949,
        buffer: 2,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '3.',
      // { key: null, x: 31.062 }, // '3.',
      // { key: null, x: 31.55 }, // '21',
      // { key: null, x: 31.663 }, // '21 ',
      {
        key: 'sum_sew',
        x: 36.574,
        buffer: 1,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '.',
      // { key: null, x: 36.763 }, // '.',
      // { key: null, x: 36.908 }, // '00',
      // { key: null, x: 37.102 }, // '00 ',
      {
        key: 'sum_total',
        x: 42.348,
        buffer: 2,
        combineTillNext: true,
        ignoreRepeat: true,
        trim: true,
      }, // '3.',
      // { key: null, x: 42.537 }, // '3.',
      // { key: null, x: 42.949 }, // '21',
      // { key: null, x: 43.138 }, // '21 '
    ],
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts[0].text === 'I' &&
        parts[3].text === 'TEM ' &&
        parts[4].text === 'CLASS ' &&
        parts[6].text === 'TOTALS>>>>>>>>>'
      ) {
        return true;
      }
    },
  },
  finalTotal: {
    type: 'finalTotal',
    reportFormat: 'normal',
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (format === 'normal' && parts[0].text.includes('FINAL TOTALS')) {
        return true;
      }
    },
    expected: [],
  },
  finalTotalBorder: {
    type: 'finalTotal',
    reportFormat: 'border',
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (format === 'border' && parts[0].text.includes('FINAL TOTALS')) {
        return true;
      }
    },
    expected: [],
  },
  finalTotalThin: {
    type: 'finalTotal',
    reportFormat: 'thin',
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (format === 'thin' && parts[0].text.includes('FINAL TOTALS')) {
        return true;
      }
    },
    expected: [],
  },
  finalTotalFat: {
    type: 'finalTotal',
    reportFormat: 'fat',
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fat' &&
        parts.length > 8 &&
        parts[0].text === 'FI' &&
        parts[2].text === 'NAL ' &&
        parts[4].text === 'TOTALS>>>>>>>>>'
      ) {
        return true;
      }
    },
    expected: [
      { key: null, x: 1.01 }, // text: 'FI',
      { key: null, x: 1.048 }, // text: 'FI',
      { key: null, x: 1.669 }, // text: 'NAL ',
      { key: null, x: 1.706 }, // text: 'NAL ',
      { key: null, x: 2.968 }, // text: 'TOTALS>>>>>>>>>',
      { key: null, x: 3.006 }, // text: 'TOTALS>>>>>>>>>',
      { key: null, x: 11.456 }, // text: '1,',
      { key: null, x: 12.084 }, // text: '388',
      { key: null, x: 12.508 }, // text: '1,',
      { key: null, x: 13.136 }, // text: '388 ',
      { key: null, x: 15.029 }, // text: 'ACCEPTED:',
      { key: null, x: 15.425 }, // text: 'ACCEPTED:',
      { key: null, x: 24.883 }, // text: '14,',
      { key: null, x: 25.084 }, // text: '14,',
      { key: null, x: 25.78 }, // text: '160',
      { key: null, x: 25.981 }, // text: '160 ',
      { key: null, x: 29.302 }, // text: '24,',
      { key: null, x: 29.796 }, // text: '24,',
      { key: null, x: 30.199 }, // text: '877.',
      { key: null, x: 30.698 }, // text: '877.',
      { key: null, x: 31.365 }, // text: '77',
      { key: null, x: 31.863999999999997 }, // text: '77 ',
      { key: null, x: 37.076 }, // text: '.',
      { key: null, x: 37.434 }, // text: '00',
      { key: null, x: 37.663 }, // text: '.',
      { key: null, x: 38.022 }, // text: '00 ',
      { key: null, x: 41.79 }, // text: '24,',
      { key: null, x: 42.013 }, // text: '24,',
      { key: null, x: 42.687 }, // text: '877.',
      { key: null, x: 42.915 }, // text: '877.',
      { key: null, x: 43.858 }, // text: '77',
      { key: null, x: 44.081 }, // text: '77 '
    ],
  },
  finalTotalFatBorder: {
    type: 'finalTotal',
    reportFormat: 'fatborder',
    ignoreRowData: true,
    rowTypeCondition: (parts, lastRowType, format) => {
      if (
        format === 'fatborder' &&
        parts.length > 8 &&
        parts[0].text === 'FI' &&
        parts[2].text === 'NAL ' &&
        parts[4].text === 'TOTALS>>>>>>>>>'
      ) {
        return true;
      }
    },
    expected: [
      { key: null, x: 2.338 }, // 'FI',
      { key: null, x: 2.375 }, // 'FI',
      { key: null, x: 2.959 }, // 'NAL ',
      { key: null, x: 2.996 }, // 'NAL ',
      { key: null, x: 4.213 }, // 'TOTALS>>>>>>>>>',
      { key: null, x: 4.251 }, // 'TOTALS>>>>>>>>>',
      { key: null, x: 12.998 }, // '236',
      { key: null, x: 13.812 }, // '236 ',
      { key: null, x: 15.61 }, // 'ACCEPTED:',
      { key: null, x: 15.875 }, // 'ACCEPTED:',
      { key: null, x: 25.861 }, // '998',
      { key: null, x: 25.96 }, // '998 ',
      { key: null, x: 29.443 }, // '1,',
      { key: null, x: 29.711 }, // '1,',
      { key: null, x: 30.04 }, // '569.',
      { key: null, x: 30.311 }, // '569.',
      { key: null, x: 31.164 }, // '46',
      { key: null, x: 31.436 }, // '46 ',
      { key: null, x: 36.347 }, // '.',
      { key: null, x: 36.682 }, // '00',
      { key: null, x: 36.763 }, // '.',
      { key: null, x: 37.102 }, // '00 ',
      { key: null, x: 41.002 }, // '1,',
      { key: null, x: 41.186 }, // '1,',
      { key: null, x: 41.602 }, // '569.',
      { key: null, x: 41.786 }, // '569.',
      { key: null, x: 42.727 }, // '46',
      { key: null, x: 42.911 }, // '46 '
    ],
  },
};
