import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('rhinestones_files_range', (table) => {
    table.integer('cute', 10).defaultTo(1).nullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('rhinestones_files_range', (table) => {
    table.boolean('cute').notNullable().alter();
  });
}
