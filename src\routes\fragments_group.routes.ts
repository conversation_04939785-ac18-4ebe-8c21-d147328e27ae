import { Router } from 'express';

import {
  AddFragmentsToGroup,
  AddGroupToRoll,
  CreateFragmentGroupCustomField,
  CreateFragmentGroupType,
  CreateGroup,
  CreateNewFragmentsGroup,
  CreateRoll,
  DeleteFragmentFromGroup,
  GetAllFragmentsGroups,
  GetAllFragmentsGroupsByMO,
  GetAllFragmentsInGroup,
  GetFragmentGroupTypes,
  GetFragmentGroups,
  GetFragmentsGroup,
  GetFragmentsGroupCustomFields,
  GetFragmentsGroupCustomFieldsByType,
  GetInfoOfFragmentsGroup,
  UpdateFragmentsGroup,
} from '@app/controllers/fragments_group.controller';

const fragmentGroupsRouter = Router();

fragmentGroupsRouter.route('/types').get(GetFragmentGroupTypes);
fragmentGroupsRouter.route('/types/create').post(CreateFragmentGroupType);
fragmentGroupsRouter
  .route('/custom-field/create')
  .post(CreateFragmentGroupCustomField);
fragmentGroupsRouter.route('/create').post(CreateNewFragmentsGroup);
fragmentGroupsRouter.route('/add-fragments').post(AddFragmentsToGroup);
fragmentGroupsRouter.route('/delete-fragments').post(DeleteFragmentFromGroup);
fragmentGroupsRouter.route('/create-group').post(CreateGroup);
fragmentGroupsRouter.route('/group/fragments').get(GetAllFragmentsInGroup);
fragmentGroupsRouter.route('/create-roll').post(CreateRoll);
fragmentGroupsRouter.route('/byId/:id').get(GetFragmentsGroup);
fragmentGroupsRouter.route('/:id').patch(UpdateFragmentsGroup);
fragmentGroupsRouter.route('/').get(GetFragmentGroups);
fragmentGroupsRouter.route('/add-group-to-roll').post(AddGroupToRoll);
fragmentGroupsRouter
  .route('/custom-fields/by-type/:id')
  .get(GetFragmentsGroupCustomFieldsByType);
fragmentGroupsRouter
  .route('/custom-fields/by-group/:id')
  .get(GetFragmentsGroupCustomFields);
fragmentGroupsRouter.route('/info/by-group/:id').get(GetInfoOfFragmentsGroup);
fragmentGroupsRouter.route('/all/byMO/:mo').get(GetAllFragmentsGroupsByMO);
fragmentGroupsRouter.route('/all').get(GetAllFragmentsGroups);

export { fragmentGroupsRouter };
