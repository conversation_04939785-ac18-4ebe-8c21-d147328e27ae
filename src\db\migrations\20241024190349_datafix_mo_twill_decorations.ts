import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.raw(`insert into mo_twill_laser_decorations (mo_id, child_mo_id, mo_twill_laser_job_type_id, comment, is_active)
select
	mo.parent_mo_id as mo_id,
	mo.mo_id as child_mo_id,
	1 as mo_twill_laser_job_type_id,
	mo.style as comment,
	1 as is_active
from (
select distinct(child_mo_id)
from mo_twill_laser_varsity_art_jobs
) as cdm
left join mo_numbers mo on mo.mo_id = cdm.child_mo_id
left join mo_twill_laser_decorations mtld on mtld.child_mo_id = mo.mo_id 
where mo.parent_mo_id is not null
and mtld.id is null`);
}

export async function down(): Promise<void> {
  return Promise.resolve();
}
