import type { Request, Response } from 'express';
import { raw, transaction } from 'objection';

import { PrintIds, PrintMos } from '@app/models/printer.schema';
import { MoNumber } from '@app/models/tickets.schema';
import { buildLogger } from '@app/settings';

interface PrintID {
  description: string;
  isRepo: boolean;
  machineID: number;
  employeeID: number;
  mos: number[];
  pieces: number;
}

const logger = buildLogger('printer.controller');

export async function createPrintId(req: Request, res: Response) {
  try {
    const { description, employeeID, isRepo, machineID, mos, pieces } =
      req.body as unknown as PrintID;

    if (mos.length === 0) {
      logger.error('No se puede crear un Print ID sin MOS');

      return res.status(400).json({
        ok: false,
        message: 'No se puede crear un Print ID sin MOS',
      });
    }

    if (!machineID) {
      logger.error('Faltan datos para crear el Print ID');

      return res.status(400).json({
        ok: false,
        message: 'Faltan datos para crear el Print ID',
      });
    }

    if (!employeeID) {
      logger.error('Faltan datos para crear el Print ID');

      return res.status(400).json({
        ok: false,
        message: 'Faltan datos para crear el Print ID',
      });
    }

    const printID = await transaction(
      PrintIds,
      PrintMos,
      MoNumber,
      async (PrintIds, PrintMos, MoNumbers) => {
        const printId: any = await PrintIds.query().insert({
          is_repo: isRepo ? 1 : 0,
          description,
          machine_id: machineID,
          pieces,
          employee_id: employeeID,
        });

        if (!printId) {
          throw new Error('No se pudo crear el Print ID');
        }

        const mosIDs: number[] = [];

        for (const mo of mos) {
          const moID: any = await MoNumbers.query().findOne({ mo_id: mo });

          if (!moID) {
            throw new Error(`No se encontró la MOID ${mo}`);
          }

          const moInPrintID: any = await PrintMos.query()
            .findOne({
              mo_id: moID.mo_id,
            })
            .where('is_active', 1);

          if (moInPrintID) {
            throw new Error(`La MOID ${mo} ya está en el Print ID`);
          }

          const printMo = await PrintMos.query().insert({
            print_id: printId.id,
            mo_id: moID.mo_id,
          });

          if (!printMo) {
            throw new Error('No se pudo agregar el MOS al Print ID');
          }

          mosIDs.push(+moID.mo_id);
        }

        return {
          printID: printId.id,
          printIDCode: `PRID-${printId.id}`,
          mos: mosIDs,
        };
      }
    );

    if (!printID.printID || printID.mos.length === 0) {
      logger.error('No se pudo crear el Print ID');

      return res.status(400).json({
        ok: false,
        message: 'No se pudo crear el Print ID',
      });
    }

    return res.status(201).json({
      ok: true,
      message: 'Print ID creado',
      data: printID,
    });
  } catch (error) {
    logger.error(`Error, ${error}`);

    return res.status(500).json({
      ok: false,
      message: `Error, ${error.message}`,
    });
  }
}

export async function addMosToPrintId(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const { mos } = req.body as unknown as { mos: number[] };

    if (mos.length === 0) {
      logger.error('No se puede agregar un Print ID sin MOS');

      return res.status(400).json({
        ok: false,
        message: 'No se puede agregar un Print ID sin MOS',
      });
    }

    const printId: any = await PrintIds.query().findById(id);

    if (!printId) {
      logger.error('No se encontró el Print ID');

      return res.status(400).json({
        ok: false,
        message: 'No se encontró el Print ID',
      });
    }

    const updatedPrintId = await transaction(
      PrintMos,
      MoNumber,
      async (PrintMos, MoNumbers) => {
        const mosIDs: number[] = [];

        for (const mo of mos) {
          const moID: any = await MoNumbers.query().findOne({ mo_id: mo });

          if (!moID) {
            throw new Error(`No se encontró la MOID ${mo}`);
          }

          const moInPrintID: any = await PrintMos.query()
            .findOne({
              mo_id: moID.mo_id,
            })
            .where('is_active', 1);

          if (moInPrintID) {
            throw new Error(`La MO ${mo} ya está en el Print ID`);
          }

          const printMo = await PrintMos.query().insert({
            print_id: +id,
            mo_id: moID.mo_id,
          });

          if (!printMo) {
            throw new Error('No se pudo agregar el MOS al Print ID');
          }

          mosIDs.push(+moID.mo_id);
        }

        return {
          printID: +id,
          printIDCode: `PRINT-${id}`,
          mos: mosIDs,
        };
      }
    );

    if (!updatedPrintId) {
      logger.error('No se pudo agregar el MOS al Print ID');

      return res.status(400).json({
        ok: false,
        message: 'No se pudo agregar el MOS al Print ID',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'MOS agregado al Print ID',
      data: updatedPrintId,
    });
  } catch (error) {
    logger.error(`Error, ${error}`);

    return res.status(500).json({
      ok: false,
      message: `Error, ${error.message}`,
    });
  }
}

export async function deleteMosFromPrintId(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const { mos } = req.body as unknown as { mos: number[] };

    if (mos.length === 0) {
      logger.error('No se puede eliminar una MO del Print ID sin MOS');

      return res.status(400).json({
        ok: false,
        message: 'No se puede eliminar una MO del Print ID sin MOS',
      });
    }

    const printId: any = await PrintIds.query().findById(id);

    if (!printId) {
      logger.error('No se encontró el Print ID');

      return res.status(400).json({
        ok: false,
        message: 'No se encontró el Print ID',
      });
    }

    // const searchMo: any = await MoNumbers.query().findOne({
    //   mo_id: moID,
    // });

    // if (!searchMo) {
    //   throw new Error(`No se encontró la moID ${moID}`);
    // }

    // const searchMoInPrinID: any = await PrintMos.query()
    //   .findOne({
    //     mo_id: moID,
    //     print_id: id,
    //   })
    //   .where('is_active', 1);

    // if (!searchMoInPrinID) {
    //   throw new Error(`La MO ${moID} no está en el Print ID`);
    // }

    // const printMo = await PrintMos.query()
    //   .update({
    //     is_active: 0,
    //   })
    //   .where('print_id', id)
    //   .andWhere('mo_id', moID);

    // if (!printMo) {
    //   throw new Error('No se pudo eliminar el MOS del Print ID');
    // }

    const deletedMos = await transaction(PrintMos, async (PrintMos) => {
      const mosIDs: number[] = [];

      for (const mo of mos) {
        const moID: any = await PrintMos.query()
          .findOne({
            mo_id: mo,
            print_id: id,
          })
          .where('is_active', 1);

        if (!moID) {
          throw new Error(`La MO ${mo} no está en el Print ID`);
        }

        const printMo = await PrintMos.query()
          .update({
            is_active: 0,
          })
          .where('print_id', id)
          .andWhere('mo_id', mo);

        if (!printMo) {
          throw new Error('No se pudo eliminar el MOS del Print ID');
        }

        mosIDs.push(+mo);
      }

      return {
        printID: +id,
        mos: mosIDs,
      };
    });

    if (!deletedMos.printID || deletedMos.mos.length === 0) {
      logger.error('No se pudo eliminar el MOS del Print ID');

      return res.status(400).json({
        ok: false,
        message: 'No se pudo eliminar el MOS del Print ID',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'MOs eliminadas del PrintID',
    });
  } catch (error) {
    logger.error(`Error, ${error}`);

    return res.status(500).json({
      ok: false,
      message: `Error, ${error.message}`,
    });
  }
}

export async function deletePrintId(req: Request, res: Response) {
  try {
    const { id } = req.params;

    const printId = await PrintIds.query().findById(id);

    if (!printId) {
      logger.error('No se encontró el Print ID');

      return res.status(400).json({
        ok: false,
        message: 'No se encontró el Print ID',
      });
    }

    const deletedPrintId = await PrintIds.query()
      .update({
        is_active: 0,
      })
      .where('id', id);

    if (!deletedPrintId) {
      logger.error('No se pudo eliminar el Print ID');

      return res.status(400).json({
        ok: false,
        message: 'No se pudo eliminar el Print ID',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Print ID eliminado',
    });
  } catch (error) {
    logger.error(`Error, ${error}`);

    return res.status(500).json({
      ok: false,
      message: `Error, ${error.message}`,
    });
  }
}

export async function getPrintIds(_req: Request, res: Response) {
  try {
    const printIds: any[] = await PrintIds.query()
      .innerJoin('employees', 'print_ids.employee_id', 'employees.employee_id')
      .where('print_ids.is_active', 1)
      .select([
        {
          id: 'print_ids.id',
        },
        { description: 'print_ids.description' },
        { isActive: 'print_ids.is_active' },
        { pieces: 'print_ids.pieces' },
        { isRepo: 'print_ids.is_repo' },
        raw(
          "CASE WHEN employees.first_name IS NULL THEN 'N/A' ELSE employees.first_name END"
        ).as('employee'),
        { createdAt: 'print_ids.created_at' },
      ]);

    if (printIds.length === 0) {
      logger.error('No se encontraron Print IDs');

      return res.status(400).json({
        ok: false,
        message: 'No se encontraron Print IDs',
      });
    }

    const printIdsWithMos = await Promise.all(
      printIds.map(async (printId) => {
        const mos: any[] = await PrintMos.query()
          .join('mo_numbers', 'print_mos.mo_id', 'mo_numbers.mo_id')
          .where('print_id', +printId.id)
          .where('is_active', 1)
          .select('mo_numbers.num');

        if (!mos) {
          return {
            ...printId,
            mos: '',
          };
        }

        return {
          ...printId,
          mos: mos.map((mo) => mo.num).join(', '),
        };
      })
    );

    return res.status(200).json({
      ok: true,
      message: 'Print IDs encontrados',
      data: printIdsWithMos,
    });
  } catch (error) {
    logger.error(`Error, ${error}`);

    return res.status(500).json({
      ok: false,
      message: `Error, ${error.message}`,
    });
  }
}

export async function getPrintIdById(req: Request, res: Response) {
  try {
    const { id } = req.params;

    if (!id) {
      logger.error('No se encontró el Print ID');

      return res.status(400).json({
        ok: false,
        message: 'No se encontró el Print ID',
      });
    }

    const printId: any = await PrintIds.query()
      .join('work_area_lines', 'print_ids.machine_id', 'work_area_lines.id')
      .join('employees', 'print_ids.employee_id', 'employees.employee_id')
      .where('print_ids.id', id)
      .select([
        'print_ids.id',
        { isRepo: 'print_ids.is_repo' },
        'print_ids.pieces',
        { isActive: 'print_ids.is_active' },
        { createdAt: 'print_ids.created_at' },
        'print_ids.description',
        { machineID: 'work_area_lines.id' },
        { machine: 'work_area_lines.name' },
        { employeeID: 'employees.employee_id' },
        raw("CONCAT(employees.first_name, ' ', employees.last_name)").as(
          'employee'
        ),
      ])
      .first();

    if (!printId) {
      logger.error('No se encontró el Print ID');

      return res.status(400).json({
        ok: false,
        message: 'No se encontró el Print ID',
      });
    }

    const mos: any[] = await PrintMos.query()
      .join('mo_numbers', 'print_mos.mo_id', 'mo_numbers.mo_id')
      .where('print_id', +id)
      .where('is_active', 1)
      .select([
        'mo_numbers.num',
        { moOrder: 'mo_numbers.mo_order' },
        { moID: 'mo_numbers.mo_id' },
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.customer',
        { moStatus: 'mo_numbers.mo_status' },
        { requiredDate: 'mo_numbers.required_date' },
      ]);

    if (!mos) {
      return res.status(200).json({
        ok: true,
        message: 'Print ID encontrado',
        data: {
          ...printId,
          mos: '',
        },
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Print ID encontrado',
      data: {
        ...printId,
        mos,
      },
    });
  } catch (error) {
    logger.error(`Error, ${error}`);

    return res.status(500).json({
      ok: false,
      message: `Error, ${error.message}`,
    });
  }
}

export async function getPrintIdByName(req: Request, res: Response) {
  try {
    const { name } = req.params;

    if (!name) {
      logger.error('No se encontró el Print ID');

      return res.status(400).json({
        ok: false,
        message: 'No se encontró el Print ID',
      });
    }

    if (!name.includes('PRID-')) {
      logger.error('No es un PrintID');

      return res.status(400).json({
        ok: false,
        message: 'No es un PrintID',
      });
    }

    const id: string = name.split('-')[1];

    const printId: any = await PrintIds.query()
      .join('work_area_lines', 'print_ids.machine_id', 'work_area_lines.id')
      .where('print_ids.id', id)
      .select([
        'print_ids.id',
        'print_ids.is_repo',
        { isActive: 'print_ids.is_active' },
        { createdAt: 'print_ids.created_at' },
        'print_ids.description',
        { machineID: 'work_area_lines.id' },
        { machine: 'work_area_lines.name' },
      ])
      .first();

    if (!printId) {
      logger.error('No se encontró el Print ID');

      return res.status(400).json({
        ok: false,
        message: 'No se encontró el Print ID',
      });
    }

    const mos: any[] = await PrintMos.query()
      .join('mo_numbers', 'print_mos.mo_id', 'mo_numbers.mo_id')
      .where('print_id', +id)
      .where('is_active', 1)
      .select([
        'mo_numbers.num',
        'mo_numbers.mo_order',
        'mo_numbers.mo_id',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.customer',
        'mo_numbers.mo_status',
        'mo_numbers.required_date',
      ]);

    if (!mos) {
      return res.status(200).json({
        ok: true,
        message: 'Print ID encontrado',
        data: {
          ...printId,
          mos: '',
        },
      });
    }

    const data = {
      ...printId,
      mos: mos,
    };

    return res.status(200).json({
      ok: true,
      message: 'Print ID encontrado',
      data,
    });
  } catch (error) {
    logger.error(`Error, ${error}`);

    return res.status(500).json({
      ok: false,
      message: `Error, ${error.message}`,
    });
  }
}

export async function updatePrintId(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const { description } = req.body as unknown as PrintID;

    if (!id) {
      logger.error('No se encontró el Print ID');

      return res.status(400).json({
        ok: false,
        message: 'No se encontró el Print ID',
      });
    }

    const printId: any = await PrintIds.query().findById(id);

    if (!printId) {
      logger.error('No se encontró el Print ID');

      return res.status(400).json({
        ok: false,
        message: 'No se encontró el Print ID',
      });
    }

    const updatedPrintId: number = await PrintIds.query()
      .update({
        description: description || printId.description,
      })
      .where('id', id)
      .where('is_active', 1);

    if (!updatedPrintId) {
      logger.error('No se pudo actualizar el Print ID');

      return res.status(400).json({
        ok: false,
        message: 'No se pudo actualizar el Print ID',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Print ID actualizado',
      data: updatedPrintId,
    });
  } catch (error) {
    logger.error(`Error, ${error}`);

    return res.status(500).json({
      ok: false,
      message: `Error, ${error.message}`,
    });
  }
}
