import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('table_name', (table: Knex.TableBuilder) => {
    table.increments('id').unsigned().primary();
    table
      .timestamp('created_at')
      .notNullable()
      .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    table
      .timestamp('updated_at')
      .notNullable()
      .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

    table.integer('someint').unsigned().notNullable();
    table.dateTime('somedatetime').notNullable();
    table.date('somedate');
    table.string('somename').notNullable();
    table.text('sometext').nullable();
    table.decimal('somedecimal', 6, 2).notNullable();
    table
      .enum('someenum', ['apparel', 'electronics', 'furniture'])
      .notNullable();
    table.jsonb('jsonb_column');
    table.timestamp('printed_at').nullable() // timestamp with null default
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('table_name');
}
