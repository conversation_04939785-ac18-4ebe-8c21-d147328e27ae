import type { Request, Response } from 'express';
import { stat } from 'fs';

import {
  createMoContainer,
  getAllBinOrders,
  getPickPackStation,
  getPickPackStations,
  nextStationSortContainer,
} from '@app/services/pickpack';

export async function getStations(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const stations = await getPickPackStations();

    return res.status(200).json({
      ok: true,
      stations: stations,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getStation(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const station_id = Number(req.params.station_id);
  try {
    const station = await getPickPackStation(station_id);

    return res.status(200).json({
      ok: true,
      station: station,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export const nextStationContainerToSplit = async (
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> => {
  const station_id = Number(req.params.station_id);

  try {
    const nextMo = await nextStationSortContainer(station_id);

    return res.status(200).json({
      ok: true,
      nextMo,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const createMoContainerReq = async (req: Request, res: Response) => {
  const mo_id = Number(req.body.mo_id);
  const bin_location_id = req.body.bin_location_id
    ? Number(req.body.bin_location_id)
    : null;

  console.log('bin_location_id', bin_location_id);
  console.log('mo_id', mo_id);

  if (isNaN(bin_location_id)) {
    return res.status(400).json({
      ok: false,
      message: 'bin_location_id must be a number',
    });
  }

  if (isNaN(mo_id)) {
    return res.status(400).json({
      ok: false,
      message: 'mo_id must be a number',
    });
  }

  try {
    const newMoBinContents = await createMoContainer(mo_id, bin_location_id);

    console.log('mo bin contents', newMoBinContents);

    return res.status(200).json({
      ok: true,
      binContents: newMoBinContents,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error.message,
    });
  }
};

export const listOrders = async (req: Request, res: Response) => {
  try {
    const orders = await getAllBinOrders();

    return res.status(200).json({
      ok: true,
      orders,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error.message,
    });
  }
};
