export function sortList<T>(
  list: T[],
  field: keyof T,
  order: 'asc' | 'desc' = 'asc'
): T[] {
  return [...list].sort((a, b): number => {
    const aValue = a[field];
    const bValue = b[field];

    const numA = Number(aValue);
    const numB = Number(bValue);

    let result: number;

    if (!isNaN(numA) && !isNaN(numB)) {
      result = numA - numB;
    } else {
      result = String(aValue).localeCompare(String(bValue));
    }

    return order === 'asc' ? result : -result;
  });
}
