import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(
    'mo_twill_laser_job_statuses',
    (table: Knex.TableBuilder): void => {
      table.increments('id').unsigned().primary();
      table.string('name').notNullable().unique();
      table.boolean('is_active').notNullable().defaultTo(true);
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
      table
        .timestamp('updated_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('mo_twill_laser_job_statuses');
}
