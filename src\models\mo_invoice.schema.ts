import { Model } from '@app/db';

export class MoInvoice extends Model {
  static tableName = 'mo_invoices';

  id!: number;
  created_at!: Date;
  updated_at!: Date;
  invoice_type!: string;
  accepted_format_type!: string;
  from_date!: string;
  to_date!: string;
  pay_date!: string;
  run_datetime!: Date;
  contractor_number!: string;
  accepted_pages!: string;
  rejected_pages!: string;
  summary_pages!: string;
  filename!: string;
}

export class MoInvoiceItem extends Model {
  static tableName = 'mo_invoice_items';

  id!: number;
  created_at!: Date;
  updated_at!: Date;
  mo_invoice_id!: number; // integer
  mo_id!: number; // integer
  page_number!: number; // integer
  row_pixel!: number; // integer
  mo_number!: string; // string
  order_number!: string; // string
  order_line_number!: string; // string
  style_number!: string; // string
  qty!: number; // integer
  unit_price!: number; // decimal
  total_price!: number; // decimal
  class_code!: string; // string
  class_name!: string; // string
  ltr!: number; // integer
  size!: number; // decimal
  CP!: string; // string
  cut_price_part!: number; // decimal
  sew_price_part!: number; // decimal
  sub_price_part!: number; // decimal
  prt_price_part!: number; // decimal
  prs_price_part!: number; // decimal
  art_price_part!: number; // decimal
}
