import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'rhinestones_orders',
    (table: Knex.TableBuilder): void => {
      table.dropIndex(['type_id'], 'type_id');
      table.integer('type_id', 11).notNullable().defaultTo(1).alter();
    }
  );
  await knex.schema.alterTable(
    'rhinestones_orders',
    (table: Knex.TableBuilder): void => {
      table.timestamp('art_date').nullable().defaultTo(null).alter();
      table.string('order_status', 10).nullable().defaultTo('active').alter();
    }
  );

  await knex.schema.createTable(
    'rhinestones_files_range',
    (table: Knex.TableBuilder): void => {
      table.increments('id').primary();
      table
        .integer('rhinestones_range_per_order_id', 10)
        .unsigned()
        .notNullable();
      table.string('description', 255).notNullable();
      table.boolean('cute').notNullable();
      table.string('file_url', 255).nullable();
      table.boolean('is_active').notNullable().defaultTo(true);
      table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
      table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
      table
        .foreign('rhinestones_range_per_order_id')
        .references('id')
        .inTable('rhinestones_ranges_per_order');
    }
  );

  await knex.schema.alterTable(
    'rhinestones_order_items',
    (table: Knex.TableBuilder): void => {
      table
        .integer('type_id', 10)
        .notNullable()
        .unsigned()
        .defaultTo(10)
        .alter();
      table.dropColumn('original_quantity');
      table.string('item_status').nullable().defaultTo('active').alter();
      table.integer('file_per_range_id', 10).unsigned().nullable();
    }
  );

  await knex.schema.alterTable(
    'rhinestones_colors',
    (table: Knex.TableBuilder): void => {
      table.dropColumn('name');
      table.dropColumn('description');
    }
  );

  await knex.schema.alterTable(
    'rhinestones_sizes',
    (table: Knex.TableBuilder): void => {
      table.dropColumn('name');
      table.dropColumn('description');
    }
  );

  await knex.schema.alterTable(
    'rhinestones_ranges_per_order',
    (table: Knex.TableBuilder): void => {
      table.dropColumn('art_per_sheet');
      table.dropColumn('cute');
      table.dropColumn('description');
      table.dropColumn('file_url');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'rhinestones_orders',
    (table: Knex.TableBuilder): void => {
      table.integer('type_id', 11).notNullable().alter();
      table.index(['type_id'], 'type_id');
    }
  );
  await knex.schema.alterTable(
    'rhinestones_orders',
    (table: Knex.TableBuilder): void => {
      table.timestamp('art_date').notNullable().alter();
      table.string('order_status', 10).notNullable().alter();
    }
  );

  await knex.schema.dropTableIfExists('rhinestones_files_range');

  await knex.schema.alterTable(
    'rhinestones_order_items',
    (table: Knex.TableBuilder): void => {
      table.integer('type_id', 10).notNullable().unsigned().alter();
      table.string('item_status').notNullable().alter();
      table
        .foreign('range_per_order_id')
        .references('id')
        .inTable('rhinestones_ranges_per_order');
      table.dropColumn('file_per_range_id');
      table.integer('original_quantity', 11).notNullable().alter();
    }
  );

  await knex.schema.alterTable(
    'rhinestones_colors',
    (table: Knex.TableBuilder): void => {
      table.string('name', 100).notNullable();
      table.string('description', 150).notNullable();
    }
  );

  await knex.schema.alterTable(
    'rhinestones_sizes',
    (table: Knex.TableBuilder): void => {
      table.string('name', 100).notNullable();
      table.string('description', 150).notNullable();
    }
  );

  await knex.schema.alterTable(
    'rhinestones_ranges_per_order',
    (table: Knex.TableBuilder): void => {
      table.integer('cute', 11).notNullable().defaultTo(1);
      table.integer('art_per_sheet').notNullable().defaultTo(1);
      table.string('file_url', 150).nullable();
      table.string('description', 150).nullable();
    }
  );
}
