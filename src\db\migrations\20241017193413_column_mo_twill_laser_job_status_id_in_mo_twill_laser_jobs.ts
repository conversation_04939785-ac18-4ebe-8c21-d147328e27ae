import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('mo_twill_laser_jobs', (table) => {
    table
      .integer('mo_twill_laser_job_status_id', 10)
      .notNullable()
      .unsigned()
      .defaultTo(1);
    table
      .foreign(
        'mo_twill_laser_job_status_id',
        'mtljsi_mo_twill_laser_job_statuses_foreign'
      )
      .references('id')
      .inTable('mo_twill_laser_job_statuses');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('mo_twill_laser_jobs', (table) => {
    table.dropForeign(
      'mo_twill_laser_job_status_id',
      'mtljsi_mo_twill_laser_job_statuses_foreign'
    );
    table.dropColumn('mo_twill_laser_job_status_id');
  });
}
