import { sign } from 'jsonwebtoken';

import { buildLogger, config } from '@app/settings';

type Exp = '1m' | '15m' | '30m' | '45m' | '1h' | '1d' | '1w' | '30d' | '1y';

interface Props {
  employee_id: number;
  area_id: number;
  exp?: Exp;
}
const logger = buildLogger('helpers/jwt.ts');

export const generateJWT = ({
  area_id,
  employee_id,
  exp = '1h',
}: Props): string | boolean => {
  const jwtSecretKey = config.app.accessToken;

  try {
    const token = sign(
      {
        area_id,
        employee_id,
      },
      jwtSecretKey,
      { expiresIn: exp }
    );

    return token;
  } catch (error) {
    if (error instanceof Error) {
      logger.error(`Error al generar el token de acceso ${error}`);

      return false;
    }

    logger.error(`Error al generar el token de acceso ${error}`);

    return false;
  }
};
