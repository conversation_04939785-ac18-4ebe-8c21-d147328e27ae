import axios from 'axios';
import type { Knex } from 'knex';

import { knex } from '@app/db';
import { MoNumber, MoSize } from '@app/models/pedreria.schema';
import { Style, StyleCombo } from '@app/models/style.schema';
import type { MoNumberFull } from '@app/services/plotter';

const RestfulPolyFabricApi = process.env.RESTFUL_POLY_FABRIC;

interface IPolyPart {
  ActualWidth: number;
  Barcode: 'string';
  ComponentFabricWidth: string;
  Customer: string;
  FabricWidth: string;
  PartNumber: string;
  GarmentPart: string;
}

export const getMo = async (
  moId: number,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  if (!moId) {
    throw Error('MO ID es requerido');
  }
  const trx = options?.trx ?? undefined;
  const mo = await MoNumber.query().findById(moId);
  if (!mo) {
    throw Error('MO no encontrado');
  }
  const mosSizes = await MoSize.query(trx).where('mo_id', mo.mo_id);
  const styleCombos = await StyleCombo.query(trx)
    .join('styles', 'style_combos.style_id', 'styles.style_id')
    .where('styles.style_number ', mo.style)
    .whereNull('style_combos.deleted_at')
    .select('style_combos.*');

  const style = await Style.query().where('style_number', mo.style).first();

  const fullMO = {
    ...mo,
    sizes: mosSizes.filter((moSize) => moSize.mo_id === mo.mo_id),
    styleCombos: styleCombos,
    styleNumber: style,
  } as unknown as MoNumberFull;

  return fullMO;
};

export const getMoByNumAndClient = async (
  moNum: string,
  client: number,
  options?: {
    trx: Knex.Transaction;
  }
) => {
  if (!moNum) {
    throw Error('MO Number is required');
  }

  if (!client) {
    throw Error('Client is required');
  }
  const trx = options?.trx ?? undefined;
  const mo = await MoNumber.query()
    .where('company_code', +client)
    .where('num', '=', `${moNum}`)
    .first();

  if (!mo) {
    throw Error('MO no encontrado');
  }

  const mosSizes = await MoSize.query(trx).where('mo_id', mo.mo_id);
  const styleCombos = await StyleCombo.query(trx)
    .join('styles', 'style_combos.style_id', 'styles.style_id')
    .where('styles.style_number ', mo.style)
    .whereNull('style_combos.deleted_at')
    .select('style_combos.*');

  const style = await Style.query().where('style_number', mo.style).first();

  const fullMO = {
    ...mo,
    sizes: mosSizes.filter((moSize) => moSize.mo_id === mo.mo_id),
    styleCombos: styleCombos,
    styleNumber: style,
  } as unknown as MoNumberFull;

  if (!mo) {
    throw Error('MO no encontrado');
  }

  return fullMO;
};

export const getMoFabrics = async (moId: number) => {
  if (!moId) {
    throw Error('MO ID es requerido');
  }

  const mo = await MoNumber.query().findById(moId);
  if (!mo) {
    throw Error('MO no encontrado');
  }
  //consume poly
  try {
    //get fabric information from poly when client is 1
    if (mo.company_code === 2) {
      const rawQuery = `select ma.part_number, ma.customer, mag.garment_part from material_allocations ma 
      left join material_allocations_garment_part mag on ma.poly_manufacture_id = mag.poly_manufacture_id AND ma.part_number  = mag.part_number
      where ma.company_code = ${mo.company_code} and ma.poly_manufacture_id = ${mo.poly_manufacture_id} group by ma.part_number, mag.garment_part;`;

      const getFabric = await knex.transaction(async () => {
        const fabrics = await knex.raw(rawQuery);
        return {
          fabrics,
        };
      });

      if (!getFabric || !getFabric.fabrics || !getFabric.fabrics[0]) {
        return [];
      }

      //convert getFabric to ipolyPart
      const getPolyInformation: IPolyPart[] = getFabric.fabrics[0].map(
        (fabric) => ({
          ActualWidth: 0,
          Barcode: '',
          ComponentFabricWidth: fabric.part_number,
          Customer: fabric.customer,
          FabricWidth: 0,
          PartNumber: fabric.part_number,
          GarmentPart: fabric.garment_part,
        })
      );

      return getPolyInformation;
    } else if (mo.company_code === 1) {
      const res = await axios.post(RestfulPolyFabricApi, { mo: mo.num });
      const getPolyInformation = res.data as IPolyPart[];
      if (!getPolyInformation) {
        return [];
      }
      return getPolyInformation;
    }
  } catch (err) {
    throw Error(`Error al consumir poly: ${err}`);
  }
};
