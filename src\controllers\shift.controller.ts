import type { Dayjs } from 'dayjs';
import { default as dayjs } from 'dayjs';
import duration from 'dayjs/plugin/duration';
import isoWeek from 'dayjs/plugin/isoWeek';
import type { Request, Response } from 'express';

const { ref } = require('objection');

const {
  MoScans,
  WorkAreaGroupShifts,
  WorkAreaGroupEmployees,
  WorkAreaGroupShiftEmployee,
  WorkShiftEmployeeLeaves,
  WorkAreaGroupShiftEmployeesVoid,
  WorkShiftLeaveReasons,
  WorkShiftEmployeeRoles,
} = require('../models/tickets.schema');

interface shift {
  work_area_group_id: number;
  start_datetime_sv: string;
  end_datetime_sv: string;
  overtime_minutes: number;
  normal_minutes: number;
  break_minutes: number;
  working_minutes: number;
  operator_count: number;
  usable_minutes: number;
  efficiency: number;
}

interface EmployeeInterface {
  employee_id: number | null;
  is_counted: boolean | null;
  role_id: number | null;
  role_name: string | null;
}

interface ReturnInsertedEmployee {
  employee_id: number;
  shift_employee_id: number;
  action: string;
  is_count: boolean;
}

interface Shift {
  id: number;
}

async function updateScansWithShift(shiftId: number, groupId: number) {
  try {
    const getShift = await WorkAreaGroupShifts.query()
      .where('id', shiftId)
      .whereNull('removed_at');

    if (getShift.length > 0) {
      let stringUpdated;
      const startDate = getShift[0].start_datetime_sv;
      const endDate = getShift[0].end_datetime_sv;
      const getAllScans = await MoScans.query()
        .where('work_area_group_id', groupId)
        .where('sew', '>=', startDate)
        .where('sew', '<=', endDate);

      if (getAllScans.length > 0) {
        for (const e of getAllScans) {
          const updateMoScan = await MoScans.query()
            .update({
              work_area_group_shift_id: shiftId,
            })
            .where('scan_id', e.scan_id);

          if (updateMoScan > 0) {
            stringUpdated = stringUpdated + '-' + 'updated';
          } else {
            stringUpdated = stringUpdated + '-' + 'error';
          }
        }
      }

      return stringUpdated;
    } else {
      // shift no encontrado
      return 'shift no encontrado';
    }
  } catch (error) {
    return error;
  }
}

async function insertEmployeesToShift(
  shiftID: number,
  listForUpdate: EmployeeInterface[]
) {
  const format1 = 'YYYY-MM-DD HH:mm:ss';
  const returnArray: ReturnInsertedEmployee[] = [];
  // in case shift id was sent
  const getShift = await WorkAreaGroupShifts.query()
    .where('id', shiftID)
    .whereNull('removed_at');

  if (getShift.length > 0) {
    const overTime = getShift[0].overtime_minutes;
    const breakMinutes = getShift[0].break_minutes;
    const startDate = new Date(
      +getShift[0].start_datetime_sv.getFullYear(),
      +getShift[0].start_datetime_sv.getMonth(),
      +getShift[0].start_datetime_sv.getDate(),
      +getShift[0].start_datetime_sv.getHours(),
      +getShift[0].start_datetime_sv.getMinutes(),
      +getShift[0].start_datetime_sv.getSeconds()
    );
    const endDate = new Date(
      +getShift[0].end_datetime_sv.getFullYear(),
      +getShift[0].end_datetime_sv.getMonth(),
      +getShift[0].end_datetime_sv.getDate(),
      +getShift[0].end_datetime_sv.getHours(),
      +getShift[0].end_datetime_sv.getMinutes(),
      +getShift[0].end_datetime_sv.getSeconds()
    );

    // update shift
    for (const e of listForUpdate) {
      if (e.employee_id !== null) {
        // check if emp is no in other area in active
        const checkEmployeeInOtherArea =
          await WorkAreaGroupShiftEmployee.query()
            .join(
              'work_area_group_shifts',
              'work_area_group_shift_employees.work_area_group_shift_id',
              '=',
              'work_area_group_shifts.id'
            )
            .where(
              'work_area_group_shifts.start_datetime_sv',
              '>=',
              dayjs(startDate).format(format1)
            )
            .where(
              'work_area_group_shifts.end_datetime_sv ',
              '<=',
              dayjs(endDate).format(format1)
            )
            .where('work_area_group_shift_employees.employee_id', e.employee_id)
            .where('work_area_group_shift_employees.is_counted', true)
            .whereNull(
              'work_area_group_shift_employees.work_area_group_shift_employees_void_id'
            )
            .whereNull('work_area_group_shifts.removed_at')
            .select(
              'work_area_group_shift_employees.work_area_group_shift_id',
              'work_area_group_shift_employees.employee_id',
              'work_area_group_shift_employees.is_counted'
            );

        if (
          checkEmployeeInOtherArea === null ||
          checkEmployeeInOtherArea.length === 0
        ) {
          // not found in other area. insert into shift
          const addEmpShift = await WorkAreaGroupShiftEmployee.query().insert({
            work_area_group_shift_id: shiftID,
            employee_id: e.employee_id,
            start_datetime: startDate,
            finish_datetime: endDate,
            is_counted: e.is_counted,
            overtime_minutes: overTime,
            break_minutes: breakMinutes,
            work_shift_employee_role_id: e.role_id,
            work_shift_employee_role: e.role_name,
          });

          if (addEmpShift.id > 0) {
            returnArray.push({
              employee_id: e.employee_id,
              shift_employee_id: addEmpShift.id,
              action: 'Empleado agregado al horario',
              is_count: e.is_counted,
            });
          }
        } else {
          returnArray.push({
            employee_id: e.employee_id,
            shift_employee_id: shiftID,
            action: 'Empleado se encuentra en otro horario para este dia',
            is_count: false,
          });
        }
      } else {
        returnArray.push({
          employee_id: e.employee_id,
          shift_employee_id: shiftID,
          action: 'Empleado se encuentra en otro horario para este dia',
          is_count: false,
        });
      }
    }
    //update operator count in shift
    if (
      returnArray.filter(
        (emp: ReturnInsertedEmployee) =>
          emp.action === 'Empleado agregado al horario'
      ).length > 0
    ) {
      const getAllActiveEmployeesInShift =
        await WorkAreaGroupShiftEmployee.query()
          .where('work_area_group_shift_id', shiftID)
          .where('is_counted', true)
          .whereNull('work_area_group_shift_employees_void_id');
      if (getAllActiveEmployeesInShift.length > 0) {
        //const getCurrentShiftInformation = await WorkAreaGroupShifts.query().where('id', shiftID).whereNull('removed_at')
        //updating efficiency for sewing area
        await WorkAreaGroupShifts.query()
          .update({
            operator_count: getAllActiveEmployeesInShift.length,
            usable_minutes:
              getShift[0].working_minutes * getAllActiveEmployeesInShift.length,
            efficiency:
              getShift[0].est_production_minutes !== null
                ? getShift[0].est_production_minutes /
                  (getShift[0].working_minutes *
                    getAllActiveEmployeesInShift.length)
                : getShift[0].efficiency,
          })
          .where('id', shiftID);
      }
    }
  } else {
    return {
      ok: false,
      data: 'No Shift for id ' + shiftID,
    };
  }

  return {
    ok: true,
    data: returnArray,
  };
}

async function insertDefaultEmployeesToShift(shiftID: number, groupID: number) {
  try {
    //const getEmployeesGroup = await WorkAreaGroupEmployees.query().where('work_group_id', groupID).whereNull('deleted_at')
    //se obtenienen todos los empleados en el area con todo y su rol
    const getEmployeesGroup = await WorkAreaGroupEmployees.query()
      .leftJoin(
        'work_shift_employee_roles',
        'work_area_group_employees.work_shift_employee_role_id',
        '=',
        'work_shift_employee_roles.id'
      )
      .where('work_area_group_employees.work_group_id', groupID)
      .whereNull('work_area_group_employees.deleted_at')
      .select(
        'work_area_group_employees.employee_id',
        'work_shift_employee_roles.is_counted',
        'work_area_group_employees.work_shift_employee_role_id',
        'work_shift_employee_roles.name'
      );

    const listForInsert: EmployeeInterface[] = [];

    if (getEmployeesGroup !== null && getEmployeesGroup.length > 0) {
      for (const emp of getEmployeesGroup) {
        listForInsert.push({
          employee_id: emp.employee_id,
          is_counted: emp.is_counted,
          role_id: emp.work_shift_employee_role_id,
          role_name: emp.name,
        });
      }

      const response = await insertEmployeesToShift(shiftID, listForInsert);
      return {
        ok: true,
        message: response,
      };
    }
    return {
      ok: false,
      message: 'No employees in group',
    };
  } catch (error) {
    return {
      ok: false,
      message: error,
    };
  }
}

async function createShiftFromArray(shifts: shift[]) {
  // its empty and create a new shift
  const addNewShift = await WorkAreaGroupShifts.query().insert(shifts);

  if (addNewShift !== null) {
    //add default employees to shift
    const addEmployees = await insertDefaultEmployeesToShift(
      +addNewShift[0].id,
      +addNewShift[0].work_area_group_id
    );
    const shiftCReated = await updateScansWithShift(
      +addNewShift[0].id,
      +addNewShift[0].work_area_group_id
    );

    return `Se agregaron los empleados ${addEmployees}, y se actualizaron los escaneos ${shiftCReated}`;
  } else {
    return 'Error';
  }
}

export async function updateShift(shiftId: number, groupId: number) {
  try {
    // get shift information
    const getShift = await WorkAreaGroupShifts.query()
      .where('id', shiftId)
      .whereNull('removed_at');
    let updated;
    let missingSam = false;
    let estProductionMin = 0;

    if (getShift.length > 0) {
      const startDate = getShift[0].start_datetime_sv;
      const endDate = getShift[0].end_datetime_sv;
      const getAllScans = await MoScans.query()
        .where('work_area_group_id', groupId)
        .where('sew', '>=', startDate)
        .where('sew', '<=', endDate)
        .where('is_repo', false)
        .whereNull('removed_at');

      if (getAllScans.length > 0) {
        // update scans if founds
        for (const e of getAllScans) {
          // check if sam is missing
          if (e.sew_sam_value === null) {
            missingSam = true;
          } else {
            estProductionMin = estProductionMin + e.quantity * e.sew_sam_value;
          }

          if (e.work_area_group_shift_id === null) {
            const updateMoScan = await MoScans.query()
              .update({
                work_area_group_shift_id: shiftId,
              })
              .where('scan_id', e.scan_id);

            if (updateMoScan > 0) {
              updated = true;
            } else {
              updated = false;
            }
          }
        }
      }
    } else {
      // shift no encontrado
      updated = false;
    }

    // if sam is null set est prod min and effi to null
    if (missingSam) {
      // update shift
      await WorkAreaGroupShifts.query()
        .update({
          est_production_minutes: null,
          efficiency: null,
        })
        .where('id', shiftId);

      return false;
    } else if (getShift[0].usable_minutes === null) {
      // check if usable_minutes is null or 0, set efficiency to null and end
      await WorkAreaGroupShifts.query()
        .update({
          efficiency: null,
        })
        .where('id', shiftId);

      return false;
    } else if (
      getShift[0].operator_count === null ||
      getShift[0].operator_count === 0
    ) {
      // if null or 0, set usable_minutes to null and efficiency to null and end
      await WorkAreaGroupShifts.query()
        .update({
          usable_minutes: null,
          efficiency: null,
        })
        .where('id', shiftId);

      return false;
    }
    // get shift before updates
    const getShiftUpdated = await WorkAreaGroupShifts.query()
      .where('id', shiftId)
      .whereNull('removed_at');
    // update shift
    const updateShift = await WorkAreaGroupShifts.query()
      .update({
        est_production_minutes:
          estProductionMin === 0 ? null : estProductionMin,
        efficiency:
          estProductionMin === 0
            ? null
            : estProductionMin / getShiftUpdated[0].usable_minutes,
      })
      .where('id', shiftId);

    if (updateShift > 0) {
      updated = true;
    }

    return updated;
  } catch (error) {
    return error;
  }
}

export async function getEmployeeByGroup(req: Request, res: Response) {
  const groupID: number = +req.body.group_id;

  try {
    const getAllEmployeesForGroup = await WorkAreaGroupEmployees.query()
      .join(
        'employees',
        'work_area_group_employees.employee_id',
        'employees.employee_id'
      )
      .leftJoin(
        'work_shift_employee_roles',
        'work_area_group_employees.work_shift_employee_role_id',
        '=',
        'work_shift_employee_roles.id'
      )
      .leftJoin(
        'employees_departments',
        'employees_departments.code',
        '=',
        'employees.department'
      )
      .where('work_area_group_employees.work_group_id', groupID)
      .select(
        'work_area_group_employees.employee_id',
        'work_area_group_employees.work_group_id',
        'work_area_group_employees.deleted_at',
        'work_area_group_employees.work_shift_employee_role_id',
        'employees.first_name',
        'employees.last_name',
        'employees_departments.description',
        'work_shift_employee_roles.is_counted',
        'work_shift_employee_roles.name'
      )
      .orderBy('work_area_group_employees.deleted_at')
      .orderBy('employees.first_name');

    return res.status(200).json({
      ok: true,
      data: getAllEmployeesForGroup,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      data: error,
    });
  }
}

export async function getShiftInfo(req: Request, res: Response) {
  const group_id = req.body.group_id;
  const shiftDate: string = req.body.shift_date;
  const shiftID = req.body.shift_id;
  const allowedEarlyMinutesStart = 90;
  const format1 = 'YYYY-MM-DD HH:mm:ss';
  let actualDate = new Date();
  let startDate = new Date(
    actualDate.getFullYear(),
    actualDate.getMonth(),
    actualDate.getDate(),
    12,
    1,
    0
  );
  let endDate = new Date(
    actualDate.getFullYear(),
    actualDate.getMonth(),
    actualDate.getDate(),
    23,
    59,
    0
  );
  // try {

  // look for shift
  let getShift = [];
  if (shiftID !== undefined && shiftID !== null) {
    getShift = await WorkAreaGroupShifts.query()
      .where('id', shiftID)
      .whereNull('removed_at')
      .select(
        'id',
        'start_datetime_sv',
        'end_datetime_sv',
        'operator_count',
        'overtime_minutes'
      );
  } else {
    getShift = await WorkAreaGroupShifts.query()
      .where('start_datetime_sv', '<=', dayjs(actualDate).format(format1))
      .where('end_datetime_sv', '>=', dayjs(actualDate).format(format1))
      .where('work_area_group_id', group_id)
      .whereNull('removed_at')
      .select(
        'id',
        'start_datetime_sv',
        'end_datetime_sv',
        'operator_count',
        'overtime_minutes'
      );
  }

  if (getShift.length === 0) {
    // add minutes to early
    const olderDate = dayjs(actualDate)
      .add(allowedEarlyMinutesStart, 'm')
      .toDate();

    getShift = await WorkAreaGroupShifts.query()
      .where('start_datetime_sv', '<=', dayjs(olderDate).format(format1))
      .where('end_datetime_sv', '>=', dayjs(olderDate).format(format1))
      .where('work_area_group_id', group_id)
      .whereNull('removed_at')
      .select('id', 'start_datetime_sv', 'end_datetime_sv', 'operator_count');
  }

  if (shiftDate !== undefined && shiftDate !== null) {
    const separator = '/';
    const dateArray = shiftDate.split(separator);

    if (dateArray.length > 1) {
      startDate = new Date(
        +dateArray[0],
        +dateArray[1] - 1,
        +dateArray[2],
        1,
        1,
        0
      );
      endDate = new Date(
        +dateArray[0],
        +dateArray[1] - 1,
        +dateArray[2],
        23,
        59,
        0
      );
    } else {
      actualDate = new Date(shiftDate);
      startDate = new Date(
        actualDate.getFullYear(),
        actualDate.getMonth(),
        actualDate.getDate()
      );
      startDate.setHours(1, 1, 0);
      endDate = new Date(
        actualDate.getFullYear(),
        actualDate.getMonth(),
        actualDate.getDate()
      );
      endDate.setHours(23, 59, 59);
    }

    getShift = await WorkAreaGroupShifts.query()
      .where('start_datetime_sv', '>=', dayjs(startDate).format(format1))
      .where('start_datetime_sv', '<=', dayjs(endDate).format(format1))
      .where('work_area_group_id', group_id)
      .whereNull('removed_at')
      .select('id', 'start_datetime_sv', 'end_datetime_sv', 'operator_count');
  }

  if (getShift.length === 0) {
    // return no shift
    return res.status(200).json({
      ok: false,
      data: null,
    });
  } else {
    // use standart shift
    return res.status(200).json({
      ok: true,
      data: getShift,
    });
  }
  /* }
    catch (error) {   
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }*/
}

export async function getShiftInfoByArea(req: Request, res: Response) {
  const area_id = req.body.area_id;
  const shiftDate = req.body.shift_date;
  const format1 = 'YYYY-MM-DD HH:mm:ss';
  const separator = '/';
  const dateArray = shiftDate.split(separator);

  const startDate = new Date(
    +dateArray[0],
    +dateArray[1] - 1,
    +dateArray[2],
    1,
    1,
    0
  );
  const endDate = new Date(
    +dateArray[0],
    +dateArray[1] - 1,
    +dateArray[2],
    23,
    59,
    0
  );

  try {
    // look for shift
    const getShift = await WorkAreaGroupShifts.query()
      .join(
        'work_area_groups',
        'work_area_group_shifts.work_area_group_id',
        '=',
        'work_area_groups.id'
      )
      .where(
        'work_area_group_shifts.start_datetime_sv',
        '>=',
        dayjs(startDate).format(format1)
      )
      .where(
        'work_area_group_shifts.start_datetime_sv',
        '<=',
        dayjs(endDate).format(format1)
      )
      .where('work_area_groups.work_area_id', area_id)
      .whereNull('removed_at')
      .select([
        'work_area_groups.id as group_id',
        'work_area_group_shifts.id as shift_id',
        'work_area_group_shifts.start_datetime_sv',
        'work_area_group_shifts.end_datetime_sv',
        WorkAreaGroupShiftEmployee.query()
          .where('work_area_group_shift_id', ref('shift_id'))
          .count()
          .as('TotalEmployees'),
      ]);

    if (getShift.length === 0) {
      // return no shift
      return res.status(200).json({
        ok: false,
        data: 'NoShift',
      });
    } else {
      // use standart shift
      return res.status(200).json({
        ok: true,
        data: 'Shift',
        shift: getShift,
      });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function insertEmployeeInShift(req: Request, res: Response) {
  // try {
  const shift_id: number = req.body.shift_id;

  const listForUpdate: EmployeeInterface[] = req.body.data;
  if (shift_id === undefined || shift_id === null) {
    return res.status(500).json({
      ok: false,
      data: 'missing shift',
    });
  }

  const insertEmployees = await insertEmployeesToShift(shift_id, listForUpdate);
  return res.status(200).json(insertEmployees);

  /* }
    catch (error) {            
    return res.status(500).json({
      ok: false,
      message: error
    });
  }*/
}

export async function createShift(req: Request, res: Response) {
  try {
    const groupId: number = req.body.group_id;
    const startDate: string = req.body.from_date;
    const finishDate: string = req.body.to_date;
    const format1 = 'YYYY-MM-DD HH:mm:ss';
    const overTime =
      req.body.over_time === null || req.body.over_time === undefined
        ? 0
        : req.body.over_time;
    const fromDate: Date = new Date(startDate);
    const toDate: Date = new Date(finishDate);
    let diff = (toDate.getTime() - fromDate.getTime()) / 1000;

    diff /= 60;
    const totalMinutes = Math.abs(Math.round(diff)) - 40;

    const getEmployeesGroup = await WorkAreaGroupEmployees.query().where(
      'work_group_id',
      groupId
    );

    const shiftToInsert: shift[] = [];
    shiftToInsert.push({
      work_area_group_id: groupId,
      start_datetime_sv: dayjs(fromDate).format(format1),
      end_datetime_sv: dayjs(toDate).format(format1),
      overtime_minutes: overTime,
      normal_minutes: totalMinutes,
      break_minutes: 40,
      working_minutes: totalMinutes + overTime,
      operator_count:
        getEmployeesGroup.length > 0 ? getEmployeesGroup.length : null,
      usable_minutes:
        getEmployeesGroup.length > 0
          ? (totalMinutes + overTime) * getEmployeesGroup.length
          : null,
      efficiency: null,
    });

    const addNewShift = await createShiftFromArray(shiftToInsert);

    return res.status(200).json({
      ok: true,
      msg: addNewShift,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function updateShiftInformation(req: Request, res: Response) {
  try {
    //destructuring
    const {
      shiftID,
      startDate,
      finishDate,
      overTime,
      breakTime,
      nextDay,
    }: {
      shiftID: number;
      startDate: string;
      finishDate: string;
      overTime: number;
      breakTime: number;
      nextDay: boolean;
    } = req.body;

    dayjs.extend(duration);

    const stDate = dayjs(startDate);
    const finishDateTime = dayjs(finishDate);
    let fnDate = dayjs(
      new Date(
        stDate.get('year'),
        stDate.get('month'),
        stDate.get('date'),
        finishDateTime.hour(),
        finishDateTime.minute(),
        finishDateTime.second()
      )
    );

    //let fnDate = dayjs(finishDate);
    const format1 = 'YYYY-MM-DD HH:mm:ss';

    if (nextDay) {
      fnDate = fnDate.add(1, 'day');
    }

    // check if hours differences are less than 24
    const hours = Math.abs(stDate.diff(fnDate, 'hours'));

    if (hours > 24) {
      return res.status(200).json({
        ok: false,
        msg: 'Fecha excede las 24 horas por turno',
      });
    }
    // get shift information
    const getShift = await WorkAreaGroupShifts.query()
      .where('id', shiftID)
      .whereNull('removed_at');

    if (getShift.length > 0) {
      const timeDuration = dayjs.duration(fnDate.diff(stDate));

      const totalMinutes = Math.round(timeDuration.as('minute')) - breakTime;

      // update shift
      const updateShifts = await WorkAreaGroupShifts.query()
        .update({
          start_datetime_sv: stDate.format(format1),
          end_datetime_sv: fnDate.format(format1),
          overtime_minutes: overTime,
          break_minutes: breakTime,
          normal_minutes: totalMinutes,
          working_minutes: totalMinutes + overTime,
          est_production_minutes: null,
          efficiency: null,
        })
        .where('id', shiftID);

      if (updateShifts > 0) {
        const getAllEmployeesToUpdateShiftTime =
          await WorkAreaGroupShiftEmployee.query()
            .where('work_area_group_shift_id', shiftID)
            .where('start_datetime', getShift[0].start_datetime_sv)
            .where('finish_datetime', getShift[0].end_datetime_sv);

        if (
          getAllEmployeesToUpdateShiftTime !== null &&
          getAllEmployeesToUpdateShiftTime.length > 0
        ) {
          for (const emp of getAllEmployeesToUpdateShiftTime) {
            await WorkAreaGroupShiftEmployee.query()
              .update({
                start_datetime: stDate.format('YYYY-MM-DD HH:mm:ss'),
                finish_datetime: fnDate.format('YYYY-MM-DD HH:mm:ss'),
                overtime_minutes: overTime,
              })
              .where('id', emp.id);
          }
        }
      }

      if (updateShifts > 0) {
        await updateShift(shiftID, +getShift[0].work_area_group_id);

        return res.status(200).json({
          ok: true,
          msg: 'Shift Actualizado',
        });
      }
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function getShiftInfoByGroupWeek(req: Request, res: Response) {
  const group_id: number = req.body.group_id;
  const shiftDate: string = req.body.shift_date;
  const single_day: boolean = req.body.single_day;

  let firstDate = dayjs(shiftDate).day(0);
  const finishDate = dayjs(shiftDate).day(6);
  const getShifts = [];
  const days: Date[] = [];
  const format1 = 'YYYY-MM-DD HH:mm:ss';

  try {
    if (single_day !== undefined && single_day !== null && single_day) {
      firstDate = dayjs(shiftDate);
      days.push(firstDate.toDate());
      //search by each day in the week
      const startDate = new Date(
        firstDate.year(),
        dayjs(firstDate).month(),
        dayjs(firstDate).date(),
        1,
        1,
        0
      );
      const endDate = new Date(
        firstDate.year(),
        firstDate.month(),
        firstDate.date(),
        23,
        59,
        0
      );

      // look for shift
      const getShift = await WorkAreaGroupShifts.query()
        .where(
          'work_area_group_shifts.start_datetime_sv',
          '>=',
          dayjs(startDate).format(format1)
        )
        .where(
          'work_area_group_shifts.start_datetime_sv',
          '<=',
          dayjs(endDate).format(format1)
        )
        .where('work_area_group_shifts.work_area_group_id', group_id)
        .whereNull('removed_at')
        .select([
          'work_area_group_shifts.id as id',
          'work_area_group_shifts.start_datetime_sv',
          'work_area_group_shifts.end_datetime_sv',
          'work_area_group_shifts.operator_count',
          'work_area_group_shifts.overtime_minutes',
          'work_area_group_shifts.break_minutes',
          WorkAreaGroupShiftEmployee.query()
            .where('work_area_group_shift_id', ref('work_area_group_shifts.id'))
            .where('is_counted', true)
            .whereNull('work_area_group_shift_employees_void_id')
            .count()
            .as('TotalCountEmployees'),
        ]);
      if (getShift.length === 0) {
        // return no shift
        getShifts.push({ start_datetime_sv: startDate, shifts: null });
      } else {
        const listShifts = [];

        // use standart shift
        getShift.forEach((shift: any) => {
          listShifts.push({
            id: shift.id,
            start_datetime_sv: shift.start_datetime_sv,
            end_datetime_sv: shift.end_datetime_sv,
            operator_count: shift.operator_count,
            overtime_minutes: shift.overtime_minutes,
            total_operator_count: shift.TotalCountEmployees,
            break_minutes: shift.break_minutes,
          });
        });

        getShifts.push({ start_datetime_sv: startDate, shifts: listShifts });
      }
    } else {
      while (firstDate <= finishDate) {
        days.push(firstDate.toDate());
        //search by each day in the week
        const startDate = new Date(
          firstDate.year(),
          dayjs(firstDate).month(),
          dayjs(firstDate).date(),
          1,
          1,
          0
        );
        const endDate = new Date(
          firstDate.year(),
          firstDate.month(),
          firstDate.date(),
          23,
          59,
          0
        );
        // look for shift
        const getShift = await WorkAreaGroupShifts.query()
          .where(
            'work_area_group_shifts.start_datetime_sv',
            '>=',
            dayjs(startDate).format(format1)
          )
          .where(
            'work_area_group_shifts.start_datetime_sv',
            '<=',
            dayjs(endDate).format(format1)
          )
          .where('work_area_group_shifts.work_area_group_id', group_id)
          .whereNull('removed_at')
          .select([
            'work_area_group_shifts.id as id',
            'work_area_group_shifts.start_datetime_sv',
            'work_area_group_shifts.end_datetime_sv',
            'work_area_group_shifts.operator_count',
            'work_area_group_shifts.overtime_minutes',
            'work_area_group_shifts.break_minutes',
            WorkAreaGroupShiftEmployee.query()
              .where(
                'work_area_group_shift_id',
                ref('work_area_group_shifts.id')
              )
              .where('is_counted', true)
              .whereNull('work_area_group_shift_employees_void_id')
              .count()
              .as('TotalCountEmployees'),
          ]);

        if (getShift.length === 0) {
          // return no shift
          getShifts.push({ start_datetime_sv: startDate, shifts: null });
        } else {
          const listShifts = [];

          // use standart shift
          getShift.forEach((shift: any) => {
            listShifts.push({
              id: shift.id,
              start_datetime_sv: shift.start_datetime_sv,
              end_datetime_sv: shift.end_datetime_sv,
              operator_count: shift.operator_count,
              overtime_minutes: shift.overtime_minutes,
              total_operator_count: shift.TotalCountEmployees,
              break_minutes: shift.break_minutes,
            });
          });

          getShifts.push({ start_datetime_sv: startDate, shifts: listShifts });
        }
        firstDate = firstDate.clone().add(1, 'd');
      }
    }

    return res.status(200).json({
      ok: true,
      data: getShifts,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function createShiftFromShift(req: Request, res: Response) {
  try {
    const shiftId: number = req.body.shift_id;
    const groupID: number = req.body.group_id;
    const createDate: string = req.body.date;
    const dateToCreate = dayjs(createDate);
    const startYear = dateToCreate.year();
    const startMonth = dateToCreate.month() + 1;
    const startDay = dateToCreate.date();
    const format1 = 'YYYY-MM-DD HH:mm:ss';
    // check if shift already exist
    const getShiftFromClipboard = await WorkAreaGroupShifts.query()
      .where('id', shiftId)
      .whereNull('removed_at')
      .select(
        'id',
        'start_datetime_sv',
        'end_datetime_sv',
        'overtime_minutes',
        'normal_minutes',
        'working_minutes',
        'operator_count',
        'usable_minutes'
      );

    if (getShiftFromClipboard.length > 0) {
      const startDateString: string =
        getShiftFromClipboard[0].start_datetime_sv;
      const finishDateString: string = getShiftFromClipboard[0].end_datetime_sv;

      const getStartDateFromDB = dayjs(startDateString);
      const getFinishDateFromDB = dayjs(finishDateString);

      const getStartDate = dayjs(
        `${startYear}-${startMonth}-${startDay} ${getStartDateFromDB.hour()}:${getStartDateFromDB.minute()}`,
        'YYYY-MM-DD HH:mm'
      );
      let getFinishDate = dayjs(
        `${startYear}-${startMonth}-${startDay} ${getFinishDateFromDB.hour()}:${getFinishDateFromDB.minute()}`,
        'YYYY-MM-DD HH:mm'
      );

      if (
        getStartDateFromDB.format('YYYY-MM-DD') !==
        getFinishDateFromDB.format('YYYY-MM-DD')
      ) {
        getFinishDate = getFinishDate.add(1, 'day');
      }

      //check if new shift doesnt exist
      const shiftExist = await WorkAreaGroupShifts.query()
        .where('start_datetime_sv', '<=', dayjs(getStartDate).format(format1))
        .where('end_datetime_sv', '>=', dayjs(getFinishDate).format(format1))
        .where('work_area_group_id', groupID)
        .whereNull('removed_at')
        .select('id');

      if (shiftExist.length > 0) {
        //shift exist
        return res.status(200).json({
          ok: false,
          msg: 'Ya existe un horario asignado para el tiempo seleccionado',
        });
      } else {
        //create shift
        const addNewShift = await WorkAreaGroupShifts.query().insert({
          work_area_group_id: groupID,
          start_datetime_sv: dayjs(getStartDate).format(format1),
          end_datetime_sv: dayjs(getFinishDate).format(format1),
          overtime_minutes: getShiftFromClipboard[0].overtime_minutes,
          normal_minutes: getShiftFromClipboard[0].normal_minutes,
          break_minutes: 40,
          working_minutes: getShiftFromClipboard[0].working_minutes,
          operator_count: getShiftFromClipboard[0].operator_count,
          usable_minutes: getShiftFromClipboard[0].usable_minutes,
          efficiency: null,
        });

        if (addNewShift !== null) {
          await insertDefaultEmployeesToShift(
            +addNewShift.id,
            +addNewShift.work_area_group_id
          );

          const respFromMoScan = await updateScansWithShift(
            +addNewShift.id,
            groupID
          );

          return res.status(200).json({
            ok: true,
            msg: respFromMoScan,
          });
        }
      }
    } else {
      return res.status(200).json({
        ok: false,
        msg: 'No se encontro shift con ese ID',
      });
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
      msg: error,
    });
  }
}

export async function createMultiplesShift(req: Request, res: Response) {
  try {
    dayjs.extend(isoWeek);
    dayjs.extend(duration);
    const {
      days,
      groups,
      timeIn,
      timeOut,
      breakMinutes,
      overTime,
      weekNumber,
      nextDay,
    }: {
      days: number[];
      groups: number[];
      timeIn: string;
      timeOut: string;
      breakMinutes: number;
      overTime: number;
      weekNumber: number;
      nextDay: boolean;
    } = req.body;

    const startTime: Date = new Date(timeIn);
    const finishTime: Date = new Date(timeOut);

    const currentYear = dayjs(startTime).format('YYYY');

    const weekDays = dayjs().year(+currentYear).isoWeek(weekNumber).day(0);

    const arr = [0, 1, 2, 3, 4, 5, 6];
    let currentDate = weekDays;
    const format1 = 'YYYY-MM-DD HH:mm:ss';

    for await (const grp of groups) {
      for await (const dayIndex of arr) {
        const daySelected = days.filter((day: number) => day === dayIndex);
        //setting date for dayjs
        const startYear = currentDate.year();
        const startMonth = currentDate.month() + 1;
        const startDay = currentDate.date();
        const startHour = startTime.getHours();
        const startMinute = startTime.getMinutes();

        const finishYear = currentDate.year();
        const finishMonth = currentDate.month() + 1;
        const finishDay = currentDate.date();
        const finishHour = finishTime.getHours();
        const finishMinute = finishTime.getMinutes();

        if (daySelected.length > 0) {
          const newStartDate = dayjs(
            `${startYear}-${startMonth}-${startDay} ${startHour}:${startMinute}`,
            'YYYY-MM-DD HH:mm'
          );
          let newFinishtDate = dayjs(
            `${finishYear}-${finishMonth}-${finishDay} ${finishHour}:${finishMinute}`,
            'YYYY-MM-DD HH:mm'
          );

          if (nextDay) {
            newFinishtDate = newFinishtDate.add(1, 'day');
          }
          // check if hours differences are less than 24
          const hours = Math.abs(newStartDate.diff(newFinishtDate, 'hours'));

          if (hours > 24) {
            return res.status(500).json({
              ok: false,
              msg: 'Fecha excede las 24 horas por turno',
            });
          }

          const timeDuration = dayjs.duration(
            newFinishtDate.diff(newStartDate)
          );

          const totalMinutes =
            Math.round(timeDuration.as('minute')) - breakMinutes;
          const shiftToInsert: shift[] = [];

          shiftToInsert.push({
            work_area_group_id: grp,
            start_datetime_sv: dayjs(newStartDate).format(format1),
            end_datetime_sv: dayjs(newFinishtDate).format(format1),
            overtime_minutes: overTime,
            normal_minutes: totalMinutes,
            break_minutes: breakMinutes,
            working_minutes: totalMinutes + overTime,
            operator_count: null,
            usable_minutes: null,
            efficiency: null,
          });

          await createShiftFromArray(shiftToInsert);
        }
        currentDate = currentDate.clone().add(1, 'd');
      }
      currentDate = weekDays;
    }
    return res.status(200).json({
      ok: true,
      msg: 'Horarios creados correctamente',
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      msg: error,
    });
  }
}

export async function deleteMultiplesShift(req: Request, res: Response) {
  try {
    const shiftList: Shift[] = req.body.data;
    const actualDate = new Date();
    const format1 = 'YYYY-MM-DD HH:mm:ss';
    let updatedRows = 0;
    for (const shift of shiftList) {
      updatedRows =
        updatedRows +
        (await WorkAreaGroupShifts.query()
          .update({
            removed_at: dayjs(actualDate).format(format1),
          })
          .where('id', shift.id));
    }
    return res.status(200).json({
      ok: true,
      msg: updatedRows,
    });
  } catch (error) {
    return res.status(200).json({
      ok: false,
      msg: error,
    });
  }
}

export async function deleteEmployeeInGroup(req: Request, res: Response) {
  try {
    const employeeID: number = req.body.employee_id;
    const groupID: number = req.body.group_id;
    const actualDate = new Date();
    const format1 = 'YYYY-MM-DD HH:mm:ss';

    const deleteEmployee = await WorkAreaGroupEmployees.query()
      .update({
        deleted_at: dayjs(actualDate).format(format1),
      })
      .where('work_group_id', groupID)
      .where('employee_id', employeeID);

    return res.status(200).json({
      ok: true,
      message: deleteEmployee,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function restoreEmployeeInGroup(req: Request, res: Response) {
  try {
    const employeeID: number = req.body.employee_id;
    const groupID: number = req.body.group_id;

    const restoreEmployee = await WorkAreaGroupEmployees.query()
      .update({
        deleted_at: null,
      })
      .where('work_group_id', groupID)
      .where('employee_id', employeeID);

    return res.status(200).json({
      ok: true,
      message: restoreEmployee,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function addEmployeeInGroup(req: Request, res: Response) {
  try {
    const employeeID: number = req.body.employee_id;
    const groupID: number = req.body.group_id;
    const roleID: number = req.body.roleID;
    const checkIfExist = await WorkAreaGroupEmployees.query()
      .where('work_group_id', groupID)
      .where('employee_id', employeeID);

    if (checkIfExist !== null && checkIfExist.length > 0) {
      //check if exist and update removed at values if was removed
      if (checkIfExist[0].deleted_at !== null) {
        const updateEmployee = await WorkAreaGroupEmployees.query()
          .update({
            deleted_at: null,
          })
          .where('id', checkIfExist[0].id);
        return res.status(200).json({
          ok: true,
          message: updateEmployee,
        });
      }
      return res.status(200).json({
        ok: false,
        message: 'Employeee exist in the group',
      });
    }

    const insertEmployee = await WorkAreaGroupEmployees.query().insert({
      work_group_id: groupID,
      employee_id: employeeID,
      work_shift_employee_role_id: roleID,
    });

    return res.status(200).json({
      ok: true,
      message: insertEmployee,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function getEmployeesInShift(req: Request, res: Response) {
  const shiftID: number = req.body.shift_id;

  try {
    const getAllEmployeesInShift = await WorkAreaGroupShiftEmployee.query()
      .join(
        'employees',
        'work_area_group_shift_employees.employee_id',
        '=',
        'employees.employee_id'
      )
      .leftJoin(
        'work_area_group_shift_employees_void',
        'work_area_group_shift_employees.work_area_group_shift_employees_void_id',
        '=',
        'work_area_group_shift_employees_void.id'
      )
      .where(
        'work_area_group_shift_employees.work_area_group_shift_id',
        shiftID
      )
      .select(
        'work_area_group_shift_employees.id',
        'work_area_group_shift_employees.start_datetime',
        'work_area_group_shift_employees.finish_datetime',
        'work_area_group_shift_employees.is_counted',
        'work_area_group_shift_employees.work_area_group_shift_employees_void_id',
        'work_area_group_shift_employees_void.void_status',
        'work_area_group_shift_employees.work_shift_employee_role',
        'work_area_group_shift_employees.late_minutes',
        'work_area_group_shift_employees.work_area_group_shift_id',
        'employees.first_name',
        'employees.last_name',
        'employees.employee_id'
      )
      .orderBy('work_area_group_shift_employees_void.void_status');

    const listOfId: number[] = [];

    getAllEmployeesInShift.forEach((item: any) => {
      listOfId.push(+item.id);
    });

    const getAllEmployeesReasons = await WorkShiftEmployeeLeaves.query()
      .join(
        'work_shift_leave_reasons',
        'work_shift_employee_leaves.work_shift_leave_reason_id',
        '=',
        'work_shift_leave_reasons.id'
      )
      .whereIn('work_area_group_employee_shift_id', listOfId)
      .whereNull('deleted_at')
      .orderBy('work_shift_employee_leaves.start_timestamp', 'desc')
      .select(
        'work_shift_employee_leaves.id',
        'work_shift_employee_leaves.work_area_group_employee_shift_id',
        'work_shift_employee_leaves.start_timestamp',
        'work_shift_employee_leaves.finish_timestamp',
        'work_shift_employee_leaves.comment',
        'work_shift_leave_reasons.name',
        'work_shift_employee_leaves.work_shift_leave_reason_id'
      );

    return res.status(200).json({
      ok: true,
      data: {
        employees: getAllEmployeesInShift,
        leaves: getAllEmployeesReasons,
      },
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function getAllVoidStatuses(req: Request, res: Response) {
  try {
    const getAllVoid = await WorkAreaGroupShiftEmployeesVoid.query();

    return res.status(200).json({
      ok: true,
      data: getAllVoid,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function getAllLeaveReasons(req: Request, res: Response) {
  try {
    const getAllReasons = await WorkShiftLeaveReasons.query();

    return res.status(200).json({
      ok: true,
      data: getAllReasons,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function getEmployeesLeaveActionFromList(
  req: Request,
  res: Response
) {
  try {
    const getList: EmployeeInterface[] = req.body.data;
    const listOfId: number[] = [];

    getList.forEach((id: EmployeeInterface) => {
      listOfId.push(id.employee_id);
    });

    const getAllEmployeesReasons = await WorkShiftEmployeeLeaves.query()
      .whereIn('work_area_group_employee_shift_id', listOfId)
      .whereNull('deleted_at')
      .orderBy('start_timestamp', 'desc');

    return res.status(200).json({
      ok: true,
      data: getAllEmployeesReasons,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function updateEmployeeToVoidShiftStatus(
  req: Request,
  res: Response
) {
  try {
    const getList: number[] = req.body.data;
    const getStatusId: number = req.body.statusId;
    const getShiftID: number = req.body.shiftId;
    let updatedRow = 0;

    for (const emp of getList) {
      updatedRow =
        updatedRow +
        (await WorkAreaGroupShiftEmployee.query()
          .update({
            work_area_group_shift_employees_void_id: getStatusId,
          })
          .where('id', emp));
    }

    if (updatedRow > 0) {
      //update operator count in shift
      const getAllActiveEmployeesInShift =
        await WorkAreaGroupShiftEmployee.query()
          .where('work_area_group_shift_id', getShiftID)
          .where('is_counted', true)
          .whereNull('work_area_group_shift_employees_void_id');
      if (getAllActiveEmployeesInShift.length > 0) {
        //const getCurrentShiftInformation = await WorkAreaGroupShifts.query().where('id', shiftID).whereNull('removed_at')
        const getShift = await WorkAreaGroupShifts.query()
          .where('id', getShiftID)
          .whereNull('removed_at');
        //updating efficiency for sewing area
        await WorkAreaGroupShifts.query()
          .update({
            operator_count: getAllActiveEmployeesInShift.length,
            usable_minutes:
              getShift[0].working_minutes * getAllActiveEmployeesInShift.length,
            efficiency:
              getShift[0].est_production_minutes !== null
                ? getShift[0].est_production_minutes /
                  (getShift[0].working_minutes *
                    getAllActiveEmployeesInShift.length)
                : getShift[0].efficiency,
          })
          .where('id', getShiftID);
      }
    }
    return res.status(200).json({
      ok: true,
      msg: `Filas actualizadas ${updatedRow}`,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function updateEmployeeLateShiftMinutes(
  req: Request,
  res: Response
) {
  try {
    const getList: number[] = req.body.data;
    const late_minutes: number = req.body.late_minutes;
    let updatedRow = 0;

    for (const emp of getList) {
      updatedRow =
        updatedRow +
        (await WorkAreaGroupShiftEmployee.query()
          .update({
            late_minutes: late_minutes,
          })
          .where('id', emp));
    }
    return res.status(200).json({
      ok: true,
      msg: `Filas actualizadas ${updatedRow}`,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function updateEmployeeShiftTime(req: Request, res: Response) {
  try {
    const getList: number[] = req.body.data;
    const startDateString: string = req.body.startDate;
    const finishDateString: string = req.body.finishDate;
    const startDate: Date = new Date(startDateString);
    const finishDate: Date = new Date(finishDateString);
    // check if hours differences are less than 24
    const hours =
      Math.abs(startDate.getTime() - finishDate.getTime()) / 3600000;

    let updatedRow = 0;
    if (hours > 24) {
      return res.status(200).json({
        ok: false,
        msg: 'Fecha excede las 24 horas por turno',
      });
    }

    for (const emp of getList) {
      updatedRow =
        updatedRow +
        (await WorkAreaGroupShiftEmployee.query()
          .update({
            start_datetime: startDate,
            finish_datetime: finishDate,
          })
          .where('id', emp));
    }
    return res.status(200).json({
      ok: true,
      msg: `Filas actualizadas ${updatedRow}`,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function addEmployeeShiftLeave(req: Request, res: Response) {
  try {
    dayjs.extend(isoWeek);
    dayjs.extend(duration);
    const format1 = 'YYYY-MM-DD HH:mm:ss';
    const shiftId: number = req.body.shiftId;
    const shiftEmployeeId: number = req.body.shiftEmployeeId;
    const startDateTimeString: string = req.body.startDate;
    const finishDateTimeString: string | undefined = req.body.finishDate;
    const leaveId: number = req.body.leaveId;

    const leave_start_time = dayjs(startDateTimeString);
    const leave_finish_time =
      finishDateTimeString !== undefined && finishDateTimeString !== null
        ? dayjs(finishDateTimeString)
        : null;

    const shift = await WorkAreaGroupShifts.query()
      .where('id', shiftId)
      .whereNull('removed_at');

    if (!shift) {
      return res.status(200).json({
        ok: false,
        msg: 'Shift not found',
      });
    }

    //setting date for dayjs, start time from shift start date
    const shiftStartTime: string = shift[0].start_datetime_sv;

    //setting date and time
    const shiftStartDate = dayjs(shiftStartTime);

    const startYear = shiftStartDate.year();
    const startMonth = shiftStartDate.month() + 1;
    const startDay = shiftStartDate.date();

    const newStartDate = dayjs(
      `${startYear}-${startMonth}-${startDay} ${leave_start_time.hour()}:${leave_start_time.minute()}`,
      'YYYY-MM-DD HH:mm'
    );
    let newFinishtDate: Dayjs;

    const existingLeaveWithoutFinish = await WorkShiftEmployeeLeaves.query()
      .where('work_area_group_employee_shift_id', shiftEmployeeId)
      .whereNull('deleted_at')
      .whereNull('finish_timestamp');

    if (existingLeaveWithoutFinish.length > 0 && leave_finish_time === null) {
      return res.status(200).json({
        ok: false,
        msg: 'Cannot create new leave without a finish date when an existing leave has no finish date',
      });
    }

    if (
      newStartDate < shift[0].start_datetime_sv ||
      newStartDate >= shift[0].end_datetime_sv
    ) {
      return res.status(200).json({
        ok: false,
        msg: 'leave_start must be within the shift time range',
      });
    }
    const hasOverlappingLeave = await WorkShiftEmployeeLeaves.query()
      .where('start_timestamp', '<=', dayjs(newStartDate).format(format1))
      .where('finish_timestamp', '>', dayjs(newStartDate).format(format1))
      .where('work_area_group_employee_shift_id', shiftEmployeeId)
      .whereNull('deleted_at');

    if (hasOverlappingLeave.length > 0) {
      return res.status(200).json({
        ok: false,
        msg: 'leave_start cannot be between an existing leave',
      });
    }

    const getAllEmployeeLeaves = await WorkShiftEmployeeLeaves.query()
      .where('start_timestamp', '>=', shift[0].start_datetime_sv)
      .where('finish_timestamp', '<=', shift[0].end_datetime_sv)
      .where('work_area_group_employee_shift_id', shiftEmployeeId)
      .whereNull('deleted_at');

    if (getAllEmployeeLeaves.length > 0) {
      const getFinishNull = getAllEmployeeLeaves.filter(
        (e) => e.start_timestamp > newStartDate && leave_finish_time === null
      );

      if (getFinishNull.length > 0) {
        return res.status(200).json({
          ok: false,
          msg: 'leave_finish must be set if an existing leave exist after start',
        });
      }
    }

    if (leave_finish_time !== null) {
      newFinishtDate = dayjs(
        `${startYear}-${startMonth}-${startDay} ${leave_finish_time.hour()}:${leave_finish_time.minute()}`,
        'YYYY-MM-DD HH:mm'
      );
      if (
        dayjs(newStartDate).format('A') === 'PM' &&
        dayjs(newFinishtDate).format('A') === 'AM'
      ) {
        newFinishtDate = newFinishtDate.add(1, 'day');
      }
      const timeDuration = dayjs.duration(newFinishtDate.diff(newStartDate));
      const totalHours = Math.round(timeDuration.as('hour'));

      if (totalHours > 24) {
        return res.status(200).json({
          ok: false,
          msg: 'time range is more than 24 hours',
        });
      }

      if (newFinishtDate <= newStartDate) {
        return res.status(200).json({
          ok: false,
          msg: 'leave_finish must be greater than leave_start',
        });
      }

      if (newFinishtDate > shift[0].end_datetime_sv) {
        return res.status(200).json({
          ok: false,
          msg: 'leave_finish must be within the shift time range',
        });
      }

      if (getAllEmployeeLeaves.length > 0) {
        const getOverLappingFinish = getAllEmployeeLeaves.filter(
          (e) =>
            e.start_timestamp > newStartDate &&
            e.start_timestamp <= newFinishtDate
        );
        if (getOverLappingFinish.length > 0) {
          return res.status(200).json({
            ok: false,
            msg: 'leave_finish must be less than to all existing leave_start',
          });
        }
      }
    }

    const addEmpShiftLeave = await WorkShiftEmployeeLeaves.query().insert({
      work_area_group_employee_shift_id: shiftEmployeeId,
      start_timestamp: dayjs(newStartDate).format(format1),
      finish_timestamp:
        leave_finish_time === null
          ? null
          : dayjs(newFinishtDate).format(format1),
      work_shift_leave_reason_id: leaveId,
    });

    if (addEmpShiftLeave.id > 0) {
      return res.status(200).json({
        ok: true,
        msg: addEmpShiftLeave.id,
      });
    }

    return res.status(200).json({
      ok: false,
      msg: 'No se actualizado nada',
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      msg: error,
    });
  }
}

export async function updateEmployeeShiftLeave(req: Request, res: Response) {
  try {
    dayjs.extend(isoWeek);
    dayjs.extend(duration);
    const format1 = 'YYYY-MM-DD HH:mm:ss';
    const shiftId: number = req.body.shiftId;
    const shiftEmployeeId: number = req.body.shiftEmployeeId;
    const startDateTimeString: string = req.body.startDate;
    const finishDateTimeString: string | undefined = req.body.finishDate;
    const leaveId: number = req.body.leaveId;
    const shiftEmployeeLeaveId: number = req.body.shiftEmployeeLeaveId;

    const leave_start_time = dayjs(startDateTimeString);
    const leave_finish_time =
      finishDateTimeString !== undefined && finishDateTimeString !== null
        ? dayjs(finishDateTimeString)
        : null;

    const shift = await WorkAreaGroupShifts.query()
      .where('id', shiftId)
      .whereNull('removed_at');

    if (!shift) {
      return res.status(200).json({
        ok: false,
        msg: 'Shift not found',
      });
    }

    const leave = await WorkShiftEmployeeLeaves.query()
      .where('id', shiftEmployeeLeaveId)
      .whereNull('deleted_at');
    if (!leave) {
      return res.status(200).json({
        ok: false,
        msg: 'Leave not found',
      });
    }

    //setting date for dayjs, start time from shift start date
    const shiftStartTime: string = shift[0].start_datetime_sv;

    //setting date and time
    const shiftStartDate = dayjs(shiftStartTime);

    const startYear = shiftStartDate.year();
    const startMonth = shiftStartDate.month() + 1;
    const startDay = shiftStartDate.date();

    const newStartDate = dayjs(
      `${startYear}-${startMonth}-${startDay} ${leave_start_time.hour()}:${leave_start_time.minute()}`,
      'YYYY-MM-DD HH:mm'
    );
    let newFinishtDate: Dayjs;

    const hasOverlappingLeave = await WorkShiftEmployeeLeaves.query()
      .where('id', '!=', shiftEmployeeLeaveId)
      .where('start_timestamp', '<=', dayjs(newStartDate).format(format1))
      .where('finish_timestamp', '>', dayjs(newStartDate).format(format1))
      .where('work_area_group_employee_shift_id', shiftEmployeeId)
      .whereNull('deleted_at');
    if (hasOverlappingLeave.length > 0) {
      return res.status(200).json({
        ok: false,
        msg: 'leave_start cannot be between an existing leave',
      });
    }

    const getAllEmployeeLeaves = await WorkShiftEmployeeLeaves.query()
      .where('start_timestamp', '>=', shift[0].start_datetime_sv)
      .where('finish_timestamp', '<=', shift[0].end_datetime_sv)
      .where('work_area_group_employee_shift_id', shiftEmployeeId)
      .where('id', '!=', shiftEmployeeLeaveId)
      .whereNull('deleted_at');

    if (getAllEmployeeLeaves.length > 0) {
      const getFinishNull = getAllEmployeeLeaves.filter(
        (e) => e.start_timestamp > newStartDate && leave_finish_time === null
      );
      if (getFinishNull.length > 0) {
        return res.status(200).json({
          ok: false,
          msg: 'leave_finish must be set if an existing leave exist after start',
        });
      }
    }

    if (leave_finish_time !== null) {
      newFinishtDate = dayjs(
        `${startYear}-${startMonth}-${startDay} ${leave_finish_time.hour()}:${leave_finish_time.minute()}`,
        'YYYY-MM-DD HH:mm'
      );

      if (
        dayjs(newStartDate).format('A') === 'PM' &&
        dayjs(newFinishtDate).format('A') === 'AM'
      ) {
        newFinishtDate = newFinishtDate.add(1, 'day');
      }

      const timeDuration = dayjs.duration(newFinishtDate.diff(newStartDate));
      const totalHours = Math.round(timeDuration.as('hour'));

      if (totalHours > 24) {
        return res.status(200).json({
          ok: false,
          msg: 'time range is more than 24 hours',
        });
      }

      if (
        newStartDate < shift[0].start_datetime_sv ||
        newStartDate >= shift[0].end_datetime_sv
      ) {
        return res.status(200).json({
          ok: false,
          msg: 'leave_start must be within the shift time range',
        });
      }

      if (newFinishtDate <= newStartDate) {
        return res.status(200).json({
          ok: false,
          msg: 'leave_finish must be greater than leave_start',
        });
      }

      if (newFinishtDate > shift[0].end_datetime_sv) {
        return res.status(200).json({
          ok: false,
          msg: 'leave_finish must be within the shift time range',
        });
      }

      if (getAllEmployeeLeaves.length > 0) {
        const getOverLappingFinish = getAllEmployeeLeaves.filter(
          (e) =>
            e.start_timestamp > newStartDate &&
            e.start_timestamp <= newFinishtDate
        );
        if (getOverLappingFinish.length > 0) {
          return res.status(200).json({
            ok: false,
            msg: 'leave_finish must be less than or equal to all existing leave_start',
          });
        }
      }
    } else if (
      leave_finish_time === null &&
      leave[0].finish_timestamp !== null
    ) {
      const existingLeaveWithoutFinish = await WorkShiftEmployeeLeaves.query()
        .where('id', '!=', shiftEmployeeLeaveId)
        .where('work_area_group_employee_shift_id', shiftEmployeeId)
        .whereNull('finish_timestamp', leave_finish_time)
        .whereNull('deleted_at');

      if (existingLeaveWithoutFinish.length > 0) {
        return res.status(200).json({
          ok: false,
          msg: 'Cannot update new leave without a finish date when an existing leave has no finish date',
        });
      }
    }

    const addEmpShiftLeave = await WorkShiftEmployeeLeaves.query()
      .update({
        start_timestamp: dayjs(newStartDate).format(format1),
        finish_timestamp:
          leave_finish_time === null
            ? null
            : dayjs(newFinishtDate).format(format1),
        work_shift_leave_reason_id: leaveId,
      })
      .where('id', shiftEmployeeLeaveId);

    if (addEmpShiftLeave > 0) {
      return res.status(200).json({
        ok: true,
        msg: addEmpShiftLeave.id,
      });
    }

    return res.status(200).json({
      ok: false,
      msg: 'No se actualizado nada',
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      msg: error,
    });
  }
}

export async function getAllRolesByWorkType(req: Request, res: Response) {
  try {
    const workTypeID: number = req.body.workTypeID;
    const getAllRoles = await WorkShiftEmployeeRoles.query().where(
      'work_type_id',
      workTypeID
    );

    return res.status(200).json({
      ok: true,
      data: getAllRoles,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function updateEmployeeRole(req: Request, res: Response) {
  try {
    const getList: number[] = req.body.data;
    const getRolId: number = req.body.getRolId;
    const getRoleName: string = req.body.getRoleName;
    const is_counted: boolean = req.body.is_counted;
    const getShiftID: number = req.body.getShiftID;
    let updatedRow = 0;

    for (const emp of getList) {
      updatedRow =
        updatedRow +
        (await WorkAreaGroupShiftEmployee.query()
          .update({
            work_shift_employee_role_id: getRolId,
            work_shift_employee_role: getRoleName,
            is_counted,
          })
          .where('id', emp));
    }

    if (updatedRow > 0) {
      //update operator count in shift
      const getAllActiveEmployeesInShift =
        await WorkAreaGroupShiftEmployee.query()
          .where('work_area_group_shift_id', getShiftID)
          .where('is_counted', true)
          .whereNull('work_area_group_shift_employees_void_id');
      if (getAllActiveEmployeesInShift.length > 0) {
        //const getCurrentShiftInformation = await WorkAreaGroupShifts.query().where('id', shiftID).whereNull('removed_at')
        const getShift = await WorkAreaGroupShifts.query()
          .where('id', getShiftID)
          .whereNull('removed_at');
        //updating efficiency for sewing area
        await WorkAreaGroupShifts.query()
          .update({
            operator_count: getAllActiveEmployeesInShift.length,
            usable_minutes:
              getShift[0].working_minutes * getAllActiveEmployeesInShift.length,
            efficiency:
              getShift[0].est_production_minutes !== null
                ? getShift[0].est_production_minutes /
                  (getShift[0].working_minutes *
                    getAllActiveEmployeesInShift.length)
                : getShift[0].efficiency,
          })
          .where('id', getShiftID);
      }
    }
    return res.status(200).json({
      ok: true,
      msg: `Filas actualizadas ${updatedRow}`,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}

export async function deleteEmployeeShiftLeave(req: Request, res: Response) {
  try {
    const shiftEmployeeLeaveBy: string = req.body.shiftEmployeeLeaveBy;
    const shiftEmployeeLeaveId: number = req.body.shiftEmployeeLeaveId;
    const format1 = 'YYYY-MM-DD HH:mm:ss';

    const leave = await WorkShiftEmployeeLeaves.query()
      .where('id', shiftEmployeeLeaveId)
      .whereNull('deleted_at');
    if (!leave) {
      return res.status(200).json({
        ok: false,
        msg: 'Leave not found',
      });
    }

    const deleteEmpShiftLeave = await WorkShiftEmployeeLeaves.query()
      .update({
        deleted_at: dayjs().format(format1),
        deleted_by: shiftEmployeeLeaveBy,
      })
      .where('id', shiftEmployeeLeaveId);

    if (deleteEmpShiftLeave > 0) {
      return res.status(200).json({
        ok: true,
        msg: deleteEmpShiftLeave,
      });
    }

    return res.status(200).json({
      ok: false,
      msg: 'No se actualizado nada',
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      msg: error,
    });
  }
}

export async function updateEmployeesGroupRoles(req: Request, res: Response) {
  try {
    const getList: number[] = req.body.data;
    const getRolId: number = req.body.getRolId;
    const getGroupId: number = req.body.getGroupId;
    let updatedRow = 0;

    for (const emp of getList) {
      updatedRow =
        updatedRow +
        (await WorkAreaGroupEmployees.query()
          .update({
            work_shift_employee_role_id: getRolId,
          })
          .where('employee_id', emp)
          .where('work_group_id ', getGroupId));
    }
    return res.status(200).json({
      ok: true,
      msg: `Filas actualizadas ${updatedRow}`,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error,
    });
  }
}
