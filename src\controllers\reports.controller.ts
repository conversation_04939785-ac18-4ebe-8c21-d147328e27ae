import dayjs from 'dayjs';
import type { Request, Response } from 'express';
import { fn, raw, ref } from 'objection';

import {
  MoNumber,
  MoScans,
  MoVouchers,
  WorkActivityLog,
  WorkAreaTickets,
  WorkInventoryBins,
  WorkNotes,
  WorkTypes,
} from '@app/models/tickets.schema';

const xl = require('excel4node');

export async function reportTicketsActive(req: Request, res: Response) {
  try {
    const { workAreaId } = req.body;

    const getMoInfo = await WorkAreaTickets.query()
      .select([
        { id: 'work_area_tickets.id' },
        { voucher_id: 'work_vouchers.id' },
        { voucher_type: 'work_voucher_types.name' },
        'work_area_tickets.created_at',
        { group: 'work_area_groups.name' },
        { line: 'work_area_lines.name' },
        { last_activity_status_employee: 'last_activity.first_name' },
        { last_activity_status_date: 'last_activity.updated_at' },
        'mo_numbers.mo_status',
        'mo_numbers.material_date',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        'mo_numbers.ItemDescription8',
        'mo_numbers.num',
        'mo_numbers.po_numbers',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.customer',
        { voucher_group: 'work_voucher_groups.name' },
        raw(
          "CASE WHEN work_vouchers.is_repo = 1 THEN 'ES REPOSICION' ELSE 'NO ES REPOSICION' END"
        ).as('is_repo'),
        raw(
          "CASE WHEN work_vouchers.is_primary = 1 THEN 'PRINCIPAL' ELSE 'NO PRINCIPAL' END"
        ).as('is_primary'),
        fn
          .coalesce(
            WorkNotes.query()
              .select('work_notes.note')
              .orderBy('work_notes.id', 'desc')
              .where(
                'work_notes.work_area_ticket_id',
                ref('work_area_tickets.id')
              )
              .limit(1),
            'Sin comentario'
          )
          .as('last_note'),
        MoScans.query()
          .select('mo_scans.sew')
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .where('mo_scans.work_area_id', workAreaId)
          .whereNull('mo_scans.removed_at')
          .limit(1)
          .as('mo_scan'),
        'voucher_ticket_counts.ticket_count',
        'work_area_tickets.exp_finish_date',
        { voucher_plate_name: 'work_voucher_plates.name' },
        { ticket_status_name: 'work_area_ticket_statuses.name' },
        { bin_location_name: 'work_inventory_bins.name' },
        { next_area_name: 'next_work_area.area_name' },
        { prev_area_name: 'prev_work_area.area_name' },
        { ticket_area: 'work_areas.area_name' },
        MoScans.query()
          .select('work_area_groups.name')
          .as('group_scan')
          .leftJoin(
            'work_area_groups',
            'mo_scans.work_area_group_id',
            'work_area_groups.id'
          )
          .where('mo_scans.work_area_id', workAreaId)
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .whereNull('mo_scans.removed_at')
          .limit(1),
        MoScans.query()
          .select('work_area_lines.name')
          .as('line_scan')
          .leftJoin(
            'work_area_lines',
            'mo_scans.work_area_line_id',
            'work_area_lines.id'
          )
          .where('mo_scans.work_area_id', workAreaId)
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .whereNull('mo_scans.removed_at')
          .limit(1),
      ])
      .leftJoin(
        'work_areas',
        'work_area_tickets.work_area_id',
        'work_areas.work_area_id'
      )
      .leftJoin(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .leftJoin('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
      .leftJoin(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .leftJoin(
        'work_voucher_groups',
        'work_vouchers.work_voucher_group_id',
        'work_voucher_groups.id'
      )
      .leftJoin(
        'work_area_groups',
        'work_area_tickets.exp_work_area_group_id',
        'work_area_groups.id'
      )
      .leftJoin(
        'work_area_lines',
        'work_area_tickets.exp_work_area_line_id',
        'work_area_lines.id'
      )
      .leftJoin(
        'work_voucher_plates',
        'work_vouchers.work_voucher_plate_id',
        'work_voucher_plates.id'
      )
      .leftJoin(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id',
        '=',
        'work_area_ticket_statuses.id'
      )
      .leftJoin(
        'work_statuses',
        'work_area_ticket_statuses.work_status_id',
        '=',
        'work_statuses.id'
      )
      .leftJoin(
        'work_inventory_bins',
        'work_area_tickets.work_inventory_location_id',
        'work_inventory_bins.id'
      )
      .leftJoin(
        raw(`work_areas next_work_area ON
          next_work_area.work_area_id =
          work_area_tickets.next_work_area_id`)
      )
      .leftJoin(
        raw(`work_areas prev_work_area ON
          prev_work_area.work_area_id =
          work_area_tickets.prev_work_area_id`)
      )
      .leftJoin(
        WorkActivityLog.query()
          .select('employees.first_name', 'work_activity_log.updated_at', {
            ticket_id: 'latest_work_activity.module_id',
          })
          .join(
            WorkActivityLog.query()
              .select('work_activity_log.module_id')
              // @ts-ignore
              .max({ max_activity_id: 'work_activity_log.id' })
              .where('work_activity_log.activity', 'TicketStatusChanged')
              .groupBy('work_activity_log.module_id')
              .as('latest_work_activity'),
            'latest_work_activity.max_activity_id',
            'work_activity_log.id'
          )
          .leftJoin(
            'employees',
            'employees.employee_id',
            'work_activity_log.employee_id'
          )
          .whereNotNull('latest_work_activity.module_id')
          .as('last_activity'),
        'last_activity.ticket_id',
        'work_area_tickets.id'
      )
      .leftJoin(
        MoVouchers.query()
          .join(
            MoVouchers.query()
              .select('mo_vouchers.mo_id')
              // @ts-ignore
              .max({ last_id: 'mo_vouchers.id' })
              .where('file_status', 'Active')
              .groupBy('mo_vouchers.mo_id')
              .as('latest_voucher'),
            'latest_voucher.last_id',
            'mo_vouchers.id'
          )
          .whereNotNull('latest_voucher.last_id')
          .as('last_packet'),
        'last_packet.mo_id',
        'work_vouchers.mo_id'
      )
      .leftJoin(
        WorkAreaTickets.query()
          .select('work_area_tickets.work_voucher_id')
          .count({ ticket_count: 'work_area_tickets.id' })
          .groupBy('work_area_tickets.work_voucher_id')
          .as('voucher_ticket_counts'),
        'voucher_ticket_counts.work_voucher_id',
        'work_area_tickets.work_voucher_id'
      )
      .whereNull('work_area_tickets.finished_at')
      .where('work_area_tickets.work_area_id', workAreaId);

    if (getMoInfo.length > 0) {
      const wb = new xl.Workbook();

      // Add Worksheets to the workbook
      const ws = wb.addWorksheet('Metis');

      // Create a reusable style
      const styleHeaders = wb.createStyle({
        font: {
          bold: true,
        },
      });

      for (const [key, value] of getMoInfo.entries()) {
        const keys = Object.keys(value);
        const values = Object.values(value);

        console.log(values);

        keys.forEach((_, ind) => {
          ws.cell(1, 1).string('ticket').style(styleHeaders);
          ws.cell(1, 2).string('voucher').style(styleHeaders);
          ws.cell(1, 3).string('tipo de voucher').style(styleHeaders);
          ws.cell(1, 4).string('creado').style(styleHeaders);
          ws.cell(1, 5).string('grupo').style(styleHeaders);
          ws.cell(1, 6).string('linea').style(styleHeaders);
          ws.cell(1, 7)
            .string('empleado que cambio el estado')
            .style(styleHeaders);
          ws.cell(1, 8).string('fecha cambio de estado').style(styleHeaders);
          ws.cell(1, 9).string('estado de la mo').style(styleHeaders);
          ws.cell(1, 10).string('material date').style(styleHeaders);
          ws.cell(1, 11).string('mo order').style(styleHeaders);
          ws.cell(1, 12).string('fecha de exportacion').style(styleHeaders);
          ws.cell(1, 13).string('itemDescription8').style(styleHeaders);
          ws.cell(1, 14).string('mo').style(styleHeaders);
          ws.cell(1, 15).string('numeros de po').style(styleHeaders);
          ws.cell(1, 16).string('estilo').style(styleHeaders);
          ws.cell(1, 17).string('cantidad').style(styleHeaders);
          ws.cell(1, 18).string('cliente').style(styleHeaders);
          ws.cell(1, 19).string('grupo de voucher').style(styleHeaders);
          ws.cell(1, 20).string('reposicion').style(styleHeaders);
          ws.cell(1, 21).string('primario').style(styleHeaders);
          ws.cell(1, 22).string('ultima nota').style(styleHeaders);
          ws.cell(1, 23).string('fecha de escaneao').style(styleHeaders);
          ws.cell(1, 24).string('total de tickets').style(styleHeaders);
          ws.cell(1, 25).string('fecha estimada').style(styleHeaders);
          ws.cell(1, 26).string('voucher plate').style(styleHeaders);
          ws.cell(1, 27).string('estado de ticket').style(styleHeaders);
          ws.cell(1, 28).string('inventario').style(styleHeaders);
          ws.cell(1, 29).string('siguiente area').style(styleHeaders);
          ws.cell(1, 30).string('area anterior').style(styleHeaders);
          ws.cell(1, 31).string('area del ticket').style(styleHeaders);
          ws.cell(1, 32).string('grupo que escaneo').style(styleHeaders);
          ws.cell(1, 33).string('linea que escaneo').style(styleHeaders);
          ws.cell(key + 2, ind + 1).string(String(values[ind]) || 'null');
        });
      }

      wb.write('Excel.xlsx', res);

      return res.status(201);
    } else {
      return res.status(200).json({ ok: false, data: [] });
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({ ok: false });
  }
}

export async function reportMosActive(req: Request, res: Response) {
  try {
    const { workAreaId } = req.body;

    const getMoInfo = await WorkAreaTickets.query()
      .select([
        'work_area_tickets.created_at',
        { group: 'work_area_groups.name' },
        { line: 'work_area_lines.name' },
        { last_activity_status_employee: 'last_activity.first_name' },
        { last_activity_status_date: 'last_activity.updated_at' },
        'mo_numbers.mo_status',
        'mo_numbers.material_date',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        'mo_numbers.ItemDescription8',
        'mo_numbers.num',
        'mo_numbers.po_numbers',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.customer',
        { voucher_group: 'work_voucher_groups.name' },
        raw(
          "CASE WHEN work_vouchers.is_repo = 1 THEN 'ES REPOSICION' ELSE 'NO ES REPOSICION' END"
        ).as('is_repo'),
        raw(
          "CASE WHEN work_vouchers.is_primary = 1 THEN 'PRINCIPAL' ELSE 'NO PRINCIPAL' END"
        ).as('is_primary'),
        fn
          .coalesce(
            WorkNotes.query()
              .select('work_notes.note')
              .orderBy('work_notes.id', 'desc')
              .where(
                'work_notes.work_area_ticket_id',
                ref('work_area_tickets.id')
              )
              .limit(1),
            'Sin comentario'
          )
          .as('last_note'),
        MoScans.query()
          .select('mo_scans.sew')
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .where('mo_scans.work_area_id', workAreaId)
          .whereNull('mo_scans.removed_at')
          .limit(1)
          .as('mo_scan'),
        'voucher_ticket_counts.ticket_count',
        'work_area_tickets.exp_finish_date',
        { voucher_plate_name: 'work_voucher_plates.name' },
        { ticket_status_name: 'work_area_ticket_statuses.name' },
        { bin_location_name: 'work_inventory_bins.name' },
        { next_area_name: 'next_work_area.area_name' },
        { prev_area_name: 'prev_work_area.area_name' },
        { ticket_area: 'work_areas.area_name' },
        MoScans.query()
          .select('work_area_groups.name')
          .as('group_scan')
          .leftJoin(
            'work_area_groups',
            'mo_scans.work_area_group_id',
            'work_area_groups.id'
          )
          .where('mo_scans.work_area_id', workAreaId)
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .whereNull('mo_scans.removed_at')
          .limit(1),
        MoScans.query()
          .select('work_area_lines.name')
          .as('line_scan')
          .leftJoin(
            'work_area_lines',
            'mo_scans.work_area_line_id',
            'work_area_lines.id'
          )
          .where('mo_scans.work_area_id', workAreaId)
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .whereNull('mo_scans.removed_at')
          .limit(1),
      ])
      .leftJoin(
        'work_areas',
        'work_area_tickets.work_area_id',
        'work_areas.work_area_id'
      )
      .leftJoin(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .leftJoin('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
      .leftJoin(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .leftJoin(
        'work_voucher_groups',
        'work_vouchers.work_voucher_group_id',
        'work_voucher_groups.id'
      )
      .leftJoin(
        'work_area_groups',
        'work_area_tickets.exp_work_area_group_id',
        'work_area_groups.id'
      )
      .leftJoin(
        'work_area_lines',
        'work_area_tickets.exp_work_area_line_id',
        'work_area_lines.id'
      )
      .leftJoin(
        'work_voucher_plates',
        'work_vouchers.work_voucher_plate_id',
        'work_voucher_plates.id'
      )
      .leftJoin(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id',
        '=',
        'work_area_ticket_statuses.id'
      )
      .leftJoin(
        'work_statuses',
        'work_area_ticket_statuses.work_status_id',
        '=',
        'work_statuses.id'
      )
      .leftJoin(
        'work_inventory_bins',
        'work_area_tickets.work_inventory_location_id',
        'work_inventory_bins.id'
      )
      .leftJoin(
        raw(`work_areas next_work_area ON
          next_work_area.work_area_id =
          work_area_tickets.next_work_area_id`)
      )
      .leftJoin(
        raw(`work_areas prev_work_area ON
          prev_work_area.work_area_id =
          work_area_tickets.prev_work_area_id`)
      )
      .leftJoin(
        WorkActivityLog.query()
          .select('employees.first_name', 'work_activity_log.updated_at', {
            ticket_id: 'latest_work_activity.module_id',
          })
          .join(
            WorkActivityLog.query()
              .select('work_activity_log.module_id')
              // @ts-ignore
              .max({ max_activity_id: 'work_activity_log.id' })
              .where('work_activity_log.activity', 'TicketStatusChanged')
              .groupBy('work_activity_log.module_id')
              .as('latest_work_activity'),
            'latest_work_activity.max_activity_id',
            'work_activity_log.id'
          )
          .leftJoin(
            'employees',
            'employees.employee_id',
            'work_activity_log.employee_id'
          )
          .whereNotNull('latest_work_activity.module_id')
          .as('last_activity'),
        'last_activity.ticket_id',
        'work_area_tickets.id'
      )
      .leftJoin(
        MoVouchers.query()
          .join(
            MoVouchers.query()
              .select('mo_vouchers.mo_id')
              // @ts-ignore
              .max({ last_id: 'mo_vouchers.id' })
              .where('file_status', 'Active')
              .groupBy('mo_vouchers.mo_id')
              .as('latest_voucher'),
            'latest_voucher.last_id',
            'mo_vouchers.id'
          )
          .whereNotNull('latest_voucher.last_id')
          .as('last_packet'),
        'last_packet.mo_id',
        'work_vouchers.mo_id'
      )
      .leftJoin(
        WorkAreaTickets.query()
          .select('work_area_tickets.work_voucher_id')
          .count({ ticket_count: 'work_area_tickets.id' })
          .groupBy('work_area_tickets.work_voucher_id')
          .as('voucher_ticket_counts'),
        'voucher_ticket_counts.work_voucher_id',
        'work_area_tickets.work_voucher_id'
      )
      .where('work_vouchers.is_primary', 1)
      .whereNull('work_area_tickets.finished_at')
      .where('work_area_tickets.work_area_id', workAreaId)
      .whereNotIn('mo_numbers.mo_status', [
        'Void',
        'Cancelled',
        'Materials',
        'Complete',
      ]);

    if (getMoInfo.length > 0) {
      const wb = new xl.Workbook();

      // Add Worksheets to the workbook
      const ws = wb.addWorksheet('Metis');

      // Create a reusable style
      const styleHeaders = wb.createStyle({
        font: {
          bold: true,
        },
      });

      for (const [key, value] of getMoInfo.entries()) {
        const keys = Object.keys(value);
        const values = Object.values(value);

        keys.forEach((_, ind) => {
          ws.cell(1, 1).string('creado').style(styleHeaders);
          ws.cell(1, 2).string('grupo').style(styleHeaders);
          ws.cell(1, 3).string('linea').style(styleHeaders);
          ws.cell(1, 4)
            .string('empleado que cambio el estado')
            .style(styleHeaders);
          ws.cell(1, 5).string('fecha cambio de estado').style(styleHeaders);
          ws.cell(1, 6).string('estado de la mo').style(styleHeaders);
          ws.cell(1, 7).string('material date').style(styleHeaders);
          ws.cell(1, 8).string('mo order').style(styleHeaders);
          ws.cell(1, 9).string('fecha de exportacion').style(styleHeaders);
          ws.cell(1, 10).string('itemDescription8').style(styleHeaders);
          ws.cell(1, 11).string('mo').style(styleHeaders);
          ws.cell(1, 12).string('numeros de po').style(styleHeaders);
          ws.cell(1, 13).string('estilo').style(styleHeaders);
          ws.cell(1, 14).string('cantidad').style(styleHeaders);
          ws.cell(1, 15).string('cliente').style(styleHeaders);
          ws.cell(1, 16).string('grupo de voucher').style(styleHeaders);
          ws.cell(1, 17).string('reposicion').style(styleHeaders);
          ws.cell(1, 18).string('primario').style(styleHeaders);
          ws.cell(1, 19).string('ultima nota').style(styleHeaders);
          ws.cell(1, 20).string('fecha de escaneao').style(styleHeaders);
          ws.cell(1, 21).string('total de tickets').style(styleHeaders);
          ws.cell(1, 22).string('fecha estimada').style(styleHeaders);
          ws.cell(1, 23).string('area del ticket').style(styleHeaders);
          ws.cell(1, 24).string('voucher plate').style(styleHeaders);
          ws.cell(1, 25).string('estado de ticket').style(styleHeaders);
          ws.cell(1, 26).string('inventario').style(styleHeaders);
          ws.cell(1, 27).string('area anterior').style(styleHeaders);
          ws.cell(1, 28).string('siguiente area').style(styleHeaders);
          ws.cell(1, 28).string('area del ticket').style(styleHeaders);
          ws.cell(1, 29).string('grupo que escaneo').style(styleHeaders);
          ws.cell(1, 30).string('linea que escaneo').style(styleHeaders);

          ws.cell(key + 2, ind + 1).string(String(values[ind]) || 'null');
        });
      }

      wb.write('Excel.xlsx', res);

      return res.status(201);
    } else {
      return res.status(200).json({ ok: false, data: [] });
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({ ok: false });
  }
}

export async function reportMosActiveAllAreas(req: Request, res: Response) {
  try {
    const getMoInfo = await WorkAreaTickets.query()
      .select([
        'work_area_tickets.created_at',
        { group: 'work_area_groups.name' },
        { line: 'work_area_lines.name' },
        { last_activity_status_employee: 'last_activity.first_name' },
        { last_activity_status_date: 'last_activity.updated_at' },
        'mo_numbers.mo_status',
        'mo_numbers.material_date',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        'mo_numbers.ItemDescription8',
        'mo_numbers.num',
        'mo_numbers.po_numbers',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.customer',
        { voucher_group: 'work_voucher_groups.name' },
        raw(
          "CASE WHEN work_vouchers.is_repo = 1 THEN 'ES REPOSICION' ELSE 'NO ES REPOSICION' END"
        ).as('is_repo'),
        raw(
          "CASE WHEN work_vouchers.is_primary = 1 THEN 'PRINCIPAL' ELSE 'NO PRINCIPAL' END"
        ).as('is_primary'),
        fn
          .coalesce(
            WorkNotes.query()
              .select('work_notes.note')
              .orderBy('work_notes.id', 'desc')
              .where(
                'work_notes.work_area_ticket_id',
                ref('work_area_tickets.id')
              )
              .limit(1),
            'Sin comentario'
          )
          .as('last_note'),
        MoScans.query()
          .select('mo_scans.sew')
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .whereNull('mo_scans.removed_at')
          .limit(1)
          .as('mo_scan'),
        'voucher_ticket_counts.ticket_count',
        'work_area_tickets.exp_finish_date',
        { area_ticket: 'work_area_ticket.area_name' },
        { voucher_plate_name: 'work_voucher_plates.name' },
        { ticket_status_name: 'work_area_ticket_statuses.name' },
        { bin_location_name: 'work_inventory_bins.name' },
        { next_area_name: 'next_work_area.area_name' },
        { prev_area_name: 'prev_work_area.area_name' },
        { ticket_area: 'work_areas.area_name' },
        MoScans.query()
          .select('work_area_groups.name')
          .as('group_scan')
          .leftJoin(
            'work_area_groups',
            'mo_scans.work_area_group_id',
            'work_area_groups.id'
          )
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .whereNull('mo_scans.removed_at')
          .limit(1),
        MoScans.query()
          .select('work_area_lines.name')
          .as('line_scan')
          .leftJoin(
            'work_area_lines',
            'mo_scans.work_area_line_id',
            'work_area_lines.id'
          )
          .where('mo_scans.mo_id', ref('mo_numbers.mo_id'))
          .whereNull('mo_scans.removed_at')
          .limit(1),
      ])
      .leftJoin(
        'work_areas',
        'work_area_tickets.work_area_id',
        'work_areas.work_area_id'
      )
      .leftJoin(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .leftJoin('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
      .leftJoin(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .leftJoin(
        'work_voucher_groups',
        'work_vouchers.work_voucher_group_id',
        'work_voucher_groups.id'
      )
      .leftJoin(
        'work_area_groups',
        'work_area_tickets.exp_work_area_group_id',
        'work_area_groups.id'
      )
      .leftJoin(
        'work_area_lines',
        'work_area_tickets.exp_work_area_line_id',
        'work_area_lines.id'
      )
      .leftJoin(
        'work_voucher_plates',
        'work_vouchers.work_voucher_plate_id',
        'work_voucher_plates.id'
      )
      .leftJoin(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id',
        '=',
        'work_area_ticket_statuses.id'
      )
      .leftJoin(
        'work_statuses',
        'work_area_ticket_statuses.work_status_id',
        '=',
        'work_statuses.id'
      )
      .leftJoin(
        'work_inventory_bins',
        'work_area_tickets.work_inventory_location_id',
        'work_inventory_bins.id'
      )
      .leftJoin(
        raw(`work_areas next_work_area ON
          next_work_area.work_area_id =
          work_area_tickets.next_work_area_id`)
      )
      .leftJoin(
        raw(`work_areas prev_work_area ON
          prev_work_area.work_area_id =
          work_area_tickets.prev_work_area_id`)
      )
      .leftJoin(
        raw(`work_areas work_area_ticket ON
        work_area_ticket.work_area_id =
          work_area_tickets.work_area_id`)
      )
      .leftJoin(
        WorkActivityLog.query()
          .select('employees.first_name', 'work_activity_log.updated_at', {
            ticket_id: 'latest_work_activity.module_id',
          })
          .join(
            WorkActivityLog.query()
              .select('work_activity_log.module_id')
              // @ts-ignore
              .max({ max_activity_id: 'work_activity_log.id' })
              .where('work_activity_log.activity', 'TicketStatusChanged')
              .groupBy('work_activity_log.module_id')
              .as('latest_work_activity'),
            'latest_work_activity.max_activity_id',
            'work_activity_log.id'
          )
          .leftJoin(
            'employees',
            'employees.employee_id',
            'work_activity_log.employee_id'
          )
          .whereNotNull('latest_work_activity.module_id')
          .as('last_activity'),
        'last_activity.ticket_id',
        'work_area_tickets.id'
      )
      .leftJoin(
        MoVouchers.query()
          .join(
            MoVouchers.query()
              .select('mo_vouchers.mo_id')
              // @ts-ignore
              .max({ last_id: 'mo_vouchers.id' })
              .where('file_status', 'Active')
              .groupBy('mo_vouchers.mo_id')
              .as('latest_voucher'),
            'latest_voucher.last_id',
            'mo_vouchers.id'
          )
          .whereNotNull('latest_voucher.last_id')
          .as('last_packet'),
        'last_packet.mo_id',
        'work_vouchers.mo_id'
      )
      .leftJoin(
        WorkAreaTickets.query()
          .select('work_area_tickets.work_voucher_id')
          .count({ ticket_count: 'work_area_tickets.id' })
          .groupBy('work_area_tickets.work_voucher_id')
          .as('voucher_ticket_counts'),
        'voucher_ticket_counts.work_voucher_id',
        'work_area_tickets.work_voucher_id'
      )
      .where('work_vouchers.is_primary', 1)
      .whereNull('work_area_tickets.finished_at')
      .whereNotIn('mo_numbers.mo_status', [
        'Void',
        'Cancelled',
        'Materials',
        'Complete',
      ]);

    if (getMoInfo.length > 0) {
      const wb = new xl.Workbook();

      // Add Worksheets to the workbook
      const ws = wb.addWorksheet('Metis');

      // Create a reusable style
      const styleHeaders = wb.createStyle({
        font: {
          bold: true,
        },
      });

      for (const [key, value] of getMoInfo.entries()) {
        const keys = Object.keys(value);
        const values = Object.values(value);

        keys.forEach((_, ind) => {
          ws.cell(1, 1).string('creado').style(styleHeaders);
          ws.cell(1, 2).string('grupo').style(styleHeaders);
          ws.cell(1, 3).string('linea').style(styleHeaders);
          ws.cell(1, 4)
            .string('empleado que cambio el estado')
            .style(styleHeaders);
          ws.cell(1, 5).string('fecha cambio de estado').style(styleHeaders);
          ws.cell(1, 6).string('estado de la mo').style(styleHeaders);
          ws.cell(1, 7).string('material date').style(styleHeaders);
          ws.cell(1, 8).string('mo order').style(styleHeaders);
          ws.cell(1, 9).string('fecha de exportacion').style(styleHeaders);
          ws.cell(1, 10).string('itemDescription8').style(styleHeaders);
          ws.cell(1, 11).string('mo').style(styleHeaders);
          ws.cell(1, 12).string('numeros de po').style(styleHeaders);
          ws.cell(1, 13).string('estilo').style(styleHeaders);
          ws.cell(1, 14).string('cantidad').style(styleHeaders);
          ws.cell(1, 15).string('cliente').style(styleHeaders);
          ws.cell(1, 16).string('grupo de voucher').style(styleHeaders);
          ws.cell(1, 17).string('reposicion').style(styleHeaders);
          ws.cell(1, 18).string('primario').style(styleHeaders);
          ws.cell(1, 19).string('ultima nota').style(styleHeaders);
          ws.cell(1, 20).string('fecha de escaneao').style(styleHeaders);
          ws.cell(1, 21).string('total de tickets').style(styleHeaders);
          ws.cell(1, 22).string('fecha estimada').style(styleHeaders);
          ws.cell(1, 23).string('area del ticket').style(styleHeaders);
          ws.cell(1, 24).string('voucher plate').style(styleHeaders);
          ws.cell(1, 25).string('estado de ticket').style(styleHeaders);
          ws.cell(1, 26).string('inventario').style(styleHeaders);
          ws.cell(1, 27).string('area anterior').style(styleHeaders);
          ws.cell(1, 28).string('siguiente area').style(styleHeaders);
          ws.cell(1, 29).string('area del ticket').style(styleHeaders);
          ws.cell(1, 30).string('grupo que escaneo').style(styleHeaders);
          ws.cell(1, 31).string('linea que escaneo').style(styleHeaders);

          ws.cell(key + 2, ind + 1).string(String(values[ind]) || 'null');
        });
      }

      wb.write('Excel.xlsx', res);

      return res.status(201);
    } else {
      return res.status(200).json({ ok: false, data: [] });
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({ ok: false });
  }
}

export async function dateTest(req: Request, res: Response) {
  const knex = MoNumber.knex();
  // res.send(await knex('work_types').select(knex.raw('date(created_at)')))
  const testData = await knex.raw(
    `select work_area_id, disabled_date, date(disabled_date), created_at from work_areas where disabled_date is not null limit 1`
  );

  res.send(testData);
  // res.send(await FabricAdjustment.query().select('revision_date').limit(5))
}

export async function moActiveSchedule(req: Request, res: Response) {
  try {
    const knex = MoNumber.knex();

    const work_types = await WorkTypes.query().orderBy('sort');
    // console.log(work_types)

    interface IPivotHeaders {
      [fields: string]: {
        fields: {
          [field: string]: {
            name: string;
            width?: number;
          };
        };
        name?: string;
      };
    }

    interface IColumnHeaders {
      [field: string]: {
        name: string;
        column: number;
        style: string;
        table?: string;
        columnType?: number;
        pivotHeader?: string;
        pivotCount?: number;
        width?: number;
      };
    }

    interface IMoData {
      [fields: string]: string;
    }

    interface IColumnDefinition {
      characterSet: number;
      encoding: string;
      name: string;
      columnLength: number;
      columnType: number;
      flags: number;
      decimals: number;
    }

    const pivotHeaders: IPivotHeaders = {};

    for (const wt of work_types) {
      pivotHeaders[wt.id] = {
        fields: {
          open_ticket_count: { name: 'to', width: 3 },
          open_scan_count: { name: 'so', width: 3 },
          finish_scan_date: { name: 'finish' },
        },
        name: wt.name,
      };
    }

    const columnHeaders: IColumnHeaders = {
      mo_id: { table: 'mo', name: 'mo_id', style: 'textCell', column: 1 },
      customer: { table: 'mo', name: 'customer', style: 'textCell', column: 2 },
      num: { table: 'mo', name: 'mo', style: 'textCell', column: 3 },
      mo_order: { table: 'mo', name: 'order', style: 'textCell', column: 4 },
      style: { table: 'mo', name: 'style', style: 'textCell', column: 5 },
      quantity: { table: 'mo', name: 'quantity', style: 'textCell', column: 6 },
      required_date: {
        table: 'mo',
        name: 'x_fac_date',
        style: 'textCell',
        column: 7,
      },
      order_required_date: {
        table: 'mo',
        name: 'order_req_date',
        style: 'textCell',
        column: 8,
      },
      po_number: {
        table: 'mo',
        name: 'po_number',
        style: 'textCell',
        column: 9,
      },
      po_numbers: {
        table: 'mo',
        name: 'po_numbers',
        style: 'textCell',
        column: 10,
      },
      mo_status: {
        table: 'mo',
        name: 'mo_status',
        style: 'textCell',
        column: 11,
      },
      order_status: {
        table: 'mo',
        name: 'order_status',
        style: 'textCell',
        column: 12,
      },
      retail_po: {
        table: 'mo',
        name: 'retail_po',
        style: 'textCell',
        column: 13,
      },
      retailer_po_numbers: {
        table: 'mo',
        name: 'retailer_po_numbers',
        style: 'textCell',
        column: 14,
      },
      order_type_name: {
        table: 'mo',
        name: 'order_type_name',
        style: 'textCell',
        column: 15,
      },
      order_type_2: {
        table: 'mo',
        name: 'order_type_2',
        style: 'textCell',
        column: 16,
      },
      order_type_3: {
        table: 'mo',
        name: 'order_type_3',
        style: 'textCell',
        column: 17,
      },
      ItemDescription8: {
        table: 'mo',
        name: 'ItemDescription8',
        style: 'textCell',
        column: 18,
      },
      material_date: {
        table: 'mo',
        name: 'material_date',
        style: 'textCell',
        column: 19,
      },
      sched_start: {
        table: 'mo',
        name: 'sched_start',
        style: 'textCell',
        column: 20,
      },
      production_status: {
        table: 'mo',
        name: 'production_status',
        style: 'textCell',
        column: 21,
      },
      material_comments: {
        table: 'mo',
        name: 'material_comments',
        style: 'textCell',
        column: 22,
      },
      withdraw_comments: {
        table: 'mo',
        name: 'withdraw_comments',
        style: 'textCell',
        column: 23,
      },
      style_category: {
        table: 'mo',
        name: 'style_category',
        style: 'textCell',
        column: 24,
      },
      product_category: {
        table: 'mo',
        name: 'product_category',
        style: 'textCell',
        column: 25,
      },
      created_at: {
        table: 'mo',
        name: 'created_at',
        style: 'textCell',
        column: 26,
      },
      create_date: {
        table: 'mo',
        name: 'create_date',
        style: 'textCell',
        column: 27,
      },
      finish_date: {
        table: 'mo',
        name: 'finish_date',
        style: 'textCell',
        column: 28,
      },
    };
    let r = 29;

    for (const wtId of Object.keys(pivotHeaders)) {
      for (const field of Object.keys(pivotHeaders[wtId].fields)) {
        const pivotHeader = pivotHeaders[wtId].fields[field];

        columnHeaders[wtId + '_' + field] = {
          name: pivotHeader.name,
          style: 'textCell',
          column: r,
          pivotHeader: pivotHeaders[wtId].name,
          pivotCount: Object.keys(pivotHeaders[wtId]).length,
          width: pivotHeader.width,
        };
        r++;
      }
    }

    console.log('getting MO info');
    const getMoInfo = await knex.raw(
      knex('mo_numbers as mo')
        .select(
          Object.keys(columnHeaders).map((field: string) =>
            columnHeaders[field].table
              ? columnHeaders[field].table + '.' + field
              : field
          )
        )
        .leftJoin(
          knex('mo_scans as ms')
            .select(['ms.mo_id'])
            .select(
              Object.keys(pivotHeaders).map((wtId) =>
                knex.raw(
                  `max(CASE when wa.work_type_id = ${wtId} then ms.sew end) as ${wtId}_finish_scan_date`
                )
              )
            )
            /*
          knex.raw('max(CASE when wa.work_type_id = 7 then date(ms.sew) end) as 7_sew'),
          knex.raw('max(CASE when wa.work_type_id = 1 then date(ms.sew) end) as 1_sew')
          */
            .leftJoin(
              'work_area_groups as wag',
              'wag.id',
              'ms.work_area_group_id'
            )
            .leftJoin('work_areas as wa', 'wa.work_area_id', 'wag.work_area_id')
            .whereNull('ms.removed_at')
            .where('wag.update_customer', 0)
            // .where('ms.sew', '>', '2022-01-01')
            .groupBy('ms.mo_id')
            .as('last_scan'),
          'last_scan.mo_id',
          'mo.mo_id'
        )
        .leftJoin(
          knex('mo_scans as ms')
            .select(['ms.mo_id'])
            .select(
              Object.keys(pivotHeaders).map((wtId) =>
                knex.raw(
                  `sum(CASE when wa.work_type_id = ${wtId} then 1 end) as ${wtId}_open_scan_count`
                )
              )
            )
            // knex.raw('sum(CASE when wa.work_type_id = 7 then 1 end) as 7_open_count'),
            // knex.raw('sum(CASE when wa.work_type_id = 1 then 1 end) as 1_open_count')
            .leftJoin(
              'work_area_groups as wag',
              'wag.id',
              'ms.work_area_group_id'
            )
            .leftJoin('work_areas as wa', 'wa.work_area_id', 'wag.work_area_id')
            .whereNull('ms.removed_at')
            .whereNotNull('ms.sew_ready')
            .whereNull('ms.sew')
            .where('wag.update_customer', 0)
            .groupBy('ms.mo_id')
            .as('open_scans'),
          'open_scans.mo_id',
          'mo.mo_id'
        )
        .leftJoin(
          knex('work_area_tickets as wat')
            .select(['wv.mo_id'])
            .select(
              Object.keys(pivotHeaders).map((wtId) =>
                knex.raw(
                  `sum(CASE when wa.work_type_id = ${wtId} then 1 end) as ${wtId}_open_ticket_count`
                )
              )
            )
            // knex.raw('sum(CASE when wa.work_type_id = 7 then 1 end) as 7_open_ticket_count'),
            .leftJoin('work_areas as wa', 'wa.work_area_id', 'wat.work_area_id')
            .leftJoin('work_vouchers as wv', 'wv.id', 'wat.work_voucher_id')
            .whereNull('wat.finished_at')
            .groupBy('wv.mo_id')
            .as('open_tickets'),
          'open_tickets.mo_id',
          'mo.mo_id'
        )
        .where('mo.mo_status', '!=', 'Complete')
        .where('mo.mo_status', '!=', 'Void')
        .where('mo.mo_status', '!=', 'Cancelled')
        .where('mo.mo_status', '!=', 'New')
        .where('mo.mo_status', '!=', 'Forecast')
        .where('product_category', '!=', 'FORECAST')
        .orderBy('mo.mo_id')
        .toString()
    );

    console.log('processing mo data');

    if (getMoInfo[0].length > 0) {
      console.log('building excel file');
      const wb = new xl.Workbook();

      // Add Worksheets to the workbook
      const ws = wb.addWorksheet('MO_DATA');

      // Create a reusable style
      const styleHeaders = wb.createStyle({
        font: {
          bold: true,
        },
      });
      const cellStyles = {
        textCell: wb.createStyle({
          font: {
            bold: true,
          },
        }),
      };

      const columnTypeChecks = {
        number: [0, 1, 2, 3, 4, 5, 8, 9, 246],
        date: [7, 10, 12, 14],
        string: [
          6, 11, 13, 15, 16, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254,
          255,
        ],
      };

      // update column type on column headers
      for (const columnDef of getMoInfo[1]) {
        if (columnHeaders[columnDef.name]) {
          // add type
          columnHeaders[columnDef.name].columnType = columnDef.columnType;
          console.log(
            'column type',
            columnHeaders[columnDef.name].column,
            columnDef.name,
            columnDef.columnType
          );
        } else {
          console.log('column type missing', columnDef.name);
        }
      }
      /*
        [
    ColumnDefinition {
      _buf: <Buffer 01 00 00 01 04 48 00 00 02 03 64 65 66 06 76 61 72 70 72 6f 0a 77 6f 72 6b 5f 61 72 65 61 73 0a 77 6f 72 6b 5f 61 72 65 61 73 0c 77 6f 72 6b 5f 61 72 ... 301 more bytes>,
      _clientEncoding: 'utf8',
      _catalogLength: 3,
      _catalogStart: 10,
      _schemaLength: 6,
      _schemaStart: 14,
      _tableLength: 10,
      _tableStart: 21,
      _orgTableLength: 10,
      _orgTableStart: 32,
      _orgNameLength: 12,
      _orgNameStart: 56,
      characterSet: 63,
      encoding: 'binary',
      name: 'work_area_id',
      columnLength: 11,
      columnType: 3,
      flags: 16931,
      decimals: 0
    },
    */
      // start on row 2 so pivot headers can go on row 1
      let prevPivotHeader;
      let prevPivotCol;

      for (const field in columnHeaders) {
        const columnData = columnHeaders[field as keyof typeof columnHeaders];

        ws.cell(2, columnData.column)
          .string(columnData.name)
          .style(styleHeaders);
        if (columnData.width) {
          ws.column(columnData.column).setWidth(columnData.width);
        }
        if (columnData.pivotHeader) {
          if (columnData.pivotHeader != prevPivotHeader) {
            ws.cell(
              1,
              columnData.column,
              1,
              columnData.column + (columnData.pivotCount ?? 0),
              true
            )
              .string(columnData.pivotHeader)
              .style(styleHeaders);
            prevPivotCol = columnData.column;
          }
        }
        prevPivotHeader = columnData.pivotHeader;
      }
      for (const [i, rowData] of getMoInfo[0].entries()) {
        for (const field in columnHeaders) {
          const columnData = columnHeaders[field as keyof typeof columnHeaders];
          const cell = ws.cell(i + 3, columnData.column);

          if (rowData[field] == null) {
            continue;
          }
          if (!columnData.columnType) {
            cell.string(rowData[field]);
          } else if (columnTypeChecks.number.includes(columnData.columnType)) {
            cell.number(
              typeof rowData[field] == 'string'
                ? parseInt(rowData[field])
                : rowData[field]
            );
          } else if (columnTypeChecks.date.includes(columnData.columnType)) {
            cell.date(rowData[field]);
          } else {
            if (typeof rowData[field] == 'object') {
              console.log('obj field');
            }
            cell.string(rowData[field]); // first row is 3
          }
        }
      }

      const reportRunDate = new Date();

      wb.write(`planning_schedule_${reportRunDate.toISOString()}.xlsx`, res);

      return res.status(200);
    } else {
      return res.status(400);
    }

    // console.log(getMoInfo)
  } catch (e) {
    console.log(e);
  }
}

export async function reportMosActiveByLocation(req: Request, res: Response) {
  const { locations } = req.body;

  if (!locations) {
    return res.status(400).json({ message: 'locations required' });
  }

  if (!Array.isArray(locations)) {
    return res.status(400).json({ message: 'locations must be an array' });
  }

  if (locations.length == 0) {
    return res.status(400).json({ message: 'locations must not be empty' });
  }

  // check if locations is an array of strings
  if (
    !locations.every((location: any) => {
      return typeof location === 'number' || typeof location === 'string';
    })
  ) {
    return res
      .status(400)
      .json({ message: 'locations must be an array of strings' });
  }

  const reportLocations = locations as string[];

  const allTickets = [];

  const today = dayjs(new Date()).format('YYYY-MM-DD');

  try {
    for (let i = 0; i < reportLocations.length; i++) {
      const getMoInfo = (await WorkAreaTickets.query()
        .select([
          { ubicacion: 'work_inventory_bins.name' },
          { mo_order: 'mo_numbers.num' },
          'mo_numbers.quantity',
          'mo_numbers.customer',
          { estado: 'work_area_ticket_statuses.name' },
        ])
        .leftJoin(
          'work_vouchers',
          'work_area_tickets.work_voucher_id',
          'work_vouchers.id'
        )
        .leftJoin('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
        .leftJoin(
          'work_inventory_bins',
          'work_area_tickets.work_inventory_location_id',
          'work_inventory_bins.id'
        )
        .leftJoin(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .whereNull('work_area_tickets.finished_at')
        .where('work_inventory_bins.id', reportLocations[i])) as unknown as {
        ubicacion: string;
        mo_order: string;
        quantity: number;
        customer: string;
        estado: string;
      }[];

      if (getMoInfo.length > 0) {
        for (const ticket of getMoInfo) {
          allTickets.push({
            ubicacion: ticket.ubicacion,
            mo_order: ticket.mo_order,
            quantity: ticket.quantity,
            customer: ticket.customer,
            estado: ticket.estado,
          });
        }
      }
    }

    if (allTickets.length > 0) {
      const wb = new xl.Workbook();

      // Add Worksheets to the workbook
      const ws = wb.addWorksheet('Ubicación');

      // Create a reusable style
      const styleHeaders = wb.createStyle({
        font: {
          bold: true,
        },
      });

      for (const [key, value] of allTickets.entries()) {
        const keys = Object.keys(value);
        const values = Object.values(value);

        keys.forEach((_, ind) => {
          ws.cell(1, 1).string('Ubicación').style(styleHeaders);
          ws.cell(1, 2).string('MO').style(styleHeaders);
          ws.cell(1, 3).string('Cant.').style(styleHeaders);
          ws.cell(1, 4).string('Cliente').style(styleHeaders);
          ws.cell(1, 5).string('Estado').style(styleHeaders);

          ws.cell(key + 2, ind + 1).string(String(values[ind]) || 'null');
        });
      }

      wb.write(`reporte_de_ubicaciones_${today}.xlsx`, res);

      return res.status(201);
    } else {
      return res.status(500).json({ ok: false, data: [] });
    }
  } catch (error) {
    console.log(error);

    return res.status(500).json({ ok: false });
  }
}

export async function getInfoLocation(req: Request, res: Response) {
  const { id } = req.body;

  try {
    const getTicketOfLocation = await WorkAreaTickets.query()
      .select([
        { id: 'work_area_tickets.id' },
        { voucher_id: 'work_vouchers.id' },
        { voucher_type: 'work_voucher_types.name' },
        'work_area_tickets.created_at',
        { group: 'work_area_groups.name' },
        { line: 'work_area_lines.name' },
        { last_activity_status_employee: 'last_activity.first_name' },
        { last_activity_status_date: 'last_activity.updated_at' },
        'mo_numbers.mo_id',
        'mo_numbers.mo_status',
        'mo_numbers.material_date',
        'mo_numbers.mo_order',
        'mo_numbers.company_code',
        'mo_numbers.required_date',
        'mo_numbers.ItemDescription8',
        'mo_numbers.num',
        'mo_numbers.po_numbers',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.customer',
        { voucher_group: 'work_voucher_groups.name' },
        { voucher_group_id: 'work_voucher_groups.id' },
        raw(
          "CASE WHEN work_vouchers.is_repo = 1 THEN 'ES REPOSICION' ELSE 'NO ES REPOSICION' END"
        ).as('is_repo'),
        raw(
          "CASE WHEN work_vouchers.is_primary = 1 THEN 'PRINCIPAL' ELSE 'NO PRINCIPAL' END"
        ).as('is_primary'),
        'work_area_tickets.work_inventory_location_id',
        'work_area_tickets.work_voucher_id',
        fn
          .coalesce(
            WorkNotes.query()
              .select('work_notes.note')
              .orderBy('work_notes.id', 'desc')
              .where(
                'work_notes.work_area_ticket_id',
                ref('work_area_tickets.id')
              )
              .limit(1),
            'Sin comentario'
          )
          .as('last_note'),
        'last_packet.file_uuid',
        'voucher_ticket_counts.ticket_count',
        'work_area_tickets.exp_finish_date',
        'work_area_tickets.prev_work_area_id',
        'work_area_tickets.next_work_area_id',
        { voucher_plate_name: 'work_voucher_plates.name' },
        { ticket_status_name: 'work_area_ticket_statuses.name' },
        { status_global: 'work_statuses.name' },
        { bin_location_name: 'work_inventory_bins.name' },
        { next_area_name: 'next_work_area.area_name' },
        { prev_area_name: 'prev_work_area.area_name' },
      ])
      .leftJoin(
        'work_vouchers',
        'work_area_tickets.work_voucher_id',
        'work_vouchers.id'
      )
      .leftJoin('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
      .leftJoin(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .leftJoin(
        'work_voucher_groups',
        'work_vouchers.work_voucher_group_id',
        'work_voucher_groups.id'
      )
      .leftJoin(
        'work_area_groups',
        'work_area_tickets.exp_work_area_group_id',
        'work_area_groups.id'
      )
      .leftJoin(
        'work_area_lines',
        'work_area_tickets.exp_work_area_line_id',
        'work_area_lines.id'
      )
      .leftJoin(
        'work_voucher_plates',
        'work_vouchers.work_voucher_plate_id',
        'work_voucher_plates.id'
      )
      .leftJoin(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id',
        '=',
        'work_area_ticket_statuses.id'
      )
      .leftJoin(
        'work_statuses',
        'work_area_ticket_statuses.work_status_id',
        '=',
        'work_statuses.id'
      )
      .leftJoin(
        'work_inventory_bins',
        'work_area_tickets.work_inventory_location_id',
        'work_inventory_bins.id'
      )
      .leftJoin(
        raw(`work_areas next_work_area ON
        next_work_area.work_area_id =
        work_area_tickets.next_work_area_id`)
      )
      .leftJoin(
        raw(`work_areas prev_work_area ON
        prev_work_area.work_area_id =
        work_area_tickets.prev_work_area_id`)
      )
      .leftJoin(
        WorkActivityLog.query()
          .select('employees.first_name', 'work_activity_log.updated_at', {
            ticket_id: 'latest_work_activity.module_id',
          })
          .join(
            WorkActivityLog.query()
              .select('work_activity_log.module_id')
              // @ts-ignore
              .max({ max_activity_id: 'work_activity_log.id' })
              .where('work_activity_log.activity', 'TicketStatusChanged')
              .groupBy('work_activity_log.module_id')
              .as('latest_work_activity'),
            'latest_work_activity.max_activity_id',
            'work_activity_log.id'
          )
          .leftJoin(
            'employees',
            'employees.employee_id',
            'work_activity_log.employee_id'
          )
          .whereNotNull('latest_work_activity.module_id')
          .as('last_activity'),
        'last_activity.ticket_id',
        'work_area_tickets.id'
      )
      .leftJoin(
        MoVouchers.query()
          .join(
            MoVouchers.query()
              .select('mo_vouchers.mo_id')
              // @ts-ignore
              .max({ last_id: 'mo_vouchers.id' })
              .where('file_status', 'Active')
              .groupBy('mo_vouchers.mo_id')
              .as('latest_voucher'),
            'latest_voucher.last_id',
            'mo_vouchers.id'
          )
          .whereNotNull('latest_voucher.last_id')
          .as('last_packet'),
        'last_packet.mo_id',
        'work_vouchers.mo_id'
      )
      .leftJoin(
        WorkAreaTickets.query()
          .select('work_area_tickets.work_voucher_id')
          .count({ ticket_count: 'work_area_tickets.id' })
          .groupBy('work_area_tickets.work_voucher_id')
          .as('voucher_ticket_counts'),
        'voucher_ticket_counts.work_voucher_id',
        'work_area_tickets.work_voucher_id'
      )
      .whereNull('work_area_tickets.finished_at')
      .where('work_inventory_bins.id', id)
      .orderBy('work_area_tickets.created_at', 'DESC');

    const getInfoLocation = await WorkInventoryBins.query()
      .join('work_zones', 'work_inventory_bins.work_zone_id', 'work_zones.id')
      .where('work_inventory_bins.id', id)
      .select([
        'work_inventory_bins.barcode',
        'work_inventory_bins.id',
        'work_inventory_bins.isle',
        'work_inventory_bins.name',
        'work_inventory_bins.rack',
        'work_inventory_bins.shelf',
        'work_inventory_bins.work_zone_id',
        { zone: 'work_zones.name' },
      ]);

    return res.status(200).json({
      ok: true,
      tickets: getTicketOfLocation,
      infoLocation: getInfoLocation,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({ ok: false });
  }
}
