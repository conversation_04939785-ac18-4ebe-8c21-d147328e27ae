import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'repair_part_categories',
    (table: Knex.TableBuilder): void => {
      table.boolean('is_active').defaultTo(true).after('category');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(
    'repair_part_categories',
    (table: Knex.TableBuilder): void => {
      table.dropColumn('is_active');
    }
  );
}
