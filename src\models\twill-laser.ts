import { Model } from '@app/db';

export class MoTwill<PERSON>aser<PERSON>ob extends Model {
  static get tableName(): string {
    return 'mo_twill_laser_jobs';
  }

  id!: number;
  mo_id!: number;
  depricated_mo_twill_laser_job_type_id!: number;
  // TODO: should be int field in database?
  layers!: string;
  // TODO: should be int field in database?
  special_layers!: string;
  is_active!: boolean;
  finished_at!: string | Date;
  machine_id!: number;
  employee_id!: number;
  created_at!: string;
  updated_at!: string;
  depricated_sub_mo_id!: number;
  comment!: string;
  quantity!: number;
  is_repo!: boolean;
  mo_twill_laser_varsity_art_job_id!: number;
  mo_twill_laser_job_status_id!: number;
}

export class MoTwillLaserJobConsumption extends Model {
  static get tableName(): string {
    return 'mo_twill_laser_job_consumptions';
  }

  id!: number;
  mo_twill_laser_job_id!: number;
  height!: number;
  width!: number;
  quantity!: number;
  is_active!: boolean;
  created_at!: string;
  updated_at!: string;
}

export class MoTwillLaserJobType extends Model {
  static get tableName(): string {
    return 'mo_twill_laser_job_types';
  }
}

export class MoTwillLaserVarsityArtJob extends Model {
  static get tableName(): string {
    return 'mo_twill_laser_varsity_art_jobs';
  }
}

export class MoTwillLaserJobProduction extends Model {
  static get tableName(): string {
    return 'mo_twill_laser_job_productions';
  }

  id!: number;
  mo_twill_laser_job_id!: number;
  quantity!: number;
  machine_id!: number;
  employee_id!: number;
  is_active!: boolean;
  created_at!: string;
  updated_at!: string;
  comment!: string;
}

export class MoTwillLaserDecoration extends Model {
  static get tableName(): string {
    return 'mo_twill_laser_decorations';
  }

  id!: number;
  mo_id!: number;
  child_mo_id!: number;
  mo_twill_laser_job_type_id!: number;
  comment!: string;
  is_active!: boolean;
  created_at!: string;
  updated_at!: string;
}

export class MoTwillLaserJobDecoration extends Model {
  static get tableName(): string {
    return 'mo_twill_laser_job_decorations';
  }
}

export class MoTwillLaserJobStatus extends Model {
  static get tableName(): string {
    return 'mo_twill_laser_job_statuses';
  }
}
