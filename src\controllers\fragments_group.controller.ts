import type { Request, Response } from 'express';
import { fn, ref } from 'objection';

import { getPagination } from '@app/helpers/pagination';
import type { WorkFragmentShape } from '@app/models/fragment.schema';
import {
  WorkFragmentCustomFields,
  WorkFragmentLog,
  WorkFragments,
} from '@app/models/fragment.schema';
import {
  WorkFragmentGroupCustomFields,
  WorkFragmentGroupRolls,
  WorkFragmentGroupTypes,
  WorkFragmentGroups,
} from '@app/models/fragments_group.schema';
import { MoNumber, WorkAreaTickets } from '@app/models/tickets.schema';
import type { CustomField } from '@app/types';

export interface CreateFragmentsGroup {
  custom_fields: CustomField[];
  employee_id: number;
  fragment_group_type_id: number;
  work_area_id: number;
}

export async function CreateNewFragmentsGroup(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const {
    custom_fields,
    employee_id,
    fragment_group_type_id,
    work_area_id,
  }: CreateFragmentsGroup = req.body;

  if (custom_fields.length === 0) {
    return res.status(400).json({
      ok: false,
      message:
        'Error, no se encontraron campos personalizados para el grupo de fragmentos',
    });
  }

  try {
    const custom_fields_values: {
      name: string;
      value: string;
      column_name: string;
    }[] = [];

    for (const custom_field of custom_fields) {
      const { name } = custom_field;

      const searchCustomFields =
        await WorkFragmentGroupCustomFields.query().findOne({
          work_fragment_type_id: fragment_group_type_id,
          name: name,
        });

      if (!searchCustomFields) {
        return res.status(400).json({
          ok: false,
          message: `Error, el campo personalizado no existe, ${name}`,
        });
      }

      custom_fields_values.push({
        ...custom_field,
        column_name: searchCustomFields.column_name,
      });
    }

    const custom_fields_values_insert: { [column_name: string]: string } = {};

    for (const custom_field of custom_fields_values) {
      const { column_name, value } = custom_field;

      custom_fields_values_insert[`custom_${column_name}`] = value;
    }

    // Create the fragments group
    const fragmentsGroup = await WorkFragmentGroups.query().insert({
      work_fragment_group_type_id: fragment_group_type_id,
      ...custom_fields_values_insert,
    });

    //Insert log
    await WorkFragmentLog.query().insert({
      employee_id,
      work_area_id,
      work_fragment_group_id: fragmentsGroup.id,
      data: JSON.stringify({
        action: 'create',
      }),
    });

    return res.status(201).json({
      ok: true,
      fragmentsGroup,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function CreateFragmentGroupType(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { name } = req.body;

  try {
    const searchFragmentGroupType =
      await WorkFragmentGroupTypes.query().findOne({
        name,
      });

    if (searchFragmentGroupType) {
      return res.status(400).json({
        ok: false,
        message: 'Error, el tipo de fragmento ya existe',
      });
    }

    const fragmentGroupType = await WorkFragmentGroupTypes.query().insert({
      name,
    });

    return res.status(201).json({
      ok: true,
      fragmentGroupType,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function CreateFragmentGroupCustomField(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { list_values, name, type, work_fragment_group_type_id } = req.body;
  try {
    const searchFragmentCustomField =
      await WorkFragmentGroupCustomFields.query().findOne({
        work_fragment_group_type_id,
        column_name: name,
      });

    if (searchFragmentCustomField) {
      return res.status(400).json({
        ok: false,
        message: 'Error, el campo personalizado ya existe',
      });
    }

    const fragmentGroupCustomField =
      await WorkFragmentGroupCustomFields.query().insert({
        name,
        type,
        work_fragment_group_type_id,
        list_values: list_values || null,
        column_name: name,
      });

    return res.status(201).json({
      ok: true,
      fragmentGroupCustomField,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetFragmentGroups(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { fragment_group_id, page = 1, size = 10 } = req.query;

  try {
    const where: {
      fragment_group_id?: number;
    } = {};

    const fragmentCache: {
      [fragmentTypeId: number]: WorkFragmentCustomFields[];
    } = {};

    const returnFragments = [];

    if (fragment_group_id) {
      where.fragment_group_id = +fragment_group_id;
    }

    const totalFragments = await WorkFragments.query().where(
      'work_fragment_group_id',
      where.fragment_group_id
    );

    if (totalFragments.length === 0) {
      return res.status(200).json({
        ok: true,
        data: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 0,
      });
    }

    const { limit, offset, currentPage, totalItems, totalPages } =
      getPagination({
        page: +page,
        size: +size,
        count: totalFragments.length,
      });

    const fragments = await WorkFragments.query()
      .join(
        'work_fragment_types',
        'work_fragments.fragment_type_id',
        'work_fragment_types.id'
      )
      .where('work_fragments.work_fragment_group_id', where.fragment_group_id)
      .select('work_fragments.*', {
        fragment_type_name: 'work_fragment_types.name',
      })
      .limit(limit)
      .offset(offset)
      .castTo<
        ({
          fragment_type_name: string;
        } & WorkFragments)[]
      >();

    for (let i = 0; i < fragments.length; i++) {
      let customFieldCache = fragmentCache[fragments[i].fragment_type_id];

      if (!customFieldCache) {
        // Get the custom fields of the fragment
        const customFieldsData = await WorkFragmentCustomFields.query().where(
          'work_fragment_type_id',
          fragments[i].fragment_type_id
        );
        fragmentCache[fragments[i].fragment_type_id] = customFieldsData;
        customFieldCache = customFieldsData;
      }

      const customFields = customFieldCache;
      const returnFragment = {
        id: fragments[i].id,
        name: fragments[i].name,
        fragment_type_name: fragments[i].fragment_type_name,
      };

      // Add the custom fields name to the fragment
      customFields.forEach((field) => {
        const customColumnName = `custom_${field.column_name}`;
        returnFragment[field.name] = fragments[i][customColumnName];
      });
      returnFragments.push(returnFragment);
    }

    if (returnFragments.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no se encontraron fragmentos',
      });
    }

    return res.status(200).json({
      ok: true,
      currentPage,
      totalItems,
      totalPages,
      data: returnFragments,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetFragmentGroupTypes(
  _: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    // Get all fragment types
    const fragmentGroupTypes = await WorkFragmentGroupTypes.query()
      .select('id', 'name')
      .castTo<
        {
          id: number;
          name: string;
        }[]
      >();

    if (fragmentGroupTypes.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no hay tipos de fragmentos',
      });
    }

    return res.status(200).json({
      ok: true,
      data: fragmentGroupTypes,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function AddFragmentsToGroup(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const { employee_id, work_area_id, fragments, fragment_group_id } =
      req.body;

    for (let i = 0; i < fragments.length; i++) {
      const fragment = await WorkFragments.query().findOne({
        id: fragments[i],
      });

      if (fragment.work_fragment_group_id) {
        return res.status(400).json({
          ok: false,
          message: 'Error, el fragmento ya esta en un grupo',
        });
      }

      const fragmentGroup = await WorkFragmentGroups.query().findOne({
        id: fragment_group_id,
      });

      if (!fragmentGroup) {
        return res.status(400).json({
          ok: false,
          message: 'Error, el grupo de fragmentos no existe',
        });
      }

      await WorkFragments.query()
        .update({
          work_fragment_group_id: fragment_group_id,
        })
        .where('id', +fragments[i]);

      await WorkFragmentLog.query().insert({
        employee_id,
        work_area_id,
        work_fragment_id: fragments[i],
        work_fragment_group_id: fragment_group_id,
        data: JSON.stringify({
          action: 'add fragment to group',
        }),
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Fragmento agregado al grupo',
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function DeleteFragmentFromGroup(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const { employee_id, work_area_id, fragment_id } = req.body;

    if (!fragment_id || typeof fragment_id !== 'number') {
      return res.status(400).json({
        ok: false,
        message: 'Error, datos no validos para fragment_id',
      });
    }

    if (!employee_id || typeof employee_id !== 'number') {
      return res.status(400).json({
        ok: false,
        message: 'Error, datos no validos para employee_id',
      });
    }

    if (!work_area_id || typeof work_area_id !== 'number') {
      return res.status(400).json({
        ok: false,
        message: 'Error, datos no validos para work_area_id',
      });
    }

    const fragment = await WorkFragments.query().findOne({
      id: fragment_id,
    });

    if (!fragment) {
      return res.status(400).json({
        ok: false,
        message: 'Error, el fragmento no existe',
      });
    }

    if (!fragment.work_fragment_group_id) {
      return res.status(400).json({
        ok: false,
        message: 'Error, el fragmento no esta en un grupo',
      });
    }

    await WorkFragments.query()
      .update({
        work_fragment_group_id: null,
      })
      .where('id', fragment_id);

    await WorkFragmentLog.query().insert({
      employee_id,
      work_area_id,
      work_fragment_id: fragment_id,
      work_fragment_group_id: fragment.work_fragment_group_id,
      data: JSON.stringify({
        action: 'delete fragment from group',
      }),
    });

    return res.status(200).json({
      ok: true,
      message: 'Fragmento eliminado del grupo',
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function CreateRoll(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const { work_fragment_group_type_id, fragments_groups } = req.body;

    const fragmentGroupType = await WorkFragmentGroupTypes.query().findOne({
      id: work_fragment_group_type_id,
    });

    if (!fragmentGroupType) {
      return res.status(400).json({
        ok: false,
        message: 'Error, el tipo de grupo de fragmentos no existe',
      });
    }

    const roll = await WorkFragmentGroupRolls.query().insert({
      work_fragment_group_type_id,
    });

    if (fragments_groups.length > 0) {
      for (let i = 0; i < fragments_groups.length; i++) {
        const fragmentGroup = await WorkFragmentGroups.query().findOne({
          id: fragments_groups[i],
        });

        if (!fragmentGroup) {
          return res.status(400).json({
            ok: false,
            message: `Error, el grupo de fragmentos no existe ${fragments_groups[i]}`,
          });
        }

        if (
          fragmentGroup.work_fragment_group_type_id !==
          work_fragment_group_type_id
        ) {
          return res.status(400).json({
            ok: false,
            message: `Error, el grupo de fragmentos no pertenece al tipo de rollo ${fragments_groups[i]}`,
          });
        }

        if (fragmentGroup.work_fragment_group_roll_id) {
          return res.status(400).json({
            ok: false,
            message: `Error, el grupo de fragmentos ya esta en un rollo ${fragments_groups[i]}`,
          });
        }

        const getMaxSort = await WorkFragmentGroups.query()
          .select('work_fragment_group_roll_sort')
          .where('work_fragment_group_roll_id', roll.id)
          .orderBy('work_fragment_group_roll_sort', 'desc')
          .first()
          .castTo<{ work_fragment_group_roll_sort: number }>();

        await WorkFragmentGroups.query()
          .update({
            work_fragment_group_roll_id: roll.id,
            work_fragment_group_roll_sort:
              getMaxSort?.work_fragment_group_roll_sort + 100 || 100,
          })
          .where('id', fragmentGroup.id);
      }
    }

    // obtenemos el rollo creado y su tipo
    const rollData = await WorkFragmentGroupRolls.query()
      .join(
        'work_fragment_group_types',
        'work_fragment_group_rolls.work_fragment_group_type_id',
        'work_fragment_group_types.id'
      )
      .select([
        { id: 'work_fragment_group_rolls.id' },
        { type: 'work_fragment_group_types.name' },
      ])
      .where('work_fragment_group_rolls.id', roll.id)
      .first();

    return res.status(200).json({
      ok: true,
      message: 'Rollo creado',
      data: rollData,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetFragmentsGroup(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { id } = req.params;

  try {
    const fragmentGroup = await WorkFragmentGroups.query()
      .join(
        'work_fragment_group_types',
        'work_fragment_groups.work_fragment_group_type_id',
        'work_fragment_group_types.id'
      )
      .select([
        { id: 'work_fragment_groups.id' },
        { type: 'work_fragment_group_types.name' },
        {
          roll: 'work_fragment_groups.work_fragment_group_roll_id',
        },
      ])
      .where('work_fragment_groups.id', id);

    if (!fragmentGroup) {
      return res.status(400).json({
        ok: false,
        message: 'Error, el grupo de fragmentos no existe',
      });
    }

    // retornamos el grupo de fragmentos
    return res.status(200).json({
      ok: true,
      data: fragmentGroup,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function AddGroupToRoll(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { roll_id, fragment_groups } = req.body;
  try {
    // obtenemos la informacion del rollo
    const roll = await WorkFragmentGroupRolls.query().findOne({
      id: roll_id,
    });

    // verificamos que el rollo exista
    if (!roll) {
      return res.status(400).json({
        ok: false,
        message: 'Error, el rollo no existe',
      });
    }

    // verificamos q el tipo de los grupos de fragmentos sea el mismo del rollo
    for (let i = 0; i < fragment_groups.length; i++) {
      const fragmentGroup = await WorkFragmentGroups.query().findOne({
        id: fragment_groups[i],
      });

      // verificamos que el grupo de fragmentos exista
      if (!fragmentGroup) {
        return res.status(400).json({
          ok: false,
          message: `Error, el grupo de fragmentos no existe ${fragment_groups[i]}`,
        });
      }

      // verificamos que el grupo de fragmentos pertenezca al tipo de rollo
      if (
        fragmentGroup.work_fragment_group_type_id !==
        roll.work_fragment_group_type_id
      ) {
        return res.status(400).json({
          ok: false,
          message: `Error, el grupo de fragmentos no pertenece al tipo de rollo ${fragment_groups[i]}`,
        });
      }

      // verificamos que el grupo de fragmentos no este en otro rollo
      if (fragmentGroup.work_fragment_group_roll_id) {
        return res.status(400).json({
          ok: false,
          message: `Error, el grupo de fragmentos ya esta en un rollo ${fragment_groups[i]}`,
        });
      }

      // obtenemos el maximo sort del rollo
      const getMaxSort = await WorkFragmentGroups.query()
        .select('work_fragment_group_roll_sort')
        .where('work_fragment_group_roll_id', roll.id)
        .orderBy('work_fragment_group_roll_sort', 'desc')
        .first()
        .castTo<{ work_fragment_group_roll_sort: number }>();

      // actualizamos el grupo de fragmentos
      await WorkFragmentGroups.query()
        .update({
          work_fragment_group_roll_id: roll.id,
          work_fragment_group_roll_sort:
            getMaxSort?.work_fragment_group_roll_sort + 100 || 100,
        })
        .where('id', +fragment_groups[i]);
    }

    // retornamos el rollo
    return res.status(200).json({
      ok: true,
      message: 'Grupo de fragmentos agregado al rollo',
      data: roll,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function CreateGroup(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const {
      data: { work_fragment_group_type_id, fragments, roll },
      custom_fields,
    } = req.body;

    const fragmentGroupType = await WorkFragmentGroupTypes.query().findOne({
      id: work_fragment_group_type_id,
    });

    if (!fragmentGroupType) {
      return res.status(400).json({
        ok: false,
        message: 'Error, el tipo de grupo de fragmentos no existe',
      });
    }

    if (custom_fields.length > 0) {
      const custom_fields_values = [];

      for (const custom_field of custom_fields) {
        const { name, id } = custom_field;

        const searchCustomFields =
          await WorkFragmentGroupCustomFields.query().findOne({
            id: id,
          });

        if (!searchCustomFields) {
          return res.status(400).json({
            ok: false,
            message: `Error, el campo personalizado no existe, ${name}`,
          });
        }

        custom_fields_values.push({
          ...custom_field,
          column_name: searchCustomFields.column_name,
        });
      }

      // convert column_name of custom_fields_values to custom_[column_name] for the insert
      const custom_fields_values_insert: {
        [customColumnName: string]: string;
      } = {};

      for (const custom_field of custom_fields_values) {
        const { column_name, value } = custom_field;

        custom_fields_values_insert[`custom_${column_name}`] = value;
      }

      // Create the fragments group
      const group = await WorkFragmentGroups.query().insert({
        work_fragment_group_type_id,
        ...custom_fields_values_insert,
      });

      if (fragments.length > 0) {
        for (let i = 0; i < fragments.length; i++) {
          const fragment = await WorkFragments.query().findOne({
            id: fragments[i],
          });

          if (!fragment) {
            return res.status(400).json({
              ok: false,
              message: `Error, el fragmento no existe ${fragments[i]}`,
            });
          }

          if (fragment.work_fragment_group_id) {
            return res.status(400).json({
              ok: false,
              message: `Error, el fragmento ya esta en un grupo ${fragments[i]}`,
            });
          }

          await WorkFragments.query()
            .update({
              work_fragment_group_id: group.id,
            })
            .where('id', +fragments[i]);
        }
      }

      if (roll.new) {
        // se creara un rollo y se le asignara al grupo
        const newRoll = await WorkFragmentGroupRolls.query().insert({
          work_fragment_group_type_id,
        });

        await WorkFragmentGroups.query()
          .update({
            work_fragment_group_roll_id: newRoll.id,
            work_fragment_group_roll_sort: 100,
          })
          .where('id', group.id);
      }

      if (roll.value) {
        // se asignara a un rollo existente
        const rollData = await WorkFragmentGroupRolls.query().findOne({
          id: roll.value,
        });

        if (!rollData) {
          return res.status(400).json({
            ok: false,
            message: `Error, el rollo no existe ${roll.value}`,
          });
        }

        // obtenemos el maximo sort del rollo
        const getMaxSort = await WorkFragmentGroups.query()
          .select('work_fragment_group_roll_sort')
          .where('work_fragment_group_roll_id', +roll.value)
          .orderBy('work_fragment_group_roll_sort', 'desc')
          .first()
          .castTo<{ work_fragment_group_roll_sort: number }>();

        await WorkFragmentGroups.query()
          .update({
            work_fragment_group_roll_id: roll.value,
            work_fragment_group_roll_sort:
              getMaxSort?.work_fragment_group_roll_sort + 100 || 100,
          })
          .where('id', group.id);
      }

      // obtenemos la informacion del grupo creado y su tipo
      const groupData = await WorkFragmentGroups.query()
        .join(
          'work_fragment_group_types',
          'work_fragment_groups.work_fragment_group_type_id',
          'work_fragment_group_types.id'
        )
        .select([
          { id: 'work_fragment_groups.id' },
          { type: 'work_fragment_group_types.name' },
        ])
        .where('work_fragment_groups.id', group.id);

      return res.status(200).json({
        ok: true,
        message: 'Grupo creado',
        data: groupData,
      });
    }

    //? Create the fragments group
    const group = await WorkFragmentGroups.query().insert({
      work_fragment_group_type_id,
    });

    if (fragments.length > 0) {
      for (let i = 0; i < fragments.length; i++) {
        const fragment = await WorkFragments.query().findOne({
          id: fragments[i],
        });

        if (!fragment) {
          return res.status(400).json({
            ok: false,
            message: `Error, el fragmento no existe ${fragments[i]}`,
          });
        }

        if (fragment.work_fragment_group_id) {
          return res.status(400).json({
            ok: false,
            message: `Error, el fragmento ya esta en un grupo ${fragments[i]}`,
          });
        }

        await WorkFragments.query()
          .update({
            work_fragment_group_id: group.id,
          })
          .where('id', +fragments[i]);
      }
    }

    if (roll.new) {
      // se creara un rollo y se le asignara al grupo
      const newRoll = await WorkFragmentGroupRolls.query().insert({
        work_fragment_group_type_id,
      });

      await WorkFragmentGroups.query()
        .update({
          work_fragment_group_roll_id: newRoll.id,
          work_fragment_group_roll_sort: 100,
        })
        .where('id', group.id);
    }

    if (roll.value) {
      // se asignara a un rollo existente
      const rollData = await WorkFragmentGroupRolls.query().findOne({
        id: roll.value,
      });

      if (!rollData) {
        return res.status(400).json({
          ok: false,
          message: `Error, el rollo no existe ${roll.value}`,
        });
      }

      // obtenemos el maximo sort del rollo
      const getMaxSort = await WorkFragmentGroups.query()
        .select('work_fragment_group_roll_sort')
        .where('work_fragment_group_roll_id', +roll.value)
        .orderBy('work_fragment_group_roll_sort', 'desc')
        .first()
        .castTo<{ work_fragment_group_roll_sort: number }>();

      await WorkFragmentGroups.query()
        .update({
          work_fragment_group_roll_id: roll.value,
          work_fragment_group_roll_sort:
            getMaxSort?.work_fragment_group_roll_sort + 100 || 100,
        })
        .where('id', group.id);
    }

    // obtenemos la informacion del grupo creado y su tipo
    const groupData = await WorkFragmentGroups.query()
      .join(
        'work_fragment_group_types',
        'work_fragment_groups.work_fragment_group_type_id',
        'work_fragment_group_types.id'
      )
      .select([
        { id: 'work_fragment_groups.id' },
        { type: 'work_fragment_group_types.name' },
      ])
      .where('work_fragment_groups.id', group.id);

    return res.status(200).json({
      ok: true,
      message: 'Grupo creado',
      data: groupData,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetFragmentsGroupCustomFieldsByType(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { id } = req.params;

  try {
    // Get all fragments group custom fields
    const fragmentsGroupCustomFields =
      await WorkFragmentGroupCustomFields.query()
        .select('id', 'name', 'type', 'list_values')
        .where({
          work_fragment_group_type_id: id,
        });

    if (fragmentsGroupCustomFields.length === 0) {
      return res.status(204).json({
        ok: false,
        message:
          'Advertencia, no hay campos personalizados para el grupo de fragmentos',
      });
    }

    return res.status(200).json({
      ok: true,
      data: fragmentsGroupCustomFields,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetAllFragmentsInGroup(req: Request, res: Response) {
  const { id, areaID, voucherTypeID } = req.query;

  try {
    const fragmentCache = {};

    const returnFragments = [];

    // Obtener todos los fragmentos de un grupo
    const fragments = await WorkFragments.query()
      .join(
        'work_fragment_types',
        'work_fragment_types.id',
        'work_fragments.fragment_type_id'
      )
      .leftJoin('mo_numbers', 'work_fragments.mo_id', 'mo_numbers.mo_id')
      .leftJoin(
        'work_fragment_groups',
        'work_fragment_groups.id',
        'work_fragments.work_fragment_group_id'
      )
      .select([
        'work_fragments.*',
        {
          num: 'mo_numbers.num',
        },
        {
          fragment_type_name: 'work_fragment_types.name',
        },
        fn
          .coalesce(
            WorkFragmentGroupTypes.query()
              .select('name')
              .where(
                'id',
                ref('work_fragment_groups.work_fragment_group_type_id')
              )
              .limit(1),
            'Sin datos'
          )
          .as('type_group'),
      ])
      .where('work_fragment_groups.id', +id)
      .castTo<
        (WorkFragmentShape & {
          num: string;
          fragment_type_name: string;
          type_group: string;
        })[]
      >();

    for (let i = 0; i < fragments.length; i++) {
      let customFieldCache: WorkFragmentGroupCustomFields[] =
        fragmentCache[fragments[i].fragment_type_id];

      if (!customFieldCache) {
        const customFieldData =
          await WorkFragmentGroupCustomFields.query().where(
            'work_fragment_group_type_id',
            +fragments[i].fragment_type_id
          );

        fragmentCache[fragments[i].fragment_type_id] = customFieldData;
        customFieldCache = customFieldData;
      }

      const customFields = customFieldCache;
      const returnFragment = {
        id: fragments[i].id,
        mo_id: fragments[i].mo_id,
        num: fragments[i].num,
        name: fragments[i].name,
        fragment_type_name: fragments[i].fragment_type_name,
        group: fragments[i].work_fragment_group_id,
        group_type: fragments[i].type_group,
      };

      customFields.forEach((field) => {
        const customColumnName = `custom_${field.name}`;
        returnFragment[customColumnName] = fragments[i][customColumnName];
      });

      returnFragments.push(returnFragment);
    }

    if (returnFragments.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no se encontraron fragmentos',
      });
    }

    // obtener los tickets de cada fragmento por area y tipo de comprobante
    for (let i = 0; i < returnFragments.length; i++) {
      const tickets = WorkAreaTickets.query()
        .leftJoin(
          'work_vouchers',
          'work_area_tickets.work_voucher_id',
          'work_vouchers.id'
        )
        .leftJoin(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .leftJoin('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
        .leftJoin(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .leftJoin(
          'work_statuses',
          'work_area_ticket_statuses.work_status_id',
          'work_statuses.id'
        )
        .leftJoin(
          'work_inventory_bins',
          'work_area_tickets.work_inventory_location_id',
          'work_inventory_bins.id'
        )
        .leftJoin(
          'work_areas',
          'work_area_tickets.work_area_id',
          'work_areas.work_area_id'
        )
        .select([
          'work_area_tickets.id',
          { ticketStatus: 'work_area_ticket_statuses.name' },
          { ticketLocation: 'work_inventory_bins.name' },
          { mo: 'mo_numbers.num' },
          { voucherType: 'work_voucher_types.name' },
          {
            ticketArea: 'work_areas.area_name',
          },
        ])
        .where('work_vouchers.mo_id', +returnFragments[i].mo_id);

      if (+areaID) {
        tickets.where('work_area_tickets.work_area_id', +areaID);
      }

      if (+voucherTypeID) {
        tickets.where('work_vouchers.work_voucher_type_id', +voucherTypeID);
      }

      const resultTickets = await tickets;

      returnFragments[i].tickets = resultTickets;
    }

    return res.status(200).json({
      ok: true,
      data: returnFragments,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      ok: false,
      message: `Error, ${error}`,
    });
  }
}

export async function GetFragmentsGroupCustomFields(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { id } = req.params;

  try {
    // Get type of group
    const groupType = await WorkFragmentGroups.query().where('id', id).first();

    // Get all fragments group custom fields
    const fragmentsGroupCustomFields =
      await WorkFragmentGroupCustomFields.query()
        .select('id', 'column_name', 'name', 'type', 'list_values')
        .where({
          work_fragment_group_type_id: groupType.work_fragment_group_type_id,
        });

    if (fragmentsGroupCustomFields.length === 0) {
      return res.status(204).json({
        ok: false,
        message:
          'Advertencia, no hay campos personalizados para el grupo de fragmentos',
      });
    }

    const returnFragmentsGroupCustomFields = [];

    fragmentsGroupCustomFields.forEach((field) => {
      const customColumnName = `custom_${field.column_name}`;

      returnFragmentsGroupCustomFields.push({
        id: field.id,
        name: field.name,
        value: groupType[customColumnName],
        list_values: field.list_values,
        column_name: field.column_name,
      });
    });

    return res.status(200).json({
      ok: true,
      data: returnFragmentsGroupCustomFields,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetInfoOfFragmentsGroup(req: Request, res: Response) {
  const { id } = req.params;
  try {
    // obtenemos la informacion del grupo de fragmentos
    const group = await WorkFragmentGroups.query()
      .join(
        'work_fragment_group_types',
        'work_fragment_groups.work_fragment_group_type_id',
        'work_fragment_group_types.id'
      )
      .select([
        { id: 'work_fragment_groups.id' },
        { type: 'work_fragment_group_types.name' },
        { roll: 'work_fragment_groups.work_fragment_group_roll_id' },
      ])
      .where('work_fragment_groups.id', id)
      .first();

    if (!group) {
      return res.status(400).json({
        ok: false,
        message: 'Error, el grupo de fragmentos no existe',
      });
    }

    return res.status(200).json({
      ok: true,
      data: group,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: `Error, ${error}`,
    });
  }
}

export async function UpdateFragmentsGroup(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { id } = req.params;

  const { custom_fields, roll } = req.body;

  if (
    !custom_fields ||
    !Array.isArray(custom_fields) ||
    custom_fields.length === 0
  ) {
    return res.status(400).json({
      ok: false,
      message: 'Error, datos no validos',
    });
  }
  const useCustomFields: CustomField[] = custom_fields;

  try {
    if (roll) {
      if (isNaN(Number(roll))) {
        return res.status(400).json({
          ok: false,
          message: 'Error, datos no validos',
        });
      }
      const work_fragment_group_roll_id = Number(roll);
      // buscamos la informacion del rollo
      const rollData = await WorkFragmentGroupRolls.query().findOne({
        id: roll,
      });

      if (!rollData) {
        return res.status(400).json({
          ok: false,
          message: 'Error, el rollo no existe',
        });
      }

      // obtenemos la informacion del grupo de fragmentos
      const group = await WorkFragmentGroups.query().where('id', id).first();

      if (!group) {
        return res.status(400).json({
          ok: false,
          message: 'Error, el grupo de fragmentos no existe',
        });
      }

      // verificamos que el tipo de los grupos de fragmentos sea el mismo del rollo
      if (
        group.work_fragment_group_type_id !==
        rollData.work_fragment_group_type_id
      ) {
        // obtenemos el nombre del tipo de grupo de fragmentos
        const groupTypeName = await WorkFragmentGroupTypes.query()
          .select('name')
          .where('id', group.work_fragment_group_type_id)
          .first()
          .castTo<{ name: string }>();

        return res.status(400).json({
          ok: false,
          message: `Error, el grupo de fragmentos no pertenece al tipo de rollo: "${groupTypeName?.name}"`,
        });
      }

      // verificamos que el grupo de fragmentos no este en otro rollo
      if (group.work_fragment_group_roll_id) {
        // log
      }

      // obtenemos el maximo sort del rollo
      const getMaxSort = await WorkFragmentGroups.query()
        .select('work_fragment_group_roll_sort')
        .where('work_fragment_group_roll_id', work_fragment_group_roll_id)
        .orderBy('work_fragment_group_roll_sort', 'desc')
        .first()
        .castTo<{ work_fragment_group_roll_sort: number }>();

      // actualizamos el grupo de fragmentos
      await WorkFragmentGroups.query()
        .update({
          work_fragment_group_roll_id: roll,
          work_fragment_group_roll_sort:
            getMaxSort?.work_fragment_group_roll_sort + 100 || 100,
        })
        .where('id', +id);
    }

    //  Get values the custom fields
    const custom_fields_values = useCustomFields.reduce((a, field) => {
      return {
        ...a,
        ['custom_' + field.name]: field.value || null,
      };
    }, {});

    // Update the fragments group
    await WorkFragmentGroups.query()
      .update({
        ...custom_fields_values,
      })
      .where({
        id,
      });

    // obtenemos el grupo
    const group = await WorkFragmentGroups.query()
      .join(
        'work_fragment_group_types',
        'work_fragment_groups.work_fragment_group_type_id',
        'work_fragment_group_types.id'
      )
      .select([
        { id: 'work_fragment_groups.id' },
        { type: 'work_fragment_group_types.name' },
        { typeID: 'work_fragment_group_types.id' },
      ])
      .where('work_fragment_groups.id', id)
      .first()
      .castTo<{
        id: number;
        type: string;
        typeID: number;
      }>();

    return res.status(200).json({
      ok: true,
      message: 'Se actualizo el grupo de fragmentos',
      data: group,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetAllFragmentsGroups(_: Request, res: Response) {
  try {
    // obtenemos todos los grupos de fragmentos
    const groups = await WorkFragmentGroups.query()
      .join(
        'work_fragment_group_types',
        'work_fragment_groups.work_fragment_group_type_id',
        'work_fragment_group_types.id'
      )
      .select([
        { id: 'work_fragment_groups.id' },
        { type: 'work_fragment_group_types.name' },
        { created_at: 'work_fragment_groups.created_at' },
        WorkFragments.query()
          .where(
            'work_fragment_groups.id',
            ref('work_fragments.work_fragment_group_id')
          )
          .count()
          .as('fragments_count'),
      ]);

    // verificamos que existan grupos de fragmentos
    if (!groups) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no hay grupos de fragmentos',
      });
    }

    return res.status(200).json({
      ok: true,
      data: groups,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: `Error, ${error}`,
    });
  }
}

export async function GetAllFragmentsGroupsByMO(req: Request, res: Response) {
  const { mo } = req.params;

  try {
    // obtenemos el mo_id de la mo
    const moID = await MoNumber.query()
      .select('mo_id')
      .where('mo_id', 'like', `${mo}%`)
      .orWhere('mo_barcode', 'like', `${mo}%`)
      .castTo<{ mo_id: number }[]>();

    if (moID.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, la orden de manufactura no existe',
      });
    }

    // obtenemos todos los fragmentos de la mo
    const fragments = await WorkFragments.query()
      .join(
        'work_fragment_types',
        'work_fragment_types.id',
        'work_fragments.fragment_type_id'
      )
      .leftJoin('mo_numbers', 'work_fragments.mo_id', 'mo_numbers.mo_id')
      .leftJoin(
        'work_fragment_groups',
        'work_fragment_groups.id',
        'work_fragments.work_fragment_group_id'
      )
      .select([
        'work_fragments.id',
        'work_fragments.mo_id',
        {
          fragment_type_name: 'work_fragment_types.name',
        },
        {
          group: 'work_fragments.work_fragment_group_id',
        },
      ])
      .where('work_fragments.mo_id', +moID[0].mo_id)
      .castTo<
        {
          id: number;
          mo_id: number;
          fragment_type_name: string;
          group: number;
        }[]
      >();

    // buscamos los grupos de fragmentos donde estan los fragmentos
    const groups = await WorkFragmentGroups.query()
      .join(
        'work_fragment_group_types',
        'work_fragment_groups.work_fragment_group_type_id',
        'work_fragment_group_types.id'
      )
      .select([
        { id: 'work_fragment_groups.id' },
        { type: 'work_fragment_group_types.name' },
        { created_at: 'work_fragment_groups.created_at' },
      ])
      .whereIn(
        'work_fragment_groups.id',
        fragments.map((fragment) => fragment.group)
      )
      .castTo<
        {
          id: number;
          type: string;
          created_at: Date;
        }[]
      >();

    return res.status(200).json({
      ok: true,
      data: groups,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: `Error, ${error}`,
    });
  }
}
