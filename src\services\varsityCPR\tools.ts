import type { LineItemExpected, LineItemInfo, FilePart } from './config';

const xBuffer = 0.2;
const pdfMagicNumber = 22.2281951;
const addSpaceWidth = 0.35;

const getTextPdfWidth = (part: FilePart) => {
  return part.w / pdfMagicNumber;
};

export const getRowString = (parts: FilePart[]) => {
  const addBarWidth = 3;
  let rowString = '';
  let prevPart: FilePart | null = null;
  for (const [partIndex, part] of parts.entries()) {
    let addingBar = false;
    const lastPartRightX = prevPart
      ? prevPart.x + getTextPdfWidth(prevPart)
      : null;
    if (lastPartRightX && part.x > lastPartRightX + addBarWidth) {
      rowString += '|';
      addingBar = true;
    }
    if (partIndex > 0 && !addingBar) {
      rowString += '~';
    }
    rowString += part.text;
    prevPart = part;
  }
  return rowString;
};

export const minMaxExpected = (expected: LineItemExpected) => {
  if (expected.min && expected.max) {
    return [expected.min, expected.max];
  }
  if (expected.x && expected.buffer) {
    return [expected.x - expected.buffer, expected.x + expected.buffer];
  }
  return [expected.x - xBuffer, expected.x + xBuffer];
};

export const minExpected = (expected: LineItemExpected) => {
  const [min] = minMaxExpected(expected);
  return min;
};

export const maxExpected = (expected: LineItemExpected) => {
  const [, max] = minMaxExpected(expected);
  return max;
};

export const combineTillNext = (
  parts: FilePart[],
  expecteds: LineItemExpected[],
  curPartIndex: number,
  curExpected: LineItemExpected,
  lineTypeInfo: LineItemInfo
) => {
  const curPart = parts[curPartIndex];

  // console.log('combine till next', curExpected, curPartIndex);
  if (!curExpected || !curExpected.combineTillNext) {
    return { combineString: curPart.text, curPartIndex: curPartIndex };
  }
  // get next parts until parts x value is greater than next expected x value
  let nextExpected: LineItemExpected | null = null;
  for (let x = 0; x < expecteds.length; x++) {
    const checkExpected = expecteds[x];
    if (checkExpected.key === curExpected.key) {
      continue;
    }
    if (
      checkExpected.x > curExpected.x &&
      checkExpected.key != curExpected.key
    ) {
      nextExpected = checkExpected;
      break;
    }
  }
  const nextMin = nextExpected ? minExpected(nextExpected) : null;

  // get parts to combine
  let checkPartIndex = curPartIndex;
  const useParts: FilePart[] = [];
  for (
    checkPartIndex = curPartIndex;
    checkPartIndex < parts.length;
    checkPartIndex++
  ) {
    const checkPart = parts[checkPartIndex];

    if (
      lineTypeInfo &&
      lineTypeInfo.ignorePartFunc &&
      lineTypeInfo.ignorePartFunc(checkPart)
    ) {
      continue;
    }

    // check if hitting next expected, if so break
    if (nextMin && checkPart.x >= nextMin) {
      // console.log('breaking', checkPart.x, nextMin);
      checkPartIndex--;
      break;
    }
    useParts.push(checkPart);
  }

  // remove repeats if needed
  // console.log('useParts', JSON.stringify(useParts));
  if (curExpected.ignoreRepeat) {
    // console.log('checking for repeats');
    let checkIndex = 1;
    while (checkIndex < useParts.length) {
      const checkPart = useParts[checkIndex];
      const lastPart = useParts[checkIndex - 1];
      if (checkPart.text.trim() === lastPart.text.trim()) {
        // console.log('removing repeat', checkPart.text.trim());
        useParts.splice(checkIndex, 1);
        continue;
      }
      // check for double repeats
      if (checkIndex >= 3) {
        const lastLastPart = useParts[checkIndex - 2];
        const lastLastLastPart = useParts[checkIndex - 3];
        if (
          checkPart.text.trim() === lastLastPart.text.trim() &&
          lastPart.text.trim() === lastLastLastPart.text.trim()
        ) {
          // console.log('removing double repeat', checkPart.text, lastPart.text);
          useParts.splice(checkIndex - 1, 2);
          checkIndex--;
          continue;
        }
      }
      checkIndex++;
    }
  }

  // combine parts
  let lastPartEndingX = null;
  let combineString = '';
  for (const checkPart of useParts) {
    // should we add a space manually?
    const space = lastPartEndingX ? checkPart.x - lastPartEndingX : null;
    lastPartEndingX = checkPart.x + getTextPdfWidth(checkPart);
    // console.log('checkpart', checkPart.text, checkPart.x, space);

    if (space && space > addSpaceWidth && !curExpected.trim) {
      // console.log('adding space', space);
      combineString += ' ';
    }

    combineString += curExpected.trim ? checkPart.text.trim() : checkPart.text;
  }

  // return combined string and updated part index
  // console.log('Combined String:', combineString, checkPartIndex);
  return { combineString, curPartIndex: checkPartIndex };
};
