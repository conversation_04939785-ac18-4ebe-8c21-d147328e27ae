import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('plotter_rolls', (table: Knex.TableBuilder) => {
    table.timestamp('checkout_at').nullable().defaultTo(null);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('plotter_rolls', (table: Knex.TableBuilder) => {
    table.dropColumn('checkout_at');
  });
}
