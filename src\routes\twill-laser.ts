import { Router } from 'express';

import {
  addConsumptionToOrder,
  addConsumptionsToOrder,
  assignOrdersToEmployee,
  deleteConsumptionToOrder,
  deleteOrderAssignment,
  editCommentOfOrder,
  getInfoOfConsumption,
  getJobTypes,
  getOrderInfo,
  getOrdersWithoutConsumption,
} from '@app/controllers/twill-laser';

const laserRouter = Router();

// laserRouter.route('/').post(createMoTwillLaserJob).get(getOrdersTwillLaser);
// laserRouter
//   .route('/:id')
//   .delete(deleteTwillLaserJob)
//   .get(getMOTwillLaserJobsByMain)
//   .patch(editMoTwillLaserJobById);
// laserRouter
//   .route('/consumptions/:id')
//   .get(getTwillLaserJobConsumptions)
//   .patch(editTwillLaserConsumptionById)
//   .delete(deleteJobTwillLaserConsumption);
// laserRouter.route('/consumptions/complete/:id').patch(completeTwillLaserJob);
// laserRouter
//   .route('/consumptions/reports/machine/:id')
//   .get(getReportsTwillLaserConsumptions);
laserRouter.route('/orders').post(getOrderInfo);
laserRouter.route('/orders/assign').post(assignOrdersToEmployee);
laserRouter
  .route('/orders/without-consumption/:id')
  .get(getOrdersWithoutConsumption);
laserRouter.route('/orders/:id/consumption').post(addConsumptionToOrder);
laserRouter.route('/orders/:id/consumptions').post(addConsumptionsToOrder);
laserRouter.route('/orders/consumption/edit').post(getInfoOfConsumption);
laserRouter.route('/orders/consumptions/:id').delete(deleteConsumptionToOrder);
laserRouter.route('/orders/assign/:id').delete(deleteOrderAssignment);
laserRouter.route('/orders/consumptions/:id/comment').put(editCommentOfOrder);
laserRouter.route('/job/types').get(getJobTypes);
// laserRouter.route('/jobs/mo/:id').get(getOrderWithInfo);
// laserRouter.route('/jobs/production/:id').post(addProductionToJob);
// laserRouter.route('/jobs/production/reposition/:id').post(addRepositionToJob);
// laserRouter.route('/jobs/decorations/:id').post(addNewDecoration);
// laserRouter.route('/jobs/decorations/:id').delete(deleteDecoration);
// laserRouter.route('/jobs/consumptions/:id').post(addConsumptionToJob);
// laserRouter.route('/jobs/productions/:id').delete(deleteProduction);
// laserRouter.route('/jobs/productions/employee/:id').get(productionsByEmployee);

export { laserRouter };
