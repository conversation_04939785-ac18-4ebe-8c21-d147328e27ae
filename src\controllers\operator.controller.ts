import type { Request, Response } from 'express';

const { Operators, WorkAreas } = require('../models/tickets.schema');

export async function getInfoOperator(req: Request, res: Response) {
  const { operatorBarcode } = req.body;

  try {
    /* verificar si el operador existe en la tabla de workgroups, si existe verificar si en workareas el campo disable_date esta NUL,
    Esta validacion se hace con el fin de mostrar informacion de tickets o informacion solo de escaneos (mo_scan)
    */

    let ticket_view = false;
    // buscar en work area groups por medio de barcode si existe buscar ese id en work area
    const getWorkAreaDisableDate = await WorkAreas.query()
      .join(
        'work_area_groups',
        'work_areas.work_area_id',
        '=',
        'work_area_groups.work_area_id'
      )
      .where('work_area_groups.barcode', operatorBarcode)
      .where('work_areas.work_status_id', 50)
      .select('work_areas.disabled_date');

    if (
      getWorkAreaDisableDate.length > 0 &&
      getWorkAreaDisableDate[0].disabled_date === null
    ) {
      ticket_view = true;
    }

    // buscar en work area groups por medio de barcode si existe buscar ese id en work area para obtener area name y customer code 0. Sino existe retornar Falso
    const getWorkArea = await WorkAreas.query()
      .join(
        'work_area_groups',
        'work_areas.work_area_id',
        '=',
        'work_area_groups.work_area_id'
      )
      .join('work_types', 'work_areas.work_type_id', '=', 'work_types.id')
      .where('work_area_groups.barcode', operatorBarcode)
      .where('work_areas.work_status_id', 50)
      .select(
        'work_area_groups.name as groupname',
        'work_types.name',
        'work_area_groups.id'
      );

    if (getWorkArea.length > 0) {
      return res.status(200).json({
        ok: true,
        operator_name: getWorkArea[0].groupname,
        task: getWorkArea[0].name,
        group: getWorkArea[0].id,
        company_code: 0,
        ticket_view,
      });
    } else {
      const getOperatorInfo = await Operators.query()
        .where('barcode', operatorBarcode)
        .where('operator_status', 1)
        .select('operator_name', 'task', 'client');

      if (getOperatorInfo.length > 0) {
        switch (getOperatorInfo[0].client.toUpperCase()) {
          case 'VARSITY':
            return res.status(200).json({
              ok: true,
              operator_name: getOperatorInfo[0].operator_name,
              task: getOperatorInfo[0].task,
              company_code: 3,
              ticket_view,
            });

          case 'ADIDAS':
            return res.status(200).json({
              ok: true,
              operator_name: getOperatorInfo[0].operator_name,
              task: getOperatorInfo[0].task,
              company_code: 2,
              ticket_view,
            });

          case 'VARPRO':
            return res.status(200).json({
              ok: true,
              operator_name: getOperatorInfo[0].operator_name,
              task: getOperatorInfo[0].task,
              company_code: 1,
              ticket_view,
            });

          default:
            return res.status(200).json({
              ok: false,
              data: 'No hay información para el operador 3',
            });
        }
      } else {
        return res.status(200).json({
          ok: false,
          data: 'No hay información para el operador 3',
        });
      }
    }
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}
