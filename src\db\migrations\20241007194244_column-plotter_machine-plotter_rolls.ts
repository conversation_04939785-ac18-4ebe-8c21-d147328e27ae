import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('plotter_rolls', (table: Knex.TableBuilder) => {
    table.integer('plotter_machine_number').nullable().defaultTo(null);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('plotter_rolls', (table: Knex.TableBuilder) => {
    table.dropColumn('plotter_machine_number');
  });
}
