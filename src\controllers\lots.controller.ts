import dayjs from 'dayjs';
import type { Request, Response } from 'express';
import { ref } from 'objection';

import { Employee } from '@app/models/employee.schema';
import {
  WorkActivityLog,
  WorkAreaBatches,
  WorkAreaTickets,
  WorkVoucherGroups,
  WorkVouchers,
} from '@app/models/tickets.schema';
import { getLotsByAreaWithTicketCount } from '@app/services/lots';

export async function getLots(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const { area: areaId }: { area: number } = req.body;

    const lots = await getLotsByAreaWithTicketCount(areaId);

    return res.status(200).json({
      ok: true,
      lots,
    });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function createNewLot(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const {
    name,
    description,
    codeEmployee,
    area,
  }: { name: string; description: string; codeEmployee: number; area: number } =
    req.body;

  try {
    const searchNameLot = await WorkAreaBatches.query()
      .where('name', name)
      .where('work_area_id', area);

    if (searchNameLot.length > 0) {
      return res
        .status(200)
        .json({ ok: false, data: 'Ya existe un lote con ese nombre' });
    }

    const dataNewLot = {
      name,
      description,
      work_area_id: area,
    };

    const newLot = await WorkAreaBatches.query().insert(dataNewLot);

    if (newLot) {
      const lotLog = await WorkActivityLog.query().insert({
        work_area_id: area,
        employee_id: codeEmployee,
        module_name: 'Lot',
        module_id: newLot.id,
        activity: 'LotCreated',
        data: JSON.stringify({}),
      });

      if (lotLog) {
        return res.status(200).json({
          ok: true,
          data: { id: newLot.id, name, description },
        });
      } else {
        return res.status(400).json({
          ok: false,
          message: `No se agrego el log de actividad al lote ${newLot.id} - ${name}`,
        });
      }
    } else {
      return res.status(400).json({ ok: false, message: 'No se creo el lote' });
    }
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function getLogOfLot(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const { id }: { id: number } = req.body;

    const getLogs = await WorkActivityLog.query()
      .select(
        'work_activity_log.id',
        'work_activity_log.activity',
        { moduleId: 'work_activity_log.module_id' },
        'work_activity_log.data',
        'work_activity_log.created_at',
        Employee.query()
          .where('employees.employee_id', ref('work_activity_log.employee_id'))
          .select('employees.first_name')
          .as('userName')
      )
      .where('work_activity_log.module_name', 'Lot')
      .where('work_activity_log.module_id', id)
      .distinct('work_activity_log.activity')
      .orderBy('created_at', 'desc');

    return res.status(200).json({
      ok: true,
      data: getLogs,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function getTicketsOfLot(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { id }: { id: number } = req.body;

  try {
    const getVouchers = await WorkVouchers.query()
      .join(
        'work_area_tickets',
        'work_vouchers.id',
        'work_area_tickets.work_voucher_id'
      )
      .leftJoin(
        'work_area_batches',
        'work_area_tickets.work_batch_id',
        'work_area_batches.id'
      )
      .leftJoin(
        'work_areas',
        'work_area_tickets.work_area_id',
        'work_areas.work_area_id'
      )
      .leftJoin(
        'work_voucher_types',
        'work_vouchers.work_voucher_type_id',
        'work_voucher_types.id'
      )
      .leftJoin(
        'work_area_ticket_statuses',
        'work_area_tickets.work_area_ticket_status_id ',
        'work_area_ticket_statuses.id'
      )
      .join('mo_numbers', 'work_vouchers.mo_id', '=', 'mo_numbers.mo_id')
      .leftJoin(
        'work_voucher_groups',
        'work_vouchers.work_voucher_group_id',
        'work_voucher_groups.id'
      )
      .select([
        { voucherId: 'work_vouchers.id' },
        { ticketId: 'work_area_tickets.id' },
        { creado: 'work_area_tickets.created_at' },
        { areaName: 'work_areas.area_name' },
        { voucherType: 'work_voucher_types.name' },
        { nameGroup: 'work_voucher_groups.name' },
        { ticketStatus: 'work_area_ticket_statuses.name' },
        'mo_numbers.mo_id',
        'mo_numbers.mo_status',
        'mo_numbers.material_date',
        'mo_numbers.mo_order',
        'mo_numbers.required_date',
        'mo_numbers.ItemDescription8',
        'mo_numbers.num',
        'mo_numbers.po_numbers',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.customer',
      ])
      .where('work_area_tickets.work_batch_id', id)
      .whereNull('work_area_batches.finished_at');

    return res.status(200).json({
      ok: true,
      data: getVouchers,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function deleteTicketsToLot(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const {
    tickets,
    lot,
    codeEmployee,
    area,
  }: { tickets: number[]; lot: string; codeEmployee: number; area: string } =
    req.body;

  try {
    const ticketsError: number[] = [];
    const ticketsSuccess: number[] = [];

    for (let i = 0; i < tickets.length; i++) {
      const searchTicketInfo = await WorkAreaTickets.query()
        .where('work_area_tickets.id', tickets[i])
        .where('work_area_tickets.work_batch_id', lot);

      if (searchTicketInfo.length > 0) {
        const dataBatch = {
          work_batch_id: null,
        };
        const updateVoucher = await WorkAreaTickets.query()
          .update(dataBatch)
          .where('work_area_tickets.id', tickets[i]);

        if (updateVoucher) {
          const ticketLog = await WorkActivityLog.query().insert({
            // TODO: should check these numbers before hand
            work_area_id: Number(area),
            employee_id: codeEmployee,
            module_name: 'Lot',
            module_id: Number(lot),
            activity: 'DeletedTicketToLot',
            data: JSON.stringify({ value: tickets[i] }),
          });

          if (ticketLog) {
            ticketsSuccess.push(tickets[i]);
          }
        } else {
          ticketsError.push(tickets[i]);
        }
      } else {
        ticketsError.push(tickets[i]);
      }
    }

    return res.status(200).json({
      ok: true,
      ticketsError,
      ticketsSuccess,
    });
  } catch (error) {
    return res.status(500).json({ ok: false });
  }
}

export async function getAllLots(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const { workAreaId } = req.query;

    if (!workAreaId) {
      return res.status(400).json({
        ok: false,
        message: 'El area es obligatoria',
      });
    }

    const getLots = await WorkAreaBatches.query()
      .where('work_area_id', +workAreaId)
      .whereNull('work_area_batches.finished_at')
      .select('work_area_batches.name', 'work_area_batches.id');

    return res.status(200).json({
      ok: true,
      data: getLots,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function convertLotToVoucherGroup(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    const { id, codeEmployee }: { id: number; codeEmployee: number } = req.body;
    const format = 'YYYY-MM-DD HH:mm:ss';
    const date = new Date();

    // get info to lot
    const infoBatch = await WorkAreaBatches.query()
      .where('id', id)
      .select('name', 'work_area_id')
      .castTo<
        {
          name: string;
          work_area_id: number;
        }[]
      >();

    // get all tickets to lot
    const getAllTicketsToBatch = await WorkVouchers.query()
      .join(
        'work_area_tickets',
        'work_vouchers.id',
        'work_area_tickets.work_voucher_id'
      )
      .where('work_area_tickets.work_batch_id', id)
      .select(
        'work_area_tickets.id as work_area_ticket_id',
        'work_vouchers.id',
        'work_vouchers.work_voucher_group_id'
      )
      .castTo<
        {
          id: number;
          work_voucher_group_id: number;
          work_area_ticket_id: number;
        }[]
      >();

    // delete all tickets to lot
    for (let i = 0; i < getAllTicketsToBatch.length; i++) {
      const searchTicketInfo = await WorkAreaTickets.query()
        .where(
          'work_area_tickets.id',
          getAllTicketsToBatch[i].work_area_ticket_id
        )
        .where('work_area_tickets.work_batch_id', id);

      if (searchTicketInfo.length > 0) {
        const updateVoucher = await WorkAreaTickets.query()
          .update({
            work_batch_id: null,
          })
          .where(
            'work_area_tickets.id',
            getAllTicketsToBatch[i].work_area_ticket_id
          );

        if (updateVoucher) {
          await WorkActivityLog.query().insert({
            work_area_id: infoBatch[0].work_area_id,
            employee_id: codeEmployee,
            module_name: 'Lot',
            module_id: id,
            activity: 'DeletedTicketToLot',
            data: JSON.stringify({
              value: getAllTicketsToBatch[i].work_area_ticket_id,
            }),
          });
        }
      }
    }

    // delete batch
    await WorkAreaBatches.query()
      .update({
        finished_at: dayjs(date).format(format),
      })
      .where('id', id);

    await WorkActivityLog.query().insert({
      work_area_id: infoBatch[0].work_area_id,
      employee_id: codeEmployee,
      module_name: 'Lot',
      module_id: id,
      activity: 'DeletedBatch',
      data: JSON.stringify({}),
    });

    // create new voucher group
    const newVoucherGroup = await WorkVoucherGroups.query().insert({
      name: infoBatch[0].name,
      employee_id_ref: codeEmployee,
      work_area_id_ref: infoBatch[0].work_area_id,
    });

    if (!newVoucherGroup) {
      return res
        .status(500)
        .json({ ok: false, message: 'No se creo el grupo de voucher' });
    }

    const voucherGroupLog = await WorkActivityLog.query().insert({
      work_area_id: infoBatch[0].work_area_id,
      employee_id: codeEmployee,
      module_name: 'voucherGroup',
      module_id: newVoucherGroup.id,
      activity: 'VoucherGroupCreated',
      data: JSON.stringify({}),
    });

    if (!voucherGroupLog) {
      return res.status(500).json({
        ok: false,
        message: 'No se agrego el log de actividad',
      });
    }

    const addBarcodeToVoucherGroup = await WorkVoucherGroups.query()
      .update({
        barcode: `MEGV${newVoucherGroup.id}`,
      })
      .where('id', newVoucherGroup.id);

    if (!addBarcodeToVoucherGroup) {
      return res.status(500).json({
        ok: false,
        message: 'No se agrego el codigo de barras al grupo de vocuher',
      });
    }

    // add tickets to new voucher group
    for (let i = 0; i < getAllTicketsToBatch.length; i++) {
      if (+getAllTicketsToBatch[i].work_voucher_group_id > 0) {
        const searchLastOrderNumber = await WorkVouchers.query()
          .where('work_vouchers.work_voucher_group_id', newVoucherGroup.id)
          .select('work_voucher_group_sort')
          .orderBy('work_voucher_group_sort', 'DESC')
          .first()
          .castTo<{
            work_voucher_group_sort: number;
          }>();

        // cambio de grupo
        const searchVoucherInfo = await WorkVoucherGroups.query()
          .join(
            'work_vouchers',
            'work_voucher_groups.id',
            'work_vouchers.work_voucher_group_id'
          )
          .where('work_vouchers.id', getAllTicketsToBatch[i].id)
          .whereNotNull('work_vouchers.work_voucher_group_id')
          .select(['work_voucher_groups.name'])
          .castTo<
            {
              name: string;
            }[]
          >();

        await WorkVouchers.query()
          .update({
            work_voucher_group_id: newVoucherGroup.id,
            work_voucher_group_sort:
              +searchLastOrderNumber?.work_voucher_group_sort + 1 || 1,
          })
          .where('work_vouchers.id', getAllTicketsToBatch[i].id);

        await WorkActivityLog.query().insert({
          work_voucher_id: getAllTicketsToBatch[i].id,
          work_area_id: infoBatch[0].work_area_id,
          employee_id: codeEmployee,
          module_name: 'voucherGroup',
          module_id: getAllTicketsToBatch[i].work_voucher_group_id,
          activity: 'MovedVoucherToGroup',
          data: JSON.stringify({
            oldGroup: searchVoucherInfo[0].name,
            newGroup: infoBatch[0].name,
          }),
        });
      } else {
        const searchLastOrderNumber = await WorkVouchers.query()
          .where('work_vouchers.work_voucher_group_id', newVoucherGroup.id)
          .select('work_voucher_group_sort')
          .orderBy('work_voucher_group_sort', 'DESC')
          .first()
          .castTo<{
            work_voucher_group_sort: number;
          }>();

        await WorkVouchers.query()
          .update({
            work_voucher_group_id: newVoucherGroup.id,
            work_voucher_group_sort:
              +searchLastOrderNumber?.work_voucher_group_sort + 1 || 1,
          })
          .where('work_vouchers.id', getAllTicketsToBatch[i].id);

        await WorkActivityLog.query().insert({
          work_voucher_id: getAllTicketsToBatch[i].id,
          work_area_id: infoBatch[0].work_area_id,
          employee_id: codeEmployee,
          module_name: 'voucherGroup',
          module_id: newVoucherGroup.id,
          activity: 'AddedVoucherToGroup',
          data: JSON.stringify({
            value: getAllTicketsToBatch[i].id,
          }),
        });
      }
    }

    // return
    return res.status(200).json({
      ok: true,
      newVoucherGroup: newVoucherGroup.id,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
    });
  }
}

export async function convertGroupToLot(req: Request, res: Response) {
  try {
    const {
      areaEmployee,
      codeEmployee,
      id: voucher_group_id,
    }: { areaEmployee: number; codeEmployee: number; id: number } = req.body;

    // TODO: agregar transaccion
    // GET info vouchers group
    const infoVoucherGroup = await WorkVoucherGroups.query()
      .where('id', voucher_group_id)
      .select(['id', 'name'])
      .castTo<{ id: number; name: string }[]>();

    // GET all vouchers from group
    const getAllVouchersFromGroup = await WorkVouchers.query()
      .join(
        'work_area_tickets',
        'work_vouchers.id',
        'work_area_tickets.work_voucher_id'
      )
      .where('work_vouchers.work_voucher_group_id', voucher_group_id)
      .select([
        'work_vouchers.id',
        'work_area_tickets.work_batch_id',
        { ticketId: 'work_area_tickets.id' },
      ])
      .castTo<
        {
          id: number;
          work_batch_id: number;
          ticketId: number;
        }[]
      >();

    //  CREATE lot
    const newLot = await WorkAreaBatches.query().insert({
      work_area_id: areaEmployee,
      name: infoVoucherGroup[0].name,
      description: infoVoucherGroup[0].name,
    });

    for (let i = 0; i < getAllVouchersFromGroup.length; i++) {
      // DELETE vouchers to group
      await WorkVouchers.query()
        .update({
          work_voucher_group_id: null,
          work_voucher_group_sort: null,
        })
        .where('id', +getAllVouchersFromGroup[i].id);

      // INSERT LOG ACTIVITY voucher get out of group
      await WorkActivityLog.query().insert({
        work_voucher_id: getAllVouchersFromGroup[i].id,
        work_area_id: areaEmployee,
        employee_id: codeEmployee,
        module_name: 'voucherGroup',
        module_id: voucher_group_id,
        activity: 'DeletedVoucherToGroup',
        data: JSON.stringify({ value: getAllVouchersFromGroup[i].id }),
      });

      // ADD vouchers to lot
      if (getAllVouchersFromGroup[i].work_batch_id) {
        // TODO: id here seems wrong.  this id is for voucher groups, not lot
        const infoBatch = await WorkAreaBatches.query()
          .where('work_area_batches.id', voucher_group_id)
          .select({
            id: 'work_area_batches.id',
            nameLot: 'work_area_batches.name',
          })
          .castTo<{ id: number; nameLot: string }[]>();

        await WorkAreaTickets.query()
          .update({
            work_batch_id: voucher_group_id,
          })
          .where('work_area_tickets.id', +getAllVouchersFromGroup[i].ticketId);

        await WorkActivityLog.query().insert({
          work_voucher_id: getAllVouchersFromGroup[i].id,
          employee_id: codeEmployee,
          work_area_id: areaEmployee,
          module_name: 'Lot',
          module_id: voucher_group_id,
          activity: 'ChangedTicketOfLot',
          data: JSON.stringify({
            value: getAllVouchersFromGroup[i].id,
            old_lot: infoBatch[0].nameLot,
            new_lot: newLot.name,
          }),
        });

        await WorkActivityLog.query().insert({
          work_voucher_id: getAllVouchersFromGroup[i].id,
          employee_id: codeEmployee,
          work_area_id: areaEmployee,
          module_name: 'Lot',
          module_id: infoBatch[0].id, // TODO: Confirm this is the correct id, was changed from infoBatch[0].id
          activity: 'ChangedTicketOfLot',
          data: JSON.stringify({
            value: getAllVouchersFromGroup[i].id,
            old_lot: infoBatch[0].nameLot,
            new_lot: newLot.name,
          }),
        });
      } else {
        await WorkAreaTickets.query()
          .update({
            work_batch_id: newLot.id,
          })
          .where('work_area_tickets.id', +getAllVouchersFromGroup[i].ticketId);

        await WorkActivityLog.query().insert({
          work_voucher_id: getAllVouchersFromGroup[i].id,
          employee_id: codeEmployee,
          work_area_id: areaEmployee,
          module_name: 'Lot',
          module_id: newLot.id,
          activity: 'AddedTicketToLot',
          data: JSON.stringify({
            value: getAllVouchersFromGroup[i].id,
          }),
        });
      }
    }

    //  DELETE vouchers group
    await WorkVoucherGroups.query()
      .update({ work_area_id_ref: null })
      .where('id', voucher_group_id);
    return res.status(200).json({
      ok: true,
      data: {
        newLot: newLot.id,
      },
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
    });
  }
}

export async function deleteLots(req: Request, res: Response) {
  const { id } = req.params;
  const { codeEmployee, area } = req.body;

  if (!id || isNaN(Number(id))) {
    return res.status(400).json({
      ok: false,
      message: 'El id es obligatorio',
    });
  }
  const useWorkBatchId = Number(id);
  try {
    const getAllTicketsFromLot = await WorkAreaTickets.query().where(
      'work_batch_id',
      useWorkBatchId
    );

    const infoLot = await WorkAreaBatches.query().where('id', useWorkBatchId);

    // delete all tickets from lot(use transaction)
    await WorkAreaTickets.transaction(async (trx) => {
      for (let i = 0; i < getAllTicketsFromLot.length; i++) {
        await WorkAreaTickets.query(trx)
          .update({
            work_batch_id: null,
          })
          .where('id', getAllTicketsFromLot[i].id);

        await WorkActivityLog.query(trx).insert({
          work_voucher_id: getAllTicketsFromLot[i].work_voucher_id,
          work_area_id: area,
          employee_id: codeEmployee,
          module_name: 'Lot',
          module_id: useWorkBatchId,
          activity: 'DeletedTicketOfLot',
          data: JSON.stringify({ value: getAllTicketsFromLot[i].id }),
        });
      }
    });

    await WorkAreaBatches.query()
      .update({ finished_at: new Date() })
      .where('id', useWorkBatchId);

    await WorkActivityLog.query().insert({
      work_area_id: area,
      employee_id: codeEmployee,
      module_name: 'Lot',
      module_id: useWorkBatchId,
      activity: 'DeletedLot',
      data: JSON.stringify({ value: useWorkBatchId }),
    });

    return res.status(200).json({
      ok: true,
      data: {
        lot: infoLot[0].name,
      },
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: error.message,
    });
  }
}
