import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'print_ids',
    (table: Knex.TableBuilder): void => {
      table.integer('employee_id').notNullable().after('id');
      table.integer('pieces').defaultTo(0).notNullable().after('description');
      table
        .foreign('employee_id')
        .references('employee_id')
        .inTable('employees');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'print_ids',
    (table: Knex.TableBuilder): void => {
      table.dropColumn('employee_id');
      table.dropColumn('pieces');
    }
  );
}
