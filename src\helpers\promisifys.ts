import type formidable from 'formidable';
import { IncomingForm } from 'formidable';

// import pdfreader from 'pdfreader';
// condt PdfReader = pdfreader.PdfReader;
const PdfReader = require('pdfreader').PdfReader;

export const parseForm = async (req) =>
  new Promise<[formidable.Fields, formidable.Files]>((resolve, reject) => {
    const form = new IncomingForm();
    // @ts-ignore
    form.keepExtensions = true;
    form.parse(req, (err, fields, files) =>
      err ? reject(err) : resolve([fields, files])
    );
  });

export const pdfParserPromise = async (pdfBuffer: Buffer) =>
  new Promise((resolve, reject) => {
    const pdfParser = new PdfReader();
    pdfParser.parseBuffer(pdfBuffer, (err, item) => {
      err ? reject(err) : resolve(item);
    });
  });
