import type { NextFunction, Request, Response } from 'express';

import type { Employee } from '@app/models/employee.schema';
import type { MoNumber } from '@app/models/pedreria.schema';
import type {
  PlotterCombosShape,
  PlotterPrintShape,
} from '@app/models/plotter.schema';
import { scanPlotterPrintWithGroup } from '@app/services/cutting';
import { getEmployeeByCode } from '@app/services/employee';
import { getMo, getMoByNumAndClient, getMoFabrics } from '@app/services/mo';
import type { PrintWithCombos } from '@app/services/plotter';
import {
  addComboToPrint,
  addPlotterMachineToRoll,
  addPlotterPrintToRollByMO,
  addPrintToRoll,
  checkOutRollService,
  createCombo,
  createExcelFileForWarehouse,
  createMixMO,
  createMultiplesCombos,
  createPrintFromCombo,
  createRoll,
  createRollWithPrint,
  deleteAllComboByMO,
  deleteCombo,
  getCombo,
  getCombosByMo,
  getPlotterPrintsWithCombosForMo,
  getPrint,
  getPrintGroups,
  getPrintWithoutRoll,
  getPrintsByMO,
  getRoll,
  getSuggestedPartNumber,
  removeComboFromPrint,
  removePrintFromRoll,
  updateCombo,
  updatePlotterPrintCutDataUsingMos,
  updatePrint,
  updatePrintByName,
  updateRollSort,
} from '@app/services/plotter';
import {
  savePlotterPrintLocation,
  scanPlotterPrint,
  suggestedBinsForPlot,
} from '@app/services/plotterStorage';

interface RequestWithEmployee extends Request {
  locals: {
    employee: Employee;
  };
}

export const employeeMiddleware = async (
  req: RequestWithEmployee,
  res: Response,
  next: NextFunction
) => {
  // get employee_id from req
  // get employee from db
  // if employee is not found, return 404
  // else, add employee to req
  const employee_id = req.headers['employeeid'];
  if (!employee_id) {
    return res.status(401).json({
      ok: false,
      error: 'No empleado id',
    });
  }

  const employeeIdNumber = Number(employee_id);
  if (!employeeIdNumber || isNaN(employeeIdNumber)) {
    return res.status(400).json({
      ok: false,
      error: 'Codigo de empleado invalido',
    });
  }

  const employee = await getEmployeeByCode(employeeIdNumber);
  if (!employee) {
    return res.status(404).json({
      ok: false,
      error: 'Empleado no encontrado',
    });
  }

  req.locals = {
    employee: employee,
  };

  next();
};

export const getMoInfo = async (req: RequestWithEmployee, res: Response) => {
  const moId = Number(req.params.mo_id);

  try {
    const mo = await getMo(moId);

    return res.status(200).json({
      ok: true,
      data: mo,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const searchMoByNumAndClient = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const moNum = req.query.mo as string;
  const client = Number(req.query.client);

  if (!moNum) {
    return res.status(400).json({
      ok: false,
      error: 'Numero de MO es requerido',
    });
  }

  if (!client || isNaN(client)) {
    return res.status(400).json({
      ok: false,
      error: 'Cliente es requerido',
    });
  }

  try {
    const mo = await getMoByNumAndClient(moNum, client);

    return res.status(200).json({
      ok: true,
      data: mo,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const getMoParts = async (req: RequestWithEmployee, res: Response) => {
  const moId = Number(req.params.mo_id);

  try {
    const parts = await getMoFabrics(moId);

    return res.status(200).json({
      ok: true,
      data: parts,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const getPlotterCombo = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const comboId = Number(req.params.combo_id);

  try {
    const combo = await getCombo(comboId);

    return res.status(200).json({
      ok: true,
      data: combo,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const getPlotterCombos = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const moId = Number(req.params.mo_id);

  try {
    const combos = await getCombosByMo(moId);

    return res.status(200).json({
      ok: true,
      data: combos,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const createPlotterCombo = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const moId = Number(req.body.mo_id);
  const comboNumber = req.body.combo_number;
  const partNumber = req.body.part_number;
  const name = req.body.name;
  const description = req.body.description;
  const isLaser = req.body.is_laser;
  const employee = req.locals.employee;
  const styleCombos = req.body.style_combos;

  try {
    const combo = await createCombo(employee, {
      mo_id: moId,
      combo_number: comboNumber,
      part_number: partNumber,
      name: name,
      description: description,
      is_laser: isLaser,
      style_combos: styleCombos,
    });

    return res.status(200).json({
      ok: true,
      data: combo,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const createMultiPlotterCombos = async (
  req: RequestWithEmployee,
  res: Response
) => {
  try {
    const employee = req.locals.employee;
    const combos = req.body.combos as Partial<PlotterCombosShape>[];
    const allCombos = await createMultiplesCombos(employee, combos);

    return res.status(200).json({
      ok: true,
      data: allCombos,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const createMixMOs = async (req: RequestWithEmployee, res: Response) => {
  try {
    const employee = req.locals.employee;
    const combos = req.body.combos as Partial<PlotterCombosShape[]>;
    const mos = req.body.mos as Partial<MoNumber>[];
    const printGroup = req.body.print_group as boolean;
    const allMOs = await createMixMO(employee, combos, mos, printGroup);

    return res.status(200).json({
      ok: true,
      data: allMOs,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const updatePlotterCombo = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const comboId = Number(req.params.combo_id);
  const comboNumber = req.body.combo_number;
  const partNumber = req.body.part_number;
  const name = req.body.name;
  const description = req.body.description;
  const isLaser = req.body.is_laser;
  const employee = req.locals.employee;

  try {
    const combo = await updateCombo(employee, comboId, {
      combo_number: comboNumber,
      part_number: partNumber,
      name: name,
      description: description,
      is_laser: isLaser,
    });

    return res.status(200).json({
      ok: true,
      data: combo,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const getPlotterPrint = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const printId = Number(req.params.print_id);

  try {
    const print = await getPrint(printId);

    return res.status(200).json({
      ok: true,
      data: print,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const getPlotterPrintsForMoReq = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const moId = Number(req.params.mo_id);

  try {
    const plots = await getPlotterPrintsWithCombosForMo(moId);

    return res.status(200).json({
      ok: true,
      data: plots,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const createPlotterPrintFromCombo = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const comboId = Number(req.params.combo_id);

  const employee = req.locals.employee;

  try {
    const print = await createPrintFromCombo(employee, comboId);

    return res.status(200).json({
      ok: true,
      data: print,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const updatePlotterPrint = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const printId = Number(req.params.print_id);
  const partNumber = req.body.part_number;
  const name = req.body.name;
  const plys = req.body.plys;
  const quantity = req.body.quantity;
  const width = req.body.width;
  const length = req.body.length;
  const utilization = req.body.utilization;
  const totalSizes = req.body.total_sizes;
  const totalFabricYards = req.body.plys_fabric_yards;
  const employee = req.locals.employee;
  const printGroupSort = req.body.print_group_sort;

  try {
    const print = await updatePrint(employee, printId, {
      part_number: partNumber,
      name: name,
      plys: plys,
      quantity: quantity,
      width: width,
      length: length,
      utilization: utilization,
      total_sizes: totalSizes,
      plys_fabric_yards: totalFabricYards,
      print_group_sort: printGroupSort,
    });

    return res.status(200).json({
      ok: true,
      data: print,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const updatePlotterPrintByName = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const employee = req.locals.employee;
  const input = req.body as Partial<PlotterPrintShape>;

  try {
    const print = await updatePrintByName(employee, input);

    return res.status(200).json({
      ok: true,
      data: print,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const removePlotterComboFromPrint = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const comboId = Number(req.params.combo_id);

  const employee = req.locals.employee;

  try {
    const combo = await removeComboFromPrint(employee, comboId);

    return res.status(200).json({
      ok: true,
      data: combo,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const addPlotterComboToPrint = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const printId = Number(req.params.print_id);
  const comboId = Number(req.params.combo_id);

  const employee = req.locals.employee;

  try {
    const combo = await addComboToPrint(employee, comboId, printId);

    return res.status(200).json({
      ok: true,
      data: combo,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const postScanPlotterPrint = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const printId = Number(req.params.print_id);
  const workAreaGroupId = Number(req.body.work_area_group_id);

  try {
    const print = await scanPlotterPrintWithGroup(printId, workAreaGroupId);

    return res.status(200).json({
      ok: true,
      data: print,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const getPlotterRoll = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const rollId = Number(req.params.roll_id);

  try {
    const roll = await getRoll(rollId);

    return res.status(200).json({
      ok: true,
      data: roll,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const addPlotterPrintToRoll = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const printId = Number(req.params.print_id);
  const rollId = Number(req.params.roll_id);

  const employee = req.locals.employee;

  try {
    const roll = await addPrintToRoll(employee, printId, rollId);

    return res.status(200).json({
      ok: true,
      data: roll,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const removePlotterPrintFromRoll = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const printId = Number(req.params.print_id);

  const employee = req.locals.employee;

  try {
    const roll = await removePrintFromRoll(employee, printId);

    return res.status(200).json({
      ok: true,
      data: roll,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const createAndAddPlotterPrintToRoll = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const printId = Number(req.params.print_id);

  const employee = req.locals.employee;

  try {
    const roll = await createRollWithPrint(employee, printId);

    return res.status(200).json({
      ok: true,
      data: roll,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const createPlotterRoll = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const employee = req.locals.employee;

  try {
    const roll = await createRoll(employee);

    return res.status(200).json({
      ok: true,
      data: roll,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const getPlotterPrintWithoutRoll = async (
  req: RequestWithEmployee,
  res: Response
) => {
  try {
    const print = await getPrintWithoutRoll();

    return res.status(200).json({
      ok: true,
      data: print,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const updateSortRoll = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const rollId = Number(req.body.roll_id);
  const prints: Partial<PrintWithCombos[]> = req.body.prints;

  try {
    const roll = await updateRollSort(rollId, prints);

    return res.status(200).json({
      ok: true,
      data: roll,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const UpdateMachineNumber = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const rollId = Number(req.body.roll_id);
  const machineNumber = Number(req.body.machine_number);

  try {
    const roll = await addPlotterMachineToRoll(rollId, machineNumber);

    return res.status(200).json({
      ok: true,
      data: roll,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const addPlotterPrintToRollByMOController = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const employee = req.locals.employee;
  const rollId = +req.body.roll_id;
  const mo: string = req.body.mo;
  const company: number = req.body.company;

  try {
    const roll = await addPlotterPrintToRollByMO(employee, rollId, mo, company);

    return res.status(200).json({
      ok: true,
      data: roll,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const getPlotterPrintByMO = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const mo: string = req.body.mo;
  const company: number = req.body.company;

  try {
    const prints = await getPrintsByMO(mo, company);

    return res.status(200).json({
      ok: true,
      data: prints,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const getPlotterSuggestedLocations = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const printId = +req.params.print_id;

  if (!printId) {
    return res.status(400).json({
      ok: false,
      error: 'plot_id is required',
    });
  }

  try {
    const bins = await suggestedBinsForPlot(printId);

    return res.status(200).json({
      ok: true,
      data: bins,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const updatePlotterPrintLocation = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const printId = +req.params.print_id;
  const location = req.body.location;

  if (!printId) {
    return res.status(400).json({
      ok: false,
      error: 'plot_id is required',
    });
  }

  if (!location || typeof location !== 'string') {
    return res.status(400).json({
      ok: false,
      error: 'location is required',
    });
  }

  try {
    await savePlotterPrintLocation(printId, location);

    return res.status(200).json({
      ok: true,
      data: 'Location saved',
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const createRollExcel = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const rollId = +req.params.roll_id;

  try {
    if (!rollId) {
      return res.status(400).json({
        ok: false,
        error: 'Roll id is required',
      });
    }
    const roll = await createExcelFileForWarehouse(rollId);
    roll.write('roll.xlsx', res);
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const deleteComboController = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const comboId = Number(req.params.combo_id);
  const employee = req.locals.employee;

  try {
    const combo = await deleteCombo(employee, comboId);

    return res.status(200).json({
      ok: true,
      data: combo,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const deleteAllComboController = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const moId = Number(req.params.mo_id);
  const employee = req.locals.employee;

  try {
    const combo = await deleteAllComboByMO(employee, moId);

    return res.status(200).json({
      ok: true,
      data: combo,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const checkOutRoll = async (req: RequestWithEmployee, res: Response) => {
  const rollId = +req.body.roll_id;
  const recipient = +req.body.recipient;

  try {
    if (!rollId) {
      return res.status(500).json({
        ok: false,
        error: 'Roll id is required',
      });
    }

    if (!recipient) {
      return res.status(500).json({
        ok: false,
        error: 'Recipient is required',
      });
    }

    const updateCheckout = await checkOutRollService(
      rollId,
      req.locals.employee,
      recipient
    );

    //check if roll has message property
    if (updateCheckout.message) {
      return res.status(500).json({
        ok: false,
        error: updateCheckout.message,
      });
    }

    return res.status(200).json({
      ok: true,
      data: updateCheckout,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const updatePlotsWithCutting = async (
  req: RequestWithEmployee,
  res: Response
) => {
  try {
    const resultsFromUpdate = await updatePlotterPrintCutDataUsingMos();
    return res.status(200).json({
      ok: true,
      data: resultsFromUpdate,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const getPlotterPrintGroup = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const printGroupId = Number(req.params.print_group_id);

  try {
    const print = await getPrintGroups(printGroupId);

    return res.status(200).json({
      ok: true,
      data: print,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const scanPlotterPrintReq = async (
  req: RequestWithEmployee,
  res: Response
) => {
  const printCodes = req.body.printCodes as string[];
  const work_area_group_id = req.body.work_area_group_id;
  const work_area_line_id = req.body.work_area_line_id;
  const location = req.body.location;

  if (!printCodes || !printCodes.length) {
    return res.status(400).json({
      ok: false,
      error: 'printCodes is required',
    });
  }
  const stringCheck = printCodes.find((code) => typeof code !== 'string');
  if (stringCheck) {
    return res.status(400).json({
      ok: false,
      error: 'printCodes must be an array of strings',
    });
  }

  if (!work_area_group_id || typeof work_area_group_id !== 'number') {
    return res.status(400).json({
      ok: false,
      error: 'work_area_group_id is required',
    });
  }

  if (
    !(
      location === null ||
      location === undefined ||
      typeof location === 'string'
    )
  ) {
    if (typeof location !== 'string') {
      return res.status(400).json({
        ok: false,
        error: 'location must be a string',
      });
    }
  }
  const useLocation = location as string | undefined | null;

  try {
    const print = await scanPlotterPrint(printCodes, work_area_group_id, {
      work_area_line_id,
      location: useLocation,
    });

    return res.status(200).json({
      ok: true,
      data: print,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const getSuggestedPartNumberController = async (
  req: Request,
  res: Response
) => {
  const partNumber = req.params.part_number;
  const styleNumber = req.params.style_number;
  try {
    const suggestedPartNumber = await getSuggestedPartNumber(
      partNumber,
      styleNumber
    );
    return res.status(200).json({
      ok: true,
      data: suggestedPartNumber,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};
