import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('head_items', (table: Knex.TableBuilder) => {
    table.increments('id').unsigned().primary();
    table
      .timestamp('created_at')
      .notNullable()
      .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    table
      .timestamp('updated_at')
      .notNullable()
      .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

    table.integer('head_id').notNullable();
    table.string('accounting_department').notNullable();
    table.integer('employee_id').notNullable();
    table.decimal('hours', 10, 4).notNullable();
    table.decimal('daily_rate', 10, 4).notNullable();
    table.decimal('amount', 10, 4).notNullable();
    table.decimal('extra_amount', 10, 4).notNullable();
    table.decimal('bonus_amount', 10, 4).notNullable();
    table.decimal('overtime_hours', 10, 4).notNullable();
    table.decimal('overtime_amount', 10, 4).notNullable();
    table.decimal('night_overtime_hours', 10, 4).notNullable();
    table.decimal('night_overtime_amount', 10, 4).notNullable();
    table.decimal('total_amount', 10, 4).notNullable();
    table.decimal('deduction_isss_amount', 10, 4).notNullable();
    table.decimal('deduction_afp_amount', 10, 4).notNullable();
    table.decimal('deduction_isr_amount', 10, 4).notNullable();
    table.decimal('deduction_other_amount', 10, 4).notNullable();
    table.decimal('total_deduction_amount', 10, 4).notNullable();
    table.decimal('net_amount', 10, 4).notNullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('head_items');
}
