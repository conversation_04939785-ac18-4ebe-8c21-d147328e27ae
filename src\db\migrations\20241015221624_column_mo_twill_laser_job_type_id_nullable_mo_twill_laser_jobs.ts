import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('mo_twill_laser_jobs', (table) => {
    table
      .integer('mo_twill_laser_job_type_id', 10)
      .nullable()
      .unsigned()
      .alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('mo_twill_laser_jobs', (table) => {
    table
      .integer('mo_twill_laser_job_type_id', 10)
      .notNullable()
      .unsigned()
      .alter();
  });
}
