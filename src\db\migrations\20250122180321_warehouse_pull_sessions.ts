import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(
    'warehouse_pull_sessions',
    (table: Knex.TableBuilder): void => {
      table.increments('id').unsigned().primary();
      table.integer('employee_id').notNullable();
      table
        .foreign('employee_id', 'ei_employees_fk')
        .references('employee_id')
        .inTable('employees');
      table.timestamp('finished_at').nullable();
      table.string('default_reason').nullable();
      table.string('default_comment').nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table
        .timestamp('updated_at')
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('warehouse_pull_sessions');
}
