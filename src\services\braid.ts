import currency from 'currency.js';
import type { Knex } from 'knex';
import type { Raw } from 'objection';

import { knex } from '@app/db';
import type {
  BraidOrderJobProductionShape,
  BraidOrderJobShape,
  BraidOrderShape,
} from '@app/models/braid.schema';
import {
  BraidColor,
  BraidFabric,
  BraidInventoryItem,
  BraidInventoryItemAdjustment,
  BraidOrder,
  BraidOrderJob,
  BraidOrderJobProduction,
  BraidOrderJobStatus,
  BraidPattern,
} from '@app/models/braid.schema';
import type { EmployeeShape } from '@app/models/employee.schema';
import { Employee } from '@app/models/employee.schema';
import type { MachineShape } from '@app/models/machines.schema';
import { Machine } from '@app/models/machines.schema';

interface BraidOrderJobProductionInfo extends BraidOrderJobProductionShape {
  machine: MachineShape;
  employee: EmployeeShape;
}
interface BraidOrderJobInfo extends BraidOrderJobShape {
  total_yards_produced: number;
  total_yards_producing: number;
  remaining_yards: number;
  order: BraidOrderShape;
  productions: BraidOrderJobProductionInfo[];
}

/*
 * Braid order can only have one active job at a time
 * Braid order job can have multiple productions but productions cannot exceed required yards unless finishing
 */

const braidOrderJobInfo = (
  braid_order_job: BraidOrderJob,
  braid_order: BraidOrder,
  productions: BraidOrderJobProduction[],
  machines: Machine[],
  employees: Employee[]
): BraidOrderJobInfo => {
  if (!braid_order_job) {
    throw new Error('Braid order job not found');
  }
  if (!braid_order) {
    throw new Error('Braid order not found');
  }
  let totalYardsProduced = currency(0);
  let totalYardsProducing = currency(0);

  const productionsInfo: BraidOrderJobProductionInfo[] = [];
  for (const production of productions) {
    if (production.finished_at) {
      totalYardsProduced = totalYardsProduced.add(production.yards);
    } else {
      totalYardsProducing = totalYardsProducing.add(production.yards);
    }
    const machine = machines.find(
      (machine) => machine.machine_id === production.machine_id
    );
    const employee = employees.find(
      (employee) => employee.employee_id === production.employee_id
    );
    productionsInfo.push({
      ...production,
      machine,
      employee,
    });
  }

  const remainingYards = currency(braid_order_job.required_yards)
    .subtract(totalYardsProduced)
    .subtract(totalYardsProducing).value;

  return {
    ...braid_order_job,
    total_yards_produced: totalYardsProduced.value,
    total_yards_producing: totalYardsProducing.value,
    remaining_yards: remainingYards,
    order: braid_order,
    productions: productionsInfo,
  };
};

export const braidOrderInfo = async (braidOrder: BraidOrder) => {
  const braidJobs = await BraidOrderJob.query().where({
    braid_order_id: braidOrder.id,
  });

  const braidJobProductions = await BraidOrderJobProduction.query()
    .whereIn(
      'braid_order_job_id',
      braidJobs.map((job) => job.id)
    )
    .where('removed_at', null)
    .where('marked_bad_at', null);

  const machines = await Machine.query().whereIn(
    'machine_id',
    braidJobProductions.map((production) => production.machine_id)
  );

  const employees = await Employee.query().whereIn(
    'employee_id',
    braidJobProductions.map((production) => production.employee_id)
  );

  const braidJobsInfo = braidJobs.map((job) => {
    const productions = braidJobProductions.filter(
      (production) => production.braid_order_job_id === job.id
    );

    return braidOrderJobInfo(job, braidOrder, productions, machines, employees);
  });

  return {
    braid_order: braidOrder,
    braid_order_jobs: braidJobsInfo,
  };
};

export const briadOrderByNumber = async (braidOrderNumber: string) => {
  const braidOrderSearch = await BraidOrder.query().findOne({
    braid_num: braidOrderNumber,
  });

  if (!braidOrderSearch) {
    throw new Error('Braid order not found');
  }

  const orderInfo = await braidOrderInfo(braidOrderSearch);

  return orderInfo;
};

export const braidOrderById = async (braidOrderId: number) => {
  const braidOrderSearch = await BraidOrder.query().findById(braidOrderId);
  if (!braidOrderSearch) {
    throw new Error('Braid order not found');
  }

  const orderInfo = await braidOrderInfo(braidOrderSearch);

  return orderInfo;
};

export const braidOrderJobById = async (
  braidOrderJobId: number,
  options?: {
    transaction?: Knex.Transaction;
  }
) => {
  if (!braidOrderJobId || isNaN(braidOrderJobId)) {
    throw new Error('Braid order job id is required');
  }
  const trx = options?.transaction ?? undefined;
  const braidOrderJobQuery = await BraidOrderJob.query(trx).findById(
    braidOrderJobId
  );

  if (!braidOrderJobQuery) {
    throw new Error('Braid order job not found');
  }

  const braidOrder = await BraidOrder.query().findById(
    braidOrderJobQuery.braid_order_id
  );

  const productions = await BraidOrderJobProduction.query(trx)
    .where('braid_order_job_id', braidOrderJobId)
    .where('removed_at', null)
    .where('marked_bad_at', null);

  const machines = await Machine.query(trx).whereIn(
    'machine_id',
    productions.map((production) => production.machine_id)
  );

  const employees = await Employee.query(trx).whereIn(
    'employee_id',
    productions.map((production) => production.employee_id)
  );

  return braidOrderJobInfo(
    braidOrderJobQuery,
    braidOrder,
    productions,
    machines,
    employees
  );
};

export const createBraidOrderJob = async (
  braidOrderId: number,
  isRepo: boolean,
  requiredYards: number
) => {
  console.log('creating braid order', braidOrderId, isRepo, requiredYards);
  if (!braidOrderId) {
    throw new Error('Braid order id is required');
  }

  const trx = await BraidOrderJob.startTransaction();
  try {
    const braidOrder = await BraidOrder.query(trx).findById(braidOrderId);
    if (!braidOrder) {
      throw new Error('Braid order not found');
    }

    const repoValue = isRepo ? true : false;
    const useYards = requiredYards || braidOrder.yards;

    // check for existing job
    const existingJob = await BraidOrderJob.query(trx)
      .where({
        braid_order_id: braidOrder.id,
        removed_at: null,
      })
      .where('status', '!=', BraidOrderJobStatus.COMPLETED)
      .first();

    if (existingJob) {
      throw new Error('Active Braid order job already exists');
    }

    if (!repoValue) {
      // check for existing prod job
      const existingProdJob = await BraidOrderJob.query(trx).where({
        braid_order_id: braidOrderId,
        is_repo: false,
        removed_at: null,
      });

      if (existingProdJob.length > 0) {
        throw new Error('Can only have 1 non repo job');
      }
    }

    const job = await BraidOrderJob.query(trx).insert({
      braid_order_id: braidOrderId,
      required_yards: useYards,
      is_repo: repoValue,
      status: BraidOrderJobStatus.PENDING,
    });

    const jobInfo = await braidOrderJobById(job.id, { transaction: trx });

    trx.commit();
    return jobInfo;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

const checkProductionEmployeeMachine = async (
  employee_id: number,
  machine_id: number
) => {
  if (!employee_id) {
    throw new Error('Employee id is required');
  }
  const employee = await Employee.query().findById(employee_id);
  if (!employee) {
    throw new Error('Employee not found');
  }

  if (!machine_id) {
    throw new Error('Machine id is required');
  }
  const machine = await Machine.query().findById(machine_id);
  if (!machine) {
    throw new Error('Machine not found');
  }
  if (machine.machine_type_id !== 17) {
    throw new Error('Machine type is not a braid machine');
  }
};

const updateJobStatusAndGetJobInfo = async (
  braidOrderJobId: number,
  extra: {
    transaction?: Knex.Transaction;
  }
): Promise<BraidOrderJobInfo> => {
  const trx = extra.transaction ?? undefined;
  const currentBraidOrderJobInfo = await braidOrderJobById(braidOrderJobId, {
    transaction: trx,
  });

  let jobFinished = true;
  let productionsPending = false;
  let totalYardsProduced = currency(0);
  for (const production of currentBraidOrderJobInfo.productions) {
    if (production.finished_at) {
      totalYardsProduced = totalYardsProduced.add(production.yards);
    } else {
      productionsPending = true;
      jobFinished = false;
      break;
    }
  }
  const remainingYards = currency(
    currentBraidOrderJobInfo.required_yards
  ).subtract(totalYardsProduced).value;

  if (remainingYards > 0) {
    jobFinished = false;
  }

  if (!productionsPending) {
    if (jobFinished) {
      await BraidOrderJob.query(trx)
        .patch({
          status: BraidOrderJobStatus.COMPLETED,
          completed_at: knex.fn.now(),
        })
        .where('id', braidOrderJobId);
    } else {
      await BraidOrderJob.query(trx)
        .patch({
          status: BraidOrderJobStatus.PARTIAL,
        })
        .where('id', braidOrderJobId);
    }
  } else {
    if (currentBraidOrderJobInfo.remaining_yards <= 0) {
      await BraidOrderJob.query(trx)
        .patch({
          status: BraidOrderJobStatus.PRODUCING,
        })
        .where('id', braidOrderJobId);
    } else {
      await BraidOrderJob.query(trx)
        .patch({
          status: BraidOrderJobStatus.PARTIAL,
        })
        .where('id', braidOrderJobId);
    }
  }

  return braidOrderJobById(braidOrderJobId, { transaction: trx });
};

const checkExistingActiveProductionForMachine = async (
  machine_id: number,
  productions: BraidOrderJobProductionShape[],
  extra?: { ignore_production_id: number }
) => {
  const existingProduction = productions.find(
    (production) =>
      production.machine_id === machine_id &&
      !production.finished_at &&
      !production.removed_at &&
      !production.marked_bad_at &&
      production.id !== extra?.ignore_production_id
  );
  if (existingProduction) {
    throw new Error('Production in process with Machine already exists');
  }
};

export const createAndFinishBraidOrderJobProduction = async (
  braid_order_job_id: number,
  yards: number,
  employee_id: number,
  machine_id: number
) => {
  if (!braid_order_job_id) {
    throw new Error('Braid order job id is required');
  }
  if (!yards || yards <= 0) {
    throw new Error('Yards is required');
  }
  if (!employee_id) {
    throw new Error('Employee id is required');
  }
  if (!machine_id) {
    throw new Error('Machine id is required');
  }

  const trx = await BraidOrderJobProduction.startTransaction();
  try {
    const job = await braidOrderJobById(braid_order_job_id, {
      transaction: trx,
    });

    if (job.status === BraidOrderJobStatus.COMPLETED) {
      throw new Error('Job already completed');
    }
    if (job.removed_at) {
      throw new Error('Job removed');
    }
    if (job.remaining_yards <= 0) {
      throw new Error('No available yards to produce');
    }

    await checkProductionEmployeeMachine(employee_id, machine_id);

    await checkExistingActiveProductionForMachine(machine_id, job.productions);

    const useYards = yards || job.remaining_yards;

    const goal_difference = currency(useYards).subtract(
      job.remaining_yards
    ).value;

    // yards is not required to create production
    await BraidOrderJobProduction.query(trx).insert({
      braid_order_job_id: braid_order_job_id,
      employee_id: employee_id,
      machine_id: machine_id,
      yards: useYards,
      finished_at: knex.fn.now(),
      goal_difference: goal_difference > 0 ? goal_difference : 0,
    });

    const updatedJob = await updateJobStatusAndGetJobInfo(braid_order_job_id, {
      transaction: trx,
    });

    trx.commit();
    return updatedJob;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

export const createBraidOrderJobProduction = async (
  braidOrderJobId: number,
  employeeId: number,
  machineId: number,
  options?: {
    yards: number | null;
    finish: boolean | null;
  }
) => {
  if (!braidOrderJobId || isNaN(braidOrderJobId)) {
    throw new Error('Braid order job id is required');
  }
  if (!employeeId || isNaN(employeeId)) {
    throw new Error('Employee id is required');
  }
  if (!machineId || isNaN(machineId)) {
    throw new Error('Machine id is required');
  }

  const trx = await BraidOrderJobProduction.startTransaction();
  try {
    const braidOrderJobInfo = await braidOrderJobById(braidOrderJobId, {
      transaction: trx,
    });
    if (braidOrderJobInfo.removed_at) {
      throw new Error('Job removed');
    }
    if (braidOrderJobInfo.status === BraidOrderJobStatus.COMPLETED) {
      throw new Error('Job already completed');
    }

    if (braidOrderJobInfo.remaining_yards <= 0) {
      throw new Error('No available yards to produce');
    }

    if (options?.yards && options?.yards > braidOrderJobInfo.remaining_yards) {
      throw new Error('Yards exceeds available yards');
    }

    const useYards = options?.yards || braidOrderJobInfo.remaining_yards;

    await checkProductionEmployeeMachine(employeeId, machineId);

    await checkExistingActiveProductionForMachine(
      machineId,
      braidOrderJobInfo.productions
    );

    // check for existing unfinished production with the same machine
    const existingProduction = braidOrderJobInfo.productions.find(
      (production) =>
        production.machine_id === machineId &&
        !production.finished_at &&
        !production.removed_at &&
        !production.marked_bad_at
    );
    if (existingProduction) {
      throw new Error('Production in process with Machine already exists');
    }

    let finished_at = null;
    if (options?.finish) {
      finished_at = knex.fn.now();
    }

    // yards is not required to create production
    await BraidOrderJobProduction.query(trx).insert({
      braid_order_job_id: braidOrderJobId,
      employee_id: employeeId,
      machine_id: machineId,
      yards: useYards,
      started_at: knex.fn.now(),
      finished_at: finished_at,
    });

    const updatedJob = await updateJobStatusAndGetJobInfo(braidOrderJobId, {
      transaction: trx,
    });

    trx.commit();
    return updatedJob;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

export const finishBraidOrderJobProduction = async (
  braid_order_job_production_id: number,
  data: {
    yards: number | null;
    machine_id?: number | null;
    employee_id?: number | null;
    reason_for_extra?: string | null;
    dont_split?: boolean | null;
  }
) => {
  const { yards: givenYards, dont_split } = data;

  const trx = await BraidOrderJobProduction.startTransaction();
  try {
    if (!braid_order_job_production_id) {
      throw new Error(
        'Braid order job production id is required to finish production'
      );
    }

    let production: BraidOrderJobProduction | null = null;
    let job: BraidOrderJobInfo | null = null;

    production = await BraidOrderJobProduction.query(trx).findById(
      braid_order_job_production_id
    );
    if (!production) {
      throw new Error('Production not found');
    }
    if (production.removed_at) {
      throw new Error('Production removed');
    }
    if (production.marked_bad_at) {
      throw new Error('Production marked bad');
    }
    if (production.finished_at) {
      throw new Error('Production already finished');
    }

    job = await braidOrderJobById(production?.braid_order_job_id, {
      transaction: trx,
    });
    if (!job) {
      throw new Error('Job not found');
    }
    if (job.removed_at) {
      throw new Error('Job removed');
    }

    const machine_id = data.machine_id || production.machine_id;
    const employee_id = data.employee_id || production.employee_id;
    const useYards = givenYards || production.yards;
    let goal_difference = currency(useYards).subtract(production.yards).value;

    // values check
    if (!useYards || useYards <= 0) {
      throw new Error('Yards is required');
    }
    await checkProductionEmployeeMachine(employee_id, machine_id);

    await checkExistingActiveProductionForMachine(machine_id, job.productions, {
      ignore_production_id: production.id,
    });

    let splitProduction = false;
    const splitYardsRemaining = currency(production.yards).subtract(
      useYards
    ).value;
    if (splitYardsRemaining > 0 && !dont_split) {
      splitProduction = true;
      goal_difference = 0;
    }

    await BraidOrderJobProduction.query(trx).where('id', production.id).patch({
      finished_at: knex.fn.now(),
      yards: useYards,
      machine_id: machine_id,
      employee_id: employee_id,
      goal_difference,
    });

    if (splitProduction) {
      await BraidOrderJobProduction.query(trx).insert({
        braid_order_job_id: job.id,
        employee_id: null,
        machine_id: machine_id,
        yards: splitYardsRemaining,
        use_inventory: false,
        split_from_production_id: production.id,
      });
    }

    const updatedJob = await updateJobStatusAndGetJobInfo(job.id, {
      transaction: trx,
    });

    trx.commit();
    return updatedJob;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

export const createBraidOrderJobProductionUsingInventory = async (
  braidOrderJobId: number,
  yards: number
) => {
  if (!braidOrderJobId || isNaN(braidOrderJobId)) {
    throw new Error('Braid order job id is required');
  }
  if (!yards || yards <= 0) {
    throw new Error('Yards is required');
  }

  const useYards = yards;

  const trx = await BraidOrderJobProduction.startTransaction();
  try {
    const braidOrderJobInfo = await braidOrderJobById(braidOrderJobId, {
      transaction: trx,
    });
    if (braidOrderJobInfo.removed_at) {
      throw new Error('Job removed');
    }
    if (braidOrderJobInfo.status === BraidOrderJobStatus.COMPLETED) {
      throw new Error('Job already completed');
    }

    // yards is not required to create production
    await BraidOrderJobProduction.query(trx).insert({
      braid_order_job_id: braidOrderJobId,
      yards: useYards,
      finished_at: knex.fn.now(),
      use_inventory: true,
    });

    const updatedJob = await updateJobStatusAndGetJobInfo(braidOrderJobId, {
      transaction: trx,
    });

    trx.commit();
    return updatedJob;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

export const braidInventoryItemById = async (braidInventoryItemId: number) => {
  if (!braidInventoryItemId || isNaN(braidInventoryItemId)) {
    throw new Error('Braid inventory item id is required');
  }
  const inventoryItem = await BraidInventoryItem.query().findById(
    braidInventoryItemId
  );
  if (!inventoryItem) {
    throw new Error('Inventory item not found');
  }

  return inventoryItem;
};

export const findBraidInventoryItemsBySku = async (
  skuInfo: {
    fabric: string;
    pattern: string;
    colors: string;
    size: number;
  },
  pageInfo: {
    page?: number;
    limit?: number;
  }
) => {
  const { fabric, pattern, colors, size } = skuInfo;
  if (!fabric) {
    throw new Error('Fabric is required');
  }
  if (!pattern) {
    throw new Error('Pattern is required');
  }
  if (!colors) {
    throw new Error('Colors is required');
  }
  if (!size || isNaN(size) || size <= 0) {
    throw new Error('Size is required');
  }

  const page =
    pageInfo.page && !isNaN(pageInfo.page)
      ? pageInfo.page < 1
        ? 1
        : pageInfo.page
      : 1;
  const limit =
    pageInfo.limit && !isNaN(pageInfo.limit)
      ? pageInfo.limit > 250
        ? 250
        : pageInfo.limit < 1
        ? 1
        : pageInfo.limit
      : 10;

  const inventoryItems = await BraidInventoryItem.query()
    .where({
      fabric: fabric.trim().toUpperCase(),
      pattern: pattern.trim().toUpperCase(),
      colors: colors.trim().toUpperCase(),
      size,
    })
    .where('yards', '>', 0)
    .page(page - 1, limit)
    .orderBy('created_at', 'asc');

  return {
    data: inventoryItems.results,
    pagination: {
      page,
      limit,
      total: inventoryItems.total,
    },
  };
};

export const createBraidInventoryItemAndAdjustmentWithoutCheck = async (
  pattern: string,
  colors: string,
  size: number,
  fabric: string,
  yards: number,
  extra?: {
    location?: string | null;
    notes?: string | null;
    from_braid_order_job_production_id?: number | null;
    adjustment_note?: string | null;
  }
) => {
  if (!pattern) {
    throw new Error('Pattern is required');
  }
  if (!colors) {
    throw new Error('Colors is required');
  }
  if (!size || isNaN(size) || size <= 0) {
    throw new Error('Size is required');
  }
  if (!fabric) {
    throw new Error('Fabric is required');
  }
  if (!yards || isNaN(yards) || yards <= 0) {
    throw new Error('Yards is required');
  }

  const trx = await BraidInventoryItem.startTransaction();
  try {
    const newInventoryItem = await BraidInventoryItem.query(trx).insert({
      pattern: pattern,
      colors: colors,
      size,
      fabric: fabric,
      yards,
      location: extra?.location || undefined,
      notes: extra?.notes || undefined,
      from_braid_order_job_production_id:
        extra?.from_braid_order_job_production_id || undefined,
    });
    await BraidInventoryItemAdjustment.query(trx).insert({
      braid_inventory_item_id: newInventoryItem.id,
      yards: newInventoryItem.yards,
      note: extra?.adjustment_note || 'Initial inventory',
    });

    trx.commit();
    return newInventoryItem;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

export const createBraidInventoryItemFromScratch = async (
  pattern: string,
  colors: string,
  size: number,
  fabric: string,
  yards: number,
  extra?: {
    location?: string | null;
    notes?: string | null;
  }
) => {
  if (!pattern) {
    throw new Error('Pattern is required');
  }
  if (!colors) {
    throw new Error('Colors is required');
  }
  if (!size || isNaN(size) || size <= 0) {
    throw new Error('Size is required');
  }
  if (!fabric) {
    throw new Error('Fabric is required');
  }
  if (!yards || isNaN(yards) || yards <= 0) {
    throw new Error('Yards is required');
  }

  const trx = await BraidInventoryItem.startTransaction();
  try {
    // look up pattern
    const patternLookup = await BraidPattern.query(trx).findOne({
      pattern: pattern.trim().toUpperCase(),
    });
    if (!patternLookup) {
      throw new Error('Pattern not found');
    }

    // fabric check
    const fabricLookup = await BraidFabric.query(trx).findOne({
      name: fabric.trim().toUpperCase(),
    });
    if (!fabricLookup) {
      throw new Error('Fabric not found');
    }

    // look up colors
    // split and capitalize colors
    const colorsArray = colors
      .split('/')
      .map((color) => color.trim().toUpperCase());
    const colorsLookup = await BraidColor.query(trx).whereIn(
      'name',
      colorsArray
    );

    if (colorsArray.length !== patternLookup.color_count) {
      throw new Error(
        `Color count does not match pattern. Expected ${patternLookup.color_count} colors`
      );
    }

    if (colorsLookup.length !== colorsArray.length) {
      // find missing colors
      const missingColors = colorsArray.filter(
        (color) => !colorsLookup.find((lookup) => lookup.name === color)
      );
      throw new Error(`Colors not found: ${missingColors.join(', ')}`);
    }

    const correctColors = colorsArray.join('/');

    const newInventoryItem =
      await createBraidInventoryItemAndAdjustmentWithoutCheck(
        patternLookup.pattern,
        correctColors,
        size,
        fabricLookup.name,
        yards,
        extra
      );

    trx.commit();
    return newInventoryItem;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

export const updateBraidInventoryItem = async (
  braid_inventory_item_id: number,
  data: {
    location?: string | null;
    notes?: string | null;
    yards?: number | null;
    adjustment?: number | null;
    adjustment_note?: string | null;
  }
) => {
  if (!braid_inventory_item_id) {
    throw new Error('Braid inventory item id is required');
  }
  if (!data) {
    throw new Error('Data is required');
  }

  if (data.yards && data.yards <= 0) {
    throw new Error('Yards must be greater than 0');
  }
  if (data.yards && data.adjustment) {
    throw new Error('Cannot update yards and adjust at the same time');
  }

  const trx = await BraidInventoryItem.startTransaction();
  try {
    const inventoryItem = await BraidInventoryItem.query(trx).findById(
      braid_inventory_item_id
    );
    if (!inventoryItem) {
      throw new Error('Inventory item not found');
    }

    let newYards: number = undefined;

    if (data.yards && data.yards !== inventoryItem.yards) {
      if (!data.adjustment_note) {
        throw new Error('Adjustment note is required');
      }
      newYards = data.yards;
      await BraidInventoryItemAdjustment.query(trx).insert({
        braid_inventory_item_id: braid_inventory_item_id,
        yards: currency(data.yards).subtract(inventoryItem.yards).value,
        note: data.adjustment_note,
      });
    }
    if (data.adjustment && data.adjustment !== 0) {
      if (!data.adjustment_note) {
        throw new Error('Adjustment note is required');
      }
      if (-data.adjustment > inventoryItem.yards) {
        throw new Error('Adjustment exceeds available yards');
      }
      newYards = currency(inventoryItem.yards).add(data.adjustment).value;
      await BraidInventoryItemAdjustment.query(trx).insert({
        braid_inventory_item_id: braid_inventory_item_id,
        yards: data.adjustment,
        note: data.adjustment_note,
      });
    }

    await BraidInventoryItem.query(trx)
      .patch({
        location: data.location,
        notes: data.notes,
        yards: newYards,
      })
      .where('id', inventoryItem.id);

    trx.commit();
    return await BraidInventoryItem.query().findById(braid_inventory_item_id);
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

export const createBraidOrderJobProductionWithInventoryItems = async (
  braidOrderJobId: number,
  braidInventoryItemAllocations: {
    braid_inventory_item_id: number;
    yards: number | null;
  }[]
) => {
  if (!braidOrderJobId || isNaN(braidOrderJobId)) {
    throw new Error('Braid order job id is required');
  }
  if (
    !braidInventoryItemAllocations ||
    braidInventoryItemAllocations.length < 1
  ) {
    throw new Error('No allocations provided');
  }

  const trx = await BraidOrderJobProduction.startTransaction();
  try {
    const braidOrderJobInfo = await braidOrderJobById(braidOrderJobId, {
      transaction: trx,
    });

    if (braidOrderJobInfo.status === BraidOrderJobStatus.COMPLETED) {
      throw new Error('Job already completed');
    }
    if (braidOrderJobInfo.removed_at) {
      throw new Error('Job removed');
    }

    const allocationInventoryItemIds: number[] = [];
    for (const [index, allocation] of braidInventoryItemAllocations.entries()) {
      if (!allocation.braid_inventory_item_id) {
        throw new Error(
          `Braid inventory item id is required for allocation index ${index}`
        );
      }
      allocationInventoryItemIds.push(allocation.braid_inventory_item_id);
    }

    const braidInventoryItems = await BraidInventoryItem.query(trx).findByIds(
      braidInventoryItemAllocations.map(
        (allocation) => allocation.braid_inventory_item_id
      )
    );

    const braidInventoryChanges: {
      braid_inventory_item_id: number;
      production: {
        braid_order_job_id: number;
        yards: number;
        finished_at: Raw;
        use_inventory: boolean;
      };
      adjustment: {
        adjustment: number;
        yardsRemaining: number;
      };
    }[] = [];
    let totalYards = currency(0);
    for (const allocation of braidInventoryItemAllocations) {
      if (!allocation.braid_inventory_item_id) {
        throw new Error('Braid inventory item id is required');
      }

      const braidInventoryItem = allocation.braid_inventory_item_id
        ? braidInventoryItems.find(
            (item) => item.id === allocation.braid_inventory_item_id
          )
        : null;

      if (!braidInventoryItem) {
        throw new Error(
          `Braid inventory item not found with id ${allocation.braid_inventory_item_id}`
        );
      }

      // check braid sku
      if (
        braidInventoryItem.pattern !== braidOrderJobInfo.order.pattern ||
        braidInventoryItem.colors !== braidOrderJobInfo.order.colors ||
        braidInventoryItem.size !== braidOrderJobInfo.order.size ||
        braidInventoryItem.fabric !== braidOrderJobInfo.order.fabric
      ) {
        throw new Error(
          `Inventory item ${allocation.braid_inventory_item_id} does not match braid order sku`
        );
      }

      const useYards = allocation.yards ?? braidInventoryItem.yards;
      if (!useYards || useYards <= 0) {
        throw new Error(
          `Yards not provided for inventory item ${allocation.braid_inventory_item_id}`
        );
      }
      if (useYards > braidInventoryItem.yards) {
        throw new Error(
          `Yards exceeds inventory item yards for item ${allocation.braid_inventory_item_id}`
        );
      }
      const remainingYards = currency(braidInventoryItem.yards).subtract(
        useYards
      ).value;
      totalYards = totalYards.add(useYards);

      braidInventoryChanges.push({
        braid_inventory_item_id: allocation.braid_inventory_item_id,
        production: {
          braid_order_job_id: braidOrderJobId,
          yards: useYards,
          finished_at: knex.fn.now(),
          use_inventory: true,
        },
        adjustment: {
          adjustment: useYards,
          yardsRemaining: remainingYards,
        },
      });
    }

    for (const change of braidInventoryChanges) {
      const returnedProd = await BraidOrderJobProduction.query(trx).insert({
        braid_inventory_item_id: change.braid_inventory_item_id,
        ...change.production,
      });
      console.log('returnedProd', returnedProd);
      await BraidInventoryItem.query(trx)
        .patch({
          yards: change.adjustment.yardsRemaining,
        })
        .where('id', change.braid_inventory_item_id);
      await BraidInventoryItemAdjustment.query(trx).insert({
        braid_inventory_item_id: change.braid_inventory_item_id,
        yards: -change.adjustment.adjustment,
        note: 'Braid Order Production',
        braid_order_job_production_id: returnedProd.id,
      });
    }

    const updatedJob = await updateJobStatusAndGetJobInfo(braidOrderJobId, {
      transaction: trx,
    });

    trx.commit();
    return updatedJob;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

export const markBraidOrderJobProductionBad = async (
  braidOrderJobProductionId: number,
  reason: string,
  extra?: {
    ignore_inventory?: boolean;
  }
) => {
  if (!braidOrderJobProductionId) {
    throw new Error('Braid order job production id is required');
  }
  if (!reason) {
    throw new Error('Reason is required');
  }

  const trx = await BraidOrderJobProduction.startTransaction();
  try {
    const production = await BraidOrderJobProduction.query(trx).findById(
      braidOrderJobProductionId
    );
    if (!production) {
      throw new Error('Production not found');
    }
    if (production.removed_at) {
      throw new Error('Production removed');
    }
    if (!production.finished_at) {
      throw new Error('Production must be finished to mark bad');
    }
    if (production.marked_bad_at) {
      throw new Error('Production already marked bad');
    }
    if (production.use_inventory) {
      throw new Error('Cannot mark production bad when using inventory');
    }

    const braidJob = await braidOrderJobById(production.braid_order_job_id, {
      transaction: trx,
    });
    if (braidJob.status === BraidOrderJobStatus.COMPLETED) {
      throw new Error('Job already completed');
    }
    if (braidJob.removed_at) {
      throw new Error('Job removed');
    }

    await BraidOrderJobProduction.query(trx)
      .patch({
        marked_bad_at: knex.fn.now(),
        marked_bad_reason: reason,
      })
      .where('id', braidOrderJobProductionId);

    if (!extra?.ignore_inventory) {
      await BraidInventoryItemAdjustment.query(trx).insert({
        braid_inventory_item_id: production.braid_inventory_item_id,
        yards: production.yards,
        note: 'Braid Order Production Marked Bad',
        braid_order_job_production_id: production.id,
      });
    }

    const updatedJob = await updateJobStatusAndGetJobInfo(
      production.braid_order_job_id,
      {
        transaction: trx,
      }
    );

    trx.commit();
    return updatedJob;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

export const removeBraidOrderJobProduction = async (
  braidOrderJobProductionId: number,
  extra?: {
    remove_finished?: boolean;
    ignore_inventory?: boolean;
  }
) => {
  if (!braidOrderJobProductionId) {
    throw new Error('Braid order job production id is required');
  }

  const trx = await BraidOrderJobProduction.startTransaction();
  try {
    const production = await BraidOrderJobProduction.query(trx).findById(
      braidOrderJobProductionId
    );
    if (!production) {
      throw new Error('Production not found');
    }
    if (production.removed_at) {
      throw new Error('Production already removed');
    }
    if (production.finished_at && !extra?.remove_finished) {
      throw new Error('Production already finished');
    }
    const braidJob = await braidOrderJobById(production.braid_order_job_id, {
      transaction: trx,
    });
    if (braidJob.status === BraidOrderJobStatus.COMPLETED) {
      throw new Error('Job already completed');
    }
    if (braidJob.removed_at) {
      throw new Error('Job already removed');
    }

    await BraidOrderJobProduction.query(trx)
      .patch({
        removed_at: knex.fn.now(),
      })
      .where('id', braidOrderJobProductionId);

    let newInventoryItem: BraidInventoryItem | null = null;
    if (!extra?.ignore_inventory) {
      newInventoryItem =
        await createBraidInventoryItemAndAdjustmentWithoutCheck(
          braidJob.order.pattern,
          braidJob.order.colors,
          braidJob.order.size,
          braidJob.order.fabric,
          production.yards,
          {
            from_braid_order_job_production_id: production.id,
            adjustment_note: 'Production Removed',
          }
        );
    }

    const updatedJob = await updateJobStatusAndGetJobInfo(
      production.braid_order_job_id,
      {
        transaction: trx,
      }
    );

    trx.commit();
    return {
      job: updatedJob,
      inventory: newInventoryItem,
    };
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

export const removeBraidOrderJob = async (braidOrderJobId: number) => {
  if (!braidOrderJobId) {
    throw new Error('Braid order job id is required');
  }

  const trx = await BraidOrderJob.startTransaction();
  try {
    const braidJob = await braidOrderJobById(braidOrderJobId, {
      transaction: trx,
    });
    if (!braidJob) {
      throw new Error('Job not found');
    }
    if (braidJob.removed_at) {
      throw new Error('Job already removed');
    }

    // check for any productions not removed
    const activeProductions = braidJob.productions.find(
      (production) => !production.removed_at
    );
    if (activeProductions) {
      throw new Error('Cannot remove job with productions on it');
    }

    await BraidOrderJob.query(trx)
      .patch({
        removed_at: knex.fn.now(),
        status: BraidOrderJobStatus.VOID,
      })
      .where('id', braidOrderJobId);

    const updatedJob = await braidOrderJobById(braidOrderJobId, {
      transaction: trx,
    });

    trx.commit();
    return updatedJob;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};
