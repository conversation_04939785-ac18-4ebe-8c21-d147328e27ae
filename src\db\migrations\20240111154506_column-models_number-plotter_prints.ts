import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'plotter_prints',
    (table: Knex.TableBuilder) => {
      table.integer('models_number').nullable();
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(
    'plotter_prints',
    (table: Knex.TableBuilder) => {
      table.dropColumn('models_number');
    }
  );
}
