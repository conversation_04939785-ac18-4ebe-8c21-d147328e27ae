import {
  MoTwillLaserDecoration,
  MoTwillLaserJob,
  MoTwillLaserJobConsumption,
  MoTwillLaserJobType,
} from '@app/models/twill-laser';
import { buildLogger } from '@app/settings';

const logger = buildLogger('service:employee');

interface MoLaserTwillJob {
  comment?: string;
  employeeID: number;
  isRepo?: boolean;
  layers: number;
  moID: number;
  moTwillLaserJobStatusID?: number;
  quantity: number;
  specialLayers?: number;
}

export const newJob = async ({
  comment = 'Sin comentario',
  employeeID,
  isRepo = false,
  layers,
  moID,
  moTwillLaserJobStatusID = 3,
  quantity,
  specialLayers = 0,
}: MoLaserTwillJob) => {
  try {
    const job = await MoTwillLaserJob.query().insert({
      comment,
      employee_id: employeeID,
      is_repo: isRepo ? true : false,
      layers: layers.toString(),
      mo_id: moID,
      mo_twill_laser_job_status_id: moTwillLaserJobStatusID,
      quantity,
      // TODO: should be int field?
      special_layers: specialLayers ? specialLayers.toString() : '0',
    });

    return job;
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      throw new Error(error.message);
    }

    logger.error('Error interno del servidor');

    throw new Error('Error interno del servidor');
  }
};

interface MoLaserTwillJobDecoration {
  comment: string;
  moID: number;
  subMoID: number | null;
  typeID?: number;
}

export const newDecoration = async ({
  comment,
  moID,
  subMoID,
  typeID = 1,
}: MoLaserTwillJobDecoration) => {
  try {
    if (!comment || !moID || !typeID)
      throw new Error(
        'Faltan los datos de la decoración(comentario, tipo de decoración)'
      );

    const searchType = await MoTwillLaserJobType.query()
      .where('id', typeID)
      .where('is_active', 1)
      .select('id')
      .first()
      .castTo<{
        id: number;
      }>();

    if (!searchType) throw new Error('No se encontró el tipo de decoración');

    const decoration = await MoTwillLaserDecoration.query().insert({
      comment,
      mo_id: moID,
      child_mo_id: subMoID,
      mo_twill_laser_job_type_id: typeID,
    });

    return decoration;
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      throw new Error(error.message);
    }

    logger.error('Error interno del servidor');

    throw new Error('Error interno del servidor');
  }
};

interface MoLaserTwillJobConsumption {
  moTwillLaserJobID: number;
  height: number;
  width: number;
  quantity: number;
}

export const newOperatorConsumption = async ({
  moTwillLaserJobID,
  height,
  width,
  quantity,
}: MoLaserTwillJobConsumption) => {
  try {
    if (!moTwillLaserJobID || !height || !width || !quantity)
      throw new Error('Faltan datos');

    const consumption = await MoTwillLaserJobConsumption.query().insert({
      mo_twill_laser_job_id: moTwillLaserJobID,
      height,
      width,
      quantity,
    });

    return consumption;
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error.message);

      throw new Error(error.message);
    }

    logger.error('Error interno del servidor');

    throw new Error('Error interno del servidor');
  }
};
