import type { Request, Response } from 'express';
import { fn, ref } from 'objection';

import { getPagination } from '@app/helpers/pagination';
import {
  WorkFragmentCustomFields,
  WorkFragmentGroupRolls,
  WorkFragmentLog,
  WorkFragmentTypes,
  WorkFragments,
} from '@app/models/fragment.schema';
import { WorkAreaTickets } from '@app/models/tickets.schema';
import type { CreateFragment, CustomField } from '@app/types';

export async function CreateNewFragment(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const {
    employee_id,
    work_area_id,
    custom_fields,
    fragment_type_id,
    mo_id,
    name,
    group_id,
  }: CreateFragment = req.body;

  if (custom_fields.length === 0) {
    return res.status(400).json({
      ok: false,
      message: 'Error, no se encontraron campos personalizados',
    });
  }

  try {
    //  Get column_name from work_fragment_custom_fields
    const custom_fields_values = [];

    for (const custom_field of custom_fields) {
      const { name } = custom_field;

      // Search if the custom field exists
      const searchCustomField = await WorkFragmentCustomFields.query().findOne({
        work_fragment_type_id: fragment_type_id,
        name: name,
      });

      if (!searchCustomField) {
        return res.status(400).json({
          ok: false,
          message: 'Error, el campo personalizado no existe',
        });
      }

      custom_fields_values.push({
        ...custom_field,
        column_name: searchCustomField.column_name,
      });
    }

    // convert column_name of custom_fields_values to custom_[column_name] for the insert
    const custom_fields_values_insert: { [customColumnName: string]: string } =
      {};

    for (const custom_field of custom_fields_values) {
      const { column_name, value } = custom_field;

      custom_fields_values_insert[`custom_${column_name}`] = value;
    }

    // Search if the name already exists
    const searchFragment = await WorkFragments.query().findOne({
      name,
    });

    if (searchFragment) {
      return res.status(400).json({
        ok: false,
        message: 'Error, el nombre ya existe',
      });
    }

    // Create the fragment
    const fragment = await WorkFragments.query().insert({
      name,
      fragment_type_id,
      mo_id,
      work_fragment_group_id: group_id ?? null,
      ...custom_fields_values_insert,
    });

    // Insert fragment log
    await WorkFragmentLog.query().insert({
      work_fragment_id: fragment.id,
      employee_id,
      work_area_id,
      data: JSON.stringify({
        action: 'createFragment',
        name,
      }),
    });

    return res.status(201).json({
      ok: true,
      fragment,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function CreateFragmentType(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { name } = req.body;

  // EAV - Entity-Attribute-Value
  try {
    // Search if the fragment type already exists
    const searchFragmentType = await WorkFragmentTypes.query().findOne({
      name,
    });

    if (searchFragmentType) {
      return res.status(400).json({
        ok: false,
        message: 'Error, el tipo de fragmento ya existe',
      });
    }

    // Create the fragment type
    const fragmentType = await WorkFragmentTypes.query().insert({
      name,
    });

    return res.status(201).json({
      ok: true,
      fragmentType,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function CreateFragmentCustomField(req: Request, res: Response) {
  const { list_values, name, type, work_fragment_type_id } = req.body;

  if (
    !name ||
    !type ||
    !work_fragment_type_id ||
    typeof name !== 'string' ||
    typeof type !== 'string' ||
    typeof work_fragment_type_id !== 'number' ||
    (list_values && typeof list_values !== 'string')
  ) {
    return res.status(400).json({
      ok: false,
      message: 'Error, datos no validos',
    });
  }

  try {
    // Search if the fragment custom field already exists for the fragment type and column name
    const searchFragmentCustomField =
      await WorkFragmentCustomFields.query().findOne({
        work_fragment_type_id,
        column_name: name,
      });

    if (searchFragmentCustomField) {
      return res.status(400).json({
        ok: false,
        message: 'Error, el campo personalizado ya existe',
      });
    }

    // Create the fragment custom field
    const fragmentCustomField = await WorkFragmentCustomFields.query().insert({
      name: name,
      type: type,
      work_fragment_type_id,
      list_values: list_values ?? null,
      column_name: name,
    });

    return res.status(201).json({
      ok: true,
      fragmentCustomField,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

interface FragmentWhere {
  mo_id?: number;
  fragment_type_id?: number;
}

export async function GetFragments(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { mo_id, fragment_type_id, page = 1, size = 10 } = req.query;

  try {
    //  Custom where
    const where: FragmentWhere = {};

    // Cache for custom fields
    const fragmentCache = {};

    // Return fragments clean
    const returnFragments = [];

    if (mo_id) {
      where.mo_id = +mo_id;
    }
    if (fragment_type_id) {
      where.fragment_type_id = +fragment_type_id;
    }

    const totalFragments = await WorkFragments.totalFragments({ where });

    if (totalFragments === 0) {
      return res.status(200).json({
        ok: true,
        data: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 0,
      });
    }

    // Get pagination
    const { limit, offset, currentPage, totalItems, totalPages } =
      getPagination({
        page: +page,
        size: +size,
        count: totalFragments,
      });

    // Get the fragments
    const fragments: any = await WorkFragments.query()
      .join(
        'work_fragment_types',
        'work_fragment_types.id',
        'work_fragments.fragment_type_id'
      )
      .leftJoin(
        'work_fragment_groups',
        'work_fragment_groups.id',
        'work_fragments.work_fragment_group_id'
      )
      .where(where)
      .select([
        'work_fragments.*',
        {
          fragment_type_name: 'work_fragment_types.name',
        },
        {
          roll: 'work_fragment_groups.work_fragment_group_roll_id',
        },
        fn
          .coalesce(
            WorkFragmentTypes.query()
              .select('work_fragment_types.name')
              .where(
                'work_fragment_types.id',
                ref('work_fragment_groups.work_fragment_group_type_id')
              )
              .limit(1),
            'Sin comentario'
          )
          .as('type_group'),
      ])
      .limit(limit)
      .offset(offset);

    for (let i = 0; i < fragments.length; i++) {
      let customFieldCache = fragmentCache[fragments[i].fragment_type_id];

      if (!customFieldCache) {
        // Get the custom fields of the fragment
        const customFieldsData: any =
          await WorkFragmentCustomFields.query().where(
            'work_fragment_type_id',
            +fragments[i].fragment_type_id
          );
        fragmentCache[fragments[i].fragment_type_id] = customFieldsData;
        customFieldCache = customFieldsData;
      }

      const customFields = customFieldCache;
      const returnFragment = {
        id: fragments[i].id,
        name: fragments[i].name,
        fragment_type_name: fragments[i].fragment_type_name,
        group: fragments[i].work_fragment_group_id,
        roll: fragments[i].roll,
        type_group: fragments[i].type_group,
      };

      // Add the custom fields name to the fragment
      customFields.forEach((field) => {
        const customColumnName = `custom_${field.column_name}`;
        returnFragment[field.name] = fragments[i][customColumnName];
      });
      returnFragments.push(returnFragment);
    }

    if (returnFragments.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no se encontraron fragmentos',
      });
    }

    return res.status(200).json({
      ok: true,
      currentPage,
      totalItems,
      totalPages,
      data: returnFragments,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetFragment(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { id } = req.params;
  try {
    // Get the fragment
    const fragment: any = await WorkFragments.query()
      .join(
        'work_fragment_types',
        'work_fragments.fragment_type_id',
        'work_fragment_types.id'
      )
      .join('mo_numbers', 'work_fragments.mo_id', 'mo_numbers.mo_id')
      .where('work_fragments.id', id)
      .select('work_fragments.*', 'mo_numbers.num', {
        fragment_type: 'work_fragment_types.name',
      });

    // Get the custom fields of the fragment
    const customFields: any = await WorkFragmentCustomFields.query().where(
      'work_fragment_type_id',
      fragment[0].fragment_type_id
    );

    const returnFragment = {
      id: fragment[0].id,
      name: fragment[0].name,
      type: fragment[0].fragment_type,
      custom_fields: [],
    };

    // Add the custom fields name to the fragment
    customFields.forEach((field) => {
      const customColumnName = `custom_${field.column_name}`;
      returnFragment.custom_fields.push({
        id: field.id,
        name: field.name,
        value: fragment[0][customColumnName],
      });
    });

    return res.status(200).json({
      ok: true,
      data: returnFragment,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetFragmentdById(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { id } = req.params;
  try {
    // Get the fragment
    const fragments = await WorkFragments.query()
      .join(
        'work_fragment_types',
        'work_fragments.fragment_type_id',
        'work_fragment_types.id'
      )
      .where('work_fragments.id', 'like', `%${id}%`)
      .orWhere('work_fragments.name', 'like', `%${id}%`)
      .select([
        { id: 'work_fragments.id' },
        { name: 'work_fragments.name' },
        { type: 'work_fragment_types.name' },
      ]);

    return res.status(200).json({
      ok: true,
      data: fragments,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetFragmentCustomFields(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { id } = req.params;
  try {
    // Get all fragment custom fields
    const fragmentCustomFields = await WorkFragmentCustomFields.query()
      .select('id', 'name', 'type', 'list_values', 'column_name')
      .where({
        work_fragment_type_id: id,
      });

    if (fragmentCustomFields.length === 0) {
      return res.status(400).json({
        ok: false,
        message:
          'Error, no hay campos personalizados para el tipo de fragmento',
      });
    }

    return res.status(200).json({
      ok: true,
      fragmentCustomFields,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetFragmentCustomField(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { id } = req.params;
  try {
    // Get the fragment custom field
    const fragmentCustomField = await WorkFragmentCustomFields.query()
      .select('id', 'name', 'type', 'list_values')
      .where({
        id,
      });

    if (fragmentCustomField.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no se encontro el campo personalizado',
      });
    }

    return res.status(200).json({
      ok: true,
      fragmentCustomField,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetFragmentTypes(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  try {
    // Get all fragment types
    const fragmentTypes: any = await WorkFragmentTypes.query().select(
      'id',
      'name'
    );

    const data = [];
    for (let i = 0; i < fragmentTypes.length; i++) {
      const customFields = await WorkFragmentCustomFields.query()
        .where('work_fragment_type_id', fragmentTypes[i].id)
        .select('column_name', 'name', 'list_values');

      data.push({
        id: fragmentTypes[i].id,
        name: fragmentTypes[i].name,
        custom_fields: customFields,
      });
    }

    if (data.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no hay tipos de fragmentos',
      });
    }

    return res.status(200).json({
      ok: true,
      data,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetFragmentType(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { id } = req.params;
  try {
    // Get the fragment type
    const fragmentType = await WorkFragmentTypes.query()
      .select('id', 'name')
      .where({
        id,
      });

    if (fragmentType.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no se encontro el tipo de fragmento',
      });
    }

    return res.status(200).json({
      ok: true,
      fragmentType,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetFragmentLog(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { id } = req.params;
  try {
    // Get the fragment log by fragment id
    const fragmentLog = await WorkFragmentLog.query()
      .join(
        'employees',
        'work_fragment_log.employee_id',
        'employees.employee_id'
      )
      .join(
        'work_areas',
        'work_fragment_log.work_area_id',
        'work_areas.work_area_id'
      )
      .join(
        'work_fragments',
        'work_fragment_log.work_fragment_id',
        'work_fragments.id'
      )
      .select(
        'work_fragment_log.created_at',
        'work_fragment_log.data',
        { fragment: 'work_fragments.name' },
        { employee: 'employees.first_name' },
        {
          area: 'work_areas.area_name',
        }
      )
      .where({
        work_fragment_id: id,
      });

    if (fragmentLog.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no se encontro el log del fragmento',
      });
    }

    return res.status(200).json({
      ok: true,
      fragmentLog,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function UpdateFragment(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { id } = req.params;
  const { custom_fields, name, employee_id, work_area_id } = req.body;
  const returnFragments = [];

  if (!custom_fields) {
    return res.status(400).json({
      ok: false,
      message: 'Error, datos no validos',
    });
  }

  //  Get values the custom fields
  const custom_fields_values = custom_fields.reduce((a, field: CustomField) => {
    return {
      ...a,
      ['custom_' + field.name]: field.value || null,
    };
  }, {});

  try {
    // Update the fragment
    await WorkFragments.query()
      .update({
        ...custom_fields_values,
        name,
      })
      .where({
        id,
      });

    // Insert the log
    await WorkFragmentLog.query().insert({
      work_fragment_id: id,
      employee_id,
      work_area_id,
      data: JSON.stringify({
        ...custom_fields_values,
        name,
        action: 'updateFragment',
      }),
    });

    // Get the fragment
    const fragment: any = await WorkFragments.query()
      .join(
        'work_fragment_types',
        'work_fragments.fragment_type_id',
        'work_fragment_types.id'
      )
      .join('mo_numbers', 'work_fragments.mo_id', 'mo_numbers.mo_id')
      .where('work_fragments.id', id)
      .select('work_fragments.*', 'mo_numbers.num', {
        fragment_type: 'work_fragment_types.name',
      });

    // Get the custom fields of the fragment
    const customFields: any = await WorkFragmentCustomFields.query().where(
      'work_fragment_type_id',
      fragment[0].fragment_type_id
    );

    const returnFragment = {
      id: fragment[0].id,
      name: fragment[0].name,
      fragment_type_id: fragment[0].fragment_type_id,
      mo_id: fragment[0].mo_id,
      created_at: fragment[0].created_at,
      updated_at: fragment[0].updated_at,
    };

    // Add the custom fields name to the fragment
    customFields.forEach((field) => {
      const customColumnName = `custom_${field.column_name}`;
      returnFragment[field.name] = fragment[0][customColumnName];
    });

    returnFragments.push(returnFragment);

    return res.status(200).json({
      ok: true,
      fragment: returnFragments[0],
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetFragmentsByNameAndMo(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { mo_id, name } = req.params;
  try {
    // Get all fragments by mo id
    const fragments: any = await WorkFragments.query()
      .join(
        'work_fragment_types',
        'work_fragments.fragment_type_id',
        'work_fragment_types.id'
      )
      .where('work_fragments.mo_id', mo_id)
      .where('work_fragments.name', 'like', `%${name}%`)
      .select(
        'work_fragments.*',
        'work_fragments.id',
        { fragment: 'work_fragments.name' },
        { type_id: 'work_fragment_types.id' },
        { type: 'work_fragment_types.name' }
      );

    const data = [];

    // Get the custom fields of the fragments
    for (let i = 0; i < fragments.length; i++) {
      const customFields: any = await WorkFragmentCustomFields.query()
        .where('work_fragment_type_id', fragments[i].type_id)
        .select('id', 'column_name', 'name', 'list_values');

      const returnFragment = {
        id: fragments[i].id,
        name: fragments[i].fragment,
        type_id: fragments[i].type_id,
        type: fragments[i].type,
        customFields: customFields,
      };

      // add the fragment custom fields value to each customField
      customFields.forEach((field) => {
        const customColumnName = `custom_${field.column_name}`;
        field.value = fragments[i][customColumnName];
      });

      data.push(returnFragment);
    }

    if (data.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no hay tipos de fragmentos',
      });
    }

    return res.status(200).json({
      ok: true,
      data,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetFragmentNameAndTypeByMo(
  req: Request,
  res: Response
): Promise<Response<unknown, Record<string, unknown>>> {
  const { mo_id } = req.params;
  try {
    // Get all fragments by mo_id
    const fragments: any = await WorkFragments.query()
      .join(
        'work_fragment_types',
        'work_fragments.fragment_type_id',
        'work_fragment_types.id'
      )
      .where('work_fragments.mo_id', mo_id)
      .select(
        {
          id: 'work_fragments.id',
        },
        {
          fragment: 'work_fragments.name',
        },
        {
          type: 'work_fragment_types.name',
        }
      );

    if (fragments.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no hay tipos de fragmentos',
      });
    }

    return res.status(200).json({
      ok: true,
      data: fragments,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: 'Error, datos no validos',
      code: error,
    });
  }
}

export async function GetRollById(req: Request, res: Response) {
  const { id } = req.query;

  try {
    const rolls = await WorkFragmentGroupRolls.query()
      .join(
        'work_fragment_group_types',
        'work_fragment_group_rolls.work_fragment_group_type_id',
        'work_fragment_group_types.id'
      )
      .select([
        'work_fragment_group_rolls.id',
        { type: 'work_fragment_group_types.name' },
      ])
      .where('work_fragment_group_rolls.id', 'like', `%${id}%`);

    if (rolls.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no hay rollos',
      });
    }

    return res.status(200).json({
      ok: true,
      data: rolls,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: `Error, ${error}`,
    });
  }
}

export async function GetAllFragmentsInRoll(req: Request, res: Response) {
  const { id, areaID, voucherTypeID } = req.query;

  try {
    const fragmentCache = {};

    const returnFragments = [];

    // Obtener todos los fragmentos de un rollo
    const fragments: any = await WorkFragments.query()
      .join(
        'work_fragment_types',
        'work_fragment_types.id',
        'work_fragments.fragment_type_id'
      )
      .leftJoin('mo_numbers', 'work_fragments.mo_id', 'mo_numbers.mo_id')
      .leftJoin(
        'work_fragment_groups',
        'work_fragment_groups.id',
        'work_fragments.work_fragment_group_id'
      )
      .select([
        'work_fragments.*',
        {
          num: 'mo_numbers.num',
        },
        {
          fragment_type_name: 'work_fragment_types.name',
        },
        {
          roll: 'work_fragment_groups.work_fragment_group_roll_id',
        },
        {
          roll_sort: 'work_fragment_groups.work_fragment_group_roll_sort',
        },
        fn
          .coalesce(
            WorkFragmentTypes.query()
              .select('work_fragment_types.name')
              .where(
                'work_fragment_types.id',
                ref('work_fragment_groups.work_fragment_group_type_id')
              )
              .limit(1),
            'Sin comentario'
          )
          .as('type_group'),
      ])
      .where('work_fragment_groups.work_fragment_group_roll_id', +id);

    for (let i = 0; i < fragments.length; i++) {
      let customFieldCache = fragmentCache[fragments[i].fragment_type_id];

      if (!customFieldCache) {
        const customFieldData: any =
          await WorkFragmentCustomFields.query().where(
            'work_fragment_type_id',
            fragments[i].fragment_type_id
          );

        fragmentCache[fragments[i].fragment_type_id] = customFieldData;
        customFieldCache = customFieldData;
      }

      const customFields = customFieldCache;
      const returnFragment = {
        id: fragments[i].id,
        mo_id: fragments[i].mo_id,
        num: fragments[i].num,
        name: fragments[i].name,
        fragment_type_name: fragments[i].fragment_type_name,
        group: fragments[i].work_fragment_group_id,
        group_type: fragments[i].type_group,
        roll: fragments[i].roll,
        roll_sort: fragments[i].roll_sort,
      };

      customFields.forEach((field) => {
        const customColumnName = `custom_${field.name}`;
        returnFragment[customColumnName] = fragments[i][customColumnName];
      });

      returnFragments.push(returnFragment);
    }

    if (returnFragments.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no se encontraron fragmentos',
      });
    }

    // obtener los tickets de cada fragmento por area y tipo de comprobante
    for (let i = 0; i < returnFragments.length; i++) {
      const tickets: any = WorkAreaTickets.query()
        .leftJoin(
          'work_vouchers',
          'work_area_tickets.work_voucher_id',
          'work_vouchers.id'
        )
        .leftJoin(
          'work_voucher_types',
          'work_vouchers.work_voucher_type_id',
          'work_voucher_types.id'
        )
        .leftJoin('mo_numbers', 'work_vouchers.mo_id', 'mo_numbers.mo_id')
        .leftJoin(
          'work_area_ticket_statuses',
          'work_area_tickets.work_area_ticket_status_id',
          'work_area_ticket_statuses.id'
        )
        .leftJoin(
          'work_statuses',
          'work_area_ticket_statuses.work_status_id',
          'work_statuses.id'
        )
        .leftJoin(
          'work_inventory_bins',
          'work_area_tickets.work_inventory_location_id',
          'work_inventory_bins.id'
        )
        .select([
          'work_area_tickets.id',
          { ticketStatus: 'work_area_ticket_statuses.name' },
          { ticketLocation: 'work_inventory_bins.name' },
          { mo: 'mo_numbers.num' },
          { voucherType: 'work_voucher_types.name' },
        ])
        .where('work_vouchers.mo_id', returnFragments[i].mo_id);

      if (+areaID) {
        tickets.where('work_area_tickets.work_area_id', +areaID);
      }

      if (+voucherTypeID) {
        tickets.where('work_vouchers.work_voucher_type_id', +voucherTypeID);
      }

      const resultTickets = await tickets;

      returnFragments[i].tickets = resultTickets;
    }

    return res.status(200).json({
      ok: true,
      data: returnFragments,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      ok: false,
      message: `Error, ${error}`,
    });
  }
}

export async function GetAllRolls(_: Request, res: Response) {
  try {
    // obtenermo todos los rollos
    const rolls = await WorkFragmentGroupRolls.query()
      .join(
        'work_fragment_group_types',
        'work_fragment_group_rolls.work_fragment_group_type_id',
        'work_fragment_group_types.id'
      )
      .select([
        'work_fragment_group_rolls.id',
        { type: 'work_fragment_group_types.name' },
      ]);

    if (rolls.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no hay rollos',
      });
    }

    return res.status(200).json({
      ok: true,
      data: rolls,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: `Error, ${error}`,
    });
  }
}

export async function GetAllFragmentsOfTheMO(req: Request, res: Response) {
  const { mo } = req.params;

  const returnFragments = [];

  try {
    // obtenemos todos los fragmentos con el mo_id
    const fragments: any = await WorkFragments.query()
      .join(
        'work_fragment_types',
        'work_fragments.fragment_type_id',
        'work_fragment_types.id'
      )
      .select([
        'work_fragments.*',
        {
          fragment_type_name: 'work_fragment_types.name',
        },
      ])
      .where('work_fragments.mo_id', +mo);

    if (fragments.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no se encontraron fragmentos',
      });
    }

    for (let i = 0; i < fragments.length; i++) {
      const customFieldData: any = await WorkFragmentCustomFields.query().where(
        'work_fragment_type_id',
        +fragments[i].fragment_type_id
      );

      const returnFragment = {
        id: fragments[i].id,
        name: fragments[i].name,
        fragment_type_name: fragments[i].fragment_type_name,
        fragment_group_id: fragments[i].work_fragment_group_id,
      };

      customFieldData.forEach(
        (field: { column_name: string; name: string | number }) => {
          const customColumnName = `custom_${field.column_name}`;
          returnFragment[field.name] = fragments[i][customColumnName];
        }
      );

      returnFragments.push(returnFragment);
    }

    return res.status(200).json({
      ok: true,
      data: returnFragments,
    });
  } catch (error) {
    return res.status(500).json({
      ok: false,
      message: `Error, ${error}`,
    });
  }
}

export async function GetFragmentWithCustomFields(req: Request, res: Response) {
  const { id } = req.params;

  const fragmentCache = {};
  const returnFragments = [];

  try {
    const fragments: any = await WorkFragments.query()
      .join(
        'work_fragment_types',
        'work_fragment_types.id',
        'work_fragments.fragment_type_id'
      )
      .leftJoin(
        'work_fragment_groups',
        'work_fragment_groups.id',
        'work_fragments.work_fragment_group_id'
      )
      .where('work_fragments.id', id)
      .select([
        'work_fragments.*',
        {
          fragment_type_name: 'work_fragment_types.name',
        },
      ]);

    for (let i = 0; i < fragments.length; i++) {
      let customFieldCache = fragmentCache[fragments[i].fragment_type_id];

      if (!customFieldCache) {
        const customFieldData: any =
          await WorkFragmentCustomFields.query().where(
            'work_fragment_type_id',
            fragments[i].fragment_type_id
          );

        fragmentCache[fragments[i].fragment_type_id] = customFieldData;
        customFieldCache = customFieldData;
      }

      const customFields = customFieldCache;
      const returnFragment = {
        id: fragments[i].id,
        name: fragments[i].name,
        fragment_type_name: fragments[i].fragment_type_name,
      };

      customFields.forEach((field) => {
        const customColumnName = `custom_${field.column_name}`;
        returnFragment[field.name] = fragments[i][customColumnName];
      });

      returnFragments.push(returnFragment);
    }

    if (returnFragments.length === 0) {
      return res.status(400).json({
        ok: false,
        message: 'Error, no se encontraron fragmentos',
      });
    }

    return res.status(200).json({
      ok: true,
      data: returnFragments,
    });
  } catch (error) {
    console.log(error);

    return res.status(500).json({
      ok: false,
      message: `Error, ${error}`,
    });
  }
}
