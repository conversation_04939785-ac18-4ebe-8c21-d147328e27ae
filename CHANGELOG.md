# Changelog
> All notable changes to this project will be documented in this file.
> This Changelog adheres to the [Semantic Versioning]("https://semver.org/")


## [1.23.0](https://bitbucket.org/varpro/server-varpro/compare/1.23.0..1.22.0) - 2025-04-21

### Features

* feat: migration tables @DiegoCruzM ([`d417b20`](https://bitbucket.org/varpro/server-varpro/commits/d417b20ae1c9c8710d42fe025cdf96973879b190))

### Fixes

* fix: Excel file for adidas @DiegoCruzM ([`c6efa7a`](https://bitbucket.org/varpro/server-varpro/commits/c6efa7a7dfb3d2d405d19ed22836ff45fd5faafc))
* fix: add order to roll by company @DiegoCruzM ([`75e123c`](https://bitbucket.org/varpro/server-varpro/commits/75e123ca68adf344f47d5367be27d810c4919ce8))
* fix: prints without combos @DiegoCruzM ([`495175f`](https://bitbucket.org/varpro/server-varpro/commits/495175f008738e752a3a7c08a6a56715f134e667))
* fix: corregir el crear sesion para warehouse pull @Carlos David Martinez Medrano ([`a576b9e`](https://bitbucket.org/varpro/server-varpro/commits/a576b9ee2d02dea86208611d7cce45585d2e0f9d))
* fix: laser for other customers @DiegoCruzM ([`8e51117`](https://bitbucket.org/varpro/server-varpro/commits/8e51117f8408ea382d73d6a708f1cea62f1130ed))
* fix: group print for optitex @DiegoCruzM ([`a82c082`](https://bitbucket.org/varpro/server-varpro/commits/a82c08216056ab1588e873e6bd678fb248a65c41))
* fix: adidas query @DiegoCruzM ([`7249f5e`](https://bitbucket.org/varpro/server-varpro/commits/7249f5e36c21f810158f0b55886e3c2aa2f5e214))




## [1.22.0](https://bitbucket.org/varpro/server-varpro/compare/1.22.0..1.21.0) - 2025-02-19

### Features

* feat: warehouse pull @Carlos David Martinez Medrano ([`01a1b0c`](https://bitbucket.org/varpro/server-varpro/commits/01a1b0cb57ecc4170cbb0759585efcfffd426c3c))
* feat: warehouse downloaders @Carlos David Martinez Medrano ([`20a5b72`](https://bitbucket.org/varpro/server-varpro/commits/20a5b726a4cd3b59cf71419b540986787f85d4be))
* feat: agregar ordenes a la warehouse pull session @Carlos David Martinez Medrano ([`788fab6`](https://bitbucket.org/varpro/server-varpro/commits/788fab668abd0a1ecbd699177a13012112284663))
* feat: crear migraciones para la app warehouse pull @Carlos David Martinez Medrano ([`4713a13`](https://bitbucket.org/varpro/server-varpro/commits/4713a13866de64bb47608703d5f6a7c993c03eef))
* feat: migraciones warehouse pull @Carlos David Martinez Medrano ([`70d36b3`](https://bitbucket.org/varpro/server-varpro/commits/70d36b310f74667fa3141443087aaf2fc2b04b15))

### Fixes

* fix: corregir campos de migracion warehouse_pull_session_allocations @Carlos David Martinez Medrano ([`7f91b82`](https://bitbucket.org/varpro/server-varpro/commits/7f91b826cc94b5c62496cdffd1c103d6e248dd4b))




## [1.21.0](https://bitbucket.org/varpro/server-varpro/compare/1.21.0..1.20.8) - 2025-02-19

### Features

* feat: style combos @DiegoCruzM ([`f5d0f61`](https://bitbucket.org/varpro/server-varpro/commits/f5d0f61abbfddc2b17bd93314753cbd95f422058))
* feat: working with style combos and ts issues @DiegoCruzM ([`c1bdb18`](https://bitbucket.org/varpro/server-varpro/commits/c1bdb1873cacc7a91cedb858747bd675512a09b2))

### Fixes

* fix: ts and objection @DiegoCruzM ([`2b4f9a2`](https://bitbucket.org/varpro/server-varpro/commits/2b4f9a225944e9e8ce200e39fabb95705f0b0c94))
* fix: fixing ts models query @DiegoCruzM ([`96b6f6e`](https://bitbucket.org/varpro/server-varpro/commits/96b6f6eb960f5fa10ce8d7e29081bb411eee49b2))




## [1.20.8](https://bitbucket.org/varpro/server-varpro/compare/1.20.8..1.20.7) - 2025-02-17






## [1.20.7](https://bitbucket.org/varpro/server-varpro/compare/1.20.7..1.20.6) - 2025-02-17






## [1.20.6](https://bitbucket.org/varpro/server-varpro/compare/1.20.6..1.20.5) - 2025-02-14






## [1.20.5](https://bitbucket.org/varpro/server-varpro/compare/1.20.5..1.20.4) - 2025-02-14






## [1.20.4](https://bitbucket.org/varpro/server-varpro/compare/1.20.4..1.20.3) - 2025-02-14






## [1.20.3](https://bitbucket.org/varpro/server-varpro/compare/1.20.3..1.14.16) - 2025-02-13

### Features

* feat: more plotter scan stuff @Peter Brink ([`f953978`](https://bitbucket.org/varpro/server-varpro/commits/f953978b9731ebb8eb900b31c391221ae1a5a7da))
* feat: obtener detalles de una orden de pedreria @Carlos David Martinez Medrano ([`8421814`](https://bitbucket.org/varpro/server-varpro/commits/842181455247041119022aac04afc317abc3fbf0))
* feat: adding repo updates for accounting app smith @DiegoCruzM ([`304061e`](https://bitbucket.org/varpro/server-varpro/commits/304061e20b73cae1add9d1852652892600d80aba))
* feat: final fixes for cutting plot scanning @Peter Brink ([`a5b701a`](https://bitbucket.org/varpro/server-varpro/commits/a5b701a87889e4f0dedf3df65ee40b3e678a403a))
* feat: adding style documents endpoint @DiegoCruzM ([`f9c93c5`](https://bitbucket.org/varpro/server-varpro/commits/f9c93c5a70a7dbeac1d24291a8d48f96efc6730b))
* feat: app rework @Carlos David Martinez Medrano ([`250c234`](https://bitbucket.org/varpro/server-varpro/commits/250c234ac9cb6caede326ca94b79b14510fc1c17))
* feat: agregar comentario en las producciones de laser @Carlos David Martinez Medrano ([`8b856c8`](https://bitbucket.org/varpro/server-varpro/commits/8b856c8913e846bc96d160aa3cd8d36bbadd81e6))
* feat: filtros en obtener todos los tickets @Carlos David Martinez Medrano ([`be7f581`](https://bitbucket.org/varpro/server-varpro/commits/be7f581b777860e2a65a0de324dbd24df40c92fd))

### Fixes

* fix: agregar alerta de orden sin cuerpo en laser art @Carlos David Martinez Medrano ([`4ca0ee0`](https://bitbucket.org/varpro/server-varpro/commits/4ca0ee00db5653a96eb9fb5fe3b2fe365e77e85b))
* fix: corregir el error al escanear mobarcode en metis @Carlos David Martinez Medrano ([`926a2c2`](https://bitbucket.org/varpro/server-varpro/commits/926a2c25c5a03c646f751424aeac5c409caa0e53))
* fix: left join in active vouchers @DiegoCruzM ([`864ce60`](https://bitbucket.org/varpro/server-varpro/commits/864ce6095740e1e96ae161e323af3517964b9887))
* fix: repo invoice date @DiegoCruzM ([`fa5ea72`](https://bitbucket.org/varpro/server-varpro/commits/fa5ea7219aaceab2398cbd6a91c002d09bf397b9))
* fix: corregir error al escanear voucher en la seccion de ubicaciones en METISAPP @Carlos David Martinez Medrano ([`8eb7788`](https://bitbucket.org/varpro/server-varpro/commits/8eb778846039f317757946eee76fc0d4810047de))
* fix: removed document @DiegoCruzM ([`428218a`](https://bitbucket.org/varpro/server-varpro/commits/428218a74638a7fde5c3b604569097bb5c32dcf9))
* fix: is removed validation @DiegoCruzM ([`38c9eb4`](https://bitbucket.org/varpro/server-varpro/commits/38c9eb415096d8a7425db4af97caad1b89e5a6c0))
* fix: corregir la descarga de tickets por ubicacion @Carlos David Martinez Medrano ([`4c6316c`](https://bitbucket.org/varpro/server-varpro/commits/4c6316cd48e930db95400e3ce69b07f262d8f7b3))
* fix: corregir el buscador de mos en la laser twill app @Carlos David Martinez Medrano ([`e15f568`](https://bitbucket.org/varpro/server-varpro/commits/e15f568ca32b00f85033f6e0c37ed24df0476d4a))
* fix: corregir los reportes de laser twill @Carlos David Martinez Medrano ([`5ac3b78`](https://bitbucket.org/varpro/server-varpro/commits/5ac3b7807d642cf51da693797c567f78fc2b7311))
* fix: fix report to get daily jobs in laser twill @Carlos David Martinez Medrano ([`6798a30`](https://bitbucket.org/varpro/server-varpro/commits/6798a3020d15edac933ebd1bfdc16df47b85276b))
* fix: renombrar campos y tabla de laser twill @Carlos David Martinez Medrano ([`c8ed6ed`](https://bitbucket.org/varpro/server-varpro/commits/c8ed6ed29162b7bb6ab37a9eabf9ebf7571cae98))
* fix: corregir el crear decoraciones desde los artistas @Carlos David Martinez Medrano ([`aa81e0f`](https://bitbucket.org/varpro/server-varpro/commits/aa81e0f7cc8d1ed4105070083f47335f1ceb8a7e))
* fix: adding invoice number for import export @Diego Cruz ([`863c46d`](https://bitbucket.org/varpro/server-varpro/commits/863c46da8fce1d0285215ab28e3fa4f5ef8b0cbc))
* fix: corregir query de obtener todos los tickets @Carlos David Martinez Medrano ([`6ae3155`](https://bitbucket.org/varpro/server-varpro/commits/6ae31555e4148cb6f810111d2dc2883613e37ddc))
* fix: corregir la fecha de busqueda en todos los tickets METIS @Carlos David Martinez Medrano ([`7edae30`](https://bitbucket.org/varpro/server-varpro/commits/7edae30a6a0af8891da9162dffa1966b4a9a6d07))
* fix: agregar columnas a reportes de metiapp @Carlos David Martinez Medrano ([`3bb390b`](https://bitbucket.org/varpro/server-varpro/commits/3bb390b893ee0d5a7e3fb89a2769a7dcf2243790))
* fix: corregir el crear ubicaciones @Carlos David Martinez Medrano ([`860f0b3`](https://bitbucket.org/varpro/server-varpro/commits/860f0b3d6bb615cfd508c01a8dc29545dad34e97))
* fix: renombrar columna del reporte de ubicaciones @Carlos David Martinez Medrano ([`680d3ba`](https://bitbucket.org/varpro/server-varpro/commits/680d3baa0fcbf5f14524b71a493ca43d70a8cf04))




## [1.14.16](https://bitbucket.org/varpro/server-varpro/compare/1.14.16..1.14.14) - 2024-11-05

### Features

* feat: laser twill artist @Carlos David Martinez Medrano ([`bcbeef9`](https://bitbucket.org/varpro/server-varpro/commits/bcbeef9462a07e40094506ef6e2c15df8e3f462c))
* feat: add decorations to laser operators @Carlos David Martinez Medrano ([`87317c0`](https://bitbucket.org/varpro/server-varpro/commits/87317c08ba0b538f07fcd04a7f3c1224c5aa4e1c))
* feat: create job and decorations @Carlos David Martinez Medrano ([`ae8830d`](https://bitbucket.org/varpro/server-varpro/commits/ae8830d565f81f6049089f67d8df423152e67045))
* feat: configuracion de producciones para los trabajos laser @Carlos David Martinez Medrano ([`4461bc6`](https://bitbucket.org/varpro/server-varpro/commits/4461bc65a28435aa7928233c857ac1e9cf8b59fc))
* feat: add decorations @Carlos David Martinez Medrano ([`01d0bc3`](https://bitbucket.org/varpro/server-varpro/commits/01d0bc3eb888ddbb0b2a8f32f988e78f62ea1fad))
* feat: agregar validacion de escaneo para entrada @Carlos David Martinez Medrano ([`f0b3599`](https://bitbucket.org/varpro/server-varpro/commits/f0b3599f05a0a09c94175d0a818934a4d288c4a0))

### Fixes

* fix: adidas fabric table @DiegoCruzM ([`48e08f2`](https://bitbucket.org/varpro/server-varpro/commits/48e08f2fba7ebd3bc70216cca43d2286f2b71477))
* fix: adding style combos @DiegoCruzM ([`f1ae89a`](https://bitbucket.org/varpro/server-varpro/commits/f1ae89af06137c3de595f2b5df6210c2e939907f))
* fix: validar que sea sub voucher la orden para asignar a un artista @Carlos David Martinez Medrano ([`7181963`](https://bitbucket.org/varpro/server-varpro/commits/71819638b36d3f72a135d683e9e7300aaffa0605))




## [1.14.14](https://bitbucket.org/varpro/server-varpro/compare/1.14.14..1.14.13) - 2024-10-11






## [1.14.13](https://bitbucket.org/varpro/server-varpro/compare/1.14.13..1.14.0) - 2024-10-10


### Fixes

* fix: corregir el duplicado de ordenes al crear @Carlos David Martinez Medrano ([`ff79a10`](https://bitbucket.org/varpro/server-varpro/commits/ff79a10e25a63f563067b55bc2e2ac5159b6b992))
* fix: corregir el obtener ordenes en FQ @Carlos David Martinez Medrano ([`63303bd`](https://bitbucket.org/varpro/server-varpro/commits/63303bd5b8ec6bba7bd682fe4bfec384e77924c9))
* fix: plotter machine number @DiegoCruzM ([`b501e65`](https://bitbucket.org/varpro/server-varpro/commits/b501e6525038b6089cfe1db6adb64b8e75e6944c))
* fix: corregir reporte de pedreria @Carlos David Martinez Medrano ([`413d265`](https://bitbucket.org/varpro/server-varpro/commits/413d2658ed32416b814a4a5ddb9014967dbe7947))
* fix: plotter remove combos @DiegoCruzM ([`9d398c8`](https://bitbucket.org/varpro/server-varpro/commits/9d398c8cd87691b511c67f58e1e39c7e032fa7a4))
* fix: adding extra inch by ply @DiegoCruzM ([`88e31d6`](https://bitbucket.org/varpro/server-varpro/commits/88e31d6be3c69241f0120f34527ce14b146e001e))
* fix: extra fabric safariland @DiegoCruzM ([`d7c2cdd`](https://bitbucket.org/varpro/server-varpro/commits/d7c2cdd83ba1189f420d21dd8b23757371b21d2c))
* fix: corregir el buscar el estilo de arte @Carlos David Martinez Medrano ([`579550e`](https://bitbucket.org/varpro/server-varpro/commits/579550eae276f3fe1766e0cb4198a74c8d592e99))
* fix: corregir el asignar ordenes q no respeta el orden del bloque @Carlos David Martinez Medrano ([`e9b883a`](https://bitbucket.org/varpro/server-varpro/commits/e9b883a6a4c409499cd49694bd04c6fad86a3c7a))
* fix: corregir el escaneo de entra en laser varsity @Carlos David Martinez Medrano ([`17b21fb`](https://bitbucket.org/varpro/server-varpro/commits/17b21fb881d10ffaf4d6c9f6cdfdaa691eeeba2b))




## [1.14.0](https://bitbucket.org/varpro/server-varpro/compare/1.14.0..1.13.13) - 2024-08-27

### Features

* feat: agregar estado FQ a las asignaciones @Carlos David Martinez Medrano ([`85a54bf`](https://bitbucket.org/varpro/server-varpro/commits/85a54bfc7cc8831ee686199b3b013285359ce94f))
* feat: laser twill partial @Carlos David Martinez Medrano ([`f38fce8`](https://bitbucket.org/varpro/server-varpro/commits/f38fce8119d1c3ca5ae3ee0d6cd65f5846ee606b))
* feat: delete orders @Carlos David Martinez Medrano ([`b92f281`](https://bitbucket.org/varpro/server-varpro/commits/b92f281bf490a77578aa9c5c157275e489244a99))
* feat: laser twill art @Carlos David Martinez Medrano ([`3844acf`](https://bitbucket.org/varpro/server-varpro/commits/3844acf1176727c15c0e58a76d1c95371d752c68))

### Fixes

* fix: corregir el duplicado de ordenes en pedreria @Carlos David Martinez Medrano ([`d827cb3`](https://bitbucket.org/varpro/server-varpro/commits/d827cb30e90aae0a222949aac065cf816d4e4207))
* fix: fix create laser jobs @Carlos David Martinez Medrano ([`26160b6`](https://bitbucket.org/varpro/server-varpro/commits/26160b64de6a0bf5c77639cfe974cc63b596cb50))
* fix: fix table jobs @Carlos David Martinez Medrano ([`7fb709a`](https://bitbucket.org/varpro/server-varpro/commits/7fb709ac7e0438295eda1475d040e9f3962f23c0))
* fix: partial qty @Carlos David Martinez Medrano ([`bc621f9`](https://bitbucket.org/varpro/server-varpro/commits/bc621f902376fe15ded415a7c9912eeed990d723))
* fix: corregir el escaneo final @Carlos David Martinez Medrano ([`cf27c92`](https://bitbucket.org/varpro/server-varpro/commits/cf27c92283d1dd17060d2ce27e740587c9c0f6f6))




## [1.13.13](https://bitbucket.org/varpro/server-varpro/compare/1.13.13..1.13.12) - 2024-08-16






## [1.13.12](https://bitbucket.org/varpro/server-varpro/compare/1.13.12..1.13.11) - 2024-08-15






## [1.13.11](https://bitbucket.org/varpro/server-varpro/compare/1.13.11..1.13.10) - 2024-08-14






## [1.13.10](https://bitbucket.org/varpro/server-varpro/compare/1.13.10..1.13.9) - 2024-08-14






## [1.13.9](https://bitbucket.org/varpro/server-varpro/compare/1.13.9..1.13.8) - 2024-08-14






## [1.13.8](https://bitbucket.org/varpro/server-varpro/compare/1.13.8..1.13.0) - 2024-08-14

### Features

* feat: working ok @Peter Brink ([`37b019f`](https://bitbucket.org/varpro/server-varpro/commits/37b019fe9513f73f8239c5c89e99e0a345f1c4cc))
* feat: added order status to cs report @Peter Brink ([`7dccb79`](https://bitbucket.org/varpro/server-varpro/commits/7dccb79dd3897784749dc9d338a293fe7e3be067))

### Fixes

* fix: agregar endpoints para reportes por maquinas @Carlos David Martinez Medrano ([`0cf6e84`](https://bitbucket.org/varpro/server-varpro/commits/0cf6e849f0703c84c9933c3fa8605456839e3e1a))
* fix: fix print group @DiegoCruzM ([`01b534f`](https://bitbucket.org/varpro/server-varpro/commits/01b534fcd6b2c978aa01c9d417366553bed2018b))
* fix: corregir el conteo de piedras en el reporte pedreria @Carlos David Martinez Medrano ([`c4f1608`](https://bitbucket.org/varpro/server-varpro/commits/c4f1608e0097e8c5694735e2b024e528bc8ddfa3))
* fix: corregir el reporte de pedreria @Carlos David Martinez Medrano ([`4ef0548`](https://bitbucket.org/varpro/server-varpro/commits/4ef0548f8688e50d53172f650bdc79de4e02577d))
* fix: corregit formula para el conteo de piedras @Carlos David Martinez Medrano ([`b0156e7`](https://bitbucket.org/varpro/server-varpro/commits/b0156e79f16e080297df9d10e2e74a8148df003a))
* fix: poly boom column @DiegoCruzM ([`44e114e`](https://bitbucket.org/varpro/server-varpro/commits/44e114e694bd056fe416b373d4f7fdac9d691dbb))
* fix: remove console logs @DiegoCruzM ([`d7b00bb`](https://bitbucket.org/varpro/server-varpro/commits/d7b00bb976dd9661c0eb5355a15bd2cf23a24d7d))
* fix: agregar columna en el reporte pedreria para el tipo @Carlos David Martinez Medrano ([`b91f6d7`](https://bitbucket.org/varpro/server-varpro/commits/b91f6d7322da0a42c2b7708c153705fbf974603d))
* fix: plotter combo join @DiegoCruzM ([`a8aa591`](https://bitbucket.org/varpro/server-varpro/commits/a8aa591eb3e252f6ee7bd7f6547f98e38d19d07e))




## [1.13.0](https://bitbucket.org/varpro/server-varpro/compare/1.13.0..1.12.0) - 2024-07-18

### Features

* feat: Add endpoint to retrieve plotter print group @DiegoCruzM ([`2539f5b`](https://bitbucket.org/varpro/server-varpro/commits/2539f5bd9ad244f7ce9b09546888e4fdff6759b0))
* feat: print group @DiegoCruzM ([`3605738`](https://bitbucket.org/varpro/server-varpro/commits/360573831e372f08c00e32627b607b95efae8b0c))
* feat: updates for cpr for new files @Peter Brink ([`c17a758`](https://bitbucket.org/varpro/server-varpro/commits/c17a758bf0cde64906aa21bb9517c952fecd6cf3))

### Fixes

* fix: corregir el crear trabajos de twill laser @Carlos David Martinez Medrano ([`44c769c`](https://bitbucket.org/varpro/server-varpro/commits/44c769c69cbc373ad01eb08ffa634d740ae4d7fb))
* fix: corregir el crear ordenes @Carlos David Martinez Medrano ([`cd12ea0`](https://bitbucket.org/varpro/server-varpro/commits/cd12ea07b1715a74147fd68d633c51cf7056647e))
* fix: corregir la migracion de twill laser app @Carlos David Martinez Medrano ([`e05623b`](https://bitbucket.org/varpro/server-varpro/commits/e05623be191c13f733bb1a9e733e75a6ef0c3ee8))
* fix: corregir el obtener los trabajos activos @Carlos David Martinez Medrano ([`7cb7555`](https://bitbucket.org/varpro/server-varpro/commits/7cb75556e88b81ae9489ac9ad4a0a9551137e556))




## [1.12.0](https://bitbucket.org/varpro/server-varpro/compare/1.12.0..1.11.0) - 2024-06-07

### Features

* feat: twill laser app @Carlos David Martinez Medrano ([`5d8ca96`](https://bitbucket.org/varpro/server-varpro/commits/5d8ca9649984e019b5d579291e33c3cdf53f7e5e))
* feat: update with mo info for import export @Peter Brink ([`63e8968`](https://bitbucket.org/varpro/server-varpro/commits/63e8968a7f7ded9ca3d818ca019d9f0b6dfdba50))
* feat: saving poly excel file @DiegoCruzM ([`97e5b15`](https://bitbucket.org/varpro/server-varpro/commits/97e5b153e6b4381c5a32a1494074f9ecc59bc817))
* feat: better queries to fix plot automation @Peter Brink ([`00bf467`](https://bitbucket.org/varpro/server-varpro/commits/00bf46721bfff6f2d04654d8e025999d9fdb094e))
* feat: added copy files to script for html files @Peter Brink ([`f54959a`](https://bitbucket.org/varpro/server-varpro/commits/f54959a5ef67284c0cf32f12ab1938e460f8e9ad))
* feat: added function to update plotter cuts @Peter Brink ([`72847da`](https://bitbucket.org/varpro/server-varpro/commits/72847daf1faf06250bbd573cbf7f26cbe865de31))
* feat: list errors on imex and country name @Peter Brink ([`4066c42`](https://bitbucket.org/varpro/server-varpro/commits/4066c423600d5ada753ed8938ee3bb5009ad64df))
* feat: convert to post to avoide limit on import export file @Peter Brink ([`a6e0691`](https://bitbucket.org/varpro/server-varpro/commits/a6e0691ff77c609be7b05f8fc61f849d9335547c))

### Fixes

* fix: upload excel file using muler @DiegoCruzM ([`7d5a6ff`](https://bitbucket.org/varpro/server-varpro/commits/7d5a6ffa2be118dafab57d6815b049a102e4b8f5))
* fix: next day checkbox @DiegoCruzM ([`89bb189`](https://bitbucket.org/varpro/server-varpro/commits/89bb1897f9cba555d5e398de1f8df61bee8c4713))
* fix: corregir el reporte excel de pedreria @Carlos David Martinez Medrano ([`dbecf28`](https://bitbucket.org/varpro/server-varpro/commits/dbecf282d78c6efff9b2c2c59894b6a3c75d0146))




## [1.11.0](https://bitbucket.org/varpro/server-varpro/compare/1.11.0..1.10.6) - 2024-05-23

### Features

* feat: add method for create report @Carlos David Martinez Medrano ([`025b25d`](https://bitbucket.org/varpro/server-varpro/commits/025b25db56d6b233072f3b73c13805792703072d))
* feat: add check out roll functionality @DiegoCruzM ([`1dbbdfa`](https://bitbucket.org/varpro/server-varpro/commits/1dbbdfac62990034a3667342d6d712ec06e26607))
* feat: Add recipient validation to checkOutRoll endpoint @DiegoCruzM ([`daf034e`](https://bitbucket.org/varpro/server-varpro/commits/daf034e1e87e773e184adc069a985f312928a33d))





## [1.10.6](https://bitbucket.org/varpro/server-varpro/compare/1.10.6..1.10.3-r3) - 2024-05-21

### Features

* feat: create migration @Carlos David Martinez Medrano ([`771e323`](https://bitbucket.org/varpro/server-varpro/commits/771e32376ebca9683cfc6d3281a67d1ec4d3dc98))
* feat: add barcode stickers methods @Carlos David Martinez Medrano ([`9c37283`](https://bitbucket.org/varpro/server-varpro/commits/9c37283281b6e4138ddedf6197658a0bd25c540d))
* feat: added endpoint to get plots by mo @Peter Brink ([`883d65d`](https://bitbucket.org/varpro/server-varpro/commits/883d65dfad380aabc8122285b33e52e9cd6ab9a4))

### Fixes

* fix: update endpoints @Carlos David Martinez Medrano ([`2fcd042`](https://bitbucket.org/varpro/server-varpro/commits/2fcd0420adf58ad5576c5e17f51fae2db02d115c))
* fix: corregir escaneo de mos desde tickets activos @Carlos David Martinez Medrano ([`407e5e1`](https://bitbucket.org/varpro/server-varpro/commits/407e5e1e2d075842526f2b6112d225fbdafa5164))




## [1.10.3-r3](https://bitbucket.org/varpro/server-varpro/compare/1.10.3-r3..1.10.3-r2) - 2024-04-16

### Features

* feat: add in current plot location if exists @Peter Brink ([`db6d7ef`](https://bitbucket.org/varpro/server-varpro/commits/db6d7efaf031c84fa3828c47cde55d0aa14132ae))





## [1.10.3-r2](https://bitbucket.org/varpro/server-varpro/compare/1.10.3-r2..1.10.3-r1) - 2024-04-16

### Features

* feat: return bins and plot on check @Peter Brink ([`3ba4b0e`](https://bitbucket.org/varpro/server-varpro/commits/3ba4b0e1c56c60b160850f56c1034727685244fe))





## [1.10.3-r1](https://bitbucket.org/varpro/server-varpro/compare/1.10.3-r1..1.10.0) - 2024-04-16

### Features

* feat: add migration rhinestone artists app @Carlos David Martinez Medrano ([`b86ebd4`](https://bitbucket.org/varpro/server-varpro/commits/b86ebd49e69d915fc4c803a27684ff9e003592c1))
* feat: plotter location storage @Peter Brink ([`a5e7229`](https://bitbucket.org/varpro/server-varpro/commits/a5e72295674f82c95f9824ff45d45e699f9dba27))
* feat: fix details print ids @Carlos David Martinez Medrano ([`18e479f`](https://bitbucket.org/varpro/server-varpro/commits/18e479f2f5497a3295a722d66e99dfc5e13a7717))

### Fixes

* fix: actualizar los endpoints de la printer app @Carlos David Martinez Medrano ([`775db3f`](https://bitbucket.org/varpro/server-varpro/commits/775db3f6cab0296d0ce7b79a72817236b0137101))
* fix: mix mo and add delete function @DiegoCruzM ([`43a6848`](https://bitbucket.org/varpro/server-varpro/commits/43a6848f22007dd3b56567da917209f9d168e897))
* fix: removed combo in excel file @DiegoCruzM ([`978832f`](https://bitbucket.org/varpro/server-varpro/commits/978832ffe254c71f8bbb262730cd01983be2e661))
* fix: agregar escaneo cuando se completa un ticket @Carlos David Martinez Medrano ([`9c33702`](https://bitbucket.org/varpro/server-varpro/commits/9c337023686d3ec24ecbd6c8e6920a9373e9927f))
* fix: remove console log @DiegoCruzM ([`de73887`](https://bitbucket.org/varpro/server-varpro/commits/de73887bf5ff34a1ebddccc59333e831544f3dd0))




## [1.10.0](https://bitbucket.org/varpro/server-varpro/compare/1.10.0..1.9.24) - 2024-03-20

### Features

* feat: corregir el endpoint de obtener printid @Carlos David Martinez Medrano ([`a14f8cc`](https://bitbucket.org/varpro/server-varpro/commits/a14f8cc9cae2200ad310e7c4663c3ae0e2ab27fd))
* feat: add methods and routes to create and modify printids @Carlos David Martinez Medrano ([`dde0fcf`](https://bitbucket.org/varpro/server-varpro/commits/dde0fcf0d68af6afb9e514f682eda8bb2e9e4ff9))
* feat: add methods to insert print ids @Carlos David Martinez Medrano ([`c8dff85`](https://bitbucket.org/varpro/server-varpro/commits/c8dff85572614a3b6924a1c8cae5909979c3337b))
* feat: agregar metodo para obtener maquinas por area @Carlos David Martinez Medrano ([`6d31cc1`](https://bitbucket.org/varpro/server-varpro/commits/6d31cc1aa8ead71bee135e9224797beb85907891))
* feat: add endpoints to remove mos from print @Carlos David Martinez Medrano ([`2c7e728`](https://bitbucket.org/varpro/server-varpro/commits/2c7e728e5e4052b08d47d35966d71080a803010f))
* feat: add print_app migration @Carlos David Martinez Medrano ([`9f1d2c7`](https://bitbucket.org/varpro/server-varpro/commits/9f1d2c7c7850c68c253a30ecd2ec7b5073ed3bed))

### Fixes

* fix: se corrigio el agregar print ids @Carlos David Martinez Medrano ([`e06b451`](https://bitbucket.org/varpro/server-varpro/commits/e06b451cd34a99ef203ce2750c2ea2668fabcce5))
* fix: se corrigio el buscador de mo activas para print app @Carlos David Martinez Medrano ([`498d24b`](https://bitbucket.org/varpro/server-varpro/commits/498d24b7eb4d1754a62c81b0e079a0e18b731da7))
* fix: se corrigio el obtener maquinas por area @Carlos David Martinez Medrano ([`d95dcf1`](https://bitbucket.org/varpro/server-varpro/commits/d95dcf16dc3b696efc4ab9ba4dddd992d181fe4e))
* Merge tag 'webpedreria-orders' into develop @Carlos David Martinez Medrano ([`7129af9`](https://bitbucket.org/varpro/server-varpro/commits/7129af97cdf26c27081312cbfb42825574575cb9))




## [1.9.24](https://bitbucket.org/varpro/server-varpro/compare/1.9.24..1.9.23) - 2024-03-20

### Features

* feat: added new endpoint for barcode generation @Peter Brink ([`e5d8d64`](https://bitbucket.org/varpro/server-varpro/commits/e5d8d640cc4a5f3cf829bb0cac8d71de16a97267))





## [1.9.23](https://bitbucket.org/varpro/server-varpro/compare/1.9.23..1.9.14) - 2024-03-19

### Features

* feat: create rhinestones order @Carlos David Martinez Medrano ([`01936c2`](https://bitbucket.org/varpro/server-varpro/commits/01936c209b78301051f9855f17b6fdc2fd2a0e43))
* feat: cpr updates @Peter Brink ([`7b08a7f`](https://bitbucket.org/varpro/server-varpro/commits/7b08a7f14856019bacf23687280117d1cec29fed))
* feat: roll and mix mo @DiegoCruzM ([`77ba3c6`](https://bitbucket.org/varpro/server-varpro/commits/77ba3c65c61a4616a73dd7c632954ff5585ecc07))
* feat: initial head creation @Peter Brink ([`a2440a1`](https://bitbucket.org/varpro/server-varpro/commits/a2440a10962fa2bbe8f9cecf82b173af81a3267f))
* feat: updated for 2020 cpr @Peter Brink ([`b7b6b5d`](https://bitbucket.org/varpro/server-varpro/commits/b7b6b5d4f65f42ffc5f5454cdb3bdb56b1f693a0))
* feat: ply calculator sizes addition @Peter Brink ([`c7db75d`](https://bitbucket.org/varpro/server-varpro/commits/c7db75df1da711c4f6bbaa377c960ba8bafc4407))
* feat: ply calculator @Peter Brink ([`6c69569`](https://bitbucket.org/varpro/server-varpro/commits/6c69569cd473d08384e631470bc4dc22107b661d))
* feat: create endpoints for the rhinestones inventory app @Carlos David Martinez Medrano ([`9c6a78c`](https://bitbucket.org/varpro/server-varpro/commits/9c6a78c810dd8ef6d5e176b4483d52375fc4a666))
* feat: add rhinestone machine inventory model and routes @Carlos David Martinez Medrano ([`d9853ee`](https://bitbucket.org/varpro/server-varpro/commits/d9853eeafdfb64038329fe9e668cd7f469aef47d))
* feat: added barcode generator @Peter Brink ([`1602484`](https://bitbucket.org/varpro/server-varpro/commits/1602484372b1cf1ee29fe1ae38fef76c934a8bf8))
* feat: working head upload @Peter Brink ([`d1ddb0d`](https://bitbucket.org/varpro/server-varpro/commits/d1ddb0d1aeee64e0fcf0e9e45a92f6dd7ec9d92d))
* feat: formatting @Peter Brink ([`ca0a064`](https://bitbucket.org/varpro/server-varpro/commits/ca0a064649a26f1f1590845d9c63b8f3b02909b9))
* feat: added options to qrcode @Peter Brink ([`a8dd540`](https://bitbucket.org/varpro/server-varpro/commits/a8dd540ff53993ad2764efdc84b188f918b33c4a))
* feat: plys sizes and quantity calc from plys @Peter Brink ([`f0187f7`](https://bitbucket.org/varpro/server-varpro/commits/f0187f751aa1c7f6772872f6265deaf382cd7ae5))
* feat: modified ply total yard to plys fabric yards @Peter Brink ([`0ca62ec`](https://bitbucket.org/varpro/server-varpro/commits/0ca62ec2aa0562ea73d8c4bb628a7d0e5e56a086))
* feat: added total sizes to plotter plys @Peter Brink ([`2077154`](https://bitbucket.org/varpro/server-varpro/commits/207715471e6de832c2bd493cf32b767a1106aa4a))
* feat: fixed bad checks on qrcode @Peter Brink ([`c355c92`](https://bitbucket.org/varpro/server-varpro/commits/c355c926eb164c216c339b0551b153b7c6986598))
* feat: fixed bad plotter ply math @Peter Brink ([`8b5914d`](https://bitbucket.org/varpro/server-varpro/commits/8b5914dd84ce38ee1d6ab9e25df76887b80a1179))
* feat: reverted qrcode info @Peter Brink ([`b0a2246`](https://bitbucket.org/varpro/server-varpro/commits/b0a22465a53d8dc24feff82d3ae068a93dba29a5))
* feat: fixed qrcode typeofs @Peter Brink ([`9abfa7d`](https://bitbucket.org/varpro/server-varpro/commits/9abfa7d0b4704327ea0bf580c0f3219cc376bf2f))
* feat: more cpr updates @Peter Brink ([`315aeb6`](https://bitbucket.org/varpro/server-varpro/commits/315aeb689c7fa597731025e845a48dd8ed331f95))
* feat: moving scan and voucher to service @DiegoCruzM ([`0ce91f5`](https://bitbucket.org/varpro/server-varpro/commits/0ce91f55671799a51ffdc82c8a247d7d40012fbd))
* feat: added plotter rolls @Peter Brink ([`1473a68`](https://bitbucket.org/varpro/server-varpro/commits/1473a6802bfbca585cbbdb6a99b8cbc39e6ccf06))
* feat: plotter logs added @Peter Brink ([`9143d74`](https://bitbucket.org/varpro/server-varpro/commits/9143d74677f786fcdade3e484ecba100aad2620f))
* feat: working plotter endpoints @Peter Brink ([`707270d`](https://bitbucket.org/varpro/server-varpro/commits/707270ddb18cfb9942b1158b41bf21161c5dd807))
* feat: bunch of untest updates for plotter @Peter Brink ([`df1f708`](https://bitbucket.org/varpro/server-varpro/commits/df1f70877a304dd363f2e9f3ea5063a00f1284ad))
* feat: adding plotter fragments @DiegoCruzM ([`8d3153a`](https://bitbucket.org/varpro/server-varpro/commits/8d3153a2a055c5939e5bc49dd1a5d933d2f4d24d))
* feat: adding comments in create tickets @DiegoCruzM ([`6307bd5`](https://bitbucket.org/varpro/server-varpro/commits/6307bd5267a7f0c6315c0cd0e8ee04a843abdf8e))
* feat: employee login @Peter Brink ([`58730cf`](https://bitbucket.org/varpro/server-varpro/commits/58730cf4502f082010222b500f478c9e798ddc12))
* feat: added employee check @Peter Brink ([`f64b016`](https://bitbucket.org/varpro/server-varpro/commits/f64b0167ec0fb33e4c9325d20357919b4795008c))
* feat: moved employee on middleware to locals @Peter Brink ([`36fa284`](https://bitbucket.org/varpro/server-varpro/commits/36fa284f235c45ce950f943b82a0c300c1b1a867))
* feat: adding scan in status view @DiegoCruzM ([`6d623a1`](https://bitbucket.org/varpro/server-varpro/commits/6d623a1336e610b1122e15c21f70ecea0449f53d))
* feat: added checks on ply and length in plotter update @Peter Brink ([`cfcdefd`](https://bitbucket.org/varpro/server-varpro/commits/cfcdefd3238db9f34e9cb106613b4572d6513f69))
* feat: small plotter fixes and uses data as label @Peter Brink ([`40f63b8`](https://bitbucket.org/varpro/server-varpro/commits/40f63b8d7f3131b99dc81103faa1a4be2f3639c6))
* feat: Translate error messages to Spanish @DiegoCruzM ([`51e0f8a`](https://bitbucket.org/varpro/server-varpro/commits/51e0f8a9de069a82793ee43ef77a45f058cc30e0))
* feat: added roll info to print @Peter Brink ([`c57bb7e`](https://bitbucket.org/varpro/server-varpro/commits/c57bb7e580037aae58df7f30359a9c9f6d80112b))
* feat: added part number restiriction when adding more combos @Peter Brink ([`181beaa`](https://bitbucket.org/varpro/server-varpro/commits/181beaa936491704c2d02d9bb4364b78dfe6a344))
* feat: plotter fabric restful fix @Peter Brink ([`40ab677`](https://bitbucket.org/varpro/server-varpro/commits/40ab677cf96432d2823fbf37e71cce6451d45a28))
* feat: fixed bad trx @Peter Brink ([`7d6fc4b`](https://bitbucket.org/varpro/server-varpro/commits/7d6fc4bf6b53f1aa9169bc67015e2b124476e04d))
* feat: added plys format check @Peter Brink ([`28bb7fe`](https://bitbucket.org/varpro/server-varpro/commits/28bb7fecf42181acf158aa559f68b83679e22fda))
* feat: added timestamps @Peter Brink ([`8695a69`](https://bitbucket.org/varpro/server-varpro/commits/8695a69a5c74e13ed2f8167596e5cc03c400a05a))
* feat: fixed employee header capitalization @Peter Brink ([`1782f65`](https://bitbucket.org/varpro/server-varpro/commits/1782f65a22290d13080a8b6f2a88040a6ca8cb50))
* feat: added index to plys error @Peter Brink ([`4de54cc`](https://bitbucket.org/varpro/server-varpro/commits/4de54cc70629233aafea3160d2570ff811f3aec4))
* feat: added fields to model @Peter Brink ([`2efb77a`](https://bitbucket.org/varpro/server-varpro/commits/2efb77a7b96a90c897a76deab30dab150b7a3080))
* feat: removed req console log on plotter @Peter Brink ([`b272ea6`](https://bitbucket.org/varpro/server-varpro/commits/b272ea6478a37fd91d758c879be1794634f20c92))
* feat: added console log on request @Peter Brink ([`06795b8`](https://bitbucket.org/varpro/server-varpro/commits/06795b803e3c459e0a95bc425b1cd4322a355244))
* feat: added check on get print @Peter Brink ([`8ce72b7`](https://bitbucket.org/varpro/server-varpro/commits/8ce72b701fb193ea6501846e6d665cdfe2303c96))
* feat: fixed created by on combo and print when creating @Peter Brink ([`fc6e2ce`](https://bitbucket.org/varpro/server-varpro/commits/fc6e2ce622d3b924904cafe365661fc73e7d1d9c))
* feat: fixed another trx plotter problem @Peter Brink ([`468c606`](https://bitbucket.org/varpro/server-varpro/commits/468c606b23f5990b830ab2772b82c784b77f30b4))
* feat: added console log on request headers @Peter Brink ([`4ea2170`](https://bitbucket.org/varpro/server-varpro/commits/4ea21701682015de5ea389f493aa8235d01dafbb))
* feat: added warehoused_at date @Peter Brink ([`c590541`](https://bitbucket.org/varpro/server-varpro/commits/c59054183193e26b787101ff65c33b44261d7217))

### Fixes

* fix: rhinestone artist update @Carlos David Martinez Medrano ([`4d42799`](https://bitbucket.org/varpro/server-varpro/commits/4d42799008baaffbc3c851d35bf0c1008795fa40))
* fix: columns in excel file @DiegoCruzM ([`1b397be`](https://bitbucket.org/varpro/server-varpro/commits/1b397be9b84bde7bfd9355ce26d8ec61b134ce5b))
* fix: roll and mix mo @DiegoCruzM ([`868ad73`](https://bitbucket.org/varpro/server-varpro/commits/868ad73c8d1897c695042a831cc8324bdefe1606))
* fix: roll view @DiegoCruzM ([`6592d4b`](https://bitbucket.org/varpro/server-varpro/commits/6592d4bd1784b3d08c85bca00c045a3a2f50691c))
* fix: plotter app, new columns @DiegoCruzM ([`f0a5b10`](https://bitbucket.org/varpro/server-varpro/commits/f0a5b10dcee3e13b350dce8c409203c5bad5696e))
* fix: mixmo endpoint @DiegoCruzM ([`7532bef`](https://bitbucket.org/varpro/server-varpro/commits/7532beff6200705a1cd6b9046d7740ac1bec009e))
* fix: fixed head group bad start chars @Peter Brink ([`7cb80c2`](https://bitbucket.org/varpro/server-varpro/commits/7cb80c2a5fdeda891088f7f1d36f7ceaf3cb20f4))
* fix: new column extra fabric @DiegoCruzM ([`40d0071`](https://bitbucket.org/varpro/server-varpro/commits/40d0071e3d2ad5b1ddc1e06f887e1b4bbf19f555))
* fix: add logger to pedreria controller @Carlos David Martinez Medrano ([`56d3add`](https://bitbucket.org/varpro/server-varpro/commits/56d3adda1a607d8e9b29e3c654af2f0e4def9290))
* fix: calculation of required for lasser @DiegoCruzM ([`dfff44d`](https://bitbucket.org/varpro/server-varpro/commits/dfff44d43b506abc76d7f9476830c5e49beb5eee))
* fix: removing logs @DiegoCruzM ([`d5b8bb3`](https://bitbucket.org/varpro/server-varpro/commits/d5b8bb313d7953a512bc03d510a2885dc8dc5bd3))
* fix: currentyear in createmultipleshift function @DiegoCruzM ([`d6c98f1`](https://bitbucket.org/varpro/server-varpro/commits/d6c98f12c91be07718e1a7b3f4f740758d94bfa1))
* fix: se corrigio el error de ordenes en la webpedreria @Carlos David Martinez Medrano ([`21cb7bf`](https://bitbucket.org/varpro/server-varpro/commits/21cb7bfae123ef083a8b83d5ce01e7342a13a956))
* fix: removing console log statements @DiegoCruzM ([`8c94feb`](https://bitbucket.org/varpro/server-varpro/commits/8c94febef7c763866e32d7393d51adf34527f491))
* fix: input length mismatch issue @DiegoCruzM ([`b1f56e3`](https://bitbucket.org/varpro/server-varpro/commits/b1f56e30f29f68272971446ce09560c9aa380535))
* fix: merge master into hotfix @DiegoCruzM ([`5d7d39c`](https://bitbucket.org/varpro/server-varpro/commits/5d7d39c064cee51d61b530158a7546b7eb0c1dae))
* fix: round 2 decimals on evaluate @DiegoCruzM ([`adf6f56`](https://bitbucket.org/varpro/server-varpro/commits/adf6f5637e8326f4f05e685e696187da61ecf624))
* fix: merge main to scan service @DiegoCruzM ([`91d7614`](https://bitbucket.org/varpro/server-varpro/commits/91d7614b5583a4134aae2e74e9a6eb19ded2b5a9))
* fix: cleaning adn testing @DiegoCruzM ([`5cac5a1`](https://bitbucket.org/varpro/server-varpro/commits/5cac5a10d3b782cb406fb18b89dfc8278f06e139))
* fix: Update error messages for MO not found @DiegoCruzM ([`711ef9b`](https://bitbucket.org/varpro/server-varpro/commits/711ef9b3bf1fa49d2a59cc3384f0eb31e440dbee))
* fix: fixing code @DiegoCruzM ([`d9ab2bf`](https://bitbucket.org/varpro/server-varpro/commits/d9ab2bfc2e296f935b60bfa33a3622ded7d04b34))
* fix: header issue with nginx @DiegoCruzM ([`39c3f95`](https://bitbucket.org/varpro/server-varpro/commits/39c3f95be60a00b6fc145051248dc476ad0594eb))
* fix: adding total ply to print @DiegoCruzM ([`1a6a2f5`](https://bitbucket.org/varpro/server-varpro/commits/1a6a2f5a5f213bdd94729981200cde0da4c55361))
* fix: fixing tickets bug @DiegoCruzM ([`d41c4aa`](https://bitbucket.org/varpro/server-varpro/commits/d41c4aa12ce7c613e9a031ea437f7cbae15ea4e5))
* fix: vscode updates @Peter Brink ([`477ceee`](https://bitbucket.org/varpro/server-varpro/commits/477ceee9b4fe35e500cda7b8a25f8a533fc3b9bb))
* fix: incorrect property name @DiegoCruzM ([`a213a9d`](https://bitbucket.org/varpro/server-varpro/commits/a213a9daa37165f84c4c6c1c173cb1d67757baec))
* fix: adding garment part @DiegoCruzM ([`e045f65`](https://bitbucket.org/varpro/server-varpro/commits/e045f65a848563746660738ce6e28af10ff8c10c))
* fix: branch 'develop' into feature/scan-controller-service @DiegoCruzM ([`0ae0c39`](https://bitbucket.org/varpro/server-varpro/commits/0ae0c3974c17deae60b1f097596ac5530d33e937))




## [1.9.14](https://bitbucket.org/varpro/server-varpro/compare/1.9.14..1.9.13) - 2023-10-12

### Features

* feat: cpr 2021 fixes @Peter Brink ([`78e2dca`](https://bitbucket.org/varpro/server-varpro/commits/78e2dca0123f674306bed01af2fad810b06d62e2))





## [1.9.13](https://bitbucket.org/varpro/server-varpro/compare/1.9.13..1.9.12) - 2023-10-12

### Features

* feat: total missing if items are 0 cost @Peter Brink ([`9c908b6`](https://bitbucket.org/varpro/server-varpro/commits/9c908b693e0c9003866d54020ee4067d8ffcbd06))





## [1.9.12](https://bitbucket.org/varpro/server-varpro/compare/1.9.12..1.9.11) - 2023-10-12

### Features

* feat: more additions for 2022 @Peter Brink ([`17063d0`](https://bitbucket.org/varpro/server-varpro/commits/17063d0f6ead906abe809d8d8ae1d615d1303c51))





## [1.9.11](https://bitbucket.org/varpro/server-varpro/compare/1.9.11..1.9.10) - 2023-10-12

### Features

* feat: more fixes for 2022 year @Peter Brink ([`6b43351`](https://bitbucket.org/varpro/server-varpro/commits/6b43351b57f1664b0e4aec99d161938c7e2ab5d0))





## [1.9.10](https://bitbucket.org/varpro/server-varpro/compare/1.9.10..1.9.9) - 2023-10-12

### Features

* feat: removed mo_id check @Peter Brink ([`2ab31f5`](https://bitbucket.org/varpro/server-varpro/commits/2ab31f5a00278e9f7b702e49658a0b7bf83aa9f1))





## [1.9.9](https://bitbucket.org/varpro/server-varpro/compare/1.9.9..1.9.8) - 2023-10-12

### Features

* feat: better error for missing mo numbers @Peter Brink ([`fba68de`](https://bitbucket.org/varpro/server-varpro/commits/fba68dead8240ac6c82be26628abe518fb983929))





## [1.9.8](https://bitbucket.org/varpro/server-varpro/compare/1.9.8..1.9.7) - 2023-10-12

### Features

* feat: last nuances of 2023 files @Peter Brink ([`3bb3573`](https://bitbucket.org/varpro/server-varpro/commits/3bb3573e35941e68b38c166073ec3f0f2f4c3458))





## [1.9.7](https://bitbucket.org/varpro/server-varpro/compare/1.9.7..1.9.6) - 2023-10-11

### Features

* feat: fixed runtime and combine till next for all @Peter Brink ([`7e6967b`](https://bitbucket.org/varpro/server-varpro/commits/7e6967b1501a0d504af8c8aac604f8d1920d7d97))





## [1.9.6](https://bitbucket.org/varpro/server-varpro/compare/1.9.6..1.9.5) - 2023-10-11

### Features

* feat: working new lettering fat @Peter Brink ([`86bccec`](https://bitbucket.org/varpro/server-varpro/commits/86bccec923f263628b756c12e16b8f0384a7a34d))
* feat: working cpr with fat font @Peter Brink ([`0aa39ae`](https://bitbucket.org/varpro/server-varpro/commits/0aa39aed56b731f24010de6cba20ce4d69e35f1f))





## [1.9.5](https://bitbucket.org/varpro/server-varpro/compare/1.9.5..1.9.4) - 2023-10-09

### Features

* feat: added new seen formats @Peter Brink ([`dd9ae75`](https://bitbucket.org/varpro/server-varpro/commits/dd9ae7560b81842fa6d3fee65281b5c8125299e1))





## [1.9.4](https://bitbucket.org/varpro/server-varpro/compare/1.9.4..1.9.3) - 2023-10-09

### Features

* feat: fixed bad naming on lettering @Peter Brink ([`d69c5bc`](https://bitbucket.org/varpro/server-varpro/commits/d69c5bce520c99a69d6b60305951d94ece1597fa))





## [1.9.3](https://bitbucket.org/varpro/server-varpro/compare/1.9.3..1.9.2) - 2023-10-09

### Features

* feat: more fixes for cpr @Peter Brink ([`b35bec3`](https://bitbucket.org/varpro/server-varpro/commits/b35bec3f0fdaae4bcb57382cc9cc00a65852662a))





## [1.9.2](https://bitbucket.org/varpro/server-varpro/compare/1.9.2..1.9.1) - 2023-09-25

### Features

* feat: add new migration for column @Peter Brink ([`423201a`](https://bitbucket.org/varpro/server-varpro/commits/423201a3faddaf264af42fb59576b6ad6b4565ff))
* feat: added stars in cpr weekly @Peter Brink ([`a44a6e8`](https://bitbucket.org/varpro/server-varpro/commits/a44a6e8a527aedd2b00733ced6eca894860cb9c1))
* feat: forgot a continue @Peter Brink ([`edff07e`](https://bitbucket.org/varpro/server-varpro/commits/edff07e4f85df8b0cc17724ab8c3fde39f79aa08))

### Fixes

* fix: fixing scanning endpoint @DiegoCruzM ([`73d6b91`](https://bitbucket.org/varpro/server-varpro/commits/73d6b919573b506c4ce7ec7c21981abfddd95227))
* fix: fixing scan voucher @DiegoCruzM ([`e3b475a`](https://bitbucket.org/varpro/server-varpro/commits/e3b475acbcd6ff62c34bd41dad6ba9e4300f6c0c))
* fix: update version @DiegoCruzM ([`989519f`](https://bitbucket.org/varpro/server-varpro/commits/989519f892ac416d3bd7c573836cb5ee2adf5ee8))




## [1.9.1](https://bitbucket.org/varpro/server-varpro/compare/1.9.1..1.8.0) - 2023-09-22

### Features

* feat: added jest testing support @Peter Brink ([`f7333d9`](https://bitbucket.org/varpro/server-varpro/commits/f7333d99eb1c6c262d476a22d8281c533cf8fd73))
* feat: big initial pickpack update with additional updates @Peter Brink ([`18fd53b`](https://bitbucket.org/varpro/server-varpro/commits/18fd53beb2ba21a9b85e163df4cdcfd536c53801))
* feat: cleared out any types @Peter Brink ([`a90f6cb`](https://bitbucket.org/varpro/server-varpro/commits/a90f6cbb7b732924cc2aa8c70c5d485961a9ad08))
* feat: added varsity cpr code @Peter Brink ([`005e970`](https://bitbucket.org/varpro/server-varpro/commits/005e9704d8387dcc38e537a5c139a9a9bf70ae4e))
* feat: agregar los grupos y rollos de fragmentos @Carlos David Martinez Medrano ([`1415f98`](https://bitbucket.org/varpro/server-varpro/commits/1415f9891727c50bfc02fd575b027040277d88bc))
* feat: adding to server @DiegoCruzM ([`685c3f4`](https://bitbucket.org/varpro/server-varpro/commits/685c3f46764b8c83c0cf159e41ecc200ad252dfb))
* feat: standardized routers @Peter Brink ([`1e7dcfd`](https://bitbucket.org/varpro/server-varpro/commits/1e7dcfda41ad2d1dda80168d7d7bb38f8ad26d9f))
* feat: agregar buscar de rollo al crear grupo @Carlos David Martinez Medrano ([`a1618e2`](https://bitbucket.org/varpro/server-varpro/commits/a1618e255350f68ee83dc5b87baf73bc6810ea1a))
* feat: batching @Peter Brink ([`019ffb5`](https://bitbucket.org/varpro/server-varpro/commits/019ffb5d85b2a8ad2a7d3805167039a85b86c3f2))
* feat: added simple auto batching feature untested @Peter Brink ([`a235098`](https://bitbucket.org/varpro/server-varpro/commits/a23509873081caf012a82955914e0aacaa5b16de))
* feat: cambios en los grupos de fragmentos @Carlos David Martinez Medrano ([`95d975f`](https://bitbucket.org/varpro/server-varpro/commits/95d975f2af6287b5ff832bbe5012b471c36b8f95))
* feat: Saving cpr invoice to database @Peter Brink ([`9319621`](https://bitbucket.org/varpro/server-varpro/commits/9319621ee51c8bb5d9101d9acf8c283bd049fbb9))
* feat: fragment groups @Carlos David Martinez Medrano ([`0232d41`](https://bitbucket.org/varpro/server-varpro/commits/0232d4123bf10b4aa144e4cc22a9a11ba951e52d))
* feat: fragments rolls temp @Carlos David Martinez Medrano ([`6b15b02`](https://bitbucket.org/varpro/server-varpro/commits/6b15b0255ba6ff9610d412257d84a3a6680b6152))
* feat: typescript updates @Peter Brink ([`312de05`](https://bitbucket.org/varpro/server-varpro/commits/312de054df5edba86894229ce68c7535905dc942))
* feat: agregar funcion de eliminar grupo de voucher @Carlos David Martinez Medrano ([`0f21836`](https://bitbucket.org/varpro/server-varpro/commits/0f2183649748b14e1411434d647ab2bc45c326c7))
* feat: fragment group typescript updates @Peter Brink ([`5b45ce6`](https://bitbucket.org/varpro/server-varpro/commits/5b45ce68878bbac5e816baa9a27dc086f250d5ac))
* feat: more small fixes @Peter Brink ([`d76376f`](https://bitbucket.org/varpro/server-varpro/commits/d76376fed4cf2d60c697832decd129dfd330b07d))
* feat: started lot service @Peter Brink ([`c1e4f1f`](https://bitbucket.org/varpro/server-varpro/commits/c1e4f1fb773cb6ccedee7ebf15b7e8d3f8719db5))
* feat: tested and working varsity cpr @Peter Brink ([`97a5b9a`](https://bitbucket.org/varpro/server-varpro/commits/97a5b9a1925513a3fd4041f9d8949ade03438a1e))
* feat: some typescript on fragments @Peter Brink ([`3b952c0`](https://bitbucket.org/varpro/server-varpro/commits/3b952c0ecd46d97fbe35fd2b8534d8647f77d284))
* feat: fixed proper typing on qrcode endpoint @Peter Brink ([`c5d5de7`](https://bitbucket.org/varpro/server-varpro/commits/c5d5de7cc065a709310da04a1f85d044bcfcbae4))
* feat: initial pickpack schema setup @Peter Brink ([`a9e5faf`](https://bitbucket.org/varpro/server-varpro/commits/a9e5faf41c21f2298d7ee32c7b513ef7d8460b15))
* feat: add new column in fragment view @Carlos David Martinez Medrano ([`c49f463`](https://bitbucket.org/varpro/server-varpro/commits/c49f463efc223c9ed3bcdaa87cd4c9f49b0dda90))
* feat: update vouchers group ignore groups removed @Carlos David Martinez Medrano ([`3987f9a`](https://bitbucket.org/varpro/server-varpro/commits/3987f9a277eb584b92a8b43d559086194336230f))
* feat: small typescript cleanup @Peter Brink ([`e93879c`](https://bitbucket.org/varpro/server-varpro/commits/e93879c350bdf6e095f055f494d4058bf684a0cc))
* feat: Added material filter for warehouse batching @Peter Brink ([`aaad3f8`](https://bitbucket.org/varpro/server-varpro/commits/aaad3f87c63b8fc06110f481434180aa106b9278))
* feat: corregir errores en los tickets @Carlos David Martinez Medrano ([`07c430e`](https://bitbucket.org/varpro/server-varpro/commits/07c430e9514349a1d3c3ad31be12da7565bd8150))
* feat: added casting @Peter Brink ([`ffe2d59`](https://bitbucket.org/varpro/server-varpro/commits/ffe2d5922e4baef92609c6bd36262a820df13aa9))
* feat: added route for use @Peter Brink ([`1e7bc4c`](https://bitbucket.org/varpro/server-varpro/commits/1e7bc4cd8d0f1adbfa30f5405e16ee04dc0f13e7))
* feat: minor warehouse batch cleanup @Peter Brink ([`5140a44`](https://bitbucket.org/varpro/server-varpro/commits/5140a448ff073f6cb3a38a1bafa8ff2711b732eb))
* feat: another typescript cleanup @Peter Brink ([`e19bbad`](https://bitbucket.org/varpro/server-varpro/commits/e19bbaddbe239ff34b4e85959c4388c3b4521179))
* feat: another typing @Peter Brink ([`6d94253`](https://bitbucket.org/varpro/server-varpro/commits/6d94253e2c868e46b0e2884183208703ec5030ba))

### Fixes

* fix: corregir el buscador de mos @Carlos David Martinez Medrano ([`98650d1`](https://bitbucket.org/varpro/server-varpro/commits/98650d1672c7b288442d4b3812e15b526066cbab))
* fix: se corrigio el modulo grupo de vouchers @Carlos David Martinez Medrano ([`bc9c06e`](https://bitbucket.org/varpro/server-varpro/commits/bc9c06e7e86de13224cd614df6100b5d46af300d))
* fix: se corrigio error a cambiar estatus completos @Carlos David Martinez Medrano ([`22ecb58`](https://bitbucket.org/varpro/server-varpro/commits/22ecb583d3588371c072ec7184bf686888a0305c))
* fix: se corrigio el escaneo de vouchers @Carlos David Martinez Medrano ([`698b63f`](https://bitbucket.org/varpro/server-varpro/commits/698b63ff77dcc63fecb20e2a5ae8086ddf498d19))
* fix: corregir el recibir grupo de voucher @Carlos David Martinez Medrano ([`c558c9e`](https://bitbucket.org/varpro/server-varpro/commits/c558c9ee7c1e90b5d5f7afcc4c8331dfa9352e61))
* fix: se corrigio el recibir grupo de vouchers @Carlos David Martinez Medrano ([`9e00f6f`](https://bitbucket.org/varpro/server-varpro/commits/9e00f6fc97f48421ea0d0a0f8716332d05047fdd))
* fix: se corrigio el escaneo de vouchers @Carlos David Martinez Medrano ([`57ca28c`](https://bitbucket.org/varpro/server-varpro/commits/57ca28c69d4a00075ca4f8a268478f871b7ba346))
* fix: fixed tsconfig build @Peter Brink ([`fcae986`](https://bitbucket.org/varpro/server-varpro/commits/fcae98679f8188b9397a04d36266afa520b7fcd5))




## [1.8.0](https://bitbucket.org/varpro/server-varpro/compare/1.8.0..1.7.0) - 2023-05-22

### Features

* feat: add fragments groups @Carlos David Martinez Medrano ([`65cf05e`](https://bitbucket.org/varpro/server-varpro/commits/65cf05ee2d3ab26531f221f5d35cd6abeb5c02f9))
* feat: added new qrcode endpoint @Peter Brink ([`5efbfed`](https://bitbucket.org/varpro/server-varpro/commits/5efbfedbb24e1796b9f6c69d72c19440cfe7789d))
* feat: Big knex cleanup @Peter Brink ([`cfc7647`](https://bitbucket.org/varpro/server-varpro/commits/cfc76478a21525b569191d26e96074154a93c69a))
* feat: migration test file @Peter Brink ([`72043e5`](https://bitbucket.org/varpro/server-varpro/commits/72043e518b28072db3217b843063c82694f22ef2))
* feat: added width param to qrcode @Peter Brink ([`59f2adf`](https://bitbucket.org/varpro/server-varpro/commits/59f2adfdca1bd9be4114caae0ff6b5a9e4c18d62))
* feat: added deploy script @Peter Brink ([`3e0e4b6`](https://bitbucket.org/varpro/server-varpro/commits/3e0e4b691ded26e330c49dc9f0eecc3c46ff04ec))

### Fixes

* fix: corregir el recuperar fragmentos de la mo @Carlos David Martinez Medrano ([`3d331e3`](https://bitbucket.org/varpro/server-varpro/commits/3d331e37a43ae59d6f72da9bb8743165a4f82605))
* fix: adding status to groups @DiegoCruzM ([`3417669`](https://bitbucket.org/varpro/server-varpro/commits/3417669e18c764ee9f096a9c49e1690707e03cd8))
* fix: add voucher type in transfer table @Carlos David Martinez Medrano ([`eb9f013`](https://bitbucket.org/varpro/server-varpro/commits/eb9f01345c879629334202515fc65e9a485d9948))
* fix: fixing operator count with overlapping shifts @DiegoCruzM ([`4e0bdb6`](https://bitbucket.org/varpro/server-varpro/commits/4e0bdb6b13142baa343f613e822e2c5d5156ae26))
* fix: fixing break minutes @DiegoCruzM ([`f6d3f98`](https://bitbucket.org/varpro/server-varpro/commits/f6d3f98f5af2c88684ae324718c679b3a479c56e))
* fix: se agrego el voucherid al log de los tickets @Carlos David Martinez Medrano ([`4dbe623`](https://bitbucket.org/varpro/server-varpro/commits/4dbe6230a35d33af08e545429a9fd350bfb6010c))
* fix: add weight in fabric audit @Carlos David Martinez Medrano ([`1244641`](https://bitbucket.org/varpro/server-varpro/commits/124464199c4231419b614dfb55c33c182cc51c36))




## [1.7.0](https://bitbucket.org/varpro/server-varpro/compare/1.7.0..1.6.3) - 2023-04-18

### Features

* feat: agregar funcion para los horarios en metis @DiegoCruzM ([`45dc2fb`](https://bitbucket.org/varpro/server-varpro/commits/45dc2fbee47bf5cb14c6356a2f6af6ab82a4ef3b))
* feat: working in shift employee @DiegoCruzM ([`867cf46`](https://bitbucket.org/varpro/server-varpro/commits/867cf46f08d29d8db76bec5640658d2125daa1fd))
* feat: agregando nuevos endpoint para los shifts @DiegoCruzM ([`d21a022`](https://bitbucket.org/varpro/server-varpro/commits/d21a022bafce7909732014596390f7babd68d4a0))
* feat: initial config for fragments @Carlos David Martinez Medrano ([`51526ba`](https://bitbucket.org/varpro/server-varpro/commits/51526ba067b56d8d609881bfa8ff38d93e018d89))
* feat: agregar funcion para escanear desde la vista de mo metis @Carlos David Martinez Medrano ([`5f08ce9`](https://bitbucket.org/varpro/server-varpro/commits/5f08ce97a8c21d6c1fe466ec1f711e701ad50ff3))
* feat: adding endpoints for roles employees @DiegoCruzM ([`273592f`](https://bitbucket.org/varpro/server-varpro/commits/273592f62fc8ee1d8e0e33cf06e036545e4e9c6f))
* feat: adding endpoints for employees leave actions @DiegoCruzM ([`a485b01`](https://bitbucket.org/varpro/server-varpro/commits/a485b0161661cdd290e629715508d0a2333f9bad))
* feat: agregar opcion de actualizar fragmentos @Carlos David Martinez Medrano ([`33b5585`](https://bitbucket.org/varpro/server-varpro/commits/33b55854d4f9d1ba638e6d1ffe0bfe1dcefbbcc9))
* feat: se agrego la funcion para convertir grupos a lotes @Carlos David Martinez Medrano ([`7ef8502`](https://bitbucket.org/varpro/server-varpro/commits/7ef8502e80bf50c68a9afa8459b67570f19b072e))
* feat: adding new endpoints for shifts @DiegoCruzM ([`59e3a0d`](https://bitbucket.org/varpro/server-varpro/commits/59e3a0d92e922ad801a2f84193e871668bb6adda))
* feat: adding new enpoints for employee groups @DiegoCruzM ([`b6243e8`](https://bitbucket.org/varpro/server-varpro/commits/b6243e8f1751c03bc73a40e1c80d16995ef1ff45))
* feat: cleaning endpoints @DiegoCruzM ([`d622ad2`](https://bitbucket.org/varpro/server-varpro/commits/d622ad2438a0124927f1087ba4e1c64f3c2b321c))
* feat: fixing voucher endpoint and adding new endpoints for emp group @<EMAIL> ([`5475951`](https://bitbucket.org/varpro/server-varpro/commits/5475951a04f61956aa0d7a0ac4591e8be45e4766))
* feat: adding employees to shift @<EMAIL> ([`a53eb3e`](https://bitbucket.org/varpro/server-varpro/commits/a53eb3e6345e977fca13de81233b40f75bd4ff1b))
* feat: cambiar la funcion de escaneo de mo @Carlos David Martinez Medrano ([`d4acfba`](https://bitbucket.org/varpro/server-varpro/commits/d4acfbac58d81de5e3a11475b7dea3acd5546b71))
* feat: adding delete shift functrion @DiegoCruzM ([`0376486`](https://bitbucket.org/varpro/server-varpro/commits/03764867b510c161c061d4b80d1d80cecdca0a05))
* feat: adding endpoints for shift employee @<EMAIL> ([`acb6678`](https://bitbucket.org/varpro/server-varpro/commits/acb6678a8412f9edfcf9b6a06cba53fc2db96c58))

### Fixes

* fix: fixing loadings and modals backdrops @DiegoCruzM ([`18f609c`](https://bitbucket.org/varpro/server-varpro/commits/18f609c73b8639e4d5e88fffc19bbeac3a34e47b))
* fix: fixing shift employee edpoints @DiegoCruzM ([`8053714`](https://bitbucket.org/varpro/server-varpro/commits/80537144253b2f1790fb5ede100511f1b02cec8d))
* fix: fixing endpoints for shifts @DiegoCruzM ([`89ad200`](https://bitbucket.org/varpro/server-varpro/commits/89ad200ca04d0263cdafdb1f2957a69e08b2ad13))
* fix: fixing endpoints for shifts @DiegoCruzM ([`639e0aa`](https://bitbucket.org/varpro/server-varpro/commits/639e0aa88116aa8c91e0ba5281313aff842de086))




## [1.6.3](https://bitbucket.org/varpro/server-varpro/compare/1.6.3..1.6.1) - 2023-02-20

### Features

* feat: convert batch to voucher group @Carlos David Martinez Medrano ([`47a6d16`](https://bitbucket.org/varpro/server-varpro/commits/47a6d163f73e5cfef897a01eaf09707bb5dc7b75))

### Fixes

* fix: fix lint error @Carlos David Martinez Medrano ([`dd7cad7`](https://bitbucket.org/varpro/server-varpro/commits/dd7cad7790ce645a8a4958e48f354c3c36c3dbbb))
* fix: fix lint errors @Carlos David Martinez Medrano ([`fc703cf`](https://bitbucket.org/varpro/server-varpro/commits/fc703cf5a9c810134dc7bd297c17326ac0263f2f))
* fix: fix project error on compile @Carlos David Martinez Medrano ([`7480bbb`](https://bitbucket.org/varpro/server-varpro/commits/7480bbb54817445abfc819c9310a1392f65cb8fc))
* fix: fix scanning error @Carlos David Martinez Medrano ([`a8b70e8`](https://bitbucket.org/varpro/server-varpro/commits/a8b70e85ed6e0251bf9cdbfa8c0dd9c2a45ab8df))
* fix: fix get user info @Carlos David Martinez Medrano ([`926e7c3`](https://bitbucket.org/varpro/server-varpro/commits/926e7c37497ce5571e197f9a9bff98ca27f2d80e))
* fix: fix get user info @Carlos David Martinez Medrano ([`7d636f5`](https://bitbucket.org/varpro/server-varpro/commits/7d636f539e8344768f69b1f5b2a430de96a17443))
* fix: fix webvoucher error @Carlos David Martinez Medrano ([`6453906`](https://bitbucket.org/varpro/server-varpro/commits/6453906d0c167835ac3c6112c24e75728291dad5))
* fix: fix lint error on mailer controller @Carlos David Martinez Medrano ([`df00fd3`](https://bitbucket.org/varpro/server-varpro/commits/df00fd3a77fa092fcb2105ead22529de0a6e5aaf))




## [1.6.1](https://bitbucket.org/varpro/server-varpro/compare/1.6.1..1.5.1) - 2023-01-05

### Features

* feat: se agrego la funcion para la vista de inventario @Carlos David Martinez Medrano ([`b254e0d`](https://bitbucket.org/varpro/server-varpro/commits/b254e0de10f4bff5bdf4b43baa73f13fd30ba1e7))
* feat: add lots view @Carlos David Martinez Medrano ([`76497d1`](https://bitbucket.org/varpro/server-varpro/commits/76497d13b45261b69995ea5035e3dfe80ad8cc9c))
* feat: se agrego la funcion para poder hacer merge desde metisapp @Carlos David Martinez Medrano ([`d613120`](https://bitbucket.org/varpro/server-varpro/commits/d613120efa7e1b9ce65135953e6db8daa92a0006))

### Fixes

* fix: se corrigio el merge vouchers @Carlos David Martinez Medrano ([`b412261`](https://bitbucket.org/varpro/server-varpro/commits/b41226163bb3d4a13e4c14a9cacbeb81d394fe58))
* fix: se corrigio el merge vouchers @Carlos David Martinez Medrano ([`cdf8550`](https://bitbucket.org/varpro/server-varpro/commits/cdf85504d3fc497505fa90011e552a17494b3bbe))
* fix: clear console log and fix transfers @Carlos David Martinez Medrano ([`467146f`](https://bitbucket.org/varpro/server-varpro/commits/467146fa2ceedb2f18dd7bee9ec15cf94f83bc1d))




## [1.5.1](https://bitbucket.org/varpro/server-varpro/compare/1.5.1..1.5.0) - 2022-11-16






## [1.5.0](https://bitbucket.org/varpro/server-varpro/compare/1.5.0..1.4.1) - 2022-11-14

### Features

* feat: se agrego la opcion de hacer merge en recibir mo @Carlos David Martinez Medrano ([`3a9fbf1`](https://bitbucket.org/varpro/server-varpro/commits/3a9fbf18df06ff3a609813c6758370a51c691de4))
* feat: se agrego metodo para obtener los tickets de una MO @Carlos David Martinez Medrano ([`dbf8c76`](https://bitbucket.org/varpro/server-varpro/commits/dbf8c76b5dbeec2e5493eb635751f70d58225f04))

### Fixes

* fix: se corrigio el status code al no encontrar tickets para la mo @Carlos David Martinez Medrano ([`8a6662e`](https://bitbucket.org/varpro/server-varpro/commits/8a6662e5a0805f543570eb980356bcea8d974a5d))




## [1.4.1](https://bitbucket.org/varpro/server-varpro/compare/1.4.1..1.3.1) - 2022-10-25

### Features

* feat: se agrego la opcion de transferencias en metisapp @Carlos David Martinez Medrano ([`8f40f80`](https://bitbucket.org/varpro/server-varpro/commits/8f40f8016e468c37c90b061179f6b8c789838437))





## [1.3.1](https://bitbucket.org/varpro/server-varpro/compare/1.3.1..1.2.27) - 2022-10-09

### Features

* feat: scanning info @Carlos David Martinez Medrano ([`29f16b2`](https://bitbucket.org/varpro/server-varpro/commits/29f16b25d9be9d75fe8b23bcd5070200928d20cd))

### Fixes

* fix: se corrigio la funcion para analizar el  mo_barcode @Carlos David Martinez Medrano ([`c026440`](https://bitbucket.org/varpro/server-varpro/commits/c026440a5041c9673cf2ff10f9e474f67cf101b3))
* fix: validar correctamente el mo barcode por cliente @Carlos David Martinez Medrano ([`29b9350`](https://bitbucket.org/varpro/server-varpro/commits/29b9350a6d44d8cec7d536997189e9a92c9abc7e))




## [1.2.27](https://bitbucket.org/varpro/server-varpro/compare/1.2.27..1.2.19) - 2022-09-28

### Features

* feat: se agrego la linea y grupo q escaneo en la vista de tickets @Carlos David Martinez Medrano ([`edb080d`](https://bitbucket.org/varpro/server-varpro/commits/edb080d6031f76dea05d57a6078f2846087750ea))
* feat: se corrigio el buscador de mos @Carlos David Martinez Medrano ([`0566ce0`](https://bitbucket.org/varpro/server-varpro/commits/0566ce019aafa43d8aa5c28859b9901297e05306))

### Fixes

* fix: se corrigio el obtener info de ordenes de varsity @Carlos David Martinez Medrano ([`dfee88c`](https://bitbucket.org/varpro/server-varpro/commits/dfee88cdb62166065efedd85fdfa3da6d19cd801))
* fix: se corrigio el obtener informacion de la mo en crear ticket @Carlos David Martinez Medrano ([`4c4c4f6`](https://bitbucket.org/varpro/server-varpro/commits/4c4c4f63135ad1016d71ee8c63f64c74e794ab1b))
* fix: se corrigio la info del voucher type por defecto al recibir mo @Carlos David Martinez Medrano ([`71ef78f`](https://bitbucket.org/varpro/server-varpro/commits/71ef78f645f853e2aa72e8a3b0fd2fd5911ddf0a))
* fix: se corrigio el obtener mos por ppmo @Carlos David Martinez Medrano ([`f0b143e`](https://bitbucket.org/varpro/server-varpro/commits/f0b143e8096b678891be8a44c15a8b41205a4157))
* fix: se corrigio el crear ticket del metisapp @Carlos David Martinez Medrano ([`5ee9dfc`](https://bitbucket.org/varpro/server-varpro/commits/5ee9dfc308ca315ea7a52616b67900fa1e037d93))
* fix: se corrigio la validacion del voucher type al recibir mos @Carlos David Martinez Medrano ([`6e3e34d`](https://bitbucket.org/varpro/server-varpro/commits/6e3e34db9e1c45fb55e33f3b3608846bc5543905))
* fix: se corrigio el crear tickets en metis app @Carlos David Martinez Medrano ([`8be90c9`](https://bitbucket.org/varpro/server-varpro/commits/8be90c9775043968f065aa61a52c7e9d76eb9b43))




## [1.2.19](https://bitbucket.org/varpro/server-varpro/compare/1.2.19..1.2.18) - 2022-09-19

### Features

* feat: se corrigio el recibir MOS @Carlos David Martinez Medrano ([`97428c4`](https://bitbucket.org/varpro/server-varpro/commits/97428c44b862a2dd08ef71d07bfb372327cac3f8))





## [1.2.18](https://bitbucket.org/varpro/server-varpro/compare/1.2.18..1.2.17) - 2022-09-14

### Features

* feat: add clone vouchers @Carlos David Martinez Medrano ([`378aada`](https://bitbucket.org/varpro/server-varpro/commits/378aadab7bd15d771602eb77d64620a6d346f1e3))





## [1.2.17](https://bitbucket.org/varpro/server-varpro/compare/1.2.17..1.2.16) - 2022-09-09






## [1.2.16](https://bitbucket.org/varpro/server-varpro/compare/1.2.16..1.2.15) - 2022-09-08






## [1.2.15](https://bitbucket.org/varpro/server-varpro/compare/1.2.15..1.2.14) - 2022-09-07






## [1.2.14](https://bitbucket.org/varpro/server-varpro/compare/1.2.14..1.2.12) - 2022-09-07


### Fixes

* fix: se corrigio la cantidad de pre barcode q se imprimen @Carlos David Martinez Medrano ([`ebf856b`](https://bitbucket.org/varpro/server-varpro/commits/ebf856b119ddcbb1481dc4013d4d4509803564ab))




## [1.2.12](https://bitbucket.org/varpro/server-varpro/compare/1.2.12..1.2.11) - 2022-09-06






## [1.2.11](https://bitbucket.org/varpro/server-varpro/compare/1.2.11..1.2.10) - 2022-09-06






## [1.2.10](https://bitbucket.org/varpro/server-varpro/compare/1.2.10..1.2.9) - 2022-09-01

### Features

* feat: se agrego la opcion de buscar por mo barcode en la vista de ubicaciones @Carlos David Martinez Medrano ([`e4ddc1b`](https://bitbucket.org/varpro/server-varpro/commits/e4ddc1bc6958be958abbde3a4e0117667b6dfb33))





## [1.2.9](https://bitbucket.org/varpro/server-varpro/compare/1.2.9..1.2.8) - 2022-08-30

### Features

* feat: se agrego la validacion de company ignore update @Carlos David Martinez Medrano ([`3508c6e`](https://bitbucket.org/varpro/server-varpro/commits/3508c6e4da874d40a349411868a15b1c65501993))
* feat: se corrigio el recibir mos @Carlos David Martinez Medrano ([`245c963`](https://bitbucket.org/varpro/server-varpro/commits/245c96394b28bab010d9c6d0b0b2a53885af964c))

### Fixes

* fix: fix create voucher in metisapp @Carlos David Martinez Medrano ([`ecd8040`](https://bitbucket.org/varpro/server-varpro/commits/ecd804078627ead4c5898f5278fa6cc8f29fae0f))




## [1.2.8](https://bitbucket.org/varpro/server-varpro/compare/1.2.8..1.2.7) - 2022-08-19

### Features

* feat: se corrigio el reporte de all view @Carlos David Martinez Medrano ([`070e72d`](https://bitbucket.org/varpro/server-varpro/commits/070e72d89461c8e2a361c8d037ddaf8aedf4a2a8))
* feat: add search tickets by mo @Carlos David Martinez Medrano ([`7c0e479`](https://bitbucket.org/varpro/server-varpro/commits/7c0e4790bdbcfe38e4a5319c39c71a4699de06f0))
* feat: se agrego la propiedad preBarcode a la lista de barcodes @Carlos David Martinez Medrano ([`8c634ce`](https://bitbucket.org/varpro/server-varpro/commits/8c634cef70ac94ef2cd74c375b303c589ff5d89e))





## [1.2.7](https://bitbucket.org/varpro/server-varpro/compare/1.2.7..1.2.5) - 2022-07-21

### Features

* feat: se agrego buscador de mos @Carlos David Martinez Medrano ([`5ebefeb`](https://bitbucket.org/varpro/server-varpro/commits/5ebefebef0473e5a7ecba9c229e5873692ce4326))

### Fixes

* fix: se corrigio la creacion de tickets @Carlos David Martinez Medrano ([`d1e8fdb`](https://bitbucket.org/varpro/server-varpro/commits/d1e8fdb38cdafccaea2cf07e7d5c255be365262d))




## [1.2.5](https://bitbucket.org/varpro/server-varpro/compare/1.2.5..1.2.4) - 2022-07-12

### Features

* feat: se agrego la opcion de añadir comentario desde crear tickets @Carlos David Martinez Medrano ([`597f635`](https://bitbucket.org/varpro/server-varpro/commits/597f635148fe8fef77833b46aa4cffcf5cc3d6e2))
* feat: se agrego la funcion para retomar la cola de impresiones para los pre barcodes @Carlos David Martinez Medrano ([`0b00757`](https://bitbucket.org/varpro/server-varpro/commits/0b007572a7a8f9b8ba677ea1f712938b3a8655f3))





## [1.2.4](https://bitbucket.org/varpro/server-varpro/compare/1.2.4..1.1.14) - 2022-07-07

### Features

* feat: se corrigio la informacion de los lotes @Carlos David Martinez Medrano ([`c8e5209`](https://bitbucket.org/varpro/server-varpro/commits/c8e5209fe0a83c7ee533027a1ad6b3486ea26167))
* feat: se agregaron las acciones en tickets a recibir @Carlos David Martinez Medrano ([`14415f2`](https://bitbucket.org/varpro/server-varpro/commits/14415f27a3d911e19777d160be720ca4abcdc6dd))
* feat: web scan @Carlos David Martinez Medrano ([`51fd0bb`](https://bitbucket.org/varpro/server-varpro/commits/51fd0bb7559a593f376f4afa083eb27e11da3e09))
* feat: se agrego el endpoint para la vista del wip @Carlos David Martinez Medrano ([`c6acb98`](https://bitbucket.org/varpro/server-varpro/commits/c6acb988f9a77daa1683322c5559db4a67091bea))
* feat: se agrego la opcion de generar reportes @Carlos David Martinez Medrano ([`f0a1634`](https://bitbucket.org/varpro/server-varpro/commits/f0a163479cb5dde62bd493917a477a78307ce9a4))
* feat: se agrego la funcion para crear un nuevo voucher group y agregarle un voucher @Carlos David Martinez Medrano ([`91d4ed5`](https://bitbucket.org/varpro/server-varpro/commits/91d4ed55032fb3e0e9d38fc35ef12987e4265dcf))
* feat: se agrego la funcion para obtener los pre barcodes @Carlos David Martinez Medrano ([`673208f`](https://bitbucket.org/varpro/server-varpro/commits/673208f82bc5b804fcab69937836996544378f0c))
* feat: se corrigio el reporte de mo activas @Carlos David Martinez Medrano ([`92aefc8`](https://bitbucket.org/varpro/server-varpro/commits/92aefc8e5a237483bebda77bfa8c40719032d1cb))
* feat: se agrego la info de las mos escaneadas @Carlos David Martinez Medrano ([`90c98bc`](https://bitbucket.org/varpro/server-varpro/commits/90c98bcb7b008923246e22cd099b2f36faee1568))
* feat: se agrego la funcion para añadir un voucher desde un grupo de vochers @Carlos David Martinez Medrano ([`d13d6ae`](https://bitbucket.org/varpro/server-varpro/commits/d13d6ae312d48c1416f306193a4abe5374198547))
* feat: cambiar entradas a la api @Carlos David Martinez Medrano ([`14f5320`](https://bitbucket.org/varpro/server-varpro/commits/14f5320d513c8ff95a0b1fa280919a58bd4f34ed))
* feat: se corrigio la vista de grupo de vouchers, mostrando solo los tickets del area @Carlos David Martinez Medrano ([`a4aba80`](https://bitbucket.org/varpro/server-varpro/commits/a4aba802b3a78065714924d0d4d107c72e628ed2))
* feat: se acgrego el company code a la peticion de vouchers @Carlos David Martinez Medrano ([`0d9f98d`](https://bitbucket.org/varpro/server-varpro/commits/0d9f98df2e317213a0da104e322da104c36cd569))
* feat: resolviendo conflictos @Carlos David Martinez Medrano ([`eb5f5ce`](https://bitbucket.org/varpro/server-varpro/commits/eb5f5ce99848b9d9c29a436999de5f8dd32a784e))

### Fixes

* fix: metis scan fix @Carlos David Martinez Medrano ([`099f24d`](https://bitbucket.org/varpro/server-varpro/commits/099f24d2a81846aca54593ebde4a2bd08eea2de5))
* fix: se agrego el campo data_base_unit_symbol @Carlos David Martinez Medrano ([`73aa8cb`](https://bitbucket.org/varpro/server-varpro/commits/73aa8cbb8f3e323f7c8abcd9da86ecda112249af))
* fix: se corrigio el controlador monumbers @Carlos David Martinez Medrano ([`89bbc1a`](https://bitbucket.org/varpro/server-varpro/commits/89bbc1a14466532771b59e98719b5eb339a361c0))




## [1.1.14](https://bitbucket.org/varpro/server-varpro/compare/1.1.14..1.1.5) - 2022-03-22

### Features

* feat: se corrigio el pegar mos en la vista de crear tickets @Carlos David Martinez Medrano ([`128ea28`](https://bitbucket.org/varpro/server-varpro/commits/128ea2817ba8dd8bb8bc1f6c9d6bf90f478312b1))
* feat: se corrigio la consulta de todos los tickets @Carlos David Martinez Medrano ([`6e9b5d3`](https://bitbucket.org/varpro/server-varpro/commits/6e9b5d3c9df422ab736ca1d15674407733f254f5))
* feat: add tickets to a group of vouchers in create tickets @Carlos David Martinez Medrano ([`152ab0f`](https://bitbucket.org/varpro/server-varpro/commits/152ab0f46ea559a7c2c1a0dbc9e114977667b6fb))
* feat: se corrigio el mover un voucher de un grupo a otro @Carlos David Martinez Medrano ([`551b3c6`](https://bitbucket.org/varpro/server-varpro/commits/551b3c6d6fd495faf2dad2026135565c88385f90))

### Fixes

* fix: se corrigio la consulta de tickets activos @Carlos David Martinez Medrano ([`712d87f`](https://bitbucket.org/varpro/server-varpro/commits/712d87fdd27c1dede301b8bc548b04b0054c657e))
* hotfix: error tickets @Carlos David Martinez Medrano ([`cc4ec97`](https://bitbucket.org/varpro/server-varpro/commits/cc4ec97892a06bf91783bf0eeb715da939276955))
* hotfix: problemas en las cors @Carlos David Martinez Medrano ([`1c7e947`](https://bitbucket.org/varpro/server-varpro/commits/1c7e9474862216fc24b5e7b1ae2594073dcc6265))
* hotfix: error tickets @Carlos David Martinez Medrano ([`6395d24`](https://bitbucket.org/varpro/server-varpro/commits/6395d240cb1658052efb6c612aa5a688350128dc))
* hotfix: error tickets @Carlos David Martinez Medrano ([`95e01ee`](https://bitbucket.org/varpro/server-varpro/commits/95e01ee2b3e7157b013d8ba944fe2424d8a20155))
* hotfix: error tickets @Carlos David Martinez Medrano ([`cb28439`](https://bitbucket.org/varpro/server-varpro/commits/cb28439f1e6aedefe7ace4dc467caeb64a02c75a))
* hotfix: ticket query fix @Carlos David Martinez Medrano ([`8daaf80`](https://bitbucket.org/varpro/server-varpro/commits/8daaf807b36a7aaf2bf9824f859218822b1bcc46))
* hotfix: getVouchers @Carlos David Martinez Medrano ([`fe3e232`](https://bitbucket.org/varpro/server-varpro/commits/fe3e232d594eb9cf653c0f4cbe8ee7a9dea04d8f))
* hotfix: error tickets @Carlos David Martinez Medrano ([`a84efb9`](https://bitbucket.org/varpro/server-varpro/commits/a84efb909997ca6fc78284d9f4fdcc9d27677145))
* hotfix: error ticket, limit add @Carlos David Martinez Medrano ([`f924169`](https://bitbucket.org/varpro/server-varpro/commits/f9241693e80ffdc320c263b475868293f05cd07e))
* hotfix: error ticket, limit add @Carlos David Martinez Medrano ([`d237cca`](https://bitbucket.org/varpro/server-varpro/commits/d237cca08efa82c0475c27af98306b05e19f346e))




## [1.1.5](https://bitbucket.org/varpro/server-varpro/compare/1.1.5..1.1.0) - 2022-02-22

### Features

* feat: se agrego la funcion para obtener las ordenes de piedras @Carlos David Martinez Medrano ([`38628d0`](https://bitbucket.org/varpro/server-varpro/commits/38628d068a135a80cc69b3b1567a5710beb3002f))
* feat: se agregaron las funciones para la vista de grupo de vouchers @Carlos David Martinez Medrano ([`546f14d`](https://bitbucket.org/varpro/server-varpro/commits/546f14d25cfa03d65d1fe80652b3c527ef4699a8))
* feat: se corrigio el crear tickets @Carlos David Martinez Medrano ([`60ae06f`](https://bitbucket.org/varpro/server-varpro/commits/60ae06f07600e293319f498b1348c537de7c0e56))
* feat: se agrego la funcion para completar un ticket antes de escanear @Carlos David Martinez Medrano ([`50eff5b`](https://bitbucket.org/varpro/server-varpro/commits/50eff5bc8742c4259696d2b088b5e5ccdc1e9724))
* feat: se agrego la opcion de crear tickets desde ticket a recibir @Carlos David Martinez Medrano ([`e1c4ac3`](https://bitbucket.org/varpro/server-varpro/commits/e1c4ac3bed8e737d23870cf30a111550785ad0e3))
* feat: se agregaron los campos de lineas y grupos en crear tickets y tickets activos @Carlos David Martinez Medrano ([`3726fec`](https://bitbucket.org/varpro/server-varpro/commits/3726fec197a9462f8d6f9db31cc9586d1603e702))
* feat: se agrego el obtener tickets no completos de otras areas @Carlos Martinez ([`2aa8cec`](https://bitbucket.org/varpro/server-varpro/commits/2aa8cecdc523d01a01748949aac0deb79d6db8e2))
* feat: se agrego la opcion para escanear vouchers @Carlos Martinez ([`b463f3e`](https://bitbucket.org/varpro/server-varpro/commits/b463f3eeb5171e944d78a6924aa97d0ac9cd2c05))
* feat: se agrego la funcion para crear tickets desde tickets a recibir @Carlos David Martinez Medrano ([`6280c73`](https://bitbucket.org/varpro/server-varpro/commits/6280c734ebda81358717bc1d46f0d5e52691767d))
* feat: se agrego la opcion de editar comentarios @Carlos David Martinez Medrano ([`fba7633`](https://bitbucket.org/varpro/server-varpro/commits/fba7633bbd6338031edcc526e38d20913bacb59b))
* feat: se corrigio la info del ticket en en log @Carlos Martinez ([`e53135f`](https://bitbucket.org/varpro/server-varpro/commits/e53135f8d219cb7039973f0a8a3445eb37770bd6))
* feat: se eliminaron los console log @Carlos David Martinez Medrano ([`c11c242`](https://bitbucket.org/varpro/server-varpro/commits/c11c242d5eb0fcf1d4838903ab2731bf30d137ae))
* feat: fixed date on tickets active @Carlos David Martinez Medrano ([`06bb4c5`](https://bitbucket.org/varpro/server-varpro/commits/06bb4c56aeca68c834d81de6716ece98b23640b6))
* feat: se agrego el main voucher para poder escanear a los clientes @Carlos Martinez ([`62f30e6`](https://bitbucket.org/varpro/server-varpro/commits/62f30e6c394ac4f12bf6a6c5800fd36df3b7ad3b))
* feat: se agrego el voucherTypeId a la informacion del usuario @Carlos David Martinez Medrano ([`a2cba52`](https://bitbucket.org/varpro/server-varpro/commits/a2cba52f42cd812b68921a1c0d4593fcc4f27e1a))





## [1.1.0](https://bitbucket.org/varpro/server-varpro/compare/1.1.0..1.0.10) - 2021-12-03






## [1.0.10](https://bitbucket.org/varpro/server-varpro/compare/1.0.10..1.0.9) - 2021-12-01

### Features

* feat: merge vouchers and clean console log @Carlos Martinez ([`63da89b`](https://bitbucket.org/varpro/server-varpro/commits/63da89bcca3d7692366c263b1080629c7d6e7a23))





## [1.0.9](https://bitbucket.org/varpro/server-varpro/compare/1.0.9..1.0.8) - 2021-11-24

### Features

* feat: agregar historial de estatus en la tabla detalles de mo list @Carlos Martinez ([`9cce7b5`](https://bitbucket.org/varpro/server-varpro/commits/9cce7b5d8c3cc322c646e83d9944b6a62f0c7e26))





## [1.0.8](https://bitbucket.org/varpro/server-varpro/compare/1.0.8..1.0.7) - 2021-11-23

### Features

* feat: se corrigio el mo status @Carlos Martinez ([`5a8d3df`](https://bitbucket.org/varpro/server-varpro/commits/5a8d3df3aebae53f014973986029dffe19231dec))





## [1.0.7](https://bitbucket.org/varpro/server-varpro/compare/1.0.7..1.0.6) - 2021-11-18

### Features

* feat: se corrigio el work activity log y el contardor de ticket en mo list @Carlos Martinez ([`2fa08f5`](https://bitbucket.org/varpro/server-varpro/commits/2fa08f564e2fa3c86dab3be40e21bd45285d0f14))





## [1.0.6](https://bitbucket.org/varpro/server-varpro/compare/1.0.6..1.0.5) - 2021-11-18

### Features

* feat: se corrigio el conteo de tickets en la lista de mo @Carlos Martinez ([`b14c2d9`](https://bitbucket.org/varpro/server-varpro/commits/b14c2d9dbc0f3bc0dfe2886d9dd0478f73d055ec))





## [1.0.5](https://bitbucket.org/varpro/server-varpro/compare/1.0.5..1.0.4) - 2021-11-17

### Features

* feat: transfers init @Carlos Martinez ([`e6bad0f`](https://bitbucket.org/varpro/server-varpro/commits/e6bad0f754b79f10251f795df2e531e3753b2e97))
* feat: add mo status @Carlos Martinez ([`e9103eb`](https://bitbucket.org/varpro/server-varpro/commits/e9103eb2b5d809d3a4e286e64089be1ab4c7f3e0))





## [1.0.4](https://bitbucket.org/varpro/server-varpro/compare/1.0.4..1.0.3) - 2021-11-15

### Features

* feat: se coorrigio el ver todas las listas del area @Carlos Martinez ([`6a5687b`](https://bitbucket.org/varpro/server-varpro/commits/6a5687bfbdfb919128b4ed12df450db1d9ba788f))





## [1.0.3](https://bitbucket.org/varpro/server-varpro/compare/1.0.3..1.0.2) - 2021-11-12

### Features

* feat: cambios para mo lists y el buscador de por mo @Carlos Martinez ([`2ecf02e`](https://bitbucket.org/varpro/server-varpro/commits/2ecf02efde753b56b7a02c92047556dbeb1a665b))





## [1.0.2](https://bitbucket.org/varpro/server-varpro/compare/1.0.2..0.1.7) - 2021-11-10

### Features

* feat: updated methods for inventory @Carlos Martinez ([`0e07939`](https://bitbucket.org/varpro/server-varpro/commits/0e07939a5a9e1293d4b147f4d276862d64668337))
* feat: cambios en getListArea, se agrego contador de tickets @Carlos Martinez ([`1db5d7c`](https://bitbucket.org/varpro/server-varpro/commits/1db5d7c46d4edf7cc56fe86a0985954c0a1628a5))
* feat: filter mo lists @Carlos David Martinez Medrano ([`28d9063`](https://bitbucket.org/varpro/server-varpro/commits/28d9063b7c8e21fe59877c0ad22669ba7cf196fb))
* feat: se configuro el metodo getVouchersInfoFilter @Carlos David Martinez Medrano ([`5c823c0`](https://bitbucket.org/varpro/server-varpro/commits/5c823c0c3acc1a70eb7973eb4d789fd20f579dfc))
* feat: correcciones para mo list @Carlos David Martinez Medrano ([`00fe132`](https://bitbucket.org/varpro/server-varpro/commits/00fe1327f90f4db9bbe4a32acdf6b9fe652f5bc1))
* feat: add columns mo list details @Carlos Martinez ([`7472d52`](https://bitbucket.org/varpro/server-varpro/commits/7472d52e05ab2c9fdec709bf70a82770854e4afb))
* feat: se agrego getVouchersMo para obtener la info de voucher para la mo @Carlos David Martinez Medrano ([`de5f505`](https://bitbucket.org/varpro/server-varpro/commits/de5f505f561cdc4a9798db1f0bfcf7653910febb))
* feat: add filter mo list @Carlos David Martinez Medrano ([`8fe12d1`](https://bitbucket.org/varpro/server-varpro/commits/8fe12d159efafd63d3892642ecd01462fd4d4df2))
* feat: add mos with multiple voucher types @Carlos David Martinez Medrano ([`743fcac`](https://bitbucket.org/varpro/server-varpro/commits/743fcac638ced52c7f67f6edf4699c249ba0b542))
* feat: add columuns to table tickets active @Carlos David Martinez Medrano ([`b81052d`](https://bitbucket.org/varpro/server-varpro/commits/b81052d8ec9e1ed5044f979a02122e31a42ceadf))
* feat: se muestra el tipo de voucher segun area @Carlos David Martinez Medrano ([`08f2db5`](https://bitbucket.org/varpro/server-varpro/commits/08f2db5e8586f45e53a7962bf3890142afebe030))
* feat: se corrigio el numero de mo en el log de la lista @Carlos David Martinez Medrano ([`f0a9db1`](https://bitbucket.org/varpro/server-varpro/commits/f0a9db154b22c7f0be3b1496b90112b956743853))
* feat: fixed export date for table mo list @Carlos David Martinez Medrano ([`29ef99b`](https://bitbucket.org/varpro/server-varpro/commits/29ef99b273cac192040f89f121a32fdd3ccc5684))
* feat: merge filter-mo-list @Carlos David Martinez Medrano ([`05fecb0`](https://bitbucket.org/varpro/server-varpro/commits/05fecb00ebc9cf7b6958c3f0c633a3c8e35d28a4))
* feat: se ordeno por secuencia el obtener status @Carlos David Martinez Medrano ([`36bd778`](https://bitbucket.org/varpro/server-varpro/commits/36bd77803728b34edb0eb6b03ff2807633e981a5))
* feat: merge development - mo-list @Carlos David Martinez Medrano ([`d79bc04`](https://bitbucket.org/varpro/server-varpro/commits/d79bc04bec5919e5e6ea0789d3e5fa562ab29266))
* feat: update method scan voucher @Carlos Martinez ([`a2d033a`](https://bitbucket.org/varpro/server-varpro/commits/a2d033a1b8d47168c7534f50210a66d736d3f2c0))
* feat: se agrego la funcion actualizar estatus de tickets @Carlos Martinez ([`894e007`](https://bitbucket.org/varpro/server-varpro/commits/894e007f65034bbe307694516e2e968f9f277b26))
* feat: endpoints molist @Carlos David Martinez Medrano ([`58490ef`](https://bitbucket.org/varpro/server-varpro/commits/58490ef5a67fe1c9d03bb4179c3eb72ee27bca08))
* feat: add columns tickets @Carlos David Martinez Medrano ([`6a8ab35`](https://bitbucket.org/varpro/server-varpro/commits/6a8ab359e93bc526287d0f4c1544f127a9ca702c))
* feat: added the function to add and remove mo to the list @Carlos David Martinez Medrano ([`cb0dea7`](https://bitbucket.org/varpro/server-varpro/commits/cb0dea71f399c0d79a81f30cc83f84b7eee56581))
* feat: se agrego el metodo para escanear a ubicacion @Carlos Martinez ([`691058c`](https://bitbucket.org/varpro/server-varpro/commits/691058c130e8e54956aa339761434187a3cdc7b2))
* feat: se agrego la funcion para escanear ubicacion @Carlos Martinez ([`8d97ae8`](https://bitbucket.org/varpro/server-varpro/commits/8d97ae85cdcff9827ee021731b79abed71e3b272))
* feat: add methods list mos @Carlos David Martinez Medrano ([`d4e84fb`](https://bitbucket.org/varpro/server-varpro/commits/d4e84fb3ea5b38a7a0743fdd4c9dc391dc2baa93))
* feat: add search mo list @Carlos David Martinez Medrano ([`9516894`](https://bitbucket.org/varpro/server-varpro/commits/9516894c3d0ca6b1b3f67f47fb5fd6dbb36e7e04))
* feat: se corrigio el contador de vouchers @Carlos David Martinez Medrano ([`195eeb4`](https://bitbucket.org/varpro/server-varpro/commits/****************************************))
* feat: se agrego funcion para validar mo en crear ticket @Carlos Martinez ([`6d5cfa0`](https://bitbucket.org/varpro/server-varpro/commits/6d5cfa004f0b78c1346bead21902931fa1f19e86))
* feat: se agrego metodo para crear comentarios en la mo @Carlos Martinez ([`a48e088`](https://bitbucket.org/varpro/server-varpro/commits/a48e0882666a325884037777730fa2e4413ab756))
* feat: add method createBatch @Carlos David Martinez Medrano ([`67f3871`](https://bitbucket.org/varpro/server-varpro/commits/67f38714201599cf92118543d73d5259c1912f15))
* feat: add merge vouchers @Carlos David Martinez Medrano ([`4a704b3`](https://bitbucket.org/varpro/server-varpro/commits/4a704b3c13e9ee24813ff2e99874747c328a0941))
* feat: funcion para obtener los comentarios del ticket @Carlos Martinez ([`8a5f782`](https://bitbucket.org/varpro/server-varpro/commits/8a5f782af3f8662170404cf0d39f0dc1c48aeb6c))
* feat: changed list info @Carlos David Martinez Medrano ([`c43d669`](https://bitbucket.org/varpro/server-varpro/commits/c43d669db1d5a54f6f902b6d2b4cde5bef417525))
* feat: change info mo list @Carlos David Martinez Medrano ([`e67369c`](https://bitbucket.org/varpro/server-varpro/commits/e67369cc3d6943ebad8b7675535001f344ad75fa))
* feat: se realizo la correccion en la fecha de creado en ticket activo @Carlos Martinez ([`62878c5`](https://bitbucket.org/varpro/server-varpro/commits/62878c54055e3414918b7254d32f2102bb3da6fb))
* feat: get all vouchers merged @Carlos David Martinez Medrano ([`5b48cad`](https://bitbucket.org/varpro/server-varpro/commits/5b48cad881bda3212917f87f6681ac81f811efeb))
* feat: add main merge voucher info @Carlos David Martinez Medrano ([`4f96b59`](https://bitbucket.org/varpro/server-varpro/commits/4f96b59ab54cecdd2027d317b32752c7b26f6b96))
* feat: se corrigio la fecha de exportacion @Carlos David Martinez Medrano ([`3a53d58`](https://bitbucket.org/varpro/server-varpro/commits/3a53d583432d1cab68b0825e74c6127d09b01d5c))
* feat: merge development @Carlos David Martinez Medrano ([`33a3a6a`](https://bitbucket.org/varpro/server-varpro/commits/33a3a6a1f1fb55659a9a25d810210860b859d261))
* feat: add column mo in scan status @Carlos David Martinez Medrano ([`39e00f5`](https://bitbucket.org/varpro/server-varpro/commits/39e00f5bc609067f7a2354354c58799eae3c0d33))
* feat: add column create ticket grid @Carlos David Martinez Medrano ([`3b04f52`](https://bitbucket.org/varpro/server-varpro/commits/3b04f52d988faff7bac1b9a458b4a704c885e948))
* feat: se agrego fecha de exportacion en todos los tickets @Carlos David Martinez Medrano ([`816632d`](https://bitbucket.org/varpro/server-varpro/commits/816632d89e8542b29fac14f17d81624949f6cb3a))
* feat: add ticket id to method info ticket @Carlos Martinez ([`5ece31e`](https://bitbucket.org/varpro/server-varpro/commits/5ece31e73d8f7c13451d907454e19b5741c29391))





## [0.1.7](https://bitbucket.org/varpro/server-varpro/compare/0.1.7..0.1.5) - 2021-07-09

### Features

* feat: scan voucher action @Carlos Martinez ([`7ea5862`](https://bitbucket.org/varpro/server-varpro/commits/7ea586288bbed4b052b5bc1d52dc878aaae3026b))
* feat: se modificaron endpoints para la creacion de tickets @Carlos Martinez ([`ab34f12`](https://bitbucket.org/varpro/server-varpro/commits/ab34f12677708c70159c3d7ddfe9cff42e82e39d))
* feat: se actualizaron los endpoints de tickets @Carlos Martinez ([`aee7364`](https://bitbucket.org/varpro/server-varpro/commits/aee736408cfb649d1a160639a3e80235f2e812f0))
* feat: se corrigio la respuesta de tickets y el estatus nuevo @Carlos Martinez ([`b41b6b7`](https://bitbucket.org/varpro/server-varpro/commits/b41b6b7a6414a34f31454b900a926ad4adcc902c))
* feat: se corrigio la informacion para obtener los vouchers y tickets @Carlos Martinez ([`a454c42`](https://bitbucket.org/varpro/server-varpro/commits/a454c42c74d9d99eb23ca8989b374460b28da251))
* feat: se actualizo la informacion del tickets @Carlos Martinez ([`4e684e7`](https://bitbucket.org/varpro/server-varpro/commits/4e684e77730b60c94731ecdc2e28ca92c1bb0e58))
* feat: se agrego el area al log de los tickets @Carlos Martinez ([`f0c292c`](https://bitbucket.org/varpro/server-varpro/commits/f0c292cec845ea566becda9b11eca022bd21a52c))
* feat: se modifico el punto de ticket status @Carlos Martinez ([`2a18863`](https://bitbucket.org/varpro/server-varpro/commits/2a18863670d8f98e8797986458d656936d6d31ef))
* feat: se corrigio la funcion para recibir mos por volumen @Carlos Martinez ([`277da75`](https://bitbucket.org/varpro/server-varpro/commits/277da757e16f5852113e67e4ae14420327daffa1))
* feat: resolviendo conflictos de git @Carlos Martinez ([`c161732`](https://bitbucket.org/varpro/server-varpro/commits/c161732b90fb81bf27f344af7b8af95daaef1a07))





## [0.1.5](https://bitbucket.org/varpro/server-varpro/compare/0.1.5..0.1.4) - 2021-06-07

### Features

* feat: se creo validacion para las areas de sew, embroidery y sublimation para escribir en mo_scans @Carlos Martinez ([`0e430a2`](https://bitbucket.org/varpro/server-varpro/commits/0e430a2fd8e3e40b6b122b4b7e4f2445d7c3c033))
* feat: se agregaron endpoints para ubicaciones @Carlos Martinez ([`08cd800`](https://bitbucket.org/varpro/server-varpro/commits/08cd80030eb9db2d15d46bf55305def6fb445924))
* feat: cambios en los endpoints para los tickets, ubicaciones y estatus del area @Carlos Martinez ([`99ac1c0`](https://bitbucket.org/varpro/server-varpro/commits/99ac1c026fe49d42f8e95eb1698bd58813e492bc))
* feat: se agregaron los endpoints para los status del area @Carlos Martinez ([`ee00c72`](https://bitbucket.org/varpro/server-varpro/commits/ee00c723cb826de516aafeb0135d95074811e93f))
* feat: se corrigio el escaneo de las dummy station y se agrego la creacion de ticket con voucher plate @Carlos Martinez ([`5a422c5`](https://bitbucket.org/varpro/server-varpro/commits/5a422c587d6e31ef27c7e6ad3a0834f3f6675938))
* feat: se esta trabajando en la creacion de tickets @Carlos Martinez ([`ad9ea70`](https://bitbucket.org/varpro/server-varpro/commits/ad9ea70d6f7ea541ad3f3e757dd1d9d813d118bf))
* feat: se agrego el endpoint para cerrar tickets @Carlos Martinez ([`63c44f6`](https://bitbucket.org/varpro/server-varpro/commits/63c44f6fc01c9fc90d7606120c037e60aa43f47c))
* feat: se actualizaron los endpoints para crear ticekts @Carlos Martinez ([`f7ae457`](https://bitbucket.org/varpro/server-varpro/commits/f7ae457f2b9ea7e0ce65d593e9b4a31689bd5fe9))





## [0.1.4](https://bitbucket.org/varpro/server-varpro/compare/0.1.4..0.1.3) - 2021-05-07

### Features

* feat: se crearon los endpoints para ver las mos y tickets del area, sistema de voucher @Carlos Martinez ([`82359db`](https://bitbucket.org/varpro/server-varpro/commits/82359db6123e835dc1cad761a03b3cf3f65a0781))





## [0.1.3](https://bitbucket.org/varpro/server-varpro/compare/0.1.3..0.1.2) - 2021-04-30

### Features

* feat: add login endpoint @Carlos Martinez ([`b65a619`](https://bitbucket.org/varpro/server-varpro/commits/b65a61906e24106e0abb52b00c27fe58d236e8d6))





## [0.1.2](https://bitbucket.org/varpro/server-varpro/compare/0.1.2..0.1.1) - 2021-04-23






## 0.1.1 - 2021-04-07

### Features

* feat: add endpoints calidad @Carlos David Martinez Medrano ([`61e8668`](https://bitbucket.org/varpro/server-varpro/commits/61e8668fc5f570765679dcbaf828b9d80d5b8f91))
* feat: endpoints para las estaciones de escaneo @Carlos David Martinez Medrano ([`cf68e61`](https://bitbucket.org/varpro/server-varpro/commits/cf68e617ce6cb2d42125e65274c811cb4fd86b7e))
* feat: add endpoint calidad tela @Carlos David Martinez Medrano ([`515311d`](https://bitbucket.org/varpro/server-varpro/commits/515311df8d428f9a00edf73b4fefd8c931dfdc71))
* feat: edit endpoints calidad @Carlos David Martinez Medrano ([`e0dfe56`](https://bitbucket.org/varpro/server-varpro/commits/e0dfe56ff75432e660ab252b057ec8305e8ca3fe))
* feat: create methods, get tasks station @Carlos Martinez ([`346aecf`](https://bitbucket.org/varpro/server-varpro/commits/346aecff3c0572942e14cdde3ef657f1047fce8f))
* feat: added endpoint calidad @Carlos David Martinez Medrano ([`46c551b`](https://bitbucket.org/varpro/server-varpro/commits/46c551be82d04fd821f625685f321fa3276705e9))
* feat: config endpoint calidad @Carlos David Martinez Medrano ([`460d785`](https://bitbucket.org/varpro/server-varpro/commits/460d78521a1d8cd711ee33c25907c691461081fa))
* feat: order by issue asc @Carlos David Martinez Medrano ([`b6fc41e`](https://bitbucket.org/varpro/server-varpro/commits/b6fc41ede89d81f444b3ed19893bbbd87008eea4))
* feat: se agrego el finished_at del ticket al endpoint getCompleteTaskStation @Carlos Martinez ([`c9f37ea`](https://bitbucket.org/varpro/server-varpro/commits/c9f37eaf27b39ae2f9716e490a341476ccaa6040))
* feat: update fabric quality methods @Carlos Martinez ([`8e6f17a`](https://bitbucket.org/varpro/server-varpro/commits/8e6f17a67f54373d4cdad9d83b97c10188be3447))
* feat: Add endpoint to calidad app @Carlos David Martinez Medrano ([`53ea553`](https://bitbucket.org/varpro/server-varpro/commits/53ea553e006e4d8f0097ad533069f9c21f8c96a4))
* feat: merge master @Carlos David Martinez Medrano ([`987397d`](https://bitbucket.org/varpro/server-varpro/commits/987397dd3298a69181af82fab66ec2d6c3ac8831))

### Fixes

* fix: added property, receive_number calidad endpoint @Carlos David Martinez Medrano ([`3f0d1cc`](https://bitbucket.org/varpro/server-varpro/commits/3f0d1cc99e3324ed3e5488d2f1cdfb688e71eb52))




