import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(
    'warehouse_pull_session_types',
    (table): void => {
      table.increments('id').unsigned().primary();
      table.string('name').notNullable();
      table.string('description').notNullable();
      table.boolean('is_active').notNullable().defaultTo(true);
      table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
      table
        .timestamp('updated_at')
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
        .notNullable();
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('warehouse_pull_session_types');
}
