import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(
    'warehouse_pull_session_allocations',
    (table): void => {
      table.increments('id').unsigned().primary();
      table
        .integer('warehouse_pull_session_order_part_id', 10)
        .notNullable()
        .unsigned();
      table
        .foreign(
          'warehouse_pull_session_order_part_id',
          'wpsopi_wpsi_orders_parts_fk'
        )
        .references('id')
        .inTable('warehouse_pull_session_order_parts');
      table.string('container_code').notNullable();
      table.float('quantity', 10, 2).notNullable();
      table.string('reason').nullable();
      table.string('comment').nullable();
      table.dateTime('poly_updated_at').nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table
        .timestamp('updated_at')
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('warehouse_pull_session_allocations');
}
