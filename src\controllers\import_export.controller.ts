import dayjs from 'dayjs';
import type { Request, Response } from 'express';
import * as path from 'path';

import { jsonToExcel } from '@app/helpers/excel';
import { importExportMoDataCsv } from '@app/services/moWithdrawels';
import {
  orderDetailsMoCondensedData,
  orderDetailsWithAllocationMos,
} from '@app/services/shippingExport/orderMo';

export const getMoListPage = async (req: Request, res: Response) => {
  console.log('directory', __dirname);
  const filePath = path.resolve(
    __dirname,
    '../html_pages/importExportMos.html'
  );
  res.sendFile(filePath);
};

export const getImportExportMoData = async (req: Request, res: Response) => {
  const { mos } = req.body;
  console.log('Exporting MO data: ', mos, req.query);
  try {
    if (!mos || !Array.isArray(mos) || mos.length === 0) {
      return res.status(400).json({
        ok: false,
        data: 'MOs invalidos',
      });
    }

    // check if all values are a string
    const useMos: string[] = [];
    for (const mo of mos) {
      if (typeof mo !== 'string') {
        return res.status(400).json({
          ok: false,
          data: 'MOs invalidos',
        });
      }
      useMos.push(mo as string);
    }

    console.log('Exporting MO data: ', mos);
    const csvData = await importExportMoDataCsv(useMos);
    // send data as csv file
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="moData.csv"');
    res.send(csvData);
  } catch (error) {
    console.log('Error exporting MO data: ', error);
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const orderDetailsMosFile = async (req: Request, res: Response) => {
  const { customerNumber: customerNumbers, poNumber: poNumbers } = req.query;
  try {
    const orderDetailsData = await orderDetailsWithAllocationMos({
      customerNumbers: customerNumbers as string,
      poNumbers: poNumbers as string,
    });

    return jsonToExcel(
      `orderDetailsMos-${customerNumbers ? `${customerNumbers}-` : ''}${
        poNumbers ? `${poNumbers}-` : ''
      }${dayjs().format('YYYY_MM_DD hh_mm')}`,
      'orderDetailsMos',
      orderDetailsData,
      {
        response: res,
        headerOrder: [
          'OrderDetailsID',
          'ShipToName',
          'ShipToCountry_',
          'CustomerNumber',
          'OrderNumber',
          'ItemNumber',
          'GarmentSize',
          'PONumber',
          'RetailerPONumber',
          'OrderStatus',
          'CreateDate',
          'OrderDate',
          'RequiredDate',
          'OrderTypeName',
          'OrderType2',
          'OrderType3',
          'StyleNumber',
          'ItemDescription8_',
          'StyleCategoryName',
          'StyleSubcategoryName',
          'ActualCount',
          'UnallocateCount',
          'AllocateCount',
          'ShipCount',
          'detail_status',
          'ManufactureNumber',
          'used_mo',
          'used_mo_status',
          'used_mo_required_date',
          'mo_finish_date',
          'RunningTasks_',
          'RunningTask_',
          'QuantityAllocated',
          'QuantityAvailable',
          'MO_to_SO_Allocated',
          'Inventory_to_SO_Allocated',
          'MfgStatus_',
          'TargetDate_',
          'MfgOriginalRequiredDate_',
          'ga_mo_numbers',
          'mo_required_date',
          'GoodsDetailsID',
        ],
        headers: {
          CreateDate: {
            fieldType: 'date',
          },
          OrderDate: {
            fieldType: 'date',
          },
          RequiredDate: {
            fieldType: 'date',
          },
          mo_required_date: {
            fieldType: 'date',
          },
          mo_finish_date: {
            fieldType: 'date',
          },
          TargetDate_: {
            fieldType: 'date',
          },
          MfgOriginalRequiredDate_: {
            fieldType: 'date',
          },
          used_mo_required_date: {
            fieldType: 'date',
          },
          ActualCount: {
            fieldType: 'number',
          },
          UnallocateCount: {
            fieldType: 'number',
          },
          AllocateCount: {
            fieldType: 'number',
          },
          ShipCount: {
            fieldType: 'number',
          },
          QuantityAllocated: {
            fieldType: 'number',
          },
          QuantityAvailable: {
            fieldType: 'number',
          },
          Inventory_to_SO_Allocated: {
            fieldType: 'number',
          },
          MO_to_SO_Allocated: {
            fieldType: 'number',
          },
        },
      }
    );
  } catch (error) {
    console.log('Error exporting order details mos: ', error);
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const orderDetailsMosShippingFile = async (
  req: Request,
  res: Response
) => {
  const { customerNumber: customerNumbers, poNumber: poNumbers } = req.query;
  try {
    const orderDetailsData = await orderDetailsWithAllocationMos({
      customerNumbers: customerNumbers as string,
      poNumbers: poNumbers as string,
    });

    // filter unallocated and shipped order detail items
    const orderDetailsFiltered = orderDetailsData.filter(
      (orderDetail) =>
        orderDetail.detail_status != 'unallocated' &&
        orderDetail.detail_status != 'shipped'
    );

    return jsonToExcel(
      `orderDetailsMosShipping-${customerNumbers ? `${customerNumbers}-` : ''}${
        poNumbers ? `${poNumbers}-` : ''
      }${dayjs().format('YYYY_MM_DD hh_mm')}`,
      'orderDetailsMos',
      orderDetailsFiltered,
      {
        response: res,
        freezeRow: 1,
        baseHeaderCellStyle: {
          font: {
            size: 10,
            bold: true,
          },
        },
        baseValueCellStyle: {
          font: {
            size: 10,
          },
        },
        headerOrder: [
          'OrderDetailsID',
          'CustomerNumber',
          'ShipToName',
          'ShipToCountry_',
          'OrderNumber',
          'ItemNumber',
          'OrderType2',
          'PONumber',
          'RetailerPONumber',
          'OrderStatus',
          'RequiredDate',
          'StyleCategoryName',
          'StyleSubcategoryName',
          'StyleNumber',
          'ItemDescription8_',
          'GarmentSize',
          'ActualCount',
          // 'UnallocateCount',
          // 'AllocateCount',
          // 'ShipCount',
          'detail_status',
          // 'ManufactureNumber',
          'used_mo',
          'used_mo_required_date',
          'used_mo_status',
          'mo_finish_date',
          // 'RunningTasks_',
          'RunningTask_',
          // 'QuantityAllocated',
          // 'QuantityAvailable',
          // 'MO_to_SO_Allocated',
          // 'Inventory_to_SO_Allocated',
          // 'MfgStatus_',
          // 'TargetDate_',
          // 'MfgOriginalRequiredDate_',
          // 'ga_mo_numbers',
          // 'mo_required_date',
          // 'GoodsDetailsID',
        ],
        headers: {
          RunningTask_: {
            headerName: 'Task',
          },
          used_mo: {
            headerName: 'MO',
          },
          used_mo_status: {
            headerName: 'MO Status',
          },
          CreateDate: {
            fieldType: 'date',
          },
          OrderDate: {
            fieldType: 'date',
          },
          RequiredDate: {
            fieldType: 'date',
            headerName: 'OrderRequiredDate',
          },
          mo_required_date: {
            fieldType: 'date',
          },
          mo_finish_date: {
            fieldType: 'date',
          },
          TargetDate_: {
            fieldType: 'date',
          },
          MfgOriginalRequiredDate_: {
            fieldType: 'date',
          },
          used_mo_required_date: {
            fieldType: 'date',
            headerName: 'MO Required Date',
          },
          ActualCount: {
            fieldType: 'number',
          },
          UnallocateCount: {
            fieldType: 'number',
          },
          AllocateCount: {
            fieldType: 'number',
          },
          ShipCount: {
            fieldType: 'number',
          },
          QuantityAllocated: {
            fieldType: 'number',
          },
          QuantityAvailable: {
            fieldType: 'number',
          },
          Inventory_to_SO_Allocated: {
            fieldType: 'number',
          },
          MO_to_SO_Allocated: {
            fieldType: 'number',
          },
        },
      }
    );
  } catch (error) {
    console.log('Error exporting order details mos: ', error);
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};

export const orderDetailsMosCondensedShippingFile = async (
  req: Request,
  res: Response
) => {
  const { customerNumber: customerNumbers, poNumber: poNumbers } = req.query;
  try {
    const orderDetailsData = await orderDetailsMoCondensedData({
      customerNumbers: customerNumbers as string,
      poNumbers: poNumbers as string,
    });

    // filter unallocated and shipped order detail items
    const orderDetailsFiltered = orderDetailsData.filter(
      (orderDetail) =>
        orderDetail.detailStatus != 'unallocated' &&
        orderDetail.detailStatus != 'shipped'
    );

    return jsonToExcel(
      `orderDetailsMosShipping-${customerNumbers ? `${customerNumbers}-` : ''}${
        poNumbers ? `${poNumbers}-` : ''
      }${dayjs().format('YYYY_MM_DD hh_mm')}`,
      'orderDetailsMos',
      orderDetailsFiltered,
      {
        response: res,
        freezeRow: 2,
        addFilter: true,
        baseHeaderCellStyle: {
          font: {
            size: 10,
            bold: true,
          },
        },
        baseValueCellStyle: {
          font: {
            size: 10,
          },
        },
        summaryHeaders: ['quantity'],
        headerOrder: [
          'customer',
          'poNumber',
          'orderNumber',
          'itemNumber',
          'size',
          'quantity',
          'manufactureNumber',
          'style',
          'itemDescription8',
          'styleCategory',
          'styleSubCategory',
          'orderStatus',
          'detailStatus',
          'moXfactoryDate',
          'moSchedFinishDate',
          'moStatus',
          'retailerPo',
          'orderType2',
          'shipToName',
          'shipToCountry',
          'sizeQtys',
        ],
        headers: {
          moXfactoryDate: {
            fieldType: 'date',
          },
          moSchedFinishDate: {
            fieldType: 'date',
          },
          quantity: {
            fieldType: 'number',
            valueCellStyle: {
              alignment: {
                horizontal: 'center',
              },
            },
          },
        },
      }
    );
  } catch (error) {
    console.log('Error exporting order details mos: ', error);
    return res.status(500).json({
      ok: false,
      error: error.message,
    });
  }
};
