import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('plotter_combos', (table: Knex.TableBuilder) => {
    table.increments('id').unsigned().primary();
    table
      .timestamp('created_at')
      .notNullable()
      .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    table
      .timestamp('updated_at')
      .notNullable()
      .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));

    table.integer('mo_id').notNullable();
    table.string('combo_number').notNullable();
    table.string('part_number').notNullable();
    table.string('name').notNullable();
    table.text('description').notNullable();
    table.integer('created_by_employee_id').notNullable();
    table.integer('plotter_print_id').unsigned().nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('plotter_combos');
}
