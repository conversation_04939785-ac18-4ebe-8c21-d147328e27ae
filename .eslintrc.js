module.exports = {
  root: true,
  parser: '@typescript-eslint/parser', // Specifies the ESLint parser
  extends: [
    'plugin:import/recommended',
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended', // Uses the recommended rules from the @typescript-eslint/eslint-plugin
    'plugin:import/typescript',
    'prettier',
  ],
  plugins: ['import', 'no-relative-import-paths'],
  parserOptions: {
    ecmaVersion: 6,
    sourceType: 'module',
    // project: './tsconfig.json',
    //project: ['tsconfig.json'],
  },
  rules: {
    'no-relative-import-paths/no-relative-import-paths': [
      'warn',
      { allowSameFolder: true, rootDir: 'src', prefix: '@app' },
    ],
    // '@typescript-eslint/camelcase': 0,
    // '@typescript-eslint/no-use-before-define': 0,
    'arrow-parens': 'off',
    //'no-console': 1,
    '@typescript-eslint/explicit-function-return-type': 'off',
    //'no-unused-vars': 'off',
    //'@typescript-eslint/no-unused-vars': ['error'],
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/array-type': ['error', { default: 'array' }],
    '@typescript-eslint/consistent-type-imports': [
      'error',
      {
        prefer: 'type-imports',
      },
    ],
    'no-use-before-define': 'warn',
    'prefer-spread': 'warn',

    // rules temporary for conversion
    // typedef documentation https://github.com/typescript-eslint/typescript-eslint/blob/main/packages/eslint-plugin/docs/rules/typedef.md
    '@typescript-eslint/typedef': [
      'warn',
      {
        // parameter: true,
        // arrowParameter: true,
        // variableDeclaration: true,
        // memberVariableDeclaration: true,
        // objectDestructuring: true,
      },
    ],
    'no-undef': 'warn',
    '@typescript-eslint/no-unused-vars': 'warn',
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/no-unsafe-call': 'warn',
    'no-prototype-builtins': 'warn',
    '@typescript-eslint/ban-ts-comment': 'warn',
    'import/no-unresolved': 'warn',
    'import/no-default-export': 'warn',
    //'import/no-unused-modules': [1, { unusedExports: true }],
    "comma-dangle": [
      "error",
      {
        "arrays": "always-multiline",
        "exports": "always-multiline",
        "functions": "never",
        "imports": "always-multiline",
        "objects": "always-multiline",
      },
    ],
  },
  env: {
    node: true,
    commonjs: true,
    es6: true,
    jest: true,
  },
  overrides: [
    {
      files: ['*.ts', '*.tsx'], // Your TypeScript files extension

      // As mentioned in the comments, you should extend TypeScript plugins here,
      // instead of extending them outside the `overrides`.
      // If you don't want to extend any rules, you don't need an `extends` attribute.

      rules: {
        // '@typescript-eslint/no-unsafe-call': 'warn', // forbids calling an expression typed as any
        // '@typescript-eslint/no-unsafe-member-access': 'warn', // forbids using any as a member name
        '@typescript-eslint/no-unsafe-argument': 'warn', // forbids passing any as a function parameter
        // '@typescript-eslint/no-unsafe-assignment': 'warn', // forbids using any in an assignment
        //'@typescript-eslint/no-floating-promises': 'error', // make sure all promises have error handling
        //'@typescript-eslint/no-misused-promises': 'error', // make sure all promises have error handling
      },

      parserOptions: {
        project: ['./tsconfig.json'], // Specify it only for TypeScript files
      },
    },
  ],
  reportUnusedDisableDirectives: true,
  ignorePatterns: ['node_modules', 'build', 'dist', 'public'],
  settings: {
    'import/parsers': {
      '@typescript-eslint/parser': ['.ts', '.tsx'],
    },
    'import/resolver': {
      typescript: {},
      node: {
        paths: ['src'],
        extensions: ['.js', '.jsx', '.ts', '.tsx'],
      },
    },
  },
};
