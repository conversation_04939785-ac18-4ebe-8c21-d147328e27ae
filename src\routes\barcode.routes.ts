import { request as bwipRequest } from 'bwip-js';
import type { Request, Response } from 'express';
import { Router } from 'express';
import type { QRCodeErrorCorrectionLevel, QRCodeMaskPattern } from 'qrcode';
import { toDataURL } from 'qrcode';

const barcodeRouter = Router();

// inputes
// text:123456789 (Required)
// bcid:datamatrix (Required)  https://github.com/metafloor/bwip-js/wiki/BWIPP-Barcode-Types
// scale:2 (default)
// textcolor:000000 (default)
// includetext:false (default)
// backgroundcolor:FFF (default is null/transparent)
// barcolor:000000 (default)
// padding:0 (default)
// height:10 (default)
// width:10 (default)

barcodeRouter.route('/').get(async (req: Request, res: Response) => {
  if (
    !req.query.text ||
    typeof req.query.text != 'string' ||
    req.query.text.length < 1
  ) {
    res.status(400).send('No text to encode');
    return;
  }
  if (
    !req.query.bcid ||
    typeof req.query.bcid != 'string' ||
    req.query.bcid.length < 2
  ) {
    res.status(400).send('No barcode symbology specified');
    return;
  }
  try {
    await bwipRequest(req, res); // Executes asynchronously
  } catch (err) {
    console.error(err);
    res.status(500).send('Internal server error');
  }
});

barcodeRouter.route('/qrcode').get(async (req: Request, res: Response) => {
  // res.send('hello world qrcode');
  const data = req.query.data as string;
  const width = req.query.width ? Number(req.query.width) : undefined;
  const margin = req.query.margin ? Number(req.query.margin) : undefined;
  const scale = req.query.scale ? Number(req.query.scale) : undefined;
  const color_dark = req.query.color_dark as string;
  const color_light = req.query.color_light as string;

  const errorCorrectionLevel =
    (req.query.errorCorrectionLevel as QRCodeErrorCorrectionLevel) ?? undefined;
  const maskPattern =
    !!req.query.maskPattern && !isNaN(Number(req.query.maskPattern))
      ? (Number(req.query.maskPattern) as QRCodeMaskPattern)
      : undefined;

  if (!data) {
    return res.status(500).send('No data');
  }

  if (width) {
    if (isNaN(width)) {
      return res.status(500).send('Width is not number');
    }
  }

  if (margin) {
    if (isNaN(Number(margin))) {
      return res.status(500).send('Margin is not number');
    }
  }

  if (scale) {
    if (isNaN(Number(scale))) {
      return res.status(500).send('Scale is not number');
    }
  }

  if (errorCorrectionLevel) {
    if (!['L', 'M', 'Q', 'H'].includes(errorCorrectionLevel)) {
      return res.status(500).send('Error correction level is not valid');
    }
  }

  if (maskPattern) {
    if (isNaN(Number(maskPattern))) {
      return res.status(500).send('Mask pattern is not number');
    }
    if (Number(maskPattern) < 0 || Number(maskPattern) > 7) {
      return res.status(500).send('Mask pattern is not valid');
    }
  }

  try {
    const qrcode = await toDataURL(data, {
      width,
      errorCorrectionLevel,
      maskPattern,
      margin,
      scale,
      color: {
        dark: color_dark,
        light: color_light,
      },
    });
    res.send(qrcode);
  } catch (err) {
    console.error(err);
    res.status(500).send('Internal server error');
  }
});

export { barcodeRouter };
