import { default as dayjs } from 'dayjs';
import { raw } from 'objection';

import { knex } from '@app/db';
import type {
  EligibleMO,
  MOSewingLinePlan,
  RecommendedLine,
  SewingLineEfficiency,
  SewingLineStyleHistory,
  SewingPlanningFilters,
  SewingPlanningResult,
} from '@app/interface/sewingPlanning.interfaces';
import {
  MoNumber,
  MoScans,
  WorkAreaGroupShifts,
  WorkAreaGroups,
} from '@app/models/tickets.schema';

/**
 * Get MOs that are not complete, void, or cancelled and have been scanned at least once
 */
export async function getEligibleMOs(
  filters: SewingPlanningFilters = {}
): Promise<EligibleMO[]> {
  let query = MoNumber.query()
    .leftJoin('styles', 'mo_numbers.style', '=', 'styles.style_number')
    .join('mo_scans', 'mo_numbers.mo_id', '=', 'mo_scans.mo_id')
    .whereNotIn('mo_numbers.mo_status', ['Complete', 'Void', 'Cancelled'])
    .groupBy(
      'mo_numbers.mo_id',
      'mo_numbers.num',
      'mo_numbers.style',
      'styles.style_id',
      'mo_numbers.customer',
      'mo_numbers.quantity',
      'mo_numbers.required_date',
      'mo_numbers.mo_status',
      'mo_numbers.style_category',
      'mo_numbers.product_category',
      'mo_numbers.company_code'
    )
    .select(
      'mo_numbers.mo_id',
      'mo_numbers.num as mo_number',
      'mo_numbers.style',
      'styles.style_id',
      'mo_numbers.customer',
      'mo_numbers.quantity',
      'mo_numbers.required_date',
      'mo_numbers.mo_status',
      'mo_numbers.style_category',
      'mo_numbers.product_category',
      'mo_numbers.company_code',
      raw('COUNT(mo_scans.id) as scan_count'),
      raw('MAX(mo_scans.created_at) as last_scan_date')
    )
    .orderBy('mo_numbers.required_date', 'asc');

  // Apply filters
  if (filters.company_codes?.length) {
    query = query.whereIn('mo_numbers.company_code', filters.company_codes);
  }

  if (filters.style_categories?.length) {
    query = query.whereIn(
      'mo_numbers.style_category',
      filters.style_categories
    );
  }

  if (filters.customers?.length) {
    query = query.whereIn('mo_numbers.customer', filters.customers);
  }

  if (filters.required_date_from) {
    query = query.where(
      'mo_numbers.required_date',
      '>=',
      filters.required_date_from
    );
  }

  if (filters.required_date_to) {
    query = query.where(
      'mo_numbers.required_date',
      '<=',
      filters.required_date_to
    );
  }

  if (filters.min_quantity) {
    query = query.where('mo_numbers.quantity', '>=', filters.min_quantity);
  }

  if (filters.max_quantity) {
    query = query.where('mo_numbers.quantity', '<=', filters.max_quantity);
  }

  return await query.castTo<EligibleMO[]>();
}

/**
 * Get sewing lines that have worked on specific styles in the past month
 */
export async function getSewingLineStyleHistory(
  styles: string[]
): Promise<SewingLineStyleHistory[]> {
  const oneMonthAgo = dayjs().subtract(1, 'month').format('YYYY-MM-DD');

  return await MoScans.query()
    .join('mo_numbers', 'mo_scans.mo_id', '=', 'mo_numbers.mo_id')
    .join(
      'work_area_groups',
      'mo_scans.work_area_group_id',
      '=',
      'work_area_groups.id'
    )
    .leftJoin('styles', 'mo_numbers.style', '=', 'styles.style_number')
    .whereIn('mo_numbers.style', styles)
    .where('mo_scans.created_at', '>=', oneMonthAgo)
    .whereNotNull('mo_scans.work_area_group_id')
    .where('work_area_groups.work_status_id', 50) // Active groups only
    .groupBy(
      'work_area_groups.id',
      'work_area_groups.name',
      'mo_numbers.style',
      'styles.style_id'
    )
    .select(
      'work_area_groups.id as work_area_group_id',
      'work_area_groups.name as work_area_group_name',
      'mo_numbers.style',
      'styles.style_id',
      raw('COUNT(mo_scans.id) as scan_count'),
      raw('MAX(mo_scans.created_at) as last_scan_date'),
      raw('SUM(mo_scans.quantity) as total_quantity_scanned'),
      raw(
        'AVG(CASE WHEN mo_scans.sew_sam_value IS NOT NULL THEN mo_scans.sew_sam_value ELSE 0 END) as avg_efficiency'
      )
    )
    .orderBy('scan_count', 'desc')
    .castTo<SewingLineStyleHistory[]>();
}

/**
 * Get latest efficiency data for sewing lines
 */
export async function getSewingLineEfficiency(
  filters: SewingPlanningFilters = {}
): Promise<SewingLineEfficiency[]> {
  const thirtyDaysAgo = dayjs().subtract(30, 'days').format('YYYY-MM-DD');

  const subquery = knex
    .select(
      'work_area_group_id',
      'efficiency',
      'start_datetime_sv',
      'operator_count',
      'usable_minutes',
      'est_production_minutes',
      knex.raw(
        'ROW_NUMBER() OVER (PARTITION BY work_area_group_id ORDER BY start_datetime_sv DESC) as rn'
      )
    )
    .from('work_area_group_shifts')
    .where('start_datetime_sv', '>=', thirtyDaysAgo)
    .whereNotNull('efficiency')
    .whereNull('removed_at');

  let query = WorkAreaGroups.query()
    .join(subquery.as('wags'), 'work_area_groups.id', 'wags.work_area_group_id')
    .where('wags.rn', '=', 1)
    .where('work_area_groups.work_status_id', '=', 50)
    .select(
      'work_area_groups.id as work_area_group_id',
      'work_area_groups.name as work_area_group_name',
      'wags.efficiency as latest_efficiency',
      'wags.start_datetime_sv as shift_date',
      'wags.operator_count',
      'wags.usable_minutes',
      'wags.est_production_minutes',
      'work_area_groups.section',
      'work_area_groups.planning_name'
    )
    .orderBy('wags.efficiency', 'desc');

  // Apply section filter
  if (filters.sections?.length) {
    query = query.whereIn('work_area_groups.section', filters.sections);
  }

  return await query.castTo<SewingLineEfficiency[]>();
}

/**
 * Calculate style similarity score between two styles
 */
function calculateStyleSimilarity(style1: string, style2: string): number {
  if (style1 === style2) return 1.0;

  // Extract style components for comparison
  const extractStyleComponents = (style: string) => {
    const parts = style.split(/[-_]/);
    return {
      prefix: parts[0] || '',
      category: parts[1] || '',
      variant: parts[2] || '',
      full: style.toLowerCase(),
    };
  };

  const comp1 = extractStyleComponents(style1);
  const comp2 = extractStyleComponents(style2);

  let similarity = 0;

  // Same prefix (e.g., "ABC" in "ABC-123-XL") - 40% weight
  if (comp1.prefix === comp2.prefix && comp1.prefix !== '') {
    similarity += 0.4;
  }

  // Same category (e.g., "123" in "ABC-123-XL") - 30% weight
  if (comp1.category === comp2.category && comp1.category !== '') {
    similarity += 0.3;
  }

  // Similar variant (e.g., "XL" vs "L") - 20% weight
  if (comp1.variant === comp2.variant && comp1.variant !== '') {
    similarity += 0.2;
  } else if (
    comp1.variant !== '' &&
    comp2.variant !== '' &&
    (comp1.variant.includes(comp2.variant) ||
      comp2.variant.includes(comp1.variant))
  ) {
    similarity += 0.1;
  }

  // String similarity for overall match - 10% weight
  const maxLength = Math.max(comp1.full.length, comp2.full.length);
  if (maxLength > 0) {
    let commonChars = 0;
    const minLength = Math.min(comp1.full.length, comp2.full.length);
    for (let i = 0; i < minLength; i++) {
      if (comp1.full[i] === comp2.full[i]) {
        commonChars++;
      } else {
        break; // Stop at first difference for prefix matching
      }
    }
    similarity += (commonChars / maxLength) * 0.1;
  }

  return Math.min(similarity, 1.0);
}

/**
 * Calculate recommendation score for a line-MO combination
 */
function calculateRecommendationScore(
  mo: EligibleMO,
  line: SewingLineEfficiency,
  styleHistory?: SewingLineStyleHistory,
  allMOs?: EligibleMO[],
  allStyleHistory?: SewingLineStyleHistory[]
): number {
  let score = 0;

  // Style experience score (30% weight - reduced from 40%)
  const styleExperienceScore = styleHistory
    ? Math.min(styleHistory.scan_count / 10, 1) * 30
    : 0;

  // Efficiency score (30% weight - reduced from 35%)
  const efficiencyScore = line.latest_efficiency
    ? Math.min(line.latest_efficiency, 1) * 30
    : 0;

  // Capacity score (20% weight - reduced from 25%)
  const capacityScore =
    line.operator_count && line.usable_minutes
      ? Math.min((line.operator_count * line.usable_minutes) / 10000, 1) * 20
      : 0;

  // Style similarity bonus (20% weight - NEW)
  let styleSimilarityScore = 0;
  if (allMOs && allStyleHistory) {
    // Find other MOs that could be scheduled on this line
    const lineStyleHistory = allStyleHistory.filter(
      (h) => h.work_area_group_id === line.work_area_group_id
    );
    const experiencedStyles = lineStyleHistory.map((h) => h.style);

    // Calculate similarity with styles this line has experience with
    let maxSimilarity = 0;
    for (const experiencedStyle of experiencedStyles) {
      const similarity = calculateStyleSimilarity(mo.style, experiencedStyle);
      maxSimilarity = Math.max(maxSimilarity, similarity);
    }

    // Also check similarity with other pending MOs for potential batching
    const otherMOs = allMOs.filter(
      (otherMO) =>
        otherMO.mo_id !== mo.mo_id &&
        Math.abs(
          new Date(otherMO.required_date).getTime() -
            new Date(mo.required_date).getTime()
        ) <=
          7 * 24 * 60 * 60 * 1000 // Within 7 days
    );

    let batchingSimilarity = 0;
    for (const otherMO of otherMOs) {
      const similarity = calculateStyleSimilarity(mo.style, otherMO.style);
      batchingSimilarity = Math.max(batchingSimilarity, similarity);
    }

    // Take the higher of experience similarity or batching similarity
    styleSimilarityScore = Math.max(maxSimilarity, batchingSimilarity) * 20;
  }

  score =
    styleExperienceScore +
    efficiencyScore +
    capacityScore +
    styleSimilarityScore;

  return Math.round(score * 100) / 100;
}

/**
 * Generate sewing planning recommendations
 */
export async function generateSewingPlan(
  filters: SewingPlanningFilters = {}
): Promise<SewingPlanningResult> {
  // Get eligible MOs
  const eligibleMOs = await getEligibleMOs(filters);

  if (eligibleMOs.length === 0) {
    return {
      eligible_mos: [],
      sewing_lines: [],
      mo_plans: [],
      summary: {
        total_mos: 0,
        total_lines: 0,
        avg_efficiency: 0,
        total_quantity: 0,
      },
    };
  }

  // Get unique styles from eligible MOs
  const styles = [...new Set(eligibleMOs.map((mo) => mo.style))];

  // Get sewing line style history and efficiency data
  const [styleHistory, sewingLines] = await Promise.all([
    getSewingLineStyleHistory(styles),
    getSewingLineEfficiency(filters),
  ]);

  // Create style history lookup
  const styleHistoryMap = new Map<string, SewingLineStyleHistory[]>();
  styleHistory.forEach((history) => {
    const key = `${history.work_area_group_id}-${history.style}`;
    if (!styleHistoryMap.has(key)) {
      styleHistoryMap.set(key, []);
    }
    const existingHistory = styleHistoryMap.get(key);
    if (existingHistory) {
      existingHistory.push(history);
    }
  });

  // Generate plans for each MO
  const moPlans: MOSewingLinePlan[] = eligibleMOs
    .map((mo) => {
      const recommendedLines: RecommendedLine[] = sewingLines
        .map((line) => {
          const historyKey = `${line.work_area_group_id}-${mo.style}`;
          const lineStyleHistory = styleHistoryMap.get(historyKey)?.[0];

          const recommendationScore = calculateRecommendationScore(
            mo,
            line,
            lineStyleHistory,
            eligibleMOs,
            styleHistory
          );

          // Calculate individual scores for display
          const styleExperienceScore = lineStyleHistory
            ? Math.min(lineStyleHistory.scan_count / 10, 1) * 30
            : 0;
          const efficiencyScore = line.latest_efficiency
            ? Math.min(line.latest_efficiency, 1) * 30
            : 0;
          const capacityScore =
            line.operator_count && line.usable_minutes
              ? Math.min(
                  (line.operator_count * line.usable_minutes) / 10000,
                  1
                ) * 20
              : 0;
          const styleSimilarityScore =
            recommendationScore -
            styleExperienceScore -
            efficiencyScore -
            capacityScore;

          return {
            work_area_group_id: line.work_area_group_id,
            work_area_group_name: line.work_area_group_name,
            recommendation_score: recommendationScore,
            style_experience_score: styleExperienceScore,
            efficiency_score: efficiencyScore,
            capacity_score: capacityScore,
            style_similarity_score: Math.max(0, styleSimilarityScore),
            last_worked_style: lineStyleHistory?.last_scan_date || '',
            latest_efficiency: line.latest_efficiency || 0,
            section: line.section || '',
            planning_name: line.planning_name || '',
          };
        })
        .sort((a, b) => b.recommendation_score - a.recommendation_score);

      // Calculate priority score for MO (based on required date and quantity)
      const daysUntilRequired = dayjs(mo.required_date).diff(dayjs(), 'days');
      const urgencyScore = Math.max(0, 100 - daysUntilRequired);
      const quantityScore = Math.min(mo.quantity / 1000, 1) * 50;
      const priorityScore = (urgencyScore + quantityScore) / 2;

      return {
        mo_id: mo.mo_id,
        mo_number: mo.mo_number,
        style: mo.style,
        customer: mo.customer,
        quantity: mo.quantity,
        required_date: mo.required_date,
        recommended_lines: recommendedLines.slice(0, 5), // Top 5 recommendations
        priority_score: Math.round(priorityScore * 100) / 100,
      };
    })
    .sort((a, b) => b.priority_score - a.priority_score);

  // Calculate summary
  const totalQuantity = eligibleMOs.reduce((sum, mo) => sum + mo.quantity, 0);
  const avgEfficiency =
    sewingLines.length > 0
      ? sewingLines.reduce(
          (sum, line) => sum + (line.latest_efficiency || 0),
          0
        ) / sewingLines.length
      : 0;

  return {
    eligible_mos: eligibleMOs,
    sewing_lines: sewingLines,
    mo_plans: moPlans,
    summary: {
      total_mos: eligibleMOs.length,
      total_lines: sewingLines.length,
      avg_efficiency: Math.round(avgEfficiency * 10000) / 100, // Convert to percentage
      total_quantity: totalQuantity,
    },
  };
}
