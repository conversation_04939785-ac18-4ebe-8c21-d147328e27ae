import { Router } from 'express';

import {
  changePasswordApp,
  changePasswordLaserOperatorApp,
  loginApp,
  loginLaserOperatorApp,
  loginRepairParts,
  loginWarehouseDownloaderApp,
  loginWarehousePullApp,
  logoutWarehousePullApp,
  registerApp,
  registerAppRepairParts,
  registerLaserOperatorApp,
  renewalToken,
} from '@app/controllers/auth.controller';
import {
  RegisterSchema,
  RepairPartsLoginSchema,
  WarehouseDownloaderLoginSchema,
  WarehousePullLoginSchema,
  sesssionIDSchema,
} from '@app/interface/zod_schemas';
import { validateJWT } from '@app/middlewares';
import { validateData } from '@app/middlewares/validateData.middleware';

const auth = Router();

auth.get('/renew', validateJWT, renewalToken);
auth.post('/rhinestone/register', registerApp);
auth.post('/rhinestone/login', loginApp);
auth.patch('/rhinestone/password', validateJWT, changePasswordApp);
auth.post('/register', registerApp);
auth.post('/login', loginApp);
auth.patch('/change_password', validateJWT, changePasswordApp);
auth.post('/laser/operators/login', loginLaserOperatorApp);
auth.post('/laser/operators/register', registerLaserOperatorApp);
auth.patch(
  '/laser/operators/change_password',
  validateJWT,
  changePasswordLaserOperatorApp
);
auth.post('/warehouse/register', validateData(RegisterSchema), registerApp);
auth.post(
  '/warehouse-pull/login',
  validateData(WarehousePullLoginSchema),
  loginWarehousePullApp
);
auth.post(
  '/warehouse-pull/logout',
  validateData(sesssionIDSchema),
  logoutWarehousePullApp
);
auth.post(
  '/warehouse-downloader/login',
  validateData(WarehouseDownloaderLoginSchema),
  loginWarehouseDownloaderApp
);
auth.post(
  '/repair-parts-auth/register',
  validateData(RegisterSchema),
  registerAppRepairParts
);
auth.post(
  '/repair-parts-auth/login',
  validateData(RepairPartsLoginSchema),
  loginRepairParts
);

export { auth };
