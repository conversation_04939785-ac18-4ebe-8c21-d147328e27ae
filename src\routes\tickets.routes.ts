import { Router } from 'express';

import {
  getEmployee<PERSON>rea,
  getLogOfEmployee,
  loginEmployee,
} from '@app/controllers/employee.controller';
import {
  convertGroupToLot,
  convertLotToVoucherGroup,
  createNewLot,
  deleteLots,
  deleteTicketsToLot,
  getAllLots,
  getLogOfLot,
  getLots,
  getTicketsOfLot,
} from '@app/controllers/lots.controller';
import {
  addCommentMoListArea,
  addListMo,
  addMoListArea,
  addMosList,
  deleteVoucherGroup,
  getAllListArea,
  getAllTheMosActiveOfTheBatch,
  getAllTheMosOfTheBatch,
  getAllTheTicketsInABatch,
  getInfoGeneralOfTheBatch,
  getListArea,
  getLogOfMoList,
  getMoInfoBatch,
  getMosListFilter,
  getMosListbyVolumen,
  getMosbyMoList,
  getTicketInfoMoList,
  moListDelete,
  moListDeleteItem,
  searchMoList,
  searchVouchersByMo,
} from '@app/controllers/mo_list.controller';
import {
  getAllTheVouchersFromAListOfMos,
  getCommentsOfMo,
  getInfoMoArea,
  getInfoOfMo,
  getMo,
  getMoStatus,
  getMos,
  getMosbyPPMO,
  getMosbyVolumen,
  getTicketByMoToChangeLocation,
  getTicketByMoToChangeStatus,
  searchMo,
} from '@app/controllers/monumbers.controller';
import {
  getPreBarcodes,
  returnBarcodesPending,
} from '@app/controllers/pre_barcode.controller';
import {
  getInfoLocation,
  reportMosActive,
  reportMosActiveAllAreas,
  reportMosActiveByLocation,
  reportTicketsActive,
} from '@app/controllers/reports.controller';
import {
  createScan,
  createVoucherMO,
} from '@app/controllers/scanning.controller';
// Controllers
import {
  addComment,
  closeTickets,
  completeToScanTickets,
  createNewTicketArea,
  createNewTickets,
  getAllTicketsFromAMo,
  getAllWorkVoucherTypes,
  getBuildings,
  getCommentsOfTicket,
  getInfoOfTicket,
  getLogOfTicket,
  getStatusAreaComplete,
  getTicketCompleteArea,
  getTicketToReceive,
  getTicketToReceiveNotComplete,
  receivingAndMergeVouchers, // CreateMOScan,
  // getCompleteTaskStation,
  // getPendingTaskStation,
  updateInfoTicket,
  updateLocationBulk,
  updateStatusBulk,
  updateStatusTicketLog,
} from '@app/controllers/tickets.controller';
import {
  cloneVouchers,
  cloneVouchersGroup,
  createVoucherByMo,
  getAllVouchers,
  getAllVouchersByMo,
  getVoucherInfo,
  getVouchers,
  getVouchersInfoFilter,
  getVouchersMo,
  get_vouchers_of_mo,
  mergeVouchers,
} from '@app/controllers/voucher.controller';
import {
  getWorkAreas,
  getWorkLines,
} from '@app/controllers/work_area.controller';
import {
  addAreaLocation,
  addNewZone,
  getAllLocationsArea,
  getAllTicketsLocationsArea,
  getAllTicketsStatusArea,
  getAllZonesArea,
  updateLocationArea,
} from '@app/controllers/work_area_location.controller';
import {
  addStatusArea,
  getGlobalStatus,
  getStatusArea,
  updateStatusArea,
} from '@app/controllers/work_area_status.controller';
import { getAreaGroups } from '@app/controllers/work_groups.controller';
import {
  addNewVoucherGroups,
  addNewVoucherGroupsToTicket,
  addVouchersToGroup,
  changeStatusToAllTicketsInGroupVouchers,
  createNewVoucherGroup,
  deleteVouchersToGroup,
  getInfoVoucherGroup,
  getLogOfVoucherGroups,
  getNameVoucherGroup,
  getVoucherGroups,
  getVouchersOfGroup,
  getVouchersOfGroupById,
  receiveVouchersGroup,
  scanMosOfGroupVouchers,
  updateVoucherGroup,
} from '@app/controllers/work_voucher_groups.controller';
// Middlewares
import {
  checkBadgeBarcode,
  checkJobBarcode,
  getNameLocationArea,
  getNameZone,
  getNewNameLocationArea,
  getWorkTicketStatuses,
} from '@app/middlewares/tickets.middlewares';

const ticketsRouter = Router();

/* router
  .route("/createdTest")
  .post(
    checkTypeAction,
    getMoId,
    getWorkArea,
    getOperatorIdAndTask,
    getWorkTicketStatuses,
    CreateWorkVoucher
  );*/

ticketsRouter
  .route('/created')
  .post(checkJobBarcode, checkBadgeBarcode, getWorkTicketStatuses, createScan);

// checkTypeAction,
// getMoId,
// getOperatorIdAndTask,
// getWorkArea,
// getWorkTicketStatuses,

/* router.route("/station/pending").post(getInlineProduction);
router.route("/station/complete").post(getCompleteProduction);
router.route("/station/operator").post(getInfoOperator);*/
ticketsRouter.route('/voucher/operator').post(getEmployeeArea);
ticketsRouter.route('/login').post(loginEmployee);
ticketsRouter.route('/voucher/mo').get(getMos);
ticketsRouter.route('/voucher/moBarcode').post(getMosbyPPMO);
ticketsRouter.route('/scan/create-voucher').post(createVoucherMO);
ticketsRouter.route('/voucher/moVolumen').post(getMosbyVolumen);
ticketsRouter.route('/voucher/moList').post(getMosbyMoList);
ticketsRouter
  .route('/voucher/create')
  .post(getWorkTicketStatuses, createVoucherByMo);
ticketsRouter.route('/voucher/close').post(closeTickets);
ticketsRouter.route('/voucher/updatedInfo').post(updateInfoTicket);
ticketsRouter.route('/voucher/types').get(getAllWorkVoucherTypes);
ticketsRouter.route('/voucher/all').post(getAllVouchers);
ticketsRouter.route('/voucher').post(getVouchers);
ticketsRouter.route('/status/global').get(getGlobalStatus);
ticketsRouter.route('/status/area/add').post(addStatusArea);
ticketsRouter.route('/voucher/status/area/update').post(updateStatusArea);
ticketsRouter
  .route('/voucher/location/area/update')
  .post(getNameLocationArea, updateLocationArea);
ticketsRouter.route('/status/area').get(getStatusArea);
ticketsRouter.route('/areas/worklines').post(getWorkLines);
ticketsRouter.route('/location/zone/add').post(getNameZone, addNewZone);
ticketsRouter
  .route('/location/area/add')
  .post(getNewNameLocationArea, addAreaLocation);
ticketsRouter.route('/inventory/zones').post(getAllZonesArea);
ticketsRouter.route('/location/area').get(getAllLocationsArea);
ticketsRouter.route('/location/tickets/area').post(getAllTicketsLocationsArea);
ticketsRouter.route('/status/tickets/area').post(getAllTicketsStatusArea);
ticketsRouter.route('/areas').get(getWorkAreas);
ticketsRouter.route('/build').get(getBuildings);
ticketsRouter.route('/ticket/log').post(getLogOfTicket);
ticketsRouter.route('/employee/log').post(getLogOfEmployee);
ticketsRouter.route('/ticket/info').post(getInfoOfTicket);
ticketsRouter.route('/mo/info').post(getInfoOfMo);
ticketsRouter.route('/ticket/comments').post(getCommentsOfTicket);
ticketsRouter.route('/mo/comments').post(getCommentsOfMo);
ticketsRouter.route('/mo/comment/add').post(addComment);
ticketsRouter.route('/voucher/changeStatus').post(updateStatusBulk);
ticketsRouter.route('/voucher/changeLocation').post(updateLocationBulk);
ticketsRouter.route('/voucher/area/info').post(getInfoMoArea);
ticketsRouter.route('/voucher/merge').post(getVoucherInfo);
ticketsRouter.route('/voucher/mergeVouchers').post(mergeVouchers);
ticketsRouter.route('/batches/addComment').post(addCommentMoListArea);
ticketsRouter.route('/batches/search/mo').post(searchMoList);
ticketsRouter.route('/batches/add/area').post(addMoListArea);
ticketsRouter.route('/batches/searchMo').post(getMoInfoBatch);
ticketsRouter.route('/batches/moVolumen').post(getMosListbyVolumen);
ticketsRouter.route('/batches/finish').post(addListMo);
ticketsRouter.route('/listArea/infoTickets').post(getTicketInfoMoList);
ticketsRouter.route('/listArea').post(getListArea);
ticketsRouter.route('/allListArea').post(getAllListArea);
ticketsRouter.route('/moList/addMos').post(addMosList);
ticketsRouter.route('/moList/deleteMo').post(moListDeleteItem);
ticketsRouter.route('/moList/log').post(getLogOfMoList);
ticketsRouter.route('/mofilter/listmos').post(getMosListFilter);
ticketsRouter.route('/moListClosed').post(moListDelete);
ticketsRouter.route('/mo/getTicketInfo').post(getVouchersMo);
ticketsRouter.route('/getVoucherInfo/moList').post(getVouchersInfoFilter);
ticketsRouter.route('/voucher/updatedInfo/log').post(updateStatusTicketLog);
ticketsRouter.route('/ticket-complete').post(getTicketCompleteArea);
ticketsRouter.route('/ticket-to-receive').post(getTicketToReceive);
ticketsRouter
  .route('/receive-not-complete')
  .post(getTicketToReceiveNotComplete);
ticketsRouter.route('/group/area').post(getAreaGroups);
ticketsRouter
  .route('/voucher/createTicket')
  .post(getWorkTicketStatuses, createNewTicketArea);
ticketsRouter.route('/createTickets/complete').post(createNewTickets);
ticketsRouter.route('/createTickets/merge').post(receivingAndMergeVouchers);
ticketsRouter.route('/status-complete/area').post(getStatusAreaComplete);
ticketsRouter.route('/complete-to-scan').post(completeToScanTickets);
ticketsRouter.route('/voucher-groups').post(getVoucherGroups);
ticketsRouter.route('/voucher-groups/add').post(createNewVoucherGroup);
ticketsRouter.route('/voucher-groups/update').post(updateVoucherGroup);
ticketsRouter.route('/voucher-groups/added-vouchers').post(addVouchersToGroup);
ticketsRouter
  .route('/voucher-groups/deleted-vouchers')
  .post(deleteVouchersToGroup);
ticketsRouter.route('/voucher-groups/log').post(getLogOfVoucherGroups);
ticketsRouter.route('/voucher-groups/vouchers').post(getVouchersOfGroup);
ticketsRouter.route('/voucher-groups/byId').post(getVouchersOfGroupById);
ticketsRouter.route('/voucher-groups/name').post(getNameVoucherGroup);
ticketsRouter.route('/voucher-groups/search/mo').post(searchVouchersByMo);
ticketsRouter.route('/voucher-groups/delete/:id').post(deleteVoucherGroup);
ticketsRouter
  .route('/voucher-groups/add-to-new-group')
  .post(addNewVoucherGroupsToTicket);
ticketsRouter
  .route('/voucher-groups/add-new-vouchers-group')
  .post(addNewVoucherGroups);

ticketsRouter.route('/voucher-groups/receive').post(receiveVouchersGroup);

ticketsRouter.route('/batch/getMos').post(getAllTheMosOfTheBatch);
ticketsRouter.route('/batch/getInfo').post(getInfoGeneralOfTheBatch);
ticketsRouter.route('/batch/getTicketsForMo').post(getAllTheTicketsInABatch);
ticketsRouter.route('/batch/getMosActive').post(getAllTheMosActiveOfTheBatch);
ticketsRouter.route('/group-voucher/scannig').post(scanMosOfGroupVouchers);
ticketsRouter
  .route('/group-voucher/status')
  .post(changeStatusToAllTicketsInGroupVouchers);
ticketsRouter.route('/wip').post(getAllTheVouchersFromAListOfMos);
ticketsRouter.route('/reports/tickets').post(reportTicketsActive);
ticketsRouter.route('/reports/mos/all').post(reportMosActiveAllAreas);
ticketsRouter.route('/reports/mos').post(reportMosActive);
ticketsRouter.route('/vouchers/mo').post(get_vouchers_of_mo);
ticketsRouter.route('/pre-barcodes').post(getPreBarcodes);
ticketsRouter.route('/pre-barcodes').post(returnBarcodesPending);
ticketsRouter.route('/mo/search-report').get(getMo);
ticketsRouter.route('/voucher-group/info').get(getInfoVoucherGroup);
ticketsRouter.route('/mo/changeStatus').post(getTicketByMoToChangeStatus);
ticketsRouter.route('/mo/changeLocation').post(getTicketByMoToChangeLocation);
ticketsRouter.route('/clone/vouchers').post(cloneVouchers);
ticketsRouter.route('/clone/vouchers-group').post(cloneVouchersGroup);
ticketsRouter.route('/mo/tickets').post(getAllTicketsFromAMo);
ticketsRouter.route('/report/locations').post(reportMosActiveByLocation);
ticketsRouter.route('/location/info').post(getInfoLocation);
ticketsRouter.route('/lots').get(getAllLots);
ticketsRouter.route('/lots').post(getLots);
ticketsRouter.route('/lots/add').post(createNewLot);
ticketsRouter.route('/lots/tickets').post(getTicketsOfLot);
ticketsRouter.route('/lots/log').post(getLogOfLot);
ticketsRouter.route('/lots/deleted/tickets').post(deleteTicketsToLot);
ticketsRouter
  .route('/lots/convert-to-voucher-group')
  .post(convertLotToVoucherGroup);
ticketsRouter.route('/lots/delete/:id').post(deleteLots);
ticketsRouter.route('/group/convert-to-lot').post(convertGroupToLot);
ticketsRouter.route('/voucher/mo/statuses').get(getMoStatus);
ticketsRouter.route('/search/mo').get(searchMo);
ticketsRouter.route('/vouchers/byMo').get(getAllVouchersByMo);

export { ticketsRouter };
