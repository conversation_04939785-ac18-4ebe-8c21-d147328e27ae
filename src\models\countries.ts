import { Model } from '@app/db';

export class Country extends Model {
  static tableName = 'countries';
  static idColumn = 'id';
  static jsonAttributes = ['alt_names'];

  id!: number; //": 1172380,
  created_at!: string; //": "2023-02-06T14:00:21.000Z",
  updated_at!: string; //": "2023-02-06T19:59:03.000Z",
  name!: string; //": "United States",
  code2!: string; //": "US",
  code3!: string; //": "USA",
  alt_names!: string[]; //": [],
  is_cafta!: boolean; //": false
}
