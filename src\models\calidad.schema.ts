import type Objection from 'objection';

import { Model } from '@app/db';

export class FabricsRevision extends Model {
  // El nombre de la tabla es la única propiedad requerida.
  static get tableName(): string {
    return 'fabrics_revision';
  }

  // Este objeto define las relaciones con otros modelos.
  static get relationMappings(): {
    revision: {
      relation: Objection.RelationType;
      modelClass: typeof FabricsRevisionItems;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      revision: {
        relation: Model.HasManyRelation,
        modelClass: FabricsRevisionItems,
        join: {
          from: 'fabrics_revision.id',
          to: 'fabrics_revision_items.fabrics_revision_id',
        },
      },
    };
  }
}

export class FabricsRevisionItems extends Model {
  static get tableName(): string {
    return 'fabrics_revision_items';
  }

  static get relationMappings(): {
    voucher: {
      relation: Objection.RelationType;
      modelClass: typeof FabricsRevision;
      join: {
        from: string;
        to: string;
      };
    };
    yardIssue: {
      relation: Objection.RelationType;
      modelClass: typeof FabricIssue;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      voucher: {
        relation: Model.BelongsToOneRelation,
        modelClass: FabricsRevision,
        join: {
          from: 'fabrics_revision_items.fabric_revision_id',
          to: 'fabrics_revision.id',
        },
      },
      yardIssue: {
        relation: Model.BelongsToOneRelation,
        modelClass: FabricIssue,
        join: {
          from: 'fabrics_revision_items.id_issue',
          to: 'fabric_issue.id',
        },
      },
    };
  }
}

export class FabricAdjustment extends Model {
  static get tableName(): string {
    return 'fabric_adjustment';
  }
}

export class FabricIssue extends Model {
  static get tableName(): string {
    return 'fabric_issue';
  }
  static get relationMappings(): {
    issueYard: {
      relation: Objection.RelationType;
      modelClass: typeof FabricsRevisionItems;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      issueYard: {
        relation: Model.HasManyRelation,
        modelClass: FabricsRevisionItems,
        join: {
          from: 'fabric_issue.id',
          to: 'fabrics_revision_items.id_issue',
        },
      },
    };
  }
}
