import type Objection from 'objection';
import type { ModelObject } from 'objection';

import { Model } from '@app/db';

export class WorkVoucherPlates extends Model {
  static get tableName(): string {
    return 'work_voucher_plates';
  }

  static get relationMappings(): {
    workVoucherPlatesWorkVouchers: {
      relation: Objection.RelationType;
      modelClass: typeof WorkVouchers;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      workVoucherPlatesWorkVouchers: {
        relation: Model.BelongsToOneRelation,
        modelClass: WorkVouchers,
        join: {
          from: 'work_voucher_plates.work_voucher_id',
          to: 'work_vouchers.id',
        },
      },
    };
  }
}

export class WorkAreaGroupEmployees extends Model {
  static get tableName(): string {
    return 'work_area_group_employees';
  }
}
export class WorkEmployeeStates extends Model {
  static get tableName(): string {
    return 'work_employee_states';
  }
}
export class StyleSams extends Model {
  sam: string;
  id: string;
  static get tableName(): string {
    return 'style_sams';
  }
}
export class WorkAreaGroupShiftEmployee extends Model {
  static get tableName(): string {
    return 'work_area_group_shift_employees';
  }
  static get relationMappings(): {
    WorkEmployeeStates: {
      relation: Objection.RelationType;
      modelClass: typeof WorkEmployeeStates;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      WorkEmployeeStates: {
        relation: Model.BelongsToOneRelation,
        modelClass: WorkEmployeeStates,
        join: {
          from: 'work_area_group_shift_employees.work_employee_state_id',
          to: 'work_employee_states.id',
        },
      },
    };
  }
}
export class WorkAreaGroupShifts extends Model {
  static get tableName(): string {
    return 'work_area_group_shifts';
  }

  id!: number;

  static get relationMappings(): {
    WorkAreaGroupShiftEmployees: {
      relation: Objection.RelationType;
      modelClass: typeof WorkAreaGroupShiftEmployee;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      WorkAreaGroupShiftEmployees: {
        relation: Model.BelongsToOneRelation,
        modelClass: WorkAreaGroupShiftEmployee,
        join: {
          from: 'work_area_group_shifts.id',
          to: 'work_area_group_shift_employees.work_area_group_shift_id',
        },
      },
    };
  }
}
export class WorkAreaTickets extends Model {
  static get tableName(): string {
    return 'work_area_tickets';
  }

  id!: number;
  work_area_id!: number;
  work_voucher_id!: number;
  work_area_ticket_status_id!: number;
  made_by_mo_scan!: boolean;
  mo_ticket_create_group_id!: number;
  work_inventory_location_id!: number;
  prev_work_area_id!: number;
  next_work_area_id!: number;
  exp_finish_date!: string | Date;
  exp_work_area_line_id!: number;
  exp_work_area_group_id!: number;
  created_by!: number;
  finished_at!: string | Date;
  notify_company!: boolean;
  is_company_notified!: boolean;
  merge_ticket_id!: number;
  created_at!: string;
  updated_at!: string;
  source_app_name!: string;
  source_app_id!: number;
  work_batch_id!: number;
  partial_received!: number;
  partial_sent!: number;

  static get relationMappings(): {
    workTicketsWorkVouchers: {
      relation: Objection.RelationType;
      modelClass: typeof WorkVouchers;
      join: {
        from: string;
        to: string;
      };
    };
    workTicketsWorkAreas: {
      relation: Objection.RelationType;
      modelClass: typeof WorkAreas;
      join: {
        from: string;
        to: string;
      };
    };
    workTicketsWorkStatuses: {
      relation: Objection.RelationType;
      modelClass: typeof WorkStatuses;

      join: {
        from: string;
        to: string;
      };
    };
    WorkTicketsWorkTicketProductions: {
      relation: Objection.RelationType;
      modelClass: typeof WorkAreaTask;

      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      workTicketsWorkVouchers: {
        relation: Model.BelongsToOneRelation,
        modelClass: WorkVouchers,
        join: {
          from: 'work_area_tickets.work_voucher_id',
          to: 'work_vouchers.id',
        },
      },
      workTicketsWorkAreas: {
        relation: Model.BelongsToOneRelation,
        modelClass: WorkAreas,
        join: {
          from: 'work_area_tickets.work_area_id',
          to: 'work_areas.work_area_id',
        },
      },
      workTicketsWorkStatuses: {
        relation: Model.BelongsToOneRelation,
        modelClass: WorkStatuses,
        join: {
          from: 'work_area_tickets.work_ticket_status_id ',
          to: 'work_statuses.id',
        },
      },
      WorkTicketsWorkTicketProductions: {
        relation: Model.HasManyRelation,
        modelClass: WorkAreaTask,
        join: {
          from: 'work_area_tickets.id',
          to: 'work_area_tasks.work_ticket_id',
        },
      },
      // WorkTicketsWorkInventoryLocationa: {
      //   relation: Model.BelongsToOneRelation,
      //   modelClass: WorkAreaTask,
      //   join: {
      //     from: "work_area_tickets.work_area_inventory_location_id",
      //     to: "work_area_inventory_locations.id",
      //   },
      // },
    };
  }
}
export class WorkVouchers extends Model {
  static get tableName(): string {
    return 'work_vouchers';
  }

  id!: number;
  mo_id!: number;
  work_voucher_type_id!: number;
  barcode!: string;
  is_primary!: boolean;
  work_voucher_plate_id!: number;
  ignore_next_area!: boolean;
  work_voucher_group_id!: number;
  work_voucher_group_sort!: number;
  is_repo!: boolean;
  work_repo_id!: number;
  merge_voucher_id!: number;
  merge_ticket_id!: number;
  merge_datetime_sv!: string;
  created_at!: string;
  updated_at!: string;
  source_app_name!: string;
  source_app_id!: number;

  static get relationMappings(): {
    workVouchersMoNumbers: {
      relation: Objection.RelationType;
      modelClass: typeof MoNumber;
      join: {
        from: string;
        to: string;
      };
    };
    workVouchersWorkTickets: {
      relation: Objection.RelationType;
      modelClass: typeof WorkAreaTickets;
      join: {
        from: string;
        to: string;
      };
    };
    workVouchersWorkVoucherPlates: {
      relation: Objection.RelationType;
      modelClass: typeof WorkVoucherPlates;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      workVouchersMoNumbers: {
        relation: Model.BelongsToOneRelation,
        modelClass: MoNumber,
        join: {
          from: 'work_vouchers.mo_id',
          to: 'mo_numbers.mo_id',
        },
      },
      workVouchersWorkTickets: {
        relation: Model.HasManyRelation,
        modelClass: WorkAreaTickets,
        join: {
          from: 'work_vouchers.id',
          to: 'work_area_tickets.work_voucher_id',
        },
      },
      workVouchersWorkVoucherPlates: {
        relation: Model.HasManyRelation,
        modelClass: WorkVoucherPlates,
        join: {
          from: 'work_vouchers.id',
          to: 'work_voucher_plates.work_voucher_id',
        },
      },
    };
  }
}
export type WorkVoucherShape = ModelObject<WorkVouchers>;

export class MoNumber extends Model {
  parent_mo_id!: number;
  num!: string;
  is_child!: boolean;

  static get tableName(): string {
    return 'mo_numbers';
  }

  static get idColumn() {
    return 'mo_id';
  }

  static get relationMappings(): {
    moNumbersWorkVouchers: {
      relation: Objection.RelationType;
      modelClass: typeof WorkVouchers;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      moNumbersWorkVouchers: {
        relation: Model.HasManyRelation,
        modelClass: WorkVouchers,
        join: {
          from: 'mo_numbers.mo_id',
          to: 'work_vouchers.mo_id',
        },
      },
    };
  }
}

export class WorkAreaOperatorMap extends Model {
  static get tableName(): string {
    return 'work_area_operator_map';
  }

  static get relationMappings(): {
    workAreaOperatorMapWorkAreas: {
      relation: Objection.RelationType;
      modelClass: typeof WorkAreas;
      join: {
        from: string;
        to: string;
      };
    };
    workAreaOperatorMapOperators: {
      relation: Objection.RelationType;
      modelClass: typeof Operators;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      workAreaOperatorMapWorkAreas: {
        relation: Model.HasManyRelation,
        modelClass: WorkAreas,
        join: {
          from: 'work_area_operator_map.work_area_id',
          to: 'work_areas.work_area_id',
        },
      },
      workAreaOperatorMapOperators: {
        relation: Model.HasManyRelation,
        modelClass: Operators,
        join: {
          from: 'work_area_operator_map.operator_id',
          to: 'operators.operator_id',
        },
      },
    };
  }
}

export class WorkAreas extends Model {
  static get tableName(): string {
    return 'work_areas';
  }

  work_area_id!: number;
  work_factory_id!: number;
  work_type_id!: number;
  area_name!: string;
  default_work_voucher_type_id!: number;
  sort!: number;
  disabled_date!: string;
  auto_ignore_next_area!: boolean;
  poly_task_name!: string;
  supervisor_id!: number;
  work_status_id!: number;
  after_scan_work_area_ticket_status_id!: number;
  created_at!: string;
  updated_at!: string;
  removed_at!: string;

  static get relationMappings(): {
    workAreasWorkTickets: {
      relation: Objection.RelationType;
      modelClass: typeof WorkAreaTickets;
      join: {
        from: string;
        to: string;
      };
    };
    workAreasWorkProductions: {
      relation: Objection.RelationType;
      modelClass: typeof WorkAreaProductions;
      join: {
        from: string;
        to: string;
      };
    };
    workAreasWorkTicketProductionTypes: {
      relation: Objection.RelationType;
      modelClass: typeof WorkAreaTaskTypes;
      join: {
        from: string;
        to: string;
      };
    };
    workAreasGroups: {
      relation: Objection.RelationType;
      modelClass: typeof WorkAreaGroups;
      join: {
        from: string;
        to: string;
      };
    };
    workTypes: {
      relation: Objection.RelationType;
      modelClass: typeof WorkTypes;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      workAreasWorkTickets: {
        relation: Model.HasManyRelation,
        modelClass: WorkAreaTickets,
        join: {
          from: 'work_areas.work_area_id',
          to: 'work_area_tickets.work_area_id',
        },
      },
      workAreasWorkProductions: {
        relation: Model.HasManyRelation,
        modelClass: WorkAreaProductions,
        join: {
          from: 'work_areas.work_area_id',
          to: 'work_area_productions.work_area_id',
        },
      },
      workAreasWorkTicketProductionTypes: {
        relation: Model.HasManyRelation,
        modelClass: WorkAreaTaskTypes,
        join: {
          from: 'work_areas.work_area_id',
          to: 'work_area_task_types.work_area_id',
        },
      },
      workAreasGroups: {
        relation: Model.HasManyRelation,
        modelClass: WorkAreaGroups,
        join: {
          from: 'work_areas.work_area_id',
          to: 'work_area_groups.work_area_id',
        },
      },
      workTypes: {
        relation: Model.HasManyRelation,
        modelClass: WorkTypes,
        join: {
          from: 'work_areas.work_type_id ',
          to: 'work_types.id',
        },
      },
    };
  }
}

export class WorkAreaGroups extends Model {
  static get tableName(): string {
    return 'work_area_groups';
  }
  static idColumn = 'id';

  id!: number;
  work_area_id!: number;
  name!: string;
  default_work_area_line_id!: number;
  work_building_id!: number;
  description!: string;
  work_area_type!: string;
  work_status_id!: number;
  barcode!: string;
  operator_id!: number;
  style_sam_group_id!: number;
  is_decoration!: boolean;
  supervisor_employee_id!: number;
  manager_employee_id!: number;
  update_customer!: boolean;
  created_at!: string;
  updated_at!: string;
  planning_name!: string;
  style_type!: string;
  section!: string;
  new_version!: boolean;
}

export class WorkStatuses extends Model {
  static get tableName(): string {
    return 'work_statuses';
  }

  static get relationMappings(): {
    workStatusesWorkTickets: {
      relation: Objection.RelationType;
      modelClass: typeof WorkAreaTickets;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      workStatusesWorkTickets: {
        relation: Model.HasManyRelation,
        modelClass: WorkAreaTickets,
        join: {
          from: 'work_statuses.id',
          to: 'work_area_tickets.work_ticket_status_id',
        },
      },
    };
  }
}

export class WorkAreaTask extends Model {
  static get tableName(): string {
    return 'work_area_tasks';
  }

  static get relationMappings(): {
    WorkTicketProductionsWorkTickets: {
      relation: Objection.RelationType;
      modelClass: typeof WorkAreaTickets;
      join: {
        from: string;
        to: string;
      };
    };
    WorkTicketProductionsWorkProductions: {
      relation: Objection.RelationType;
      modelClass: typeof WorkAreaProductions;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      WorkTicketProductionsWorkTickets: {
        relation: Model.BelongsToOneRelation,
        modelClass: WorkAreaTickets,
        join: {
          from: 'work_area_tasks.work_ticket_id',
          to: 'work_area_tickets.id',
        },
      },
      WorkTicketProductionsWorkProductions: {
        relation: Model.BelongsToOneRelation,
        modelClass: WorkAreaProductions,
        join: {
          from: 'work_area_tasks.work_production_id',
          to: 'work_area_productions.id',
        },
      },
    };
  }
}

export class WorkAreaTaskTypes extends Model {
  static get tableName(): string {
    return 'work_area_task_types';
  }

  static get relationMappings(): {
    WorkTicketProductionTypesWorkAreas: {
      relation: Objection.RelationType;
      modelClass: typeof WorkAreas;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      WorkTicketProductionTypesWorkAreas: {
        relation: Model.BelongsToOneRelation,
        modelClass: WorkAreas,
        join: {
          from: 'work_area_task_types.work_area_id',
          to: 'work_areas.work_area_id',
        },
      },
    };
  }
}

export class WorkAreaProductions extends Model {
  static get tableName(): string {
    return 'work_area_productions';
  }

  static get relationMappings(): {
    WorkProductionsWorkAreas: {
      relation: Objection.RelationType;
      modelClass: typeof WorkAreas;
      join: {
        from: string;
        to: string;
      };
    };
  } {
    return {
      WorkProductionsWorkAreas: {
        relation: Model.BelongsToOneRelation,
        modelClass: WorkAreas,
        join: {
          from: 'work_area_productions.work_area_id',
          to: 'work_areas.work_area_id',
        },
      },
    };
  }
}
export class MoScans extends Model {
  static get tableName(): string {
    return 'mo_scans';
  }
  scan_id!: number;
  mo_id!: number;
  sew_ready!: string;
  sew!: string;
  received_at!: string;
  closed_at!: string;
  start_utc!: string;
  finish_utc!: string;
  is_from_scan!: number;
  is_manual_change!: boolean;
  is_repo!: boolean;
  supervisor!: string;
  supervisor_code!: number;
  task_name!: string;
  quantity!: number;
  work_area_id!: number;
  work_area_group_id!: number;
  work_voucher_id!: number;
  work_area_ticket_id!: number;
  work_area_line_id!: number;
  work_area_group_shift_id!: number;
  poly_status: number;
  varsity_status: number;
  employee_id!: number;
  created_at!: string;
  updated_at!: string;
  removed_at!: string;
  style_sam_id!: number;
  style_sam_group_id!: number;
  sew_sam_value!: string;
  work_repo_id!: number;
  work_fragment_id!: number;
}

export class WorkTypes extends Model {
  static get tableName(): string {
    return 'work_types';
  }

  id!: number;
  name!: string;
}

export class Operators extends Model {
  static get tableName(): string {
    return 'operators';
  }

  operator_id!: number;
  barcode!: string;
  operator!: string;
  operator_name!: string;
  task!: string;
  client!: string;
  company_code!: number;
  operator_status!: number;
  employee_id!: number;
  location!: string;
}
export class WorkZones extends Model {
  static get tableName(): string {
    return 'work_zones';
  }
}
export class WorkAreaZones extends Model {
  static get tableName(): string {
    return 'work_area_zones';
  }
}
export class WorkInventoryBins extends Model {
  static get tableName(): string {
    return 'work_inventory_bins';
  }
}

export class WorkAreaTicketStatuses extends Model {
  static get tableName(): string {
    return 'work_area_ticket_statuses';
  }

  id!: number;
  work_area_id!: number;
  color_hex!: string;
  name!: string;
  work_status_id!: number;
  sequence!: number;
  created_at!: string;
  updated_at!: string;
}

export class WorkVoucherTypes extends Model {
  static get tableName(): string {
    return 'work_voucher_types';
  }
}
export class WorkAreaEmployees extends Model {
  static get tableName(): string {
    return 'work_area_employees';
  }
}
export class Buildings extends Model {
  static get tableName(): string {
    return 'buildings';
  }
}
export class WorkActivityLog extends Model {
  static get tableName(): string {
    return 'work_activity_log';
  }

  id!: number;
  work_voucher_id!: number;
  work_area_id!: number;
  employee_id!: number;
  work_area_group_id!: number;
  module_name!: string;
  module_id!: number;
  activity!: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data!: any;
  created_at!: string;
  updated_at!: string;
}
export class MoVouchers extends Model {
  static get tableName(): string {
    return 'mo_vouchers';
  }
}

export class WorkNotes extends Model {
  static get tableName(): string {
    return 'work_notes';
  }
}

export class WorkAreaLines extends Model {
  static get tableName(): string {
    return 'work_area_lines';
  }

  id!: number;
  created_at!: string;
  updated_at!: string;
  work_area_id!: number;
  name!: string;
  barcode!: string;
  description!: string;
  removed_at!: string;
}
export class WorkLists extends Model {
  static get tableName(): string {
    return 'work_lists';
  }
}

export class WorkListMos extends Model {
  static get tableName(): string {
    return 'work_list_mos';
  }
}

export class WorkAreaLists extends Model {
  static get tableName(): string {
    return 'work_area_lists';
  }
}

export class WorkTypeVoucherTypes extends Model {
  static get tableName(): string {
    return 'work_type_voucher_types';
  }
}
export class WorkVoucherGroups extends Model {
  static get tableName(): string {
    return 'work_voucher_groups';
  }

  id!: number;
  name!: string;
  employee_id_ref!: number;
  work_area_id_ref!: number;
  barcode!: string;
  removed_at!: string | Date;
}

export class EmployeeScanTemp extends Model {
  static get tableName(): string {
    return 'employee_scan_temp';
  }
}

export class WorkPreBarcodes extends Model {
  static get tableName(): string {
    return 'work_pre_barcodes';
  }
}
export class WorkVoucherCompanyIgnoreUpdates extends Model {
  static get tableName(): string {
    return 'work_voucher_company_ignore_updates';
  }
}
export class WorkAreaBatches extends Model {
  static get tableName(): string {
    return 'work_area_batches';
  }

  id!: number;
  name!: string;
  description!: string;
  work_area_id: number;
  finished_at!: string | Date;
}
export class WorkShiftEmployeeLeaves extends Model {
  static get tableName(): string {
    return 'work_shift_employee_leaves';
  }
}

export class WorkAreaGroupShiftEmployeesVoid extends Model {
  static get tableName(): string {
    return 'work_area_group_shift_employees_void';
  }
}

export class WorkShiftLeaveReasons extends Model {
  static get tableName(): string {
    return 'work_shift_leave_reasons';
  }
}

export class WorkShiftEmployeeRoles extends Model {
  static get tableName(): string {
    return 'work_shift_employee_roles';
  }
}
