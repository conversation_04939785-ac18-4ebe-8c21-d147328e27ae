export interface IWorkVouchers {
  id: number;
  mo_id: number;
  is_primary: number;
  work_voucher_group_id?: number;
  work_voucher_group_sort?: number;
  is_repo: number;
  repo_ticket_id?: number;
  source_app_name?: string;
  source_app_id?: number;
}

export interface IWorkAreaTickets {
  id: number;
  work_area_id: number;
  work_voucher_id: number;
  made_by_mo_scan: number;
  mo_ticket_create_group_id: number;
  created_by: number;
  inventory_location: string;
  finished_at: Date;
  notify_company: number;
  is_company_notified: number;
  work_ticket_status_id: number;
  source_app_name: string;
  source_app_id: number;
}

export interface IWorkAreaTasks {
  work_ticket_id: number;
  work_production_id: number;
  quantity: number;
  operator_id: number;
  work_group_id: number;
  work_ticket_production_type_id: number;
  art_code: string;
  comment: string;
  style_sam_id: number;
  stitch_count: number;
  area_type: string;
  started_at: Date;
  finished_at: Date;
  source_app_name: string;
  source_app_id: number;
}

export interface IWorkAreaProductions {
  id: number;
  work_area_id: number;
  work_shift_type_id: number;
  work_group_id: number;
  operator_id: number;
  employee_id: number;
  user_id: number;
  person_name: string;
  machine_id: number;
  speed: number;
  load_number: number;
  sam: number;
  style_number_sam_ref: string;
  source_app_name: string;
  source_app_id: number;
}

export interface IWorkVoucherPlates {
  barcode: string;
  barcode_finish: string;
  barcode_production_start: string;
  barcode_production_end: string;
  barcode_receive: string;
  is_printed: number;
  name: string;
}
