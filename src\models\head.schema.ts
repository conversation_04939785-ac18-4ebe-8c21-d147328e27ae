import type { ModelObject } from 'objection';
import { Model } from '@app/db';

export class Head extends Model {
  static tableName = 'heads';
  static idColumn = 'id';

  id!: number;
  name!: string;
  from_date!: string | Date;
  to_date!: string | Date;
}
export type HeadsShape = ModelObject<Head>;

export class HeadItem extends Model {
  static tableName = 'head_items';
  static idColumn = 'id';

  id!: number;
  head_id!: number;
  accounting_department!: string;
  employee_id!: number;
  employee_name!: string;
  hours!: number;
  daily_rate!: number;
  amount!: number;
  extra_amount!: number;
  bonus_amount!: number;
  overtime_hours!: number;
  overtime_amount!: number;
  night_overtime_hours!: number;
  night_overtime_amount!: number;
  total_amount!: number;
  deduction_isss_amount!: number;
  deduction_afp_amount!: number;
  deduction_isr_amount!: number;
  deduction_other_amount!: number;
  total_deduction_amount!: number;
  net_amount!: number;
}
export type HeadItemsShape = ModelObject<HeadItem>;

export class HeadBase extends Model {
  static tableName = 'head_base';
  static idColumn = 'id';

  employee_id!: number;
  daily_rate!: number;
}
export type HeadBaseShape = ModelObject<HeadBase>;
