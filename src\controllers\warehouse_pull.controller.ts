import axios from 'axios';
import dayjs from 'dayjs';
import { type Request, type Response } from 'express';
import { transaction } from 'objection';
import { ZodError, type z } from 'zod';

import { sortList } from '@app/helpers/sortList';
import type {
  AddOrdersToSessionSchema,
  CreateAllocationSchema,
  CreateAllocationsSchema,
  CreateNewSessionSchema,
  CreatePullSessionOrderPartsSchema,
  DownloadsSchema,
  FinishDownloadSchema,
  GetOrdersWithoutAllocationSchema,
  RoundedQuantitySchema,
  UpdateAllocationSchema,
  sesssionIDSchema,
} from '@app/interface/zod_schemas';
import { MoNumber } from '@app/models/tickets.schema';
import {
  WarehouseDownloaderOrders,
  WarehousePullSessionAllocations,
  WarehousePullSessionOrderParts,
  WarehousePullSessionOrders,
  WarehousePullSessionTypes,
  WarehousePullSessions,
} from '@app/models/warehouse_pull';
import { buildLogger, config } from '@app/settings';

const logger = buildLogger('controllers/warehouse_pull.controller.ts');

interface ReasonsPoly {
  DropDownValueID: string;
  DropDownValue: string;
  TransactionReasonCode: string;
}

type Part = {
  RawAllocationID: string;
  ManufactureNumber: string;
  PartNumber: string;
  Description: string;
  CategoryName: string;
  SubcategoryName: string;
  DatabaseUnits: string;
  QuantityRequired: string;
  QuantityWithdrawn: string;
  QuantityOnHand: string;
};

export async function getSessionTypes(req: Request, res: Response) {
  try {
    const sessionTypes = await WarehousePullSessionTypes.query()
      .where('is_active', true)
      .select('id', 'name')
      .castTo<{ id: number; name: string }[]>();

    if (!sessionTypes) {
      return res.status(400).json({
        ok: false,
        message: 'No se encontraron tipos de sesiones',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Tipos de sesiones obtenidos correctamente',
      data: sessionTypes,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      logger.error(`Error de validación: ${error}`);

      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error(`Error interno del servidor: ${error}`);
    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function addOrdersToSession(
  req: Request<unknown, unknown, z.infer<typeof AddOrdersToSessionSchema>>,
  res: Response
) {
  try {
    const { orders, sessionID } = req.body;

    const session = await WarehousePullSessions.query()
      .where('id', sessionID)
      .whereNull('finished_at')
      .first()
      .castTo<{ id: number }>();

    if (!session) {
      logger.error(`Session not found: ${sessionID}`);

      return res.status(400).json({
        ok: false,
        message: 'Session not found',
      });
    }

    const ordersToAssign = await transaction(
      WarehousePullSessionOrders,
      async (WarehousePullSessionOrders) => {
        for (const order of orders) {
          const num = await MoNumber.query()
            .where('mo_id', order.moID)
            .select('num')
            .first()
            .castTo<{ num: string }>();

          if (!num) {
            logger.error('No se encontro el mo');

            throw new Error('No se encontro el mo');
          }

          const bodyRequest = {
            query: 'ShowAllRawAllocations',
            columns:
              'ManufactureNumber, PartNumber, Description, CategoryName, SubcategoryName, DatabaseUnits, QuantityRequired, QuantityWithdrawn, QuantityOnHand',
            filter: `[QuantityRequired] > 0 AND ([CategoryName] = 'Care Labels' OR [CategoryName] = 'Trim') AND  [ManufactureNumber] = '${num.num}'`,
          };

          const restfulResponse: { data: Part[] } = await axios.post(
            `${config.app.restfulURL}/poly/table`,
            JSON.stringify(bodyRequest),
            {
              headers: {
                'Content-Type': 'application/json',
              },
            }
          );

          if (restfulResponse.data.length === 0) {
            logger.error(
              `No se encontraron los materiales para la MO ${num.num}`
            );

            throw new Error(
              `No se encontraron los materiales para la MO ${num.num}`
            );
          }

          await WarehousePullSessionOrders.query().insert({
            mo_id: order.moID,
            warehouse_pull_session_id: sessionID,
            work_area_voucher_id: order.voucherID || null,
          });
        }

        return true;
      }
    );

    if (!ordersToAssign) {
      logger.error(`Error al agregar las ordenes a la sesion: ${sessionID}`);

      return res.status(500).json({
        ok: false,
        message: 'Error al agregar las ordenes a la sesion',
      });
    }

    return res.status(200).json({
      ok: true,
      message: `Se agregaron las ordenes a la sesion ${sessionID}`,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      logger.error(`Error de validación: ${error}`);

      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error(`Error interno del servidor: ${error}`);
    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function getMaterialsByMos(
  req: Request<unknown, unknown, z.infer<typeof sesssionIDSchema>>,
  res: Response
) {
  try {
    const { sessionID } = req.body;

    const session = await WarehousePullSessions.query()
      .where('id', sessionID)
      .whereNull('finished_at')
      .select(['id', { isReposition: 'is_reposition' }])
      .first()
      .castTo<{ id: number; isReposition: boolean }>();

    if (!session) {
      logger.error(`Session not found: ${sessionID}`);

      return res.status(400).json({
        ok: false,
        message: 'Session not found',
      });
    }

    const mos = await MoNumber.query()
      .join(
        'warehouse_pull_session_orders',
        'mo_numbers.mo_id',
        'warehouse_pull_session_orders.mo_id'
      )
      .where(
        'warehouse_pull_session_orders.warehouse_pull_session_id',
        sessionID
      )

      .select('mo_numbers.mo_id', 'mo_numbers.num')
      .castTo<{ mo_id: number; num: string }[]>();

    if (mos.length === 0) {
      logger.error('No se encontraron las mos');

      return res.status(400).json({
        ok: false,
        message: 'No se encontraron mos',
        data: [],
      });
    }

    const materialsWorked = await WarehousePullSessionOrderParts.query()
      .join(
        'warehouse_pull_session_orders',
        'warehouse_pull_session_order_parts.warehouse_pull_session_order_id',
        'warehouse_pull_session_orders.id'
      )
      .whereIn(
        'warehouse_pull_session_orders.mo_id',
        mos.map((mo) => mo.mo_id)
      )
      .select('warehouse_pull_session_order_parts.part_number')
      .castTo<{ part_number: string }[]>();

    const bodyRequest = {
      query: 'ShowAllRawAllocations',
      columns:
        'ManufactureNumber, PartNumber, Description, CategoryName, SubcategoryName, DatabaseUnits, QuantityRequired, QuantityWithdrawn, QuantityOnHand',
      filter: `[QuantityRequired] > 0 AND ([CategoryName] = 'Care Labels' OR [CategoryName] = 'Trim') AND ${mos
        .map(
          (mo: { num: string }): string => `[ManufactureNumber] = '${mo.num}'`
        )
        .join(' OR ')}`,
    };

    const restfulResponse: { data: Part[] } = await axios.post(
      `${config.app.restfulURL}/poly/table`,
      JSON.stringify(bodyRequest),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (restfulResponse.data.length === 0) {
      logger.error('No se encontraron los materiales');

      return res.status(400).json({
        ok: false,
        message: 'No se encontraron los materiales',
        data: [],
      });
    }

    const groupedParts = restfulResponse.data.reduce<
      Record<
        string,
        {
          sumOnHand: number;
          nums: {
            num: string;
            required: number;
          }[];
          qtyRequired: number;
          units: string;
          subCategory: string;
        }
      >
    >((acc, part) => {
      const {
        PartNumber,
        QuantityOnHand,
        ManufactureNumber,
        SubcategoryName,
        DatabaseUnits,
      } = part;
      const quantity = parseInt(QuantityOnHand, 10) || 0;
      const quantityRequired = parseFloat(part.QuantityRequired) || 0;

      if (!acc[PartNumber]) {
        acc[PartNumber] = {
          sumOnHand: 0,
          nums: [],
          qtyRequired: 0,
          units: DatabaseUnits,
          subCategory: SubcategoryName,
        };
      }

      acc[PartNumber].sumOnHand += quantity;
      acc[PartNumber].nums.push({
        num: ManufactureNumber,
        required: quantityRequired,
      });
      acc[PartNumber].qtyRequired += quantityRequired;

      return acc;
    }, {});

    const result = Object.entries(groupedParts).map(
      ([name, { sumOnHand, nums, qtyRequired, units, subCategory }]) => ({
        name,
        subCategory,
        quantity: sumOnHand,
        qtyRequired,
        units,
        nums: Array.from(nums),
      })
    );

    const sortedAllocations = sortList(result, 'subCategory', 'asc');
    const filteredAllocations = sortedAllocations.filter((material) => {
      const foundMaterial = materialsWorked.find(
        (m) => m.part_number === material.name
      );

      return !foundMaterial;
    });

    return res.status(200).json({
      ok: true,
      message: 'Se encontraron los materiales',
      data: session.isReposition ? sortedAllocations : filteredAllocations,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      logger.error(`Error de validación: ${error}`);

      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function addOrderPartsToSession(
  req: Request<
    unknown,
    unknown,
    z.infer<typeof CreatePullSessionOrderPartsSchema>
  >,
  res: Response
) {
  try {
    const { sessionID, partNumbers } = req.body;

    const doesSessionExist = await WarehousePullSessions.query()
      .where('id', sessionID)
      .whereNull('finished_at')
      .first()
      .castTo<{ id: number }>();

    if (!doesSessionExist) {
      logger.error(`Session not found: ${sessionID}`);

      return res.status(400).json({
        ok: false,
        message: `No se encontro la sesion: ${sessionID}`,
      });
    }

    const partNums = partNumbers.flatMap((p) => p.nums.map((n) => n.num));
    const moIDs = await MoNumber.query()
      .join(
        'warehouse_pull_session_orders',
        'mo_numbers.mo_id',
        'warehouse_pull_session_orders.mo_id'
      )
      .where(
        'warehouse_pull_session_orders.warehouse_pull_session_id',
        sessionID
      )
      .whereIn('mo_numbers.num', partNums)
      .select('mo_numbers.num', 'mo_numbers.mo_id')
      .castTo<{ num: string; mo_id: number }[]>();

    const moIDMap = new Map(moIDs.map((m) => [m.num, m.mo_id]));
    const missingMoNums = partNums.filter((num) => !moIDMap.has(num));

    if (missingMoNums.length) {
      logger.error(`MOs not found: ${missingMoNums.join(', ')}`);

      return res.status(400).json({
        ok: false,
        message: `No se encontraron las MOs: ${missingMoNums.join(', ')}`,
      });
    }

    const sessionOrders = await WarehousePullSessionOrders.query()
      .whereIn(
        'mo_id',
        moIDs.map((m) => m.mo_id)
      )
      .andWhere('warehouse_pull_session_id', sessionID)
      .select('mo_id', 'id');

    const sessionOrderMap = new Map(sessionOrders.map((s) => [s.mo_id, s.id]));
    const missingSessionOrders = moIDs
      .map((m) => m.mo_id)
      .filter((mo_id) => !sessionOrderMap.has(mo_id));

    if (missingSessionOrders.length) {
      logger.error(
        `Session Orders not found: ${missingSessionOrders.join(', ')}`
      );

      return res.status(400).json({
        ok: false,
        message: `No se encontraron las session orders para las MO IDs: ${missingSessionOrders.join(
          ', '
        )}`,
      });
    }

    const insertData = partNumbers.flatMap((partNumber) =>
      partNumber.nums.map((part) => ({
        warehouse_pull_session_order_id: sessionOrderMap.get(
          moIDMap.get(part.num)
        ),
        part_number: partNumber.name,
        sub_category: partNumber.subCategory,
        unit: partNumber.units,
        quantity_bom: Number(part.required),
        quantity_rounded: Number(part.required),
        quantity_pulling: 0,
      }))
    );

    const sessionOrderParts = await transaction(
      WarehousePullSessionOrderParts,
      async (WarehousePullSessionOrderParts) => {
        for (const part of insertData) {
          const doesPartExist = await WarehousePullSessionOrderParts.query()
            .where(
              'warehouse_pull_session_order_id',
              part.warehouse_pull_session_order_id
            )
            .where('part_number', part.part_number)
            .where('sub_category', part.sub_category)
            .select('id')
            .first()
            .castTo<{ id: number }>();

          if (doesPartExist) continue;

          const result = await WarehousePullSessionOrderParts.query().insert(
            part
          );

          if (!result) throw new Error('Error al insertar los materiales');
        }

        return true;
      }
    );

    if (!sessionOrderParts) {
      logger.error('Error al insertar los materiales');

      return res.status(500).json({
        ok: false,
        message: 'Error al insertar los materiales',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Se agregaron los materiales a la session',
      data: insertData,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      logger.error(`Error de validación: ${error}`);

      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function getMaterialsBySession(req: Request, res: Response) {
  try {
    const { sessionID } = req.params as unknown as {
      sessionID: number;
    };

    const session = await WarehousePullSessions.query()
      .where('id', sessionID)
      .whereNull('finished_at')
      .first()
      .castTo<{ id: number }>();

    if (!session) {
      logger.error(`No se encontro la sesion: ${sessionID}`);

      return res.status(400).json({
        ok: false,
        message: `No se encontro la sesion: ${sessionID}`,
      });
    }

    const partNumbers = await WarehousePullSessionOrderParts.query()
      .join(
        'warehouse_pull_session_orders',
        'warehouse_pull_session_order_parts.warehouse_pull_session_order_id',
        'warehouse_pull_session_orders.id'
      )
      .join(
        'mo_numbers',
        'warehouse_pull_session_orders.mo_id',
        'mo_numbers.mo_id'
      )
      .where(
        'warehouse_pull_session_orders.warehouse_pull_session_id',
        sessionID
      )
      .select(
        'warehouse_pull_session_order_parts.id',
        'warehouse_pull_session_order_parts.part_number',
        'warehouse_pull_session_order_parts.sub_category',
        {
          quantity_required: 'warehouse_pull_session_order_parts.quantity_bom',
        },
        'warehouse_pull_session_order_parts.quantity_rounded',
        'warehouse_pull_session_order_parts.unit',
        'warehouse_pull_session_order_parts.warehouse_pull_session_order_id',
        'warehouse_pull_session_orders.mo_id',
        'mo_numbers.mo_order',
        'mo_numbers.customer',
        'mo_numbers.num',
        'mo_numbers.style',
        'mo_numbers.quantity'
      )
      .castTo<
        {
          id: number;
          part_number: string;
          sub_category: string;
          quantity_required: number;
          quantity_rounded: number;
          unit: string;
          warehouse_pull_session_order_id: number;
          mo_id: number;
          order: string;
          customer: string;
          num: string;
          style: string;
          quantity: number;
        }[]
      >();

    const partNumbersFormatted = sortList(partNumbers, 'sub_category', 'asc');

    if (partNumbersFormatted.length === 0) {
      logger.error(`No se encontraron los materiales, ${sessionID}`);

      return res.status(400).json({
        ok: false,
        message: 'No se encontraron los materiales',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Se encontraron los materiales',
      data: partNumbersFormatted,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      logger.error(`Error de validación: ${error}`);

      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

interface Container {
  RawContainerID: number;
  PartNumber: string;
  ContainerCode: string;
  Bin: string;
  QuantityOnHand: number;
  DatabaseUnits: string;
  SubcategoryName: string;
}

export async function getContainersByPartNumber(req: Request, res: Response) {
  try {
    const { partNumber } = req.params as unknown as { partNumber: string };

    if (!partNumber) {
      logger.error('No se proporcionó el part number');

      return res.status(400).json({
        ok: false,
        message: 'No se proporcionó el part number',
      });
    }

    const bodyRequest = {
      query: 'ShowAllRawContainers',
      columns: 'ContainerCode, Bin, QuantityOnHand, DatabaseUnits',
      filter: `[ContainerCode] <> '<Default>' AND [QuantityOnHand] > 0 AND ([PartNumber] = '${decodeURIComponent(
        partNumber
      )}')`,
    };

    const restfulResponse: { data: Container[] } = await axios.post(
      `${config.app.restfulURL}/poly/table`,
      JSON.stringify(bodyRequest),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (restfulResponse.data.length === 0) {
      logger.error(
        `No se encontraron contenedores para el part number ${partNumber}`
      );

      return res.status(200).json({
        ok: true,
        message: `No se encontraron contenedores para el part number ${partNumber}`,
        data: [],
      });
    }

    const allocatedContainers = await WarehousePullSessionAllocations.query()
      .whereNull('poly_updated_at')
      .whereIn(
        'container_code',
        restfulResponse.data.map((container): string => container.ContainerCode)
      )
      .select('container_code', 'quantity')
      .castTo<{ container_code: string; quantity: number }[]>();

    const result = restfulResponse.data.map((container) => {
      const containersDB = allocatedContainers.map((allocatedContainer) => {
        if (allocatedContainer.container_code === container.ContainerCode) {
          return allocatedContainer.quantity;
        }
      });

      const quantityToDiscount = containersDB.reduce((acc, curr) => {
        if (curr) {
          return acc + curr;
        }
        return acc;
      }, 0);

      const totalOnHand = (
        container.QuantityOnHand - quantityToDiscount
      ).toFixed(2);

      return {
        rawContainerID: container.RawContainerID,
        partNumber: container.PartNumber,
        containerCode: container.ContainerCode,
        bin: container.Bin,
        quantityOnHand:
          parseFloat(totalOnHand) > 0 ? parseFloat(totalOnHand) : 0,
        databaseUnits: container.DatabaseUnits,
      };
    });

    const sortedResult = sortList(result, 'containerCode', 'asc').filter(
      (container) => container.quantityOnHand > 0
    );

    if (sortedResult.length === 0) {
      logger.error(`No se encontraron los contenedores, ${partNumber}`);

      return res.status(400).json({
        ok: false,
        message: 'No se encontraron los contenedores',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Se encontraron los contenedores',
      data: result,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      logger.error(`Error de validación: ${error}`);

      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function createAllocations(
  req: Request<unknown, unknown, z.infer<typeof CreateAllocationsSchema>>,
  res: Response
) {
  try {
    const { allocations, sessionID } = req.body;

    const sessionOrderParts: boolean = await transaction(
      WarehousePullSessionAllocations,
      WarehousePullSessions,
      async (WarehousePullSessionAllocations, WarehousePullSessions) => {
        const session: { id: number } = await WarehousePullSessions.query()
          .where('id', sessionID)
          .whereNull('finished_at')
          .select('id')
          .first()
          .castTo<{ id: number }>();

        if (!session) return false;
        if (allocations.length === 0) return false;

        for (const allocation of allocations) {
          await WarehousePullSessionAllocations.query().insert({
            quantity: allocation.quantity,
            warehouse_pull_session_order_part_id: allocation.sessionOrderPartID,
            container_code: allocation.containerCode,
          });
        }

        await WarehousePullSessions.query().where('id', sessionID).update({
          finished_at: new Date(),
        });

        return true;
      }
    );

    if (!sessionOrderParts) {
      logger.error('Error al insertar las asignaciones');

      return res.status(500).json({
        ok: false,
        message: 'Error interno del servidor',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Datos guardados correctamente',
    });
  } catch (error) {
    if (error instanceof ZodError) {
      logger.error(`Error de validación: ${error}`);

      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function createAllocation(
  req: Request<unknown, unknown, z.infer<typeof CreateAllocationSchema>>,
  res: Response
) {
  try {
    const { allocation, sessionID } = req.body;

    const allocationID = await transaction(
      WarehousePullSessionAllocations,
      WarehousePullSessions,
      async (WarehousePullSessionAllocations, WarehousePullSessions) => {
        const session: { id: number } = await WarehousePullSessions.query()
          .where('id', sessionID)
          .whereNull('finished_at')
          .select('id')
          .first()
          .castTo<{ id: number }>();

        if (!session) throw new Error('No se encontro la sesion');
        if (!allocation) throw new Error('No se proporciono la asignacion');

        const downloadID = await WarehousePullSessionAllocations.query()
          .insert({
            quantity: allocation.quantity,
            warehouse_pull_session_order_part_id: allocation.sessionOrderPartID,
            container_code: allocation.containerCode,
          })
          .castTo<{ id: number }>();

        return downloadID.id;
      }
    );

    if (!allocationID) {
      logger.error('Error al insertar las asignaciones');

      return res.status(500).json({
        ok: false,
        message: 'Error interno del servidor',
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Datos guardados correctamente',
      data: allocationID,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      logger.error(`Error de validación: ${error}`);

      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function updateAllocation(
  req: Request<unknown, unknown, z.infer<typeof UpdateAllocationSchema>>,
  res: Response
) {
  try {
    const { id, quantity } = req.body;

    if (!id) {
      return res
        .status(400)
        .json({ ok: false, message: 'No se proporcionó la asignación' });
    }

    if (quantity === 0) {
      await WarehousePullSessionAllocations.query()
        .where('id', id)
        .update({ quantity: 0 });

      return res
        .status(200)
        .json({ ok: true, message: 'Cantidad actualizada correctamente' });
    }

    const allocationUpdate = await WarehousePullSessionAllocations.query()
      .where('id', id)
      .update({ quantity });

    if (!allocationUpdate) {
      return res
        .status(500)
        .json({ ok: false, message: 'Error interno del servidor' });
    }

    return res.status(200).json({
      ok: true,
      message: 'Asignación actualizada correctamente',
    });
  } catch (error) {
    if (error instanceof ZodError) {
      logger.error(`Error de validación: ${error}`);

      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function getAllocations(req: Request, res: Response) {
  try {
    const { sessionID } = req.params as unknown as {
      sessionID: number;
    };

    const sessionExists: { id: number } = await WarehousePullSessions.query()
      .where('id', sessionID)
      .whereNull('finished_at')
      .select('id')
      .first()
      .castTo<{ id: number }>();

    if (!sessionExists) {
      return res
        .status(400)
        .json({ ok: false, message: 'No se encontró la sesión' });
    }

    const allocations = await WarehousePullSessionAllocations.query()
      .join(
        'warehouse_pull_session_order_parts',
        'warehouse_pull_session_allocations.warehouse_pull_session_order_part_id',
        'warehouse_pull_session_order_parts.id'
      )
      .join(
        'warehouse_pull_session_orders',
        'warehouse_pull_session_order_parts.warehouse_pull_session_order_id',
        'warehouse_pull_session_orders.id'
      )
      .where(
        'warehouse_pull_session_orders.warehouse_pull_session_id',
        sessionID
      )
      .select([
        'warehouse_pull_session_allocations.id',
        {
          sessionOrderPartID:
            'warehouse_pull_session_allocations.warehouse_pull_session_order_part_id',
        },
        { containerCode: 'warehouse_pull_session_allocations.container_code' },
        { quantityTaken: 'warehouse_pull_session_allocations.quantity' },
      ])
      .castTo<
        {
          id: number;
          sessionOrderPartID: number;
          containerCode: string;
          quantityTaken: number;
        }[]
      >();

    if (allocations.length === 0) {
      return res.status(200).json({
        ok: true,
        message:
          allocations.length > 0
            ? 'Asignaciones obtenidas correctamente'
            : 'No se encontraron asignaciones',
        data: [],
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Asignaciones obtenidas correctamente',
      data: allocations,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      logger.error(`Error de validación: ${error}`);

      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function getOrdersWithAllocation(
  req: Request<
    unknown,
    unknown,
    z.infer<typeof GetOrdersWithoutAllocationSchema>
  >,
  res: Response
) {
  try {
    const { customer } = req.body;

    const query = WarehousePullSessionOrders.query()
      .join(
        'mo_numbers',
        'mo_numbers.mo_id',
        'warehouse_pull_session_orders.mo_id'
      )
      .leftJoin(
        'warehouse_downloader_orders',
        'warehouse_downloader_orders.mo_id',
        'mo_numbers.mo_id'
      )
      .whereNull('warehouse_downloader_orders.mo_id')
      .whereNotIn('mo_numbers.mo_status', [
        'Void',
        'Cancelled',
        'Materials',
        'Complete',
      ])
      .select([
        'mo_numbers.mo_id',
        'mo_numbers.num',
        'mo_numbers.style',
        'mo_numbers.quantity',
        'mo_numbers.material_date',
        'mo_numbers.customer',
        'mo_numbers.mo_status',
      ]);

    if (customer) {
      query.where('mo_numbers.customer', customer.toUpperCase());
    }

    const orders = await query.castTo<
      {
        mo_id: number;
        num: string;
        style: string;
        quantity: number;
        material_date: string;
        customer: string;
        mo_status: string;
      }[]
    >();

    if (orders.length === 0)
      return res
        .status(404)
        .json({ ok: true, message: 'No hay datos', data: [] });

    return res.status(200).json({
      ok: true,
      message: 'Ordenes obtenidas correctamente',
      data: orders,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      logger.error(`Error de validación: ${error}`);

      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function startDownload(
  req: Request<unknown, unknown, z.infer<typeof DownloadsSchema>>,
  res: Response
) {
  try {
    const { employeeID, moID } = req.body;

    const order = await WarehouseDownloaderOrders.query()
      .join(
        'employees',
        'employees.employee_id',
        'warehouse_downloader_orders.employee_id'
      )
      .where('warehouse_downloader_orders.mo_id', moID)
      .whereNull('warehouse_downloader_orders.finished_at')
      .select([
        'warehouse_downloader_orders.id',
        'employees.employee_id',
        'employees.first_name',
      ])
      .first()
      .castTo<{ id: number; employee_id: number; first_name: string }>();

    if (order) {
      return res.status(400).json({
        ok: false,
        message: `La orden está asignada a ${order.first_name}`,
      });
    }

    const orderExists = await WarehouseDownloaderOrders.query()
      .where('employee_id', employeeID)
      .whereNull('finished_at')
      .select('id')
      .first()
      .castTo<{ id: number }>();

    if (orderExists) {
      return res
        .status(400)
        .json({ ok: false, message: 'Ya tienes una orden asignada' });
    }

    const download = await WarehouseDownloaderOrders.query()
      .insert({
        employee_id: employeeID,
        mo_id: moID,
      })
      .castTo<{ id: number }>();

    return res.status(200).json({
      ok: true,
      message: 'Orden asignada correctamente',
      data: {
        id: download.id,
      },
    });
  } catch (error) {
    if (error instanceof ZodError) {
      logger.error(`Error de validación: ${error}`);

      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function finishDownload(
  req: Request<unknown, unknown, z.infer<typeof FinishDownloadSchema>>,
  res: Response
) {
  try {
    const { downloadID } = req.params as unknown as { downloadID: number };
    const { container, employee_id } = req.body;

    const download = await WarehouseDownloaderOrders.query()
      .where('id', downloadID)
      .whereNull('finished_at')
      .first();

    if (!download) {
      return res
        .status(404)
        .json({ ok: false, message: 'No se encontro la sesion de descarga' });
    }

    const downloadTransaction = await transaction(
      WarehousePullSessionAllocations,
      WarehousePullSessionOrderParts,

      async (Allocations, Parts) => {
        await Allocations.query()
          .where(
            'warehouse_pull_session_allocations.id',
            container.warehouse_pull_session_allocations_id
          )
          .patch({
            poly_updated_at: new Date(),
            poly_updated_by: employee_id,
          });

        await Parts.query()
          .where('id', container.warehouse_pull_session_order_parts_id)
          .update({ quantity_pulling: container.pulling_quantity });

        // TODO: ACTUALIZAR STATUS DEL VOUCHER

        return true;
      }
    );

    if (!downloadTransaction) {
      return res
        .status(400)
        .json({ ok: false, message: 'Error interno del servidor' });
    }

    return res
      .status(200)
      .json({ ok: true, message: 'Finalizado correctamente' });
  } catch (error) {
    if (error instanceof ZodError) {
      logger.error(`Error de validación: ${error}`);

      return res.status(500).json({
        ok: false,
        message: error.message,
      });
    }

    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export type DownloadInfo = {
  voucher_id: number;
  bin: string;
  container_code: string;
  database_units: string;
  difference: string;
  onhand: number;
  part_number: string;
  pulling_quantity: number;
  quantity_bom: number | string;
  warehouse_pull_session_allocations_id: number;
  warehouse_pull_session_order_parts_id: number;
  warehouse_pull_session_orders_id: number;
  subcategory: string;
  warehouse_pull_sessions_id: number;
  session_type: string;
  poly_updated_flag: number;
};

export type GeneralDownloadInfo = {
  mo_id?: number;
  num: string;
  style: string;
  quantity?: number;
  material_date: string;
  customer: string;
  mo_status: string;
  prepared_by: string;
  reason: string;
  created_at: string;
};

export async function getOrderMaterials(req: Request, res: Response) {
  try {
    const { moID, id } = req.params as unknown as { moID: number; id: number };

    const materials = await WarehousePullSessionAllocations.query()
      .join(
        'warehouse_pull_session_order_parts',
        'warehouse_pull_session_order_parts.id',
        'warehouse_pull_session_allocations.warehouse_pull_session_order_part_id'
      )
      .join(
        'warehouse_pull_session_orders',
        'warehouse_pull_session_orders.id',
        'warehouse_pull_session_order_parts.warehouse_pull_session_order_id'
      )
      .join(
        'warehouse_pull_sessions',
        'warehouse_pull_sessions.id',
        'warehouse_pull_session_orders.warehouse_pull_session_id'
      )
      .join(
        'warehouse_pull_session_types',
        'warehouse_pull_session_types.id',
        'warehouse_pull_sessions.warehouse_pull_session_type_id'
      )
      .join(
        'mo_numbers',
        'mo_numbers.mo_id',
        'warehouse_pull_session_orders.mo_id'
      )
      .join(
        'warehouse_downloader_orders',
        'warehouse_downloader_orders.mo_id',
        'mo_numbers.mo_id'
      )
      .join(
        'employees',
        'employees.employee_id',
        'warehouse_pull_sessions.employee_id'
      )
      .where('warehouse_pull_session_orders.mo_id', moID)
      .where('warehouse_downloader_orders.id', id)
      .where('warehouse_pull_sessions.status', 'in_progress')
      .select([
        'warehouse_pull_session_order_parts.part_number',
        'warehouse_pull_session_order_parts.quantity_bom',
        'warehouse_pull_session_allocations.container_code',
        { pulling_quantity: 'warehouse_pull_session_allocations.quantity' },
        'warehouse_pull_session_allocations.created_at',
        'mo_numbers.num',
        'mo_numbers.style',
        'mo_numbers.material_date',
        'mo_numbers.customer',
        'mo_numbers.mo_status',
        { reason: 'warehouse_pull_sessions.default_reason' },
        { prepared_by: 'employees.first_name' },
        {
          mo_id: 'mo_numbers.mo_id',
        },
        {
          voucher_id: 'warehouse_pull_session_orders.work_area_voucher_id',
        },
        {
          warehouse_pull_session_orders_id: 'warehouse_pull_session_orders.id',
        },
        {
          warehouse_pull_session_allocations_id:
            'warehouse_pull_session_allocations.id',
        },
        {
          warehouse_pull_session_order_parts_id:
            'warehouse_pull_session_order_parts.id',
        },
        { warehouse_pull_sessions_id: 'warehouse_pull_sessions.id' },
        { session_type: 'warehouse_pull_session_types.name' },
        {
          poly_updated_flag: WarehousePullSessionAllocations.raw(
            'CASE WHEN warehouse_pull_session_allocations.poly_updated_at IS NULL THEN 0 ELSE 1 END'
          ),
        },
      ])
      .orderBy('warehouse_pull_session_order_parts.part_number', 'asc')
      .castTo<
        {
          voucher_id: number | null;
          part_number: string;
          quantity_bom: number;
          container_code: string;
          pulling_quantity: number;
          created_at: string;
          num: string;
          style: string;
          material_date: string;
          customer: string;
          mo_status: string;
          prepared_by: string;
          mo_id: number;
          reason: string;
          warehouse_pull_session_orders_id: number;
          warehouse_pull_session_allocations_id: number;
          warehouse_pull_session_order_parts_id: number;
          warehouse_pull_sessions_id: number;
          session_type: string;
          poly_updated_flag: number;
        }[]
      >();

    if (materials.length === 0) {
      return res.status(204).json({
        ok: false,
        message: 'No se encontraron materiales',
        data: [],
      });
    }

    const containerCodes = [...new Set(materials.map((m) => m.container_code))];

    const containerCodesFilter = containerCodes
      .map(
        (cc: string): string => `[ContainerCode] = '${decodeURIComponent(cc)}'`
      )
      .join(' OR ');

    const bodyRequest = {
      query: 'ShowAllRawContainers',
      columns:
        'ContainerCode, Bin, QuantityOnHand, DatabaseUnits, SubcategoryName',
      filter: `(${containerCodesFilter})`,
    };

    let containersData: Container[] = [];

    try {
      const restfulResponse: { data: Container[] } = await axios.post(
        `${config.app.restfulURL}/poly/table`,
        JSON.stringify(bodyRequest),
        { headers: { 'Content-Type': 'application/json' } }
      );

      containersData = restfulResponse.data;
    } catch (error) {
      logger.error(`Error consultando contenedores: ${error}`);

      return res.status(500).json({
        ok: false,
        message: 'Error consultando contenedores',
      });
    }

    const containersByCode = containersData.reduce<Record<string, Container>>(
      (acc, container) => {
        acc[container.ContainerCode] = container;
        return acc;
      },
      {}
    );

    const enrichedMaterials = materials.map((material) => {
      const container = containersByCode[material.container_code];
      const countContainers = materials.filter(
        (m) =>
          m.warehouse_pull_session_order_parts_id ===
          material.warehouse_pull_session_order_parts_id
      );

      if (countContainers.length > 1) {
        const sumPullingQuantity = countContainers.reduce(
          (acc: number, m) => acc + m.pulling_quantity,
          0
        );

        return {
          ...material,
          quantity_bom: `${material.quantity_bom}(${countContainers.length})`,
          difference: (sumPullingQuantity - material.quantity_bom).toFixed(2),
          onhand: Number(container.QuantityOnHand).toFixed(2),
          database_units: container.DatabaseUnits,
          bin: container.Bin,
          subcategory: container.SubcategoryName,
        };
      }

      return {
        ...material,
        difference: (material.pulling_quantity - material.quantity_bom).toFixed(
          2
        ),
        onhand: Number(container.QuantityOnHand).toFixed(2),
        database_units: container.DatabaseUnits,
        bin: container.Bin,
        subcategory: container.SubcategoryName,
      };
    });

    const orderMaterials: DownloadInfo[] = enrichedMaterials.map((material) => {
      return {
        voucher_id: material.voucher_id,
        bin: material.bin,
        container_code: material.container_code,
        database_units: material.database_units,
        difference: material.difference,
        onhand: +material.onhand,
        part_number: material.part_number,
        pulling_quantity: material.pulling_quantity,
        quantity_bom: material.quantity_bom,
        warehouse_pull_session_allocations_id:
          material.warehouse_pull_session_allocations_id,
        warehouse_pull_session_order_parts_id:
          material.warehouse_pull_session_order_parts_id,
        warehouse_pull_session_orders_id:
          material.warehouse_pull_session_orders_id,
        subcategory: material.subcategory,
        session_type: material.session_type,
        warehouse_pull_sessions_id: material.warehouse_pull_sessions_id,
        poly_updated_flag: material.poly_updated_flag,
      };
    });

    const generalDownloadInfo: GeneralDownloadInfo = {
      mo_id: materials[0].mo_id,
      num: materials[0].num,
      style: materials[0].style,
      quantity: materials[0].quantity_bom,
      material_date: materials[0].material_date,
      customer: materials[0].customer,
      mo_status: materials[0].mo_status,
      prepared_by: materials[0].prepared_by,
      reason: materials[0].reason,
      created_at: materials[0].created_at,
    };

    return res.status(200).json({
      ok: true,
      message: 'Materiales obtenidos correctamente',
      data: {
        downloads: orderMaterials,
        generalDownloadInfo,
      },
    });
  } catch (error) {
    logger.error(`Error interno del servidor: ${error}`);
    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function getSessionInfo(req: Request, res: Response) {
  try {
    const { sessionID } = req.params as unknown as { sessionID: number };

    const session = await WarehousePullSessions.query()
      .where('id', sessionID)
      .whereNull('finished_at')
      .first();

    if (!session) {
      return res.status(400).json({
        ok: false,
        message: `No se encontró la sesión: ${sessionID}`,
      });
    }

    const orders = await WarehousePullSessionOrders.query()
      .where('warehouse_pull_session_id', sessionID)
      .select('id')
      .castTo<{ id: number }[]>();

    if (orders.length === 0) {
      return res.status(200).json({
        ok: true,
        message: 'Informacion de la sesion obtenida correctamente',
        data: {
          hasOrders: false,
          hasParts: false,
        },
      });
    }

    const parts = await WarehousePullSessionOrderParts.query()
      .where(
        'warehouse_pull_session_order_id',
        'in',
        orders.map((o): number => o.id)
      )
      .select('id')
      .castTo<{ id: number }[]>();

    if (parts.length === 0) {
      return res.status(200).json({
        ok: true,
        message: 'Informacion de la sesion obtenida correctamente',
        data: {
          hasOrders: true,
          hasParts: false,
        },
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Informacion de la sesion obtenida correctamente',
      data: {
        hasOrders: true,
        hasParts: true,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(`Error: ${error.message}`);

      return res
        .status(400)
        .json({ ok: false, message: `Error: ${error.message}` });
    }

    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function roundedQuantity(
  req: Request<unknown, unknown, z.infer<typeof RoundedQuantitySchema>>,
  res: Response
) {
  try {
    const { quantity, sessionOrderPartID } = req.body;

    const sessionOrderPart = await WarehousePullSessionOrderParts.query()
      .where('id', sessionOrderPartID)
      .first()
      .castTo<WarehousePullSessionOrderParts>();

    if (!sessionOrderPart) {
      return res.status(400).json({
        ok: false,
        message: `No se encontró el material: ${sessionOrderPartID}`,
      });
    }

    if (
      Number(quantity.toFixed(2)) ===
      Number(sessionOrderPart.quantity_bom.toFixed(2))
    ) {
      return res.status(400).json({
        ok: false,
        message: 'La cantidad es la misma, no se realizó ninguna ajuste',
      });
    }

    if (
      Number(quantity.toFixed(2)) >
      Number(sessionOrderPart.quantity_bom.toFixed(2)) + 1.5
    ) {
      return res.status(400).json({
        ok: false,
        message: `La cantidad no puede ser mayor a la cantidad requerida del BOM (${sessionOrderPart.quantity_bom.toFixed(
          2
        )})`,
      });
    }

    const roundedQuantity = await WarehousePullSessionOrderParts.query()
      .where('id', sessionOrderPartID)
      .update({
        quantity_rounded: quantity,
      });

    return res.status(200).json({
      ok: true,
      message: 'Cantidad ajustada correctamente',
      data: {
        quantity_rounded: roundedQuantity,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(`Error: ${error.message}`);

      return res
        .status(400)
        .json({ ok: false, message: `Error: ${error.message}` });
    }

    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function createNewSession(
  req: Request<unknown, unknown, z.infer<typeof CreateNewSessionSchema>>,
  res: Response
) {
  try {
    const {
      employeeID,
      sessionTypeID,
      reason,
      comment,
      customer,
      isReposition,
    } = req.body;

    const newSession = await WarehousePullSessions.query().insert({
      employee_id: employeeID,
      warehouse_pull_session_type_id: sessionTypeID,
      default_reason: reason,
      default_comment: comment,
      customer,
      is_reposition: isReposition,
    });

    return res.status(200).json({
      ok: true,
      message: 'Sesion creada correctamente',
      data: {
        id: newSession.id,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(`Error: ${error.message}`);

      return res
        .status(400)
        .json({ ok: false, message: `Error: ${error.message}` });
    }

    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function getInProgressOrders(req: Request, res: Response) {
  try {
    const { customer, sessionType } = req.body as unknown as {
      customer: string;
      sessionType: number;
    };

    let ordersQuery = WarehousePullSessionOrders.query()
      .join(
        'warehouse_pull_session_order_parts',
        'warehouse_pull_session_order_parts.warehouse_pull_session_order_id',
        'warehouse_pull_session_orders.id'
      )
      .join(
        'warehouse_pull_session_allocations',
        'warehouse_pull_session_allocations.warehouse_pull_session_order_part_id',
        'warehouse_pull_session_order_parts.id'
      )
      .innerJoin(
        'mo_numbers',
        'mo_numbers.mo_id',
        'warehouse_pull_session_orders.mo_id'
      )
      .innerJoin(
        'warehouse_pull_sessions',
        'warehouse_pull_sessions.id',
        'warehouse_pull_session_orders.warehouse_pull_session_id'
      )
      .whereNull('warehouse_pull_session_allocations.poly_updated_at')
      .whereNotNull('warehouse_pull_sessions.finished_at')
      .where('warehouse_pull_sessions.status', 'in_progress')
      .select([
        { sessionID: 'warehouse_pull_sessions.id' },
        'warehouse_pull_session_orders.mo_id',
        'warehouse_pull_sessions.created_at',
        'mo_numbers.customer',
        'mo_numbers.quantity',
        'mo_numbers.num',
        'mo_numbers.style',
        'mo_numbers.material_date',
        'mo_numbers.mo_status',
      ])
      .castTo<
        {
          sessionID: number;
          mo_id: number;
          created_at: string;
          customer: string;
          quantity: number;
          num: string;
          style: string;
          material_date: string;
        }[]
      >();

    if (customer) {
      ordersQuery = ordersQuery.where('mo_numbers.customer', customer);
    }

    if (sessionType) {
      ordersQuery = ordersQuery.where(
        'warehouse_pull_sessions.warehouse_pull_session_type_id',
        Number(sessionType)
      );
    }

    const orders = await ordersQuery;

    if (orders.length === 0) {
      return res.status(200).json({
        ok: true,
        message: 'No hay sesiones en progreso',
        data: [],
      });
    }

    return res.status(200).json({
      ok: true,
      message: 'Sesiones en progreso obtenidas correctamente',
      data: orders,
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(`Error: ${error.message}`);

      return res
        .status(400)
        .json({ ok: false, message: `Error: ${error.message}` });
    }
    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function getSessionsActiveByEmployee(req: Request, res: Response) {
  try {
    const { employeeID } = req.params as unknown as { employeeID: number };

    const sessions = await WarehousePullSessions.query()
      .join(
        'warehouse_pull_session_types',
        'warehouse_pull_session_types.id',
        'warehouse_pull_sessions.warehouse_pull_session_type_id'
      )
      .where('warehouse_pull_sessions.employee_id', employeeID)
      .whereNull('warehouse_pull_sessions.finished_at')
      .select([
        { sessionID: 'warehouse_pull_sessions.id' },
        {
          sessionType: 'warehouse_pull_session_types.name',
        },
        {
          createdAt: 'warehouse_pull_sessions.created_at',
        },
        'warehouse_pull_sessions.customer',
        {
          reason: 'warehouse_pull_sessions.default_reason',
        },
        {
          isReposition: 'warehouse_pull_sessions.is_reposition',
        },
      ])
      .orderBy('warehouse_pull_sessions.created_at', 'asc')
      .castTo<
        {
          sessionID: number;
          sessionType: string;
          createdAt: Date;
          customer: string;
          reason: string;
          isReposition: boolean;
        }[]
      >();

    const sessionsWithDetails = await Promise.all(
      sessions.map(async (session) => {
        const orders = await WarehousePullSessionOrders.query()
          .where('warehouse_pull_session_id', session.sessionID)
          .select('id')
          .castTo<{ id: number }[]>();

        if (orders.length === 0)
          return {
            ...session,
            hasOrders: false,
            hasParts: false,
          };

        const parts = await WarehousePullSessionOrderParts.query()
          .whereIn(
            'warehouse_pull_session_order_id',
            orders.map((o): number => o.id)
          )
          .select('id')
          .castTo<{ id: number }[]>();

        return {
          ...session,
          hasOrders: true,
          hasParts: parts.length > 0,
        };
      })
    );

    return res.status(200).json({
      ok: true,
      message: 'Sesiones obtenidas correctamente',
      data: {
        sessions: sessionsWithDetails.sort((a, b) => b.sessionID - a.sessionID),
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(`Error: ${error.message}`);

      return res
        .status(400)
        .json({ ok: false, message: `Error: ${error.message}` });
    }
    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function csvMaterials(req: Request, res: Response) {
  try {
    const { downloadID } = req.params as unknown as { downloadID: number };

    const download = await WarehouseDownloaderOrders.query()
      .where('id', downloadID)
      .select(['id', 'mo_id'])
      .first()
      .castTo<{ id: number; mo_id: number }>();

    if (!download) {
      return res
        .status(404)
        .json({ ok: false, message: 'No se encontro la sesion de descarga' });
    }

    const materials = await WarehousePullSessionAllocations.query()
      .join(
        'warehouse_pull_session_order_parts',
        'warehouse_pull_session_order_parts.id',
        'warehouse_pull_session_allocations.warehouse_pull_session_order_part_id'
      )
      .join(
        'warehouse_pull_session_orders',
        'warehouse_pull_session_orders.id',
        'warehouse_pull_session_order_parts.warehouse_pull_session_order_id'
      )
      .join(
        'warehouse_pull_sessions',
        'warehouse_pull_sessions.id',
        'warehouse_pull_session_orders.warehouse_pull_session_id'
      )
      .join(
        'warehouse_pull_session_types',
        'warehouse_pull_session_types.id',
        'warehouse_pull_sessions.warehouse_pull_session_type_id'
      )
      .join(
        'mo_numbers',
        'mo_numbers.mo_id',
        'warehouse_pull_session_orders.mo_id'
      )
      .join(
        'employees',
        'employees.employee_id',
        'warehouse_pull_sessions.employee_id'
      )
      .where('warehouse_pull_session_orders.mo_id', download.mo_id)
      .where('warehouse_pull_sessions.status', 'in_progress')
      .select([
        { ppmo: 'mo_numbers.mo_barcode' },
        'warehouse_pull_session_order_parts.part_number',
        'warehouse_pull_session_allocations.container_code',
        { pulling_quantity: 'warehouse_pull_session_allocations.quantity' },
        {
          poly_updated_flag: WarehousePullSessionAllocations.raw(
            'CASE WHEN warehouse_pull_session_allocations.poly_updated_at IS NULL THEN 0 ELSE 1 END'
          ),
        },
        {
          reason: 'warehouse_pull_sessions.default_reason',
        },
      ])
      .orderBy('warehouse_pull_session_order_parts.part_number', 'asc')
      .castTo<
        {
          ppmo: string;
          part_number: string;
          container_code: string;
          pulling_quantity: number;
          poly_updated_flag: number;
          reason: string;
        }[]
      >();

    if (materials.length === 0) {
      return res
        .status(404)
        .json({ ok: false, message: 'No se encontraron materiales' });
    }

    const response: { data: ReasonsPoly[] } = await axios.post(
      'https://restfulapi.varpro.org/PolyService.svc/poly/table',
      {
        query: 'ShowTransactionReasons',
        columns: 'DropDownValue, TransactionReasonCode',
        filter: "[StatusName] = 'Active'",
      }
    );

    const result = response.data.map(
      ({
        DropDownValueID,
        DropDownValue,
        TransactionReasonCode,
      }: ReasonsPoly) => ({
        id: TransactionReasonCode,
        name: DropDownValue,
        valueID: DropDownValueID,
      })
    );

    const allMaterialDownloaded = materials.every((material) => {
      return material.poly_updated_flag === 1;
    });

    if (!allMaterialDownloaded) {
      logger.error('Existen materiales sin descargar');

      return res.status(400).json({
        ok: false,
        message:
          'Existen materiales sin descargar, descargue los materiales pendientes',
      });
    }

    const dataCSV = materials.map((material) => {
      const findReasonID = result.find(
        (reason) => reason.name === material.reason
      );

      if (!findReasonID)
        throw new Error(`No se encontro la razon: ${material.reason}`);

      return {
        mfg: 'MFG',
        ppmo: material.ppmo,
        date: dayjs().format('YYYYMDD'),
        hour: dayjs().format('HHmms'),
        quantity: 1,
        takq: 'TAKQ',
        pprc: material.container_code,
        description: '',
        pulling_quantity: material.pulling_quantity,
        description_2: '',
        reason: findReasonID.id,
      };
    });

    return res.status(200).json({
      ok: true,
      message: 'Sesion de descarga pendiente, materiales descargados',
      data: {
        csv: dataCSV,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(`Error: ${error.message}`);

      return res
        .status(400)
        .json({ ok: false, message: `Error: ${error.message}` });
    }
    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}

export async function finishDownloadSession(req: Request, res: Response) {
  try {
    const { downloadID } = req.params as unknown as { downloadID: number };

    const download = await WarehouseDownloaderOrders.query()
      .where('id', downloadID)
      .whereNull('finished_at')
      .first();

    if (!download) {
      return res
        .status(404)
        .json({ ok: false, message: 'No se encontro la sesion de descarga' });
    }

    const materials = await WarehousePullSessionAllocations.query()
      .join(
        'warehouse_pull_session_order_parts',
        'warehouse_pull_session_order_parts.id',
        'warehouse_pull_session_allocations.warehouse_pull_session_order_part_id'
      )
      .join(
        'warehouse_pull_session_orders',
        'warehouse_pull_session_orders.id',
        'warehouse_pull_session_order_parts.warehouse_pull_session_order_id'
      )
      .join(
        'warehouse_pull_sessions',
        'warehouse_pull_sessions.id',
        'warehouse_pull_session_orders.warehouse_pull_session_id'
      )
      .join(
        'warehouse_pull_session_types',
        'warehouse_pull_session_types.id',
        'warehouse_pull_sessions.warehouse_pull_session_type_id'
      )
      .join(
        'mo_numbers',
        'mo_numbers.mo_id',
        'warehouse_pull_session_orders.mo_id'
      )
      .join(
        'employees',
        'employees.employee_id',
        'warehouse_pull_sessions.employee_id'
      )
      .join(
        'warehouse_downloader_orders',
        'warehouse_downloader_orders.mo_id',
        'warehouse_pull_session_orders.mo_id'
      )
      .where('warehouse_pull_session_orders.mo_id', download.mo_id)
      .where('warehouse_downloader_orders.id', download.id)
      .where('warehouse_pull_sessions.status', 'in_progress')
      .select([
        { ppmo: 'mo_numbers.mo_barcode' },
        'warehouse_pull_session_order_parts.part_number',
        'warehouse_pull_session_allocations.container_code',
        { pulling_quantity: 'warehouse_pull_session_allocations.quantity' },
        {
          poly_updated_flag: WarehousePullSessionAllocations.raw(
            'CASE WHEN warehouse_pull_session_allocations.poly_updated_at IS NULL THEN 0 ELSE 1 END'
          ),
        },
      ])
      .orderBy('warehouse_pull_session_order_parts.part_number', 'asc')
      .castTo<
        {
          ppmo: string;
          part_number: string;
          container_code: string;
          pulling_quantity: number;
          poly_updated_flag: number;
        }[]
      >();

    if (materials.length === 0) {
      return res
        .status(404)
        .json({ ok: false, message: 'No se encontraron materiales' });
    }

    if (materials.every((material) => material.poly_updated_flag === 1)) {
      await WarehouseDownloaderOrders.query().where('id', downloadID).update({
        finished_at: new Date(),
      });

      return res
        .status(200)
        .json({ ok: true, message: 'Sesion de descarga finalizada' });
    }

    return res.status(400).json({
      ok: false,
      message:
        'Materiales pendientes de actualizar, no se puede finalizar la sesion de descarga',
    });
  } catch (error) {
    if (error instanceof Error) {
      logger.error(`Error: ${error.message}`);

      return res
        .status(400)
        .json({ ok: false, message: `Error: ${error.message}` });
    }
    logger.error(`Error interno del servidor: ${error}`);

    return res
      .status(500)
      .json({ ok: false, message: 'Error interno del servidor' });
  }
}
