import type { ModelObject } from 'objection';

import { Model } from '@app/db';
import type { MoNumberFull } from '@app/services/plotter';

interface PlyLay {
  plys: number;
  end: number;
  length: number;
  totalSizes: number;
}

export class PlotterCombo extends Model {
  static tableName = 'plotter_combos';
  static idColumn = 'id';

  id!: number;
  created_at!: Date;
  updated_at!: Date;
  mo_id!: number;
  combo_number!: string;
  part_number!: string;
  name!: string;
  description!: string;
  created_by_employee_id!: number;
  plotter_print_id!: number;
  is_laser!: boolean;
  removed_at!: Date;
  bundled_at!: Date;
  style_combos!: string;
}

export class MaterialAllocations extends Model {
  static tableName = 'material_allocations';
  static idColumn = 'id';

  id!: number;
  poly_raw_allocation_id!: number;
  poly_manufacture_id!: number;
  num!: string;
  mo_status!: string;
  part_number!: string;
  poly_raw_material_id!: number;
  part_color!: string;
  poly_component_id!: number;
  component_name!: string;
  category_name!: string;
  sub_category_name!: string;
  unit_symbol!: string;
  quantity_on_hand!: number;
  quantity_ordered!: number;
  quantity_allocated!: number;
  quantity_required!: number;
  quantity_adjust!: number;
  quantity_withdrawn!: number;
  order_delivery_date!: Date;
  style_number!: string;
  style_id!: number;
  style_color!: string;
  customer!: string;
  stock_warehouse_id!: number;
  company_code!: number;
}

export class MaterialAllocationsGarmentPart extends Model {
  static tableName = 'material_allocations_garment_parts';
  static idColumn = 'id';
}

export type PlotterCombosShape = ModelObject<PlotterCombo> & {
  mo?: Partial<MoNumberFull>;
  create_print?: boolean;
};

export class PlotterComboLog extends Model {
  static tableName = 'plotter_combo_log';
  static idColumn = 'id';

  id!: number;
  created_at!: Date;
  updated_at!: Date;
  plotter_combo_id!: number;
  created_by_employee_id!: number;
  type!: string;
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  data!: any; // eslint-disable-line @typescript-eslint/no-explicit-any
}
export type PlotterComboLogShape = ModelObject<PlotterComboLog>;

export class PlotterPrint extends Model {
  static tableName = 'plotter_prints';
  static idColumn = 'id';
  static jsonAttributes = ['plys'];

  id!: number;
  created_at!: Date;
  updated_at!: Date;
  part_number!: string;
  created_by_employee_id!: number;
  name!: string;
  plys!: PlyLay[];
  quantity!: number;
  width!: number;
  length!: number;
  utilization!: number;
  total_sizes!: number;
  marked_at!: Date;
  printed_at!: Date;
  warehoused_at!: Date;
  spread_at!: Date;
  spread_work_area_group_id!: number;
  spread_work_area_group_shift_id!: number;
  spread_work_area_line_id!: number;
  spread_scanned!: boolean;
  cut_at!: Date;
  cut_work_area_group_id!: number;
  cut_work_area_group_shift_id!: number;
  cut_work_area_line_id!: number;
  cut_scanned!: boolean;
  plotter_roll_id!: number;
  plys_fabric_yards!: number;
  roll_sort!: number;
  models_number!: number;
  total_pieces!: number;
  extra_fabric!: number;
  location!: string;
  print_group_id!: number;
  print_group_sort!: number;
}
export type PlotterPrintShape = ModelObject<PlotterPrint>;

export type PlotterPrintGroupShape = ModelObject<PlotterPrint>[];

export class PlotterPrintLog extends Model {
  static tableName = 'plotter_print_log';
  static idColumn = 'id';

  id!: number;
  created_at!: Date;
  updated_at!: Date;
  plotter_print_id!: number;
  created_by_employee_id!: number;
  type!: string;
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  data!: any; // eslint-disable-line @typescript-eslint/no-explicit-any
}
export type PlotterPrintLogShape = ModelObject<PlotterPrintLog>;

export class PlotterRoll extends Model {
  static tableName = 'plotter_rolls';
  static idColumn = 'id';

  id!: number;
  created_at!: Date;
  updated_at!: Date;
  created_by_employee_id!: number;
  name!: string;
  checkout_at!: Date;
  recipient_employee_id!: number;
  recipient_name!: string;
  plotter_machine_number!: number;
}
export type PlotterRollShape = ModelObject<PlotterRoll>;

export class PlotterRollLog extends Model {
  static tableName = 'plotter_roll_log';
  static idColumn = 'id';

  id!: number;
  created_at!: Date;
  updated_at!: Date;
  plotter_roll_id!: number;
  created_by_employee_id!: number;
  type!: string;
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  data!: any; // eslint-disable-line @typescript-eslint/no-explicit-any
}

export class PlotterPrintGroups extends Model {
  static tableName = 'plotter_print_groups';
  static idColumn = 'id';

  id!: number;
  created_by_employee_id!: number;
  created_at!: Date;
  updated_at!: Date;
}
