import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('print_ids', (table: Knex.CreateTableBuilder): void => {
      table.increments('id').primary();
      table.integer('machine_id').notNullable().unsigned();
      table.boolean('is_repo').defaultTo(false);
      table.string('description', 255).nullable();
      table.boolean('is_active').defaultTo(true);
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').defaultTo(knex.fn.now());
      table.foreign('machine_id').references('id').inTable('work_area_lines');
    })
    .createTable('print_mos', (table: Knex.CreateTableBuilder): void => {
      table.increments('id').primary();
      table.integer('print_id', 10).unsigned().notNullable();
      table.integer('mo_id').notNullable();
      table.boolean('is_active').defaultTo(true);
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').defaultTo(knex.fn.now());
      table.foreign('print_id').references('id').inTable('print_ids');
      table.foreign('mo_id').references('mo_id').inTable('mo_numbers');
    });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('print_mos').dropTable('print_ids');
}
