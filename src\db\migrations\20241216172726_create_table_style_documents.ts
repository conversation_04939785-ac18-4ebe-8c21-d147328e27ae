import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(
    'style_documents',
    (table: Knex.TableBuilder) => {
      table.increments('id').unsigned().primary();
      table.integer('style_document_type_id').unsigned().notNullable();
      table.integer('style_id').unsigned().notNullable();
      table.text('comment').notNullable();
      table.boolean('is_removed').defaultTo(false).notNullable();
      table.string('file_uuid', 200).notNullable();
      table.string('file_extension', 20).notNullable();
      table
        .timestamp('created_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      table
        .timestamp('updated_at')
        .notNullable()
        .defaultTo(knex.raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
      table
        .foreign('style_document_type_id', 'fk_style_document_type_id')
        .references('id')
        .inTable('style_document_types');
      table
        .foreign('style_id', 'fk_style_id')
        .references('style_id')
        .inTable('styles');
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('style_documents');
}
