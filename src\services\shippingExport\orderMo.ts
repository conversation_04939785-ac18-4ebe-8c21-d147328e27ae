import type {
  PolyActiveOrderDetails,
  PolyGoodsAllocation,
  PolyManufactureDetails,
} from '@app/Librarys/vpPolyService';
import {
  getPolyActiveOrderDetails,
  getPolyGoodsAllocations,
  getPolyManufactureDetails,
} from '@app/Librarys/vpPolyService';
import { knex } from '@app/db';

interface goodAllocationHistory {
  ga_customer: string;
  ga_order: string;
  ga_item: string;
  ga_size: string;
  ga_mo_numbers: string;
  mo_id: number;
  mo_required_date: string;
  mo_finish_date: string;
  mo_status: string;
}

interface OrderDetailsWithAllocationMosFilters {
  customerNumbers?: string;
  poNumbers?: string;
}

type DetailStatus =
  | 'unknown'
  | 'unallocated'
  | 'processing'
  | 'packing'
  | 'shipped';

export const orderDetailsWithAllocationMos = async (
  filters?: OrderDetailsWithAllocationMosFilters
) => {
  console.log('getting order details');
  const orderDetails = await getPolyActiveOrderDetails(filters);
  console.log('orderDetails', orderDetails.length);
  const detailAllocations = await getPolyGoodsAllocations(filters);
  console.log('detailAllocations', detailAllocations.length);

  // check for missing orderdetailid
  for (const orderDetail of orderDetails) {
    if (!orderDetail.OrderDetailsID) {
      throw new Error('OrderDetailsID is required on order details');
    }
  }
  for (const detailAllocation of detailAllocations) {
    if (!detailAllocation.OrderDetailsID) {
      throw new Error('OrderDetailsID is required on goods allocation');
    }
  }

  // create default values of null from orderDetailsGrouped
  const defaultOrderDetails = Object.fromEntries(
    Object.keys(detailAllocations[0]).map((key) => [key, null])
  ) as PolyGoodsAllocation;

  // group by orderdetailsid
  // sort by goods allocation id so last one is used
  const sortedDetailAllocations = detailAllocations.sort(
    (a, b) => parseInt(b.GoodsDetailsID) - parseInt(a.GoodsDetailsID)
  );
  const orderDetailsGrouped: { [orderDetailsId: string]: PolyGoodsAllocation } =
    sortedDetailAllocations.reduce((acc, orderDetail) => {
      if (!acc[orderDetail.OrderDetailsID]) {
        acc[orderDetail.OrderDetailsID] = {};
      }
      acc[orderDetail.OrderDetailsID] = orderDetail;
      return acc;
    }, {});

  // map goods allocation to order details
  let allocationsMatched = 0;
  const orderDetailsWithAllocations: (PolyActiveOrderDetails &
    PolyGoodsAllocation)[] = orderDetails.map((orderDetail) => {
    const gad = orderDetailsGrouped[orderDetail.OrderDetailsID];
    if (gad) {
      allocationsMatched++;
    }
    return {
      ...(gad || defaultOrderDetails),
      ...orderDetail,
    };
  });

  console.log('allocations matched', allocationsMatched);

  // check for missing orderdetailid
  for (const orderDetail of orderDetailsWithAllocations) {
    if (!orderDetail.OrderDetailsID) {
      throw new Error('OrderDetailsID is required on order detail allocations');
    }
  }

  console.log('orderDetails grabbed', orderDetails.length);
  console.log('order details', typeof orderDetails);

  // map goods allocation to order details
  console.log('getting good allocation details');
  const goodsAllocationHistoryRaw = await knex.raw<
    [goodAllocationHistory[], any]
  >(`
        select
	gad.*,
	mo.mo_id,
  mo.required_date as mo_required_date,
  mo.finish_date as mo_finish_date,
  mo.mo_status as mo_status
from (
select
    ga.CustomerNumber as ga_customer, 
    ga.OrderNumber as ga_order, 
    ga.ItemNumber as ga_item, 
    ga.GarmentSize as ga_size,
	GROUP_CONCAT(DISTINCT if (ga.ManufactureNumber ='', null, ga.ManufactureNumber)) as ga_mo_numbers
from good_allocations ga
left join mo_numbers mo on mo.num = ga.ManufactureNumber and mo.customer = ga.CustomerNumber and mo.mo_status <> 'Void' 
where ga.CustomerNumber is not null
and ga.OrderNumber is not null
and ga.ItemNumber is not null
and ga.GarmentSize is not null
and ga.ManufactureNumber is not null
and mo.mo_id is not null
and (mo.mo_status = null OR mo.mo_status <> 'Void')
and ga.OrderNumber IN (${orderDetails
    .map((orderDetail) => `'${orderDetail.OrderNumber}'`)
    .join(',')})
group by ga.CustomerNumber, ga.OrderNumber, ga.ItemNumber, ga.GarmentSize
) as gad
left join mo_numbers mo on mo.num = gad.ga_mo_numbers and mo.customer = gad.ga_customer`);

  const goodAllocationHistory = goodsAllocationHistoryRaw[0];

  // create default values of null from orderDetailsGrouped
  const defaultGoodsAllocationHistory = Object.fromEntries(
    Object.keys(goodAllocationHistory[0]).map((key) => [key, null])
  ) as goodAllocationHistory;

  // map order details to goods allocation details
  const goodsAllocationHistoryMap: Record<string, goodAllocationHistory> =
    goodAllocationHistory.length === 0
      ? {}
      : goodAllocationHistory.reduce((acc, gad) => {
          acc[
            `${gad.ga_customer}-${gad.ga_order}-${gad.ga_item}-${gad.ga_size}`
          ] = gad;
          return acc;
        }, {});

  const goodsAllocationExampleKey = Object.keys(goodsAllocationHistoryMap)[0];
  console.log(
    'goodsAllocationDetailMap example',
    goodsAllocationExampleKey,
    goodsAllocationHistoryMap[goodsAllocationExampleKey]
  );

  const orderDetailsWithAllocatoinAndLastMo: ((typeof orderDetailsWithAllocations)[0] &
    goodAllocationHistory & {
      used_mo: string;
      used_mo_required_date: string;
      used_mo_status: string;
      detail_status: DetailStatus;
    })[] = [];
  let matches = 0;
  for (const orderDetail of orderDetailsWithAllocations) {
    const key = `${orderDetail.CustomerNumber}-${orderDetail.OrderNumber}-${orderDetail.ItemNumber}-${orderDetail.GarmentSize}`;
    const gad = goodsAllocationHistoryMap[key];
    let useStatus: DetailStatus = 'unallocated';
    if (Number(orderDetail.QuantityAllocated) > 0) {
      useStatus = 'processing';
    }
    if (Number(orderDetail.ShipCount) > 0) {
      useStatus = 'shipped';
    }
    if (
      Number(orderDetail.Inventory_to_SO_Allocated) > 0 ||
      (Number(orderDetail.MO_to_SO_Allocated) > 0 &&
        (orderDetail.RunningTask_ === 'Pre-Pack' ||
          orderDetail.RunningTask_ === 'Packed'))
    ) {
      useStatus = 'packing';
    }

    const useMoFromLastMo =
      !orderDetail.ManufactureNumber ||
      orderDetail.ManufactureNumber.trim() === '';
    if (gad) {
      orderDetailsWithAllocatoinAndLastMo.push({
        ...orderDetail,
        ...gad,
        used_mo: useMoFromLastMo
          ? gad.ga_mo_numbers
          : orderDetail.ManufactureNumber,
        used_mo_required_date: useMoFromLastMo
          ? gad.mo_required_date
          : orderDetail.TargetDate_,
        used_mo_status: useMoFromLastMo
          ? gad.mo_status
          : orderDetail.MfgStatus_,
        detail_status: useStatus,
      });
      matches++;
    } else {
      orderDetailsWithAllocatoinAndLastMo.push({
        ...orderDetail,
        ...defaultGoodsAllocationHistory,
        used_mo: orderDetail.ManufactureNumber,
        used_mo_required_date: orderDetail.TargetDate_,
        used_mo_status: orderDetail.MfgStatus_,
        detail_status: useStatus,
      });
    }
  }

  console.log(
    'orderItemsWithMos',
    'matches',
    matches,
    'length',
    orderDetailsWithAllocatoinAndLastMo.length,
    'example',
    orderDetailsWithAllocatoinAndLastMo[0]
  );

  // Filter out any order details that we dont want
  // ignore orderTypeName = 'Sample'
  const orderDetailsWithAllocatoinAndLastMoFiltered =
    orderDetailsWithAllocatoinAndLastMo.filter(
      (orderDetail) => orderDetail.OrderTypeName !== 'Sample'
    );

  return orderDetailsWithAllocatoinAndLastMoFiltered;
};

const getDetailStatus = (
  orderDetail: PolyActiveOrderDetails,
  moDetail?: PolyManufactureDetails | null
) => {
  let useStatus: DetailStatus = null;
  if (Number(orderDetail.ShipCount) >= Number(orderDetail.ActualCount)) {
    useStatus = 'shipped';
  }
  if (Number(orderDetail.PackCount) > 0) {
    useStatus = 'packing';
  }
  if (Number(orderDetail.CommitCount) > 0) {
    useStatus = 'packing';
  }
  if (Number(orderDetail.AllocateCount) > 0) {
    useStatus = 'processing';
    if (
      moDetail &&
      (moDetail.RunningTask_ === 'Pre-Pack' ||
        moDetail.RunningTask_ === 'Packed')
    ) {
      useStatus = 'packing';
    }
  }
  if (
    Number(orderDetail.UnallocateCount) === Number(orderDetail.ActualCount) ||
    Number(orderDetail.UnallocateCount) === Number(orderDetail.RequestCount)
  ) {
    useStatus = 'unallocated';
  }
  if (!useStatus) {
    useStatus = 'unknown';
  }
  return useStatus;
};

export interface OrderItemDetailCondensed {
  customer: string;
  poNumber: string;
  orderNumber: string;
  itemNumber: string;
  manufactureNumber: string;
  size: string;
  quantity: number;
  style: string;
  itemDescription8: string;
  styleCategory: string;
  styleSubCategory: string;
  orderStatus: string;
  detailStatus: DetailStatus;
  moXfactoryDate: string;
  moSchedFinishDate: string;
  moStatus: string;
  retailerPo: string;
  orderType2: string;
  shipToCountry: string;
  shipToName: string;
  sizeQtys: string;
}

interface OrderDetailMoMap {
  manufactureNumber: string;
  quantity: number;
}

export const orderDetailsMoCondensedData = async (
  filters?: OrderDetailsWithAllocationMosFilters
) => {
  console.log('getting order details');
  const orderDetails = await getPolyActiveOrderDetails(filters);
  console.log('orderDetails', orderDetails.length);
  const moDetails = await getPolyManufactureDetails(filters);
  console.log('moDetails', moDetails.length);

  // check for missing orderdetailid, should not happen
  for (const orderDetail of orderDetails) {
    if (!orderDetail.OrderDetailsID) {
      throw new Error('OrderDetailsID is required on order details');
    }
  }

  for (const moDetailItem of moDetails) {
    if (!moDetailItem.ManufactureUnitID) {
      throw new Error('OrderDetailsID is required on goods allocation');
    }
  }

  const mosOrderDetailMap = moDetails.reduce((acc, moDetail) => {
    if (!acc[moDetail.ManufactureUnitID]) {
      acc[moDetail.ManufactureUnitID] = [];
    }
    acc[moDetail.ManufactureUnitID].push({
      manufactureNumber: moDetail.ManufactureNumber,
      quantity: Number(moDetail.QuantityOrdered),
    });
    return acc;
  }, {} as Record<string, OrderDetailMoMap[]>);

  const mosGroupedByMO: {
    [orderDetailsId: string]: {
      detailMap: Record<string, PolyManufactureDetails>;
      firstStatus: string;
      firstOrder: string;
      firstItemNumber: string;
      firstXfactoryDate: string;
      firstSchedFinishDate: string;
      multipleOrderItems: boolean;
    };
  } = {};
  for (const moDetail of moDetails) {
    if (!mosGroupedByMO[moDetail.ManufactureNumber]) {
      mosGroupedByMO[moDetail.ManufactureNumber] = {
        detailMap: {},
        firstStatus: moDetail.MfgStatusName,
        firstOrder: moDetail.ItemOrderNumber,
        firstItemNumber: moDetail.ItemNumber2,
        firstXfactoryDate: moDetail.TargetDate,
        firstSchedFinishDate: moDetail.SchedFinish,
        multipleOrderItems: false,
      };
    }
    if (
      mosGroupedByMO[moDetail.ManufactureNumber].detailMap[
        moDetail.ManufactureUnitID
      ]
    ) {
      console.log(
        'duplicate',
        moDetail.ManufactureNumber,
        moDetail.ManufactureUnitID
      );
    }
    mosGroupedByMO[moDetail.ManufactureNumber].detailMap[
      moDetail.ManufactureUnitID
    ] = moDetail;
    if (
      mosGroupedByMO[moDetail.ManufactureNumber].firstOrder !==
      moDetail.ItemOrderNumber
    ) {
      mosGroupedByMO[moDetail.ManufactureNumber].multipleOrderItems = true;
    }
    if (
      mosGroupedByMO[moDetail.ManufactureNumber].firstItemNumber !==
      moDetail.ItemNumber2
    ) {
      mosGroupedByMO[moDetail.ManufactureNumber].multipleOrderItems = true;
    }
  }

  const orderDetailsGrouped: {
    [orderNumber: string]: {
      customer: string;
      poNumber: string;
      status: string;
      items: {
        [itemNumber: string]: {
          style: string;
          itemDescription8: string;
          styleCategory: string;
          styleSubCategory: string;
          retailerPo: string;
          orderType2: string;
          shipToCountry: string;
          shipToName: string;
          totalQty: number;
          detailsMap: Record<
            string,
            PolyActiveOrderDetails & {
              moNumbers: string[];
              detailStatus: DetailStatus;
            }
          >;
          hasMos: boolean;
          moQtys: Record<string, number>;
          moTotalQty: number;
          allSame: boolean;
          usedMoNumber: string | null;
          usedDetailStatus: DetailStatus | null;
        };
      };
    };
  } = {};
  for (const orderDetail of orderDetails) {
    if (!orderDetailsGrouped[orderDetail.OrderNumber]) {
      orderDetailsGrouped[orderDetail.OrderNumber] = {
        customer: orderDetail.CustomerNumber,
        poNumber: orderDetail.PONumber,
        status: orderDetail.OrderStatus,
        items: {},
      };
    }
    if (
      !orderDetailsGrouped[orderDetail.OrderNumber].items[
        orderDetail.ItemNumber
      ]
    ) {
      orderDetailsGrouped[orderDetail.OrderNumber].items[
        orderDetail.ItemNumber
      ] = {
        style: orderDetail.StyleNumber,
        itemDescription8: orderDetail.ItemDescription8_,
        styleCategory: orderDetail.StyleCategoryName,
        styleSubCategory: orderDetail.StyleSubcategoryName,
        retailerPo: orderDetail.RetailerPONumber,
        orderType2: orderDetail.OrderType2,
        shipToCountry: orderDetail.ShipToCountry_,
        shipToName: orderDetail.ShipToName,
        totalQty: 0,
        detailsMap: {},
        hasMos: false,
        moQtys: {},
        moTotalQty: 0,
        allSame: true,
        usedMoNumber: null,
        usedDetailStatus: null,
      };
    }
    const orderItem =
      orderDetailsGrouped[orderDetail.OrderNumber].items[
        orderDetail.ItemNumber
      ];
    if (
      orderDetailsGrouped[orderDetail.OrderNumber].items[orderDetail.ItemNumber]
        .detailsMap[orderDetail.OrderDetailsID]
    ) {
      throw new Error('duplicate order details id on order');
    }
    const moFounds = mosOrderDetailMap[orderDetail.OrderDetailsID];

    const useStatus = getDetailStatus(
      orderDetail,
      moFounds && moFounds.length === 1
        ? mosGroupedByMO[moFounds[0].manufactureNumber]?.detailMap[
            orderDetail.OrderDetailsID
          ] ?? null
        : null
    );

    orderDetailsGrouped[orderDetail.OrderNumber].items[
      orderDetail.ItemNumber
    ].detailsMap[orderDetail.OrderDetailsID] = {
      ...orderDetail,
      moNumbers: null,
      detailStatus: useStatus,
    };
    const orderItemDetail =
      orderDetailsGrouped[orderDetail.OrderNumber].items[orderDetail.ItemNumber]
        .detailsMap[orderDetail.OrderDetailsID];

    if (!orderItem.usedDetailStatus) {
      orderItem.usedDetailStatus = useStatus;
    }
    if (orderItem.usedDetailStatus !== useStatus) {
      orderItem.allSame = false;
    }
    if (moFounds) {
      orderItem.hasMos = true;
      for (const moFound of moFounds) {
        const moGroupData = mosGroupedByMO[moFound.manufactureNumber];
        if (!orderItem.usedMoNumber && orderItem.allSame) {
          orderItem.usedMoNumber = moFound.manufactureNumber;
        }
        if (orderItem.usedMoNumber !== moFound.manufactureNumber) {
          orderItem.allSame = false;
        }
        if (!orderItem.moQtys[moFound.manufactureNumber]) {
          orderItem.moQtys[moFound.manufactureNumber] = 0;
        }
        if (moGroupData.multipleOrderItems) {
          orderItem.allSame = false;
        }
        orderItem.moQtys[moFound.manufactureNumber] += Number(moFound.quantity);
        orderItem.moTotalQty += Number(moFound.quantity);
      }
      orderItemDetail.moNumbers = moFounds.map(
        (moFound) => moFound.manufactureNumber
      );
    }

    orderDetailsGrouped[orderDetail.OrderNumber].items[
      orderDetail.ItemNumber
    ].totalQty += Number(orderDetail.ActualCount);
  }

  // map goods allocation to order details
  const orderLineDetailsCondensed: OrderItemDetailCondensed[] = [];
  for (const orderNumber in orderDetailsGrouped) {
    const order = orderDetailsGrouped[orderNumber];
    for (const itemNumber in order.items) {
      const item = order.items[itemNumber];
      // if quantity on all MOs match quantity on order item, we use a single line with size {todos}
      // if not we show all detail lines with sizes
      if (
        !item.hasMos ||
        (item.moTotalQty === item.totalQty && item.allSame && item.usedMoNumber)
      ) {
        // single line
        const mo = mosGroupedByMO[item.usedMoNumber];
        orderLineDetailsCondensed.push({
          customer: order.customer,
          poNumber: order.poNumber,
          orderNumber,
          orderStatus: order.status,
          itemNumber,
          manufactureNumber: item.usedMoNumber,
          size: '{{todos}}',
          quantity: item.totalQty,
          style: item.style,
          itemDescription8: item.itemDescription8,
          styleCategory: item.styleCategory,
          styleSubCategory: item.styleSubCategory,
          detailStatus: item.usedDetailStatus,
          moXfactoryDate: mo?.firstXfactoryDate ?? null,
          moSchedFinishDate: mo?.firstSchedFinishDate ?? null,
          moStatus: mo?.firstStatus ?? null,
          retailerPo: item.retailerPo,
          orderType2: item.orderType2,
          shipToCountry: item.shipToCountry,
          shipToName: item.shipToName,
          sizeQtys: Object.entries(item.detailsMap)
            .map(
              ([orderDetailId, orderDetail]) =>
                `${orderDetail.GarmentSize}: ${orderDetail.ActualCount}`
            )
            .join(', '),
        });
      } else {
        // multiple lines
        for (const orderDetailId in item.detailsMap) {
          const orderDetail = item.detailsMap[orderDetailId];
          const moFounds = mosOrderDetailMap[orderDetailId];
          if (!moFounds) {
            orderLineDetailsCondensed.push({
              customer: order.customer,
              poNumber: order.poNumber,
              orderNumber,
              orderStatus: order.status,
              itemNumber,
              manufactureNumber: null,
              size: orderDetail.GarmentSize,
              quantity: Number(orderDetail.ActualCount),
              style: orderDetail.StyleNumber,
              itemDescription8: orderDetail.ItemDescription8_,
              styleCategory: orderDetail.StyleCategoryName,
              styleSubCategory: orderDetail.StyleSubcategoryName,
              detailStatus: orderDetail.detailStatus,
              moXfactoryDate: null,
              moSchedFinishDate: null,
              moStatus: null,
              retailerPo: orderDetail.RetailerPONumber,
              orderType2: orderDetail.OrderType2,
              shipToCountry: orderDetail.ShipToCountry_,
              shipToName: orderDetail.ShipToName,
              sizeQtys: `${orderDetail.GarmentSize}: ${orderDetail.ActualCount}`,
            });
          } else if (
            moFounds.length === 1 &&
            (moFounds[0].quantity === Number(orderDetail.RequestCount) ||
              moFounds[0].quantity === Number(orderDetail.ActualCount))
          ) {
            const moFound = moFounds[0];
            const mo = mosGroupedByMO[moFound.manufactureNumber];
            orderLineDetailsCondensed.push({
              customer: orderDetail.CustomerNumber,
              poNumber: orderDetail.PONumber,
              orderNumber,
              orderStatus: order.status,
              itemNumber,
              manufactureNumber: moFound.manufactureNumber,
              size: orderDetail.GarmentSize,
              quantity: Number(orderDetail.ActualCount),
              style: orderDetail.StyleNumber,
              itemDescription8: orderDetail.ItemDescription8_,
              styleCategory: orderDetail.StyleCategoryName,
              styleSubCategory: orderDetail.StyleSubcategoryName,
              detailStatus: orderDetail.detailStatus,
              moXfactoryDate: mo.firstXfactoryDate,
              moSchedFinishDate: mo.firstSchedFinishDate,
              moStatus: mo.firstStatus,
              retailerPo: orderDetail.RetailerPONumber,
              orderType2: orderDetail.OrderType2,
              shipToCountry: orderDetail.ShipToCountry_,
              shipToName: orderDetail.ShipToName,
              sizeQtys: `${orderDetail.GarmentSize}: ${orderDetail.ActualCount}`,
            });
          } else {
            console.log('order detail', orderDetail);
            console.log('order', order, 'item', item);
            console.log('mos found', moFounds);
            orderLineDetailsCondensed.push({
              customer: orderDetail.CustomerNumber,
              poNumber: orderDetail.PONumber,
              orderNumber,
              orderStatus: order.status,
              itemNumber,
              manufactureNumber: moFounds
                .map((mo) => mo.manufactureNumber)
                .join(', '),
              size: orderDetail.GarmentSize,
              quantity: Number(orderDetail.ActualCount),
              style: orderDetail.StyleNumber,
              itemDescription8: orderDetail.ItemDescription8_,
              styleCategory: orderDetail.StyleCategoryName,
              styleSubCategory: orderDetail.StyleSubcategoryName,
              detailStatus: orderDetail.detailStatus,
              moXfactoryDate: null,
              moSchedFinishDate: null,
              moStatus: 'multiple',
              retailerPo: orderDetail.RetailerPONumber,
              orderType2: orderDetail.OrderType2,
              shipToCountry: orderDetail.ShipToCountry_,
              shipToName: orderDetail.ShipToName,
              sizeQtys: `${orderDetail.GarmentSize}: ${orderDetail.ActualCount}`,
            });

            // throw new Error(
            //   `multiple mos found on order: ${orderNumber}, item: ${itemNumber}, mos: ${moFounds
            //     .map((mo) => mo.manufactureNumber)
            //     .join(', ')}`
            // );
          }
        }
      }
    }
  }

  console.log('orderLineDetailsCondensed', orderLineDetailsCondensed.length);
  console.log(
    'orderLineDetailsCondensed example',
    orderLineDetailsCondensed[0]
  );

  // sort by customer, order, item (number), size
  orderLineDetailsCondensed.sort((a, b) => {
    if (a.customer < b.customer) {
      return -1;
    }
    if (a.customer > b.customer) {
      return 1;
    }
    if (a.poNumber < b.poNumber) {
      return -1;
    }
    if (a.poNumber > b.poNumber) {
      return 1;
    }
    if (Number(a.itemNumber) < Number(b.itemNumber)) {
      return -1;
    }
    if (Number(a.itemNumber) > Number(b.itemNumber)) {
      return 1;
    }
    if (a.size < b.size) {
      return -1;
    }
    if (a.size > b.size) {
      return 1;
    }
    return 0;
  });

  return orderLineDetailsCondensed;
};
