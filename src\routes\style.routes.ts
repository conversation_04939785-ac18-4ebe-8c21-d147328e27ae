import { Router } from 'express';
import multer from 'multer';

import {
  getStyleCombosController,
  getStyleDocument,
  getSuggestedStylesController,
  updateStyleCombosController,
  uploadStyleComboDocumentsController,
} from '@app/controllers/style.controller';

const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

export const styleRouter = Router();

styleRouter.route('/suggestion/:style').get(getSuggestedStylesController);
styleRouter.route('/style-combos/:styleId').get(getStyleCombosController);
styleRouter.route('/get-document/:type/:styleId').get(getStyleDocument);
styleRouter.route('/update-style-combos').post(updateStyleCombosController);
styleRouter
  .route('/upload-style-document')
  .post(upload.single('file'), uploadStyleComboDocumentsController);
